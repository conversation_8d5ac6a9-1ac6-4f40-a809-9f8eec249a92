package net.summerfarm.mall.repository;

import com.aliyun.odps.utils.StringUtils;
import net.summerfarm.mall.facade.auth.AuthQueryFacade;
import net.summerfarm.mall.facade.usercenter.MerchantAccountQueryFacade;
import net.summerfarm.mall.facade.usercenter.convert.MerchantStoreAccountResultRespConvert;
import net.summerfarm.mall.mapper.MerchantSubAccountMapper;
import net.summerfarm.mall.model.domain.MerchantSubAccount;
import net.summerfarm.mall.model.vo.merchant.subaccount.MerchantSubAccountQuery;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.usercenter.client.merchant.enums.MerchantAccountEnums;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 子账号仓储层
 */
@Repository
public class MerchantSubAccountRepository {

    @Resource
    MerchantSubAccountMapper merchantSubAccountMapper;
    @Resource
    MerchantAccountQueryFacade merchantAccountQueryFacade;
    @Resource
    AuthQueryFacade authQueryFacade;

    /**
     * merchantSubAccountMapper.selectOne();
     * 根据openid,unionid,mp_openid,account_id,phone
     * 查询一个唯一的子账号
     * 不能同时查询
     *
     * @param merchantSubAccountQuery
     * @return
     */
    public MerchantSubAccount selectOne(MerchantSubAccountQuery merchantSubAccountQuery) {
       return selectOne(merchantSubAccountQuery, MerchantAccountEnums.DeleteFlag.NORMAL);
    }

    public MerchantSubAccount selectOne(MerchantSubAccountQuery merchantSubAccountQuery, MerchantAccountEnums.DeleteFlag deleteFlag) {
        // 根据微信信息的去去bizId
        if (!StringUtils.isEmpty(merchantSubAccountQuery.getMpOpenId())
                || !StringUtils.isEmpty(merchantSubAccountQuery.getOpenId())
                || !StringUtils.isEmpty(merchantSubAccountQuery.getUnionId())) {
            List<AuthUserAuthResp> resps = authQueryFacade.queryAuthUserAuthByAuthId(merchantSubAccountQuery);
            if (CollectionUtils.isEmpty(resps)) {
                return null;
            }
            // 这些去auth查询accountId
            if (Objects.isNull(merchantSubAccountQuery.getAccountId())){
                List<Long> accountIds = resps.stream().map(AuthUserAuthResp::getBizUserId).distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(accountIds)){
                    merchantSubAccountQuery.setAccountId(accountIds.get(0));
                }
            }
        }

        List<MerchantSubAccount> collect = mergeOldMsg(merchantAccountQueryFacade.getMerchantAccountByAccountIds(merchantSubAccountQuery.getAccountId()==null?null:Collections.singletonList(merchantSubAccountQuery.getAccountId()),
                merchantSubAccountQuery.getPhone(), deleteFlag).stream().map(MerchantStoreAccountResultRespConvert::convertMerchant).collect(Collectors.toList()));

        return CollectionUtils.isEmpty(collect) ? null : collect.get(0);
    }


    /**
     * 查询未删除 审核通过的
     *
     * @param mid mid
     * @return
     */
    public List<MerchantSubAccount> selectByMid(Long mid) {
        return mergeOldMsg(merchantAccountQueryFacade.getMerchantAccount(mid, MerchantAccountEnums.DeleteFlag.NORMAL, MerchantAccountEnums.Status.AUDIT_SUCCESS).
                stream().map(MerchantStoreAccountResultRespConvert::convertMerchant).collect(Collectors.toList()));
    }

    /**
     * 查询未删除 审核通过的
     *
     * @param mid mid
     * @return
     */
    public MerchantSubAccount selectMangerByMId(Long mid) {
        List<MerchantSubAccount> collect = mergeOldMsg(merchantAccountQueryFacade.getMerchantAccount(mid, MerchantAccountEnums.DeleteFlag.NORMAL, null).
                stream().map(MerchantStoreAccountResultRespConvert::convertMerchant).collect(Collectors.toList()).stream().filter(it->Objects.equals(it.getType(),MerchantAccountEnums.Type.MANAGER.getCode())).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(collect)){
            return null;
        }
        return collect.get(0);
    }

    /**
     * @param mid        店铺mid
     * @param deleteFlag 删除状态
     * @param status     审核状态
     * @return
     */
    public List<MerchantSubAccount> selectByMid(Long mid, MerchantAccountEnums.DeleteFlag deleteFlag, MerchantAccountEnums.Status status) {
        return mergeOldMsg(merchantAccountQueryFacade.getMerchantAccount(mid, deleteFlag, status).
                stream().map(MerchantStoreAccountResultRespConvert::convertMerchant).collect(Collectors.toList()));
    }


    /**
     * 补充用户中心没接的数据
     * @param accounts
     * @return
     */
    private List<MerchantSubAccount>  mergeOldMsg(List<MerchantSubAccount> accounts){
        if (CollectionUtils.isEmpty(accounts)){
            return new ArrayList<>();
        }
        List<Long> accountIds = accounts.stream().distinct().map(MerchantSubAccount::getAccountId).collect(Collectors.toList());
        //原数据表补充
        Map<Long, MerchantSubAccount> oldMap = oldMap(accountIds);
        accounts.forEach(
                it -> {
                    Long accountId = it.getAccountId();
                    MerchantSubAccount account = oldMap.get(accountId);
                    if (account != null) {
                        it.setPopView(account.getPopView());
                        it.setFirstPopView(account.getFirstPopView());
                        it.setmInfo(account.getmInfo());
                        it.setAuditUser(account.getAuditUser());
                        it.setAuditTime(account.getAuditTime());
                        it.setCashAmount(account.getCashAmount());
                        it.setCashUpdateTime(account.getCashUpdateTime());
                    }
                }
        );
        return accounts;
    }
    /**
     * old 用户中心没接
     *
     * @param accountIds
     * @return
     */
    private Map<Long, MerchantSubAccount> oldMap(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return new HashMap<>();
        }
        return merchantSubAccountMapper.listContactByAccountIds(accountIds).stream()
                .collect(Collectors.toMap(MerchantSubAccount::getAccountId, Function.identity(), (a, b) -> a));

    }


}
