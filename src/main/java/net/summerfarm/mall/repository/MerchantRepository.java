package net.summerfarm.mall.repository;

import cn.hutool.core.collection.CollUtil;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.facade.usercenter.MerchantQueryFacade;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.dto.merchant.MerchantDTO;
import net.summerfarm.mall.model.vo.MerchantVO;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * 门店仓库：从各个域获取所需的门店数据
 */
@Component
public class MerchantRepository {

    @Resource
    private MerchantQueryFacade merchantQueryFacade;

    @Resource
    private MerchantMapper merchantMapper;


    /**
     * 根据mid查询门店（不带权限）
     * @param mId
     * @return
     */
    public MerchantDTO getMerchantByMid(Long mId) {
        MerchantStoreAndExtendResp merchantExtends = merchantQueryFacade.getMerchantExtendsByMid(mId);
        if (null == merchantExtends) {
            return null;
        }

        MerchantDTO dto = toMerchant(merchantExtends);
        // 补充其他域的信息
        Merchant merchant = merchantMapper.selectByPrimaryKey(mId);
        this.warpMerchantDTO(merchant, dto);

        return dto;
    }


    private MerchantDTO toMerchant(MerchantStoreAndExtendResp merchantStoreAndExtendResp) {
        MerchantDTO dto = new MerchantDTO();
        dto.setTenantId(merchantStoreAndExtendResp.getTenantId());
        dto.setAdminId(merchantStoreAndExtendResp.getAdminId() == null ? null : merchantStoreAndExtendResp.getAdminId().intValue());
        dto.setMname(merchantStoreAndExtendResp.getStoreName());
        dto.setMcontact(merchantStoreAndExtendResp.getAccountName());
        dto.setPhone(merchantStoreAndExtendResp.getPhone());
        dto.setSize(MerchantDTO.transSizeFromUserCenter(merchantStoreAndExtendResp.getSize()));
        dto.setIslock(MerchantDTO.transStatusFromUserCenter(merchantStoreAndExtendResp.getStatus()));
        dto.setEnterpriseScale(MerchantDTO.transTypeFromUserCenter(merchantStoreAndExtendResp.getType()));
        if (merchantStoreAndExtendResp.getRegisterTime() != null) {
            dto.setRegisterTime(BaseDateUtils.localDateTime2Date(merchantStoreAndExtendResp.getRegisterTime()));
        }
        dto.setRemark(merchantStoreAndExtendResp.getAuditRemark());
        if (merchantStoreAndExtendResp.getAuditTime() != null) {
            dto.setAuditTime(BaseDateUtils.localDateTime2Date(merchantStoreAndExtendResp.getAuditTime()));
        }
        dto.setmId(merchantStoreAndExtendResp.getMId());
        dto.setType(merchantStoreAndExtendResp.getBusinessType());
        dto.setChannelCode(merchantStoreAndExtendResp.getChannelCode());
        dto.setPopView(merchantStoreAndExtendResp.getPopView());
        dto.setChangePop(merchantStoreAndExtendResp.getChangePop());
        dto.setFirstLoginPop(merchantStoreAndExtendResp.getFirstLoginPop());
        dto.setDisplayButton(merchantStoreAndExtendResp.getDisplayButton());
        dto.setPreRegisterFlag(merchantStoreAndExtendResp.getPreRegisterFlag());
        dto.setAreaNo(merchantStoreAndExtendResp.getAreaNo());
        dto.setProvince(merchantStoreAndExtendResp.getProvince());
        dto.setCity(merchantStoreAndExtendResp.getCity());
        dto.setArea(merchantStoreAndExtendResp.getArea());
        dto.setMockLoginFlag(merchantStoreAndExtendResp.getMockLoginFlag());
        dto.setPoiNote(merchantStoreAndExtendResp.getPoiNote());
        return dto;
    }

    private void warpMerchantDTO(Merchant merchant, MerchantDTO dto) {
        if(merchant == null || dto == null) {
            return;
        }
        dto.setSkuShow(merchant.getSkuShow());
        dto.setRechargeAmount(merchant.getRechargeAmount());
        dto.setInviterChannelCode(merchant.getInviterChannelCode());
        dto.setCashAmount(merchant.getCashAmount());
        dto.setDoorPic(merchant.getDoorPic());
        dto.setMemberIntegral(merchant.getMemberIntegral());
        dto.setDirect(merchant.getDirect());
        dto.setGrade(merchant.getGrade());
        dto.setHouseNumber(merchant.getHouseNumber());
        dto.setLastOrderTime(merchant.getLastOrderTime());
        dto.setOpenid(merchant.getOpenid());
        dto.setUnionid(merchant.getUnionid());
        dto.setMpOpenid(merchant.getMpOpenid());
        dto.setRankId(merchant.getRankId());
        dto.setLoginTime(merchant.getLoginTime());
        dto.setAuditUser(merchant.getAuditUser());
        dto.setAddress(merchant.getAddress());
        dto.setServer(merchant.getServer());
        dto.setCluePool(merchant.getCluePool());
        dto.setCompanyBrand(merchant.getCompanyBrand());
        dto.setEnterpriseScale(merchant.getEnterpriseScale());
        dto.setMerchantType(merchant.getMerchantType());
    }


}
