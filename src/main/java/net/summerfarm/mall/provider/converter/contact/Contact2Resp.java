package net.summerfarm.mall.provider.converter.contact;

import net.summerfarm.mall.client.resp.ContactQueryResp;
import net.summerfarm.mall.model.domain.Contact;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
public class Contact2Resp {

    public static ContactQueryResp domain2VO(Contact contact) {
        ContactQueryResp resp = new ContactQueryResp();
        resp.setMId(contact.getmId());
        resp.setContactId(contact.getContactId());
        resp.setContact(contact.getContact());
        if (StringUtils.isEmpty(contact.getHouseNumber())) {
            resp.setAddress(contact.getAddress());
        }else {
            resp.setAddress(contact.getAddress() + contact.getHouseNumber());
        }
        resp.setArea(contact.getArea());
        resp.setCity(contact.getCity());
        resp.setProvince(contact.getProvince());
        resp.setDeliveryFee(contact.getDeliveryFee());
        resp.setPhone(contact.getPhone());
        resp.setStoreNo(contact.getStoreNo());
        resp.setPoiNote(contact.getPoiNote());
        return resp;
    }

}
