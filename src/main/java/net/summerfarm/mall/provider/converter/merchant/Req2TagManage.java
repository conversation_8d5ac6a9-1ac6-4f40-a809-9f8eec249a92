package net.summerfarm.mall.provider.converter.merchant;

import net.summerfarm.mall.client.req.merchant.TagManageReq;
import net.summerfarm.mall.wechat.model.TagManage;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
public class Req2TagManage {

    public static TagManage tagManageReq2TagManage(TagManageReq tagManageReq) {
        if (ObjectUtils.isEmpty(tagManageReq)) {
            return null;
        }
        TagManage tagManage = new TagManage();
        tagManage.setTagName(tagManageReq.getTagName());
        tagManage.setmIds(tagManageReq.getMIds());
        tagManage.setTagId(tagManageReq.getTagId());
        return tagManage;
    }
}
