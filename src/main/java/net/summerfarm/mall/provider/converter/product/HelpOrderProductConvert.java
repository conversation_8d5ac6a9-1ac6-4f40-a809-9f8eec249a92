package net.summerfarm.mall.provider.converter.product;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import net.summerfarm.mall.client.req.HelpOrderProductListQueryReq;
import net.summerfarm.mall.client.resp.HelpOrderProductInfoQueryResp;
import net.summerfarm.mall.client.resp.LadderPriceDTO;
import net.summerfarm.mall.model.input.HelpOrderProductListQueryInput;
import net.summerfarm.mall.model.vo.LadderPriceVO;
import net.summerfarm.mall.model.vo.ProductInfoVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-08-18
 * @description
 */
public class HelpOrderProductConvert {


    private HelpOrderProductConvert() {
        // 无需实现
    }
    public static HelpOrderProductListQueryInput toHelpOrderProductListQueryInput(HelpOrderProductListQueryReq helpOrderProductListQueryReq) {
        if (helpOrderProductListQueryReq == null) {
            return null;
        }
        HelpOrderProductListQueryInput helpOrderProductListQueryInput = new HelpOrderProductListQueryInput();
        helpOrderProductListQueryInput.setPageIndex(helpOrderProductListQueryReq.getPageIndex());
        helpOrderProductListQueryInput.setPageSize(helpOrderProductListQueryReq.getPageSize());
        helpOrderProductListQueryInput.setMId(helpOrderProductListQueryReq.getMId());
        helpOrderProductListQueryInput.setPdName(helpOrderProductListQueryReq.getPdName());
        helpOrderProductListQueryInput.setAreaNo(helpOrderProductListQueryReq.getAreaNo());
        helpOrderProductListQueryInput.setType(helpOrderProductListQueryReq.getType());
        return helpOrderProductListQueryInput;
    }

    public static List<HelpOrderProductInfoQueryResp> productPageInfo2Resp(List<ProductInfoVO> productInfoVOS){
        List<HelpOrderProductInfoQueryResp> respList = Lists.newArrayList();
        productInfoVOS.forEach(productInfoVO -> {
            HelpOrderProductInfoQueryResp resp = new HelpOrderProductInfoQueryResp();
            resp.setPdId(productInfoVO.getPdId());
            resp.setPdName(productInfoVO.getPdName());
            resp.setPddetail(productInfoVO.getPddetail());
            resp.setCategoryId(productInfoVO.getCategoryId());
            resp.setCateType(productInfoVO.getCateType());
            resp.setPicturePath(Objects.isNull(productInfoVO.getSkuPic()) ?
                    productInfoVO.getPicturePath() : productInfoVO.getSkuPic());
            resp.setSku(productInfoVO.getSku());
            resp.setSkuName(productInfoVO.getSkuName());
            resp.setSkuPic(productInfoVO.getSkuPic());
            resp.setUnit(productInfoVO.getUnit());
            resp.setWeight(productInfoVO.getWeight());
            resp.setBaseSaleUnit(productInfoVO.getBaseSaleUnit());
            resp.setBaseSaleQuantity(productInfoVO.getBaseSaleQuantity());
            resp.setExtType(productInfoVO.getExtType());
            resp.setSalesMode(productInfoVO.getSalesMode());
            resp.setLimitedQuantity(productInfoVO.getLimitedQuantity());
            resp.setQuantity(productInfoVO.getQuantity());
            resp.setLadderPrice(productInfoVO.getLadderPrice());
            resp.setOriginalPrice(productInfoVO.getOriginalPrice());
            resp.setSalePrice(productInfoVO.getSalePrice());
            resp.setMajorPrice(productInfoVO.getMPrice());
            resp.setActivityOriginPrice(productInfoVO.getActivityOriginPrice());
            resp.setMallShow(productInfoVO.getMallShow());
            resp.setDirect(productInfoVO.getDirect());
            resp.setInfo(productInfoVO.getInfo());
            resp.setQualityTime(productInfoVO.getQualityTime());
            resp.setQualityTimeUnit(productInfoVO.getQualityTimeUnit());
            resp.setOtherSlogan(productInfoVO.getOtherSlogan());
            //resp.setLadderPrices(ladderPrices2DTO(productInfoVO.getLadderPrices()));
            resp.setPlatform(productInfoVO.getPlatform());
            resp.setArrivalNotice(productInfoVO.getArrivalNotice());
            if (CollectionUtil.isNotEmpty(productInfoVO.getActivityLadderPrices())) {
                resp.setLadderPrices(ladderPrices2DTO(productInfoVO.getActivityLadderPrices()));
            }
            respList.add(resp);
        });
        return respList;
    }
    public static List<LadderPriceDTO> ladderPrices2DTO(List<LadderPriceVO> ladderPriceVOS){
        if (CollectionUtil.isEmpty(ladderPriceVOS)) {
            return null;
        }
        List<LadderPriceDTO> ladderPriceDTOList = new ArrayList<>();
        for (LadderPriceVO ladderPriceVO : ladderPriceVOS) {
            ladderPriceDTOList.add(toLadderPriceDTO(ladderPriceVO));
        }
        return ladderPriceDTOList;
    }
    public static LadderPriceDTO toLadderPriceDTO(LadderPriceVO ladderPriceVO) {
        if (ladderPriceVO == null) {
            return null;
        }
        LadderPriceDTO ladderPriceDTO = new LadderPriceDTO();
        ladderPriceDTO.setUnit(ladderPriceVO.getUnit());
        ladderPriceDTO.setPrice(ladderPriceVO.getPrice());
        ladderPriceDTO.setAdjustType(ladderPriceVO.getAdjustType());
        ladderPriceDTO.setAmount(ladderPriceVO.getAmount());
        ladderPriceDTO.setRoundingMode(ladderPriceVO.getRoundingMode());
        return ladderPriceDTO;
    }
}
