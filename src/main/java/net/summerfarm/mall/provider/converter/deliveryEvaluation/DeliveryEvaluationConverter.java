package net.summerfarm.mall.provider.converter.deliveryEvaluation;

import net.summerfarm.mall.client.req.DeliveryEvaluationQueryReq;
import net.summerfarm.mall.client.resp.DeliveryEvaluationQueryResp;
import net.summerfarm.mall.model.domain.DeliveryEvaluation;

/**
 * @Author: zhuyantao
 * @date: 2023/3/15 4:37 下午
 * @description
 */
public class DeliveryEvaluationConverter {

    public static DeliveryEvaluationQueryResp domain2Resp(DeliveryEvaluation deliveryEvaluation){
        DeliveryEvaluationQueryResp resp = new DeliveryEvaluationQueryResp();
        resp.setOrderNo(deliveryEvaluation.getOrderNo());
        resp.setDeliveryPlanId(deliveryEvaluation.getDeliveryPlanId());
        resp.setSatisfactionLevel(deliveryEvaluation.getSatisfactionLevel());
        resp.setTag(deliveryEvaluation.getTag());
        resp.setRemark(deliveryEvaluation.getRemark());
        resp.setOperatorAccountId(deliveryEvaluation.getOperatorAccountId());
        resp.setOperator(deliveryEvaluation.getOperator());
        resp.setOperatorPhone(deliveryEvaluation.getOperatorPhone());
        resp.setType(deliveryEvaluation.getType());
        return resp;
    }

    public static DeliveryEvaluation Req2Domain(DeliveryEvaluationQueryReq req){
        DeliveryEvaluation de = new DeliveryEvaluation();
        de.setOrderNo(req.getOrderNo());
        de.setContactId(Long.valueOf(req.getContactId()));
        de.setDeliveryTime(req.getDeliveryTime());
        return de;
    }

}
