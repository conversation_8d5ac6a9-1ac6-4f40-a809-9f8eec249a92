package net.summerfarm.mall.provider.converter.afterSaleOrder;

import net.summerfarm.mall.client.req.afterSale.AfterSaleOrderReq;
import net.summerfarm.mall.client.req.afterSale.ExchangeGoodsReq;
import net.summerfarm.mall.model.domain.ExchangeGoods;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AfterSaleOrderReq2VO {

    public static AfterSaleOrderVO afterSaleOrderReq2VO(AfterSaleOrderReq afterSaleOrderReq) {
        if (ObjectUtils.isEmpty(afterSaleOrderReq)){
            return new AfterSaleOrderVO();
        }
        AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setOrderTime(afterSaleOrderReq.getOrderTime());
        afterSaleOrderVO.setTotalPrice(afterSaleOrderReq.getTotalPrice());
        afterSaleOrderVO.setPdName(afterSaleOrderReq.getPdName());
        afterSaleOrderVO.setPicturePath(afterSaleOrderReq.getPicturePath());
        afterSaleOrderVO.setAmount(afterSaleOrderReq.getAmount());
        afterSaleOrderVO.setPrice(afterSaleOrderReq.getPrice());
        afterSaleOrderVO.setWeight(afterSaleOrderReq.getWeight());
        afterSaleOrderVO.setMaturity(afterSaleOrderReq.getMaturity());
        afterSaleOrderVO.setAfterSaleTime(afterSaleOrderReq.getAfterSaleTime());
        afterSaleOrderVO.setAfterSaleQuantity(afterSaleOrderReq.getAfterSaleQuantity());
        afterSaleOrderVO.setCategoryId(afterSaleOrderReq.getCategoryId());
        afterSaleOrderVO.setProofPic(afterSaleOrderReq.getProofPic());
        afterSaleOrderVO.setHandleType(afterSaleOrderReq.getHandleType());
        afterSaleOrderVO.setHandleNum(afterSaleOrderReq.getHandleNum());
        afterSaleOrderVO.setHandler(afterSaleOrderReq.getHandler());
        afterSaleOrderVO.setAuditer(afterSaleOrderReq.getAuditer());
        afterSaleOrderVO.setApplyRemark(afterSaleOrderReq.getApplyRemark());
        afterSaleOrderVO.setUpdatetime(afterSaleOrderReq.getUpdatetime());
        afterSaleOrderVO.setQuantity(afterSaleOrderReq.getQuantity());
        afterSaleOrderVO.setHandleRemark(afterSaleOrderReq.getHandleRemark());
        afterSaleOrderVO.setAfterSaleType(afterSaleOrderReq.getAfterSaleType());
        afterSaleOrderVO.setRefundType(afterSaleOrderReq.getRefundType());
        afterSaleOrderVO.setMname(afterSaleOrderReq.getMname());
        afterSaleOrderVO.setOverTime(afterSaleOrderReq.getOverTime());
        afterSaleOrderVO.setSaleUnit(afterSaleOrderReq.getSaleUnit());
        afterSaleOrderVO.setAccountContact(afterSaleOrderReq.getAccountContact());
        afterSaleOrderVO.setAccountPhone(afterSaleOrderReq.getAccountPhone());
        afterSaleOrderVO.setDeliveryDate(afterSaleOrderReq.getDeliveryDate());
        afterSaleOrderVO.setDeliveryQuantity(afterSaleOrderReq.getDeliveryQuantity());
        afterSaleOrderVO.setAddress(afterSaleOrderReq.getAddress());
        afterSaleOrderVO.setCouponId(afterSaleOrderReq.getCouponId());
        afterSaleOrderVO.setAuditeRemark(afterSaleOrderReq.getAuditeRemark());
        afterSaleOrderVO.setApplyer(afterSaleOrderReq.getApplyer());
        afterSaleOrderVO.setRecoveryNum(afterSaleOrderReq.getRecoveryNum());
        afterSaleOrderVO.setExtraRemark(afterSaleOrderReq.getExtraRemark());
        afterSaleOrderVO.setOrderDeliveryStatus(afterSaleOrderReq.getOrderDeliveryStatus());
        afterSaleOrderVO.setProofVideo(afterSaleOrderReq.getProofVideo());
        afterSaleOrderVO.setApplySecondaryRemark(afterSaleOrderReq.getApplySecondaryRemark());
        afterSaleOrderVO.setDeliveryStatus(afterSaleOrderReq.getDeliveryStatus());
        afterSaleOrderVO.setHandleSecondaryRemark(afterSaleOrderReq.getHandleSecondaryRemark());
        afterSaleOrderVO.setIsTwoRefund(afterSaleOrderReq.getIsTwoRefund());
        afterSaleOrderVO.setId(afterSaleOrderReq.getId());
        afterSaleOrderVO.setAfterSaleOrderNo(afterSaleOrderReq.getAfterSaleOrderNo());
        afterSaleOrderVO.setmId(afterSaleOrderReq.getMId());
        afterSaleOrderVO.setAccountId(afterSaleOrderReq.getAccountId());
        afterSaleOrderVO.setOrderNo(afterSaleOrderReq.getOrderNo());
        afterSaleOrderVO.setSku(afterSaleOrderReq.getSku());
        afterSaleOrderVO.setStatus(afterSaleOrderReq.getStatus());
        afterSaleOrderVO.setAfterSaleUnit(afterSaleOrderReq.getAfterSaleUnit());
        afterSaleOrderVO.setAddTime(afterSaleOrderReq.getAddTime());
        afterSaleOrderVO.setType(afterSaleOrderReq.getType());
        afterSaleOrderVO.setGrade(afterSaleOrderReq.getGrade());
        afterSaleOrderVO.setDeliveryed(afterSaleOrderReq.getDeliveryed());
        afterSaleOrderVO.setSuitId(afterSaleOrderReq.getSuitId());
        afterSaleOrderVO.setTimes(afterSaleOrderReq.getTimes());
        afterSaleOrderVO.setView(afterSaleOrderReq.getView());
        afterSaleOrderVO.setIsFull(afterSaleOrderReq.getIsFull());
        afterSaleOrderVO.setDeliveryId(afterSaleOrderReq.getDeliveryId());
        afterSaleOrderVO.setRecoveryType(afterSaleOrderReq.getRecoveryType());
        afterSaleOrderVO.setCloser(afterSaleOrderReq.getCloser());
        afterSaleOrderVO.setCloseTime(afterSaleOrderReq.getCloseTime());
        afterSaleOrderVO.setAfterSaleOrderStatus(afterSaleOrderReq.getAfterSaleOrderStatus());
        afterSaleOrderVO.setRefundFreight(afterSaleOrderReq.getRefundFreight());
        afterSaleOrderVO.setProductType(afterSaleOrderReq.getProductType());
        afterSaleOrderVO.setCarryingGoods(afterSaleOrderReq.getCarryingGoods());
        afterSaleOrderVO.setIsManage(afterSaleOrderReq.getIsManage());
        afterSaleOrderVO.setAfterSaleRemarkType(afterSaleOrderReq.getAfterSaleRemarkType());
        afterSaleOrderVO.setAfterSaleRemark(afterSaleOrderReq.getAfterSaleRemark());
        afterSaleOrderVO.setExchangeGoodList(AfterSaleOrderReq2VO.exchangeGoodsReq2VO(afterSaleOrderReq.getExchangeGoodList()));
        afterSaleOrderVO.setSnapshot(afterSaleOrderReq.getSnapshot());
        return afterSaleOrderVO;
    }

    public static List<ExchangeGoods> exchangeGoodsReq2VO(List<ExchangeGoodsReq> exchangeGoodsReqList) {
        List<ExchangeGoods> exchangeGoodsList = new ArrayList<>();
        if (CollectionUtils.isEmpty(exchangeGoodsReqList)) {
            return exchangeGoodsList;
        }
        for (ExchangeGoodsReq exchangeGoodsReq : exchangeGoodsReqList){
            ExchangeGoods exchangeGoods = new ExchangeGoods();
            exchangeGoods.setPdName(exchangeGoodsReq.getPdName());
            exchangeGoods.setQuantity(exchangeGoodsReq.getQuantity());
            exchangeGoods.setSku(exchangeGoodsReq.getSku());
            exchangeGoods.setWeight(exchangeGoodsReq.getWeight());
            exchangeGoodsList.add(exchangeGoods);
        }
        return exchangeGoodsList;
    }

    public static List<AfterSaleOrderVO> afterSaleOrderReq2List(List<AfterSaleOrderReq> afterSaleOrderReqs) {
        List<AfterSaleOrderVO> afterSaleOrderList = new ArrayList<>();
        for (AfterSaleOrderReq afterSaleOrderReq : afterSaleOrderReqs){
            AfterSaleOrderVO afterSaleOrderVO = afterSaleOrderReq2VO(afterSaleOrderReq);
            afterSaleOrderList.add(afterSaleOrderVO);
        }
        return afterSaleOrderList;
    }

}
