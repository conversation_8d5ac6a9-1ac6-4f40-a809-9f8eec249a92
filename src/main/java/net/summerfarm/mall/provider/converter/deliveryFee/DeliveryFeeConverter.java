package net.summerfarm.mall.provider.converter.deliveryFee;

import com.cosfo.summerfarm.model.dto.SummerfarmDeliveryDTO;
import com.cosfo.summerfarm.model.input.SummerfarmDeliveryFeeRuleInput;
import net.summerfarm.mall.client.saas.req.DeliveryFeeRuleQueryReq;
import net.summerfarm.mall.client.saas.req.OrderItemCalcReq;
import net.summerfarm.mall.client.saas.req.SummerFarmDeliveryReq;
import net.summerfarm.mall.client.saas.resp.SummerFarmDeliveryResp;
import net.summerfarm.mall.controller.api.saas.model.dto.SaasOrderDTO;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/16
 */
public class DeliveryFeeConverter {

    /**
     * 转化为SaasOrderDTO
     * 
     * @param summerFarmDeliveryReq
     * @return
     */
    public static SaasOrderDTO convertToSaasOrderDTO(SummerFarmDeliveryReq summerFarmDeliveryReq){
        if (summerFarmDeliveryReq == null) {
            return null;
        }
        
        SaasOrderDTO saasOrderDTO = new SaasOrderDTO();
        saasOrderDTO.setCity(summerFarmDeliveryReq.getCity());
        saasOrderDTO.setArea(summerFarmDeliveryReq.getArea());
        List<OrderItemCalcReq> orderItemDTOList = summerFarmDeliveryReq.getOrderItemDTOList();
        List<OrderItemCalcDTO> orderItemCalcDTOS = orderItemDTOList.stream().map(item -> {
            OrderItemCalcDTO orderItemCalcDTO = new OrderItemCalcDTO();
            orderItemCalcDTO.setCalcPartDeliveryFee(item.getCalcPartDeliveryFee());
            orderItemCalcDTO.setSupplySkuId(item.getSupplySkuId());
            return orderItemCalcDTO;
        }).collect(Collectors.toList());
        saasOrderDTO.setOrderItemDTOList(orderItemCalcDTOS);
        return saasOrderDTO;
    }

    /**
     * 转化为SummerFarmDeliveryResp
     *
     * @param deliveryDTO
     * @return
     */
    public static SummerFarmDeliveryResp convertToSummerFarmDeliveryResp(SummerfarmDeliveryDTO deliveryDTO){
        if (deliveryDTO == null) {
            return null;
        }

        SummerFarmDeliveryResp summerFarmDeliveryResp = new SummerFarmDeliveryResp();
        summerFarmDeliveryResp.setDeliveryFee(deliveryDTO.getDeliveryFee());
        summerFarmDeliveryResp.setDeliveryFeeRule(deliveryDTO.getDeliveryFeeRule());
        summerFarmDeliveryResp.setHavingRule(deliveryDTO.getHavingRule());
        return summerFarmDeliveryResp;
    }

    /**
     * 转化为SummerfarmDeliveryFeeRuleInput
     *
     * @param deliveryFeeRuleQueryReq
     * @return
     */
    public static SummerfarmDeliveryFeeRuleInput convertToSummerfarmDeliveryFeeRuleInput(DeliveryFeeRuleQueryReq deliveryFeeRuleQueryReq){
        if (deliveryFeeRuleQueryReq == null) {
            return null;
        }

        SummerfarmDeliveryFeeRuleInput summerfarmDeliveryFeeRuleInput = new SummerfarmDeliveryFeeRuleInput();
        summerfarmDeliveryFeeRuleInput.setProvince(deliveryFeeRuleQueryReq.getProvince());
        summerfarmDeliveryFeeRuleInput.setCity(deliveryFeeRuleQueryReq.getCity());
        summerfarmDeliveryFeeRuleInput.setArea(deliveryFeeRuleQueryReq.getArea());
        return summerfarmDeliveryFeeRuleInput;
    }
}
