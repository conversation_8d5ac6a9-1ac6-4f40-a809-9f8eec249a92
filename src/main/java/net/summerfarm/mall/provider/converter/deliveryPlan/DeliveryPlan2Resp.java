package net.summerfarm.mall.provider.converter.deliveryPlan;
import java.time.LocalDate;

import net.summerfarm.mall.client.resp.DeliveryPlanQueryResp;
import net.summerfarm.mall.client.resp.OrderCompareCommonQueryResp;
import net.summerfarm.mall.client.resp.OrderDetailCompareCommonQueryResp;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.model.vo.ofc.AfterSaleOrderCompareVO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DeliveryPlan2Resp {
    public static DeliveryPlanQueryResp domain2VO(DeliveryPlan deliveryPlan) {
        DeliveryPlanQueryResp resp = new DeliveryPlanQueryResp();
        resp.setContactId(deliveryPlan.getContactId());
        resp.setDeliveryTime(deliveryPlan.getDeliveryTime());
        resp.setDeliveryType(deliveryPlan.getDeliverytype());
        resp.setId(deliveryPlan.getId());
        resp.setInterceptFlag(deliveryPlan.getInterceptFlag());
        resp.setOldDeliveryTime(deliveryPlan.getOldDeliveryTime());
        resp.setOrderStoreNo(deliveryPlan.getOrderStoreNo());
        resp.setOrderNo(deliveryPlan.getOrderNo());
        resp.setQuantity(deliveryPlan.getQuantity());
        resp.setStatus(deliveryPlan.getStatus());
        resp.setTimeFrame(deliveryPlan.getTimeFrame());
        resp.setAdminId(deliveryPlan.getAdminId());
        resp.setAccountId(deliveryPlan.getAccountId());
        resp.setAddTime(deliveryPlan.getAddTime());
        return resp;
    }

    public static DeliveryPlanQueryResp domainDTO2VO(DeliveryPlanVO deliveryPlan) {
        DeliveryPlanQueryResp resp = new DeliveryPlanQueryResp();
        resp.setContactId(deliveryPlan.getContactId());
        resp.setDeliveryTime(deliveryPlan.getDeliveryTime());
        resp.setDeliveryType(deliveryPlan.getDeliverytype());
        resp.setId(deliveryPlan.getId());
        resp.setInterceptFlag(deliveryPlan.getInterceptFlag());
        resp.setOldDeliveryTime(deliveryPlan.getOldDeliveryTime());
        resp.setOrderStoreNo(deliveryPlan.getOrderStoreNo());
        resp.setOrderNo(deliveryPlan.getOrderNo());
        resp.setQuantity(deliveryPlan.getQuantity());
        resp.setStatus(deliveryPlan.getStatus());
        resp.setTimeFrame(deliveryPlan.getTimeFrame());
        resp.setAdminId(deliveryPlan.getAdminId());
        resp.setAccountId(deliveryPlan.getAccountId());
        return resp;
    }


    public static List<OrderCompareCommonQueryResp> buildNormalOrderDataList(List<DeliveryPlanVO> deliveryPlanList, Map<String, List<OrderItem>> orderItemMap) {
        if (CollectionUtils.isEmpty(deliveryPlanList)){
            return Collections.emptyList();
        }
        List<OrderCompareCommonQueryResp> resultList = new ArrayList<>(deliveryPlanList.size());
        deliveryPlanList.forEach(deliveryPlanVO -> {
            OrderCompareCommonQueryResp resp = new OrderCompareCommonQueryResp();
            resp.setOrderNo(deliveryPlanVO.getOrderNo());
            resp.setAfterSaleOrderNo("");
            resp.setDeliveryTime(deliveryPlanVO.getDeliveryTime());
            resp.setContactId(deliveryPlanVO.getContactId());
            // resp.setAfterSaleOrderType();
            resp.setOrderStatus(deliveryPlanVO.getStatus());
            resp.setDeliveryType(deliveryPlanVO.getDeliverytype());
            resp.setOrderStoreNo(deliveryPlanVO.getOrderStoreNo());

            List<OrderDetailCompareCommonQueryResp> detailList = new ArrayList<>();
            List<OrderItem> orderItems = orderItemMap.get(deliveryPlanVO.getOrderNo());
            if (!CollectionUtils.isEmpty(orderItems)){
                orderItems.forEach(orderItem -> {
                    OrderDetailCompareCommonQueryResp orderDetailCompareCommonQueryResp = new OrderDetailCompareCommonQueryResp();
                    orderDetailCompareCommonQueryResp.setSkuId(orderItem.getSku());
                    orderDetailCompareCommonQueryResp.setAmount(orderItem.getAmount());
                    // warehouse暂时不设置
                    detailList.add(orderDetailCompareCommonQueryResp);
                });
            }
            resp.setDetailList(detailList);
            resultList.add(resp);

        });
        return resultList;
    }

    public static List<OrderCompareCommonQueryResp> buildSampleOrderDataList(List<SampleApply> sampleApplyList, Map<Integer, List<SampleSku>> sampleSkuMap) {
        if (CollectionUtils.isEmpty(sampleApplyList)){
            return Collections.emptyList();
        }
        List<OrderCompareCommonQueryResp> resultList = new ArrayList<>(sampleApplyList.size());
        sampleApplyList.forEach(sampleApply -> {
            OrderCompareCommonQueryResp resp = new OrderCompareCommonQueryResp();
            resp.setOrderNo(String.valueOf(sampleApply.getSampleId()));
            resp.setAfterSaleOrderNo("");
            resp.setDeliveryTime(sampleApply.getDeliveryTime());
            resp.setContactId(Long.valueOf(sampleApply.getContactId()));
            // resp.setAfterSaleOrderType();
            resp.setOrderStatus(sampleApply.getStatus());
            resp.setOrderStoreNo(sampleApply.getStoreNo());

            List<OrderDetailCompareCommonQueryResp> detailList = new ArrayList<>();
            List<SampleSku> sampleSkuList = sampleSkuMap.get(sampleApply.getSampleId());
            if (!CollectionUtils.isEmpty(sampleSkuList)){
                sampleSkuList.forEach(sampleSku -> {
                    OrderDetailCompareCommonQueryResp orderDetailCompareCommonQueryResp = new OrderDetailCompareCommonQueryResp();
                    orderDetailCompareCommonQueryResp.setSkuId(sampleSku.getSku());
                    orderDetailCompareCommonQueryResp.setAmount(sampleSku.getAmount());
                    // warehouse暂时不设置
                    detailList.add(orderDetailCompareCommonQueryResp);
                });
            }
            resp.setDetailList(detailList);
            resultList.add(resp);

        });
        return resultList;
    }

    public static List<OrderCompareCommonQueryResp> buildDirectPurchaseDataList(List<DirectPurchaseInfo> directPurchaseInfos, Map<String, List<OrderItem>> orderItemMap) {
        if (CollectionUtils.isEmpty(directPurchaseInfos)){
            return Collections.emptyList();
        }
        List<OrderCompareCommonQueryResp> resultList = new ArrayList<>(directPurchaseInfos.size());
        directPurchaseInfos.forEach(directPurchaseInfo -> {
            OrderCompareCommonQueryResp resp = new OrderCompareCommonQueryResp();
            resp.setOrderNo(directPurchaseInfo.getOrderNo());
            resp.setAfterSaleOrderNo("");
            resp.setDeliveryTime(directPurchaseInfo.getDeliveryDate());
            resp.setContactId(directPurchaseInfo.getContactId());
            // resp.setAfterSaleOrderType();
            resp.setOrderStatus(null);
            resp.setOrderStoreNo(0);

            List<OrderDetailCompareCommonQueryResp> detailList = new ArrayList<>();
            List<OrderItem> orderItems = orderItemMap.get(directPurchaseInfo.getOrderNo());
            if (!CollectionUtils.isEmpty(orderItems)){
                orderItems.forEach(orderItem -> {
                    OrderDetailCompareCommonQueryResp orderDetailCompareCommonQueryResp = new OrderDetailCompareCommonQueryResp();
                    orderDetailCompareCommonQueryResp.setSkuId(orderItem.getSku());
                    orderDetailCompareCommonQueryResp.setAmount(orderItem.getAmount());
                    // warehouse暂时不设置
                    detailList.add(orderDetailCompareCommonQueryResp);
                });
            }
            resp.setDetailList(detailList);
            resultList.add(resp);

        });
        return resultList;
    }

    public static List<OrderCompareCommonQueryResp> buildAfterSaleOrderDataList(List<AfterSaleOrderCompareVO> afterSaleOrderCompareVOS,
                                                                                Map<Integer, List<AfterSaleDeliveryDetail>> afterSaleDeliveryDetailMap) {
        if (CollectionUtils.isEmpty(afterSaleOrderCompareVOS)){
            return Collections.emptyList();
        }
        List<OrderCompareCommonQueryResp> resultList = new ArrayList<>(afterSaleOrderCompareVOS.size());
        afterSaleOrderCompareVOS.forEach(afterSaleOrderCompareVO -> {
            OrderCompareCommonQueryResp resp = new OrderCompareCommonQueryResp();
            resp.setOrderNo(afterSaleOrderCompareVO.getOrderNo());
            resp.setAfterSaleOrderNo(afterSaleOrderCompareVO.getAfterSaleOrderNo());
            resp.setDeliveryTime(afterSaleOrderCompareVO.getDeliveryTime());
            resp.setContactId(afterSaleOrderCompareVO.getContactId());
            resp.setAfterSaleOrderType(afterSaleOrderCompareVO.getAfterSaleOrderType());
            resp.setOrderStatus(afterSaleOrderCompareVO.getOrderStatus());
            resp.setOrderStoreNo(afterSaleOrderCompareVO.getOrderStoreNo());

            List<OrderDetailCompareCommonQueryResp> detailList = new ArrayList<>();
            List<AfterSaleDeliveryDetail> afterSaleDeliveryDetails = afterSaleDeliveryDetailMap.get(afterSaleOrderCompareVO.getAfterSaleDeliveryPathId());
            if (!CollectionUtils.isEmpty(afterSaleDeliveryDetails)){
                afterSaleDeliveryDetails.forEach(afterSaleDeliveryDetail -> {
                    OrderDetailCompareCommonQueryResp orderDetailCompareCommonQueryResp = new OrderDetailCompareCommonQueryResp();
                    orderDetailCompareCommonQueryResp.setSkuId(afterSaleDeliveryDetail.getSku());
                    orderDetailCompareCommonQueryResp.setAmount(afterSaleDeliveryDetail.getQuantity());
                    // warehouse暂时不设置
                    detailList.add(orderDetailCompareCommonQueryResp);
                });
            }
            resp.setDetailList(detailList);
            resultList.add(resp);

        });
        return resultList;
    }
}
