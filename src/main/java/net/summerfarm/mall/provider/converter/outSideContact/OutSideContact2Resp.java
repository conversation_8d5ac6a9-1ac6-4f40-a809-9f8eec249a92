package net.summerfarm.mall.provider.converter.outSideContact;

import net.summerfarm.mall.client.resp.OutSideContactResp;
import net.summerfarm.mall.model.domain.OutsideContact;

/**
 * <AUTHOR>
 */
public class OutSideContact2Resp {

    public static OutSideContactResp dto2VO(OutsideContact outsideContact) {
        OutSideContactResp outSideContactResp = new OutSideContactResp();
        outSideContactResp.setId(outsideContact.getId());
        outSideContactResp.setCreateTime(outsideContact.getCreateTime());
        outSideContactResp.setUpdateTime(outsideContact.getUpdateTime());
        outSideContactResp.setTenantId(outsideContact.getTenantId());
        outSideContactResp.setStoreId(outsideContact.getStoreId());
        outSideContactResp.setMName(outsideContact.getMName());
        outSideContactResp.setPhone(outsideContact.getPhone());
        outSideContactResp.setName(outsideContact.getName());
        outSideContactResp.setProvince(outsideContact.getProvince());
        outSideContactResp.setCity(outsideContact.getCity());
        outSideContactResp.setArea(outsideContact.getArea());
        outSideContactResp.setAddress(outsideContact.getAddress());
        outSideContactResp.setPoi(outsideContact.getPoi());
        outSideContactResp.setDistance(outsideContact.getDistance());
        return outSideContactResp;
    }
}
