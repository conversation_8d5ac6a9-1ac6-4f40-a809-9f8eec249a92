package net.summerfarm.mall.provider.converter.shoppingCart;

import net.summerfarm.mall.client.resp.ShoppingCartResp;
import net.summerfarm.mall.model.vo.ShoppingCartVO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description
 * @date 2023/12/8 16:10:35
 */
public class ShoppingCartConverter {

    public static List<ShoppingCartResp> shoppingCartVOSToShoppingCartResp (List<ShoppingCartVO> shoppingCartVOS) {
        List<ShoppingCartResp> shoppingCartResps = new ArrayList<>();
        shoppingCartVOS.stream().forEach(e -> {
            ShoppingCartResp shoppingCartResp = new ShoppingCartResp();
            shoppingCartResp.setAccountId(e.getAccountId());
            shoppingCartResp.setMId(e.getMId());
            shoppingCartResp.setBizId(e.getBizId());
            shoppingCartResp.setProductType(e.getProductType());
            shoppingCartResp.setParentSku(e.getParentSku());
            shoppingCartResp.setId(e.getId());
            shoppingCartResp.setPdName(e.getPdName());
            shoppingCartResp.setQuantity(e.getQuantity());
            shoppingCartResp.setSku(e.getSku());
            shoppingCartResp.setWeight(e.getWeight());
            shoppingCartResps.add(shoppingCartResp);
        });
        return shoppingCartResps;
    }
}
