package net.summerfarm.mall.provider.converter.sampleApply;

import net.summerfarm.mall.client.resp.SampleApplyResp;
import net.summerfarm.mall.model.domain.SampleApply;

/**
 * <AUTHOR>
 */
public class SampleApply2Resp {


    public static SampleApplyResp domain2VO(SampleApply sampleApply) {
        SampleApplyResp resp = new SampleApplyResp();
        resp.setAddTime(sampleApply.getAddTime());
        resp.setAreaNo(sampleApply.getAreaNo());
        resp.setBdId(sampleApply.getBdId());
        resp.setBdName(sampleApply.getBdName());
        resp.setContactId(sampleApply.getContactId());
        resp.setCreateId(sampleApply.getCreateId());
        resp.setCreateName(sampleApply.getCreateName());
        resp.setDeliveryTime(sampleApply.getDeliveryTime());
        resp.setGrade(sampleApply.getGrade());
        resp.setMContact(sampleApply.getMContact());
        resp.setMId(sampleApply.getMId());
        resp.setMName(sampleApply.getMName());
        resp.setMPhone(sampleApply.getMPhone());
        resp.setMSize(sampleApply.getMSize());
        resp.setPurchaseIntention(sampleApply.getPurchaseIntention());
        resp.setRemark(sampleApply.getRemark());
        resp.setSampleId(sampleApply.getSampleId());
        resp.setSatisfaction(sampleApply.getSatisfaction());
        resp.setStatus(sampleApply.getStatus());
        resp.setStoreNo(sampleApply.getStoreNo());
        resp.setUpdateTime(sampleApply.getUpdateTime());
        resp.setSellingEntityName(sampleApply.getSellingEntityName());
        return resp;
    }

}
