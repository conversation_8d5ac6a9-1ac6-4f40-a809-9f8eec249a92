package net.summerfarm.mall.provider.converter.afterSaleDeliveryPath;

import net.summerfarm.mall.client.resp.AfterSaleDeliveryPathQueryResp;
import net.summerfarm.mall.client.resp.ContactQueryResp;
import net.summerfarm.mall.model.domain.AfterSaleDeliveryPath;

/**
 * <AUTHOR>
 */
public class AfterSaleDeliveryPath2Resp {

    public static AfterSaleDeliveryPathQueryResp domain2VO(AfterSaleDeliveryPath afterSaleDeliveryPath) {
        AfterSaleDeliveryPathQueryResp resp = new AfterSaleDeliveryPathQueryResp();
        resp.setCreateTime(afterSaleDeliveryPath.getCreateTime());
        resp.setAfterSaleNo(afterSaleDeliveryPath.getAfterSaleNo());
        resp.setConcatId(afterSaleDeliveryPath.getConcatId());
        resp.setCreateTime(afterSaleDeliveryPath.getCreateTime());
        resp.setDeliveryTime(afterSaleDeliveryPath.getDeliveryTime());
        resp.setId(afterSaleDeliveryPath.getId());
        resp.setMId(afterSaleDeliveryPath.getMId());
        resp.setOutStoreNo(afterSaleDeliveryPath.getOutStoreNo());
        resp.setStatus(afterSaleDeliveryPath.getStatus());
        resp.setType(afterSaleDeliveryPath.getType());
        return resp;
    }


}
