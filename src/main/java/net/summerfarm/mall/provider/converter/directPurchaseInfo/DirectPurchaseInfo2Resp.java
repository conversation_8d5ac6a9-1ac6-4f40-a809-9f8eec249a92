package net.summerfarm.mall.provider.converter.directPurchaseInfo;

import net.summerfarm.mall.client.resp.DirectPurchaseInfoQueryResp;
import net.summerfarm.mall.model.domain.DirectPurchaseInfo;

/**
 * <AUTHOR>
 */
public class DirectPurchaseInfo2Resp {

    public static DirectPurchaseInfoQueryResp domain2VO(DirectPurchaseInfo directPurchaseInfo) {
        DirectPurchaseInfoQueryResp resp = new DirectPurchaseInfoQueryResp();
        resp.setAddtime(directPurchaseInfo.getAddtime());
        resp.setContactId(directPurchaseInfo.getContactId());
        resp.setContactPhone(directPurchaseInfo.getContactPhone());
        resp.setDeliveryDate(directPurchaseInfo.getDeliveryDate());
        resp.setId(directPurchaseInfo.getId());
        resp.setLogisticsInfo(directPurchaseInfo.getLogisticsInfo());
        resp.setMId(directPurchaseInfo.getMId());
        resp.setOneClickShipTime(directPurchaseInfo.getOneClickShipTime());
        resp.setOrderNo(directPurchaseInfo.getOrderNo());
        resp.setPurchaseNo(directPurchaseInfo.getPurchaseNo());
        resp.setReceivePlace(directPurchaseInfo.getReceivePlace());
        resp.setSendTime(directPurchaseInfo.getSendTime());
        return resp;
    }

}
