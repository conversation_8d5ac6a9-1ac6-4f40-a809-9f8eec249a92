package net.summerfarm.mall.provider.converter.sampleSku;

import net.summerfarm.mall.client.resp.SampleSkuResp;
import net.summerfarm.mall.model.domain.SampleSku;

/**
 * <AUTHOR>
 */
public class SampleSku2Resp {

    public static SampleSkuResp domain2VO(SampleSku sampleSku) {
        SampleSkuResp resp = new SampleSkuResp();
        resp.setAmount(sampleSku.getAmount());
        resp.setId(sampleSku.getId());
        resp.setInterceptFlag(sampleSku.getInterceptFlag());
        resp.setInterceptTime(sampleSku.getInterceptTime());
        resp.setPdName(sampleSku.getPdName());
        resp.setSampleId(sampleSku.getSampleId());
        resp.setShowFlag(sampleSku.getShowFlag());
        resp.setSku(sampleSku.getSku());
        resp.setWeight(sampleSku.getWeight());
        resp.setSkuType(sampleSku.getSkuType());
        resp.setWarehouseNo(sampleSku.getWarehouseNo());
        resp.setPicturePath(sampleSku.getPicturePath());
        return resp;
    }
}
