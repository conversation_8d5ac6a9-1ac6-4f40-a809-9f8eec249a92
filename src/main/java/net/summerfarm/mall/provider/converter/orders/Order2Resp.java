package net.summerfarm.mall.provider.converter.orders;
import com.google.common.collect.Lists;

import net.summerfarm.mall.client.resp.OrderBasicInfoResp;
import net.summerfarm.mall.client.resp.OrderResp;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.OrderVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public class Order2Resp {

    public static OrderResp dto2VO(OrderVO orderVO) {
        OrderResp resp = new OrderResp();
        resp.setOrderNo(orderVO.getOrderNo());
        resp.setAdminId(orderVO.getAdminId());
        resp.setAreaNo(orderVO.getAreaNo());
        resp.setDeliveryFee(orderVO.getDeliveryFee());
        resp.setMId(orderVO.getmId());
        resp.setMSize(orderVO.getmSize());
        resp.setOrderSaleType(orderVO.getOrderSaleType());
        resp.setOrderTime(orderVO.getOrderTime());
        resp.setOriginPrice(orderVO.getOriginPrice());
        resp.setOutStock(orderVO.getOutStock());
        resp.setOutTimesFee(orderVO.getOutTimesFee());
        resp.setStatus(orderVO.getStatus());
        resp.setTotalPrice(orderVO.getOriginPrice());
        resp.setType(orderVO.getType());
        resp.setActualTotalPrice(orderVO.getTotal());
        resp.setTotalDiscountAmount(orderVO.getOriginPrice().subtract(orderVO.getTotal()));
        resp.setAccuratePrice(orderVO.getAccuratePrice());
        resp.setAccountContact(orderVO.getAccountContact());
        resp.setAccountPhone(orderVO.getAccountPhone());
        resp.setOrderRemark(orderVO.getRemark());
        resp.setSellingEntityName(orderVO.getSellingEntityName());
        return resp;
    }


    public static List<OrderBasicInfoResp> modelList2Resp(List<Orders> orders) {
        List<OrderBasicInfoResp> orderBasicInfoResplist=Lists.newArrayList();
        for (Orders order :orders) {
        	orderBasicInfoResplist.add(convertFromOrders(order));
        }
        return orderBasicInfoResplist;
    }

    private static OrderBasicInfoResp convertFromOrders(Orders order) {
        OrderBasicInfoResp orderBasicInfoResp = new OrderBasicInfoResp();
        orderBasicInfoResp.setOrderNo(order.getOrderNo());
        orderBasicInfoResp.setMId(order.getmId());
        orderBasicInfoResp.setOrderTime(order.getOrderTime());
        orderBasicInfoResp.setType(order.getType());
        orderBasicInfoResp.setStatus(order.getStatus());
        orderBasicInfoResp.setOutTimes(order.getOutTimes());
        orderBasicInfoResp.setMSize(order.getmSize());
        orderBasicInfoResp.setDirect(order.getDirect());
        orderBasicInfoResp.setOutStock(order.getOutStock());
        return orderBasicInfoResp;
    }
}
