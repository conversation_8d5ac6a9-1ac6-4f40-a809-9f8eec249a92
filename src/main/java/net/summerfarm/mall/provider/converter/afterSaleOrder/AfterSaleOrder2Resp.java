package net.summerfarm.mall.provider.converter.afterSaleOrder;

import net.summerfarm.mall.after.service.impl.ExecutableAfterSale;
import net.summerfarm.mall.client.resp.AfterSaleOrderQueryResp;
import net.summerfarm.mall.client.resp.ExecutableAfterSaleResp;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AfterSaleOrder2Resp {

    public static AfterSaleOrderQueryResp domain2VO(AfterSaleOrderVO afterSaleOrderVO) {
        AfterSaleOrderQueryResp resp = new AfterSaleOrderQueryResp();
        resp.setAccountId(afterSaleOrderVO.getAccountId());
        resp.setAddTime(afterSaleOrderVO.getAddTime());
        resp.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        resp.setAfterSaleOrderStatus(afterSaleOrderVO.getAfterSaleOrderStatus());
        resp.setAfterSaleRemark(afterSaleOrderVO.getAfterSaleRemark());
        resp.setAfterSaleRemarkType(afterSaleOrderVO.getAfterSaleRemarkType());
        resp.setAfterSaleUnit(afterSaleOrderVO.getAfterSaleUnit());
        resp.setCarryingGoods(afterSaleOrderVO.getCarryingGoods());
        resp.setDeliveryed(afterSaleOrderVO.getDeliveryed());
        resp.setDeliveryId(afterSaleOrderVO.getDeliveryId());
        resp.setGrade(afterSaleOrderVO.getGrade());
        resp.setId(afterSaleOrderVO.getId());
        resp.setIsFull(afterSaleOrderVO.getIsFull());
        resp.setIsManage(afterSaleOrderVO.getIsManage());
        resp.setMId(afterSaleOrderVO.getmId());
        resp.setOrderNo(afterSaleOrderVO.getOrderNo());
        resp.setProductType(afterSaleOrderVO.getProductType());
        resp.setRecoveryType(afterSaleOrderVO.getRecoveryType());
        resp.setRefundFreight(afterSaleOrderVO.getRefundFreight());
        resp.setSku(afterSaleOrderVO.getSku());
        resp.setStatus(afterSaleOrderVO.getStatus());
        resp.setSuitId(afterSaleOrderVO.getSuitId());
        resp.setTimes(afterSaleOrderVO.getTimes());
        resp.setType(afterSaleOrderVO.getType());
        resp.setView(afterSaleOrderVO.getView());
        resp.setHandleType(afterSaleOrderVO.getHandleType());
        resp.setHandleNum(afterSaleOrderVO.getHandleNum());
        resp.setHandler(afterSaleOrderVO.getHandler());
        resp.setApplyer(afterSaleOrderVO.getApplyer());
        resp.setHandleTime(afterSaleOrderVO.getHandleTime());
        return resp;
    }

    public static ExecutableAfterSaleResp executableAfterSale2Resp(ExecutableAfterSale executableAfterSale) {
        ExecutableAfterSaleResp executableAfterSaleResp = new ExecutableAfterSaleResp();
        executableAfterSaleResp.setDelivery(executableAfterSale.getDelivery());
        executableAfterSaleResp.setExecuteHandleType(executableAfterSale.getExecuteHandleType());
        return executableAfterSaleResp;
    }

    public static List<AfterSaleOrderQueryResp> afterSaleOrderList2Resp(List<AfterSaleOrderVO> afterSaleOrderList) {
        List<AfterSaleOrderQueryResp> afterSaleOrderQueryResps = new ArrayList<>();
        if (CollectionUtils.isEmpty(afterSaleOrderList)){
            return afterSaleOrderQueryResps;
        }
        for (AfterSaleOrderVO afterSaleOrderVO : afterSaleOrderList){
            AfterSaleOrderQueryResp afterSaleOrderQueryResp = AfterSaleOrder2Resp.domain2VO(afterSaleOrderVO);
            afterSaleOrderQueryResps.add(afterSaleOrderQueryResp);
        }
        return afterSaleOrderQueryResps;
    }
}
