package net.summerfarm.mall.provider.converter.orders;

import net.summerfarm.mall.client.req.order.PlaceOrderReq;
import net.summerfarm.mall.client.req.order.TrolleyReq;
import net.summerfarm.mall.client.resp.PlaceOrderResultResp;
import net.summerfarm.mall.enums.CommonStatus;
import net.summerfarm.mall.model.domain.Trolley;
import net.summerfarm.mall.model.vo.neworder.PlaceOrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class PlaceOrderConverter {


    public static PlaceOrderVO placeOrderReq2VO(PlaceOrderReq placeOrderReq) {
        if (ObjectUtils.isEmpty(placeOrderReq)){
            return null;
        }
        PlaceOrderVO placeOrderVO = new PlaceOrderVO();
        placeOrderVO.setOrderNow(PlaceOrderConverter.trolleyReqs2Trolleys(placeOrderReq.getOrderNow()));
        placeOrderVO.setAccountId(placeOrderReq.getAccountId());
        placeOrderVO.setContactId(placeOrderReq.getContactId());
        placeOrderVO.setMname(placeOrderReq.getMname());
        placeOrderVO.setOpenId(placeOrderReq.getOpenId());
        placeOrderVO.setOutTimes(placeOrderReq.getOutTimes());
        placeOrderVO.setTimeFrameName(placeOrderReq.getTimeFrameName());
        placeOrderVO.setMerchantCouponId(placeOrderReq.getMerchantCouponId());
        placeOrderVO.setHelpOrder(placeOrderReq.getHelpOrder());
        placeOrderVO.setSize(placeOrderReq.getSize());
        placeOrderVO.setOperateId(placeOrderReq.getOperateId());
        placeOrderVO.setOrderNo(placeOrderReq.getOrderNo());
        placeOrderVO.setMId(placeOrderReq.getMId());
        placeOrderVO.setRemark(placeOrderReq.getRemark());
        placeOrderVO.setMajorMerchant(placeOrderReq.getMajorMerchant());
        placeOrderVO.setAdminId(placeOrderReq.getAdminId());
        placeOrderVO.setDirect(placeOrderReq.getDirect());
        placeOrderVO.setSkuShow(placeOrderReq.getSkuShow());
        placeOrderVO.setServer(placeOrderReq.getServer());
        placeOrderVO.setCloseOrderType(placeOrderReq.getCloseOrderType());
        placeOrderVO.setCloseOrderTime(placeOrderReq.getCloseOrderTime());
        placeOrderVO.setExpandActivityId(placeOrderReq.getExpandActivityId());
        placeOrderVO.setBizId(placeOrderReq.getBizId());
        placeOrderVO.setPayType(placeOrderReq.getPayType());
        placeOrderVO.setApplicant(placeOrderReq.getApplicant());
        placeOrderVO.setUpdater(placeOrderReq.getUpdater());
        placeOrderVO.setTakePriceFlag(placeOrderReq.getTakePriceFlag());
        placeOrderVO.setEffectiveNum(placeOrderReq.getEffectiveNum());
        placeOrderVO.setCouponId(placeOrderReq.getCouponId());
        placeOrderVO.setIsTakePrice(placeOrderReq.getIsTakePrice());
        placeOrderVO.setTimingSkuPrice(placeOrderReq.getTimingSkuPrice());
        placeOrderVO.setTimingRuleId(placeOrderReq.getTimingRuleId());
        placeOrderVO.setDeliveryRulesType(CommonStatus.YES.getCode());
        return placeOrderVO;
    }

    public static List<Trolley> trolleyReqs2Trolleys(List<TrolleyReq> trolleyReqs) {
        if (CollectionUtils.isEmpty(trolleyReqs)){
            return null;
        }
        List<Trolley> trolleys = new ArrayList<>();
        for (TrolleyReq trolleyReq : trolleyReqs){
            Trolley trolley = new Trolley();
            trolley.setmId(trolleyReq.getMId());
            trolley.setAccountId(trolleyReq.getAccountId());
            trolley.setSku(trolleyReq.getSku());
            trolley.setParentSku(trolleyReq.getParentSku());
            trolley.setSuitId(trolleyReq.getSuitId());
            trolley.setQuantity(trolleyReq.getQuantity());
            trolley.setCheck(trolleyReq.getCheck());
            trolley.setUpdateTime(trolleyReq.getUpdateTime());
            trolley.setDelFlag(trolleyReq.getDelFlag());
            trolley.setProductType(trolleyReq.getProductType());
            trolley.setBizId(trolleyReq.getBizId());
            trolleys.add(trolley);
        }
        return trolleys;
    }

    public static PlaceOrderResultResp placeOrderResultResp(PlaceOrderResultVO placeOrderResultVO) {
        PlaceOrderResultResp placeOrderResultResp = new PlaceOrderResultResp();
        if (Objects.isNull(placeOrderResultVO)) {
            return null;
        }
        placeOrderResultResp.setMasterOrderNo(placeOrderResultVO.getMasterOrderNo());
        placeOrderResultResp.setActualAmount(placeOrderResultVO.getActualAmount());
        placeOrderResultResp.setMName(placeOrderResultVO.getMName());
        placeOrderResultResp.setSubOrderNos(placeOrderResultVO.getSubOrderNos());
        placeOrderResultResp.setSkuActing(placeOrderResultVO.getSkuActing());
        placeOrderResultResp.setRechargeAmount(placeOrderResultVO.getRechargeAmount());
        return placeOrderResultResp;
    }
}
