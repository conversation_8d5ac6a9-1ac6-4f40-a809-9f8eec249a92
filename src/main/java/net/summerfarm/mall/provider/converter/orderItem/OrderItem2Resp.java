package net.summerfarm.mall.provider.converter.orderItem;

import net.summerfarm.mall.client.resp.InventoryQueryResp;
import net.summerfarm.mall.client.resp.OrderItemResp;
import net.summerfarm.mall.model.vo.InventoryVO;
import net.summerfarm.mall.model.vo.OrderItemVO;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
public class OrderItem2Resp {

    public static OrderItemResp domain2VO(OrderItemVO orderItem) {
        OrderItemResp resp = new OrderItemResp();
        resp.setOrderNo(orderItem.getOrderNo());
        resp.setActualTotalPrice(!ObjectUtils.isEmpty(orderItem.getActualTotalPrice())?orderItem.getActualTotalPrice():orderItem.getTotalPrice());
        resp.setAmount(orderItem.getAmount());
        resp.setId(orderItem.getId());
        resp.setPdName(orderItem.getPdName());
        resp.setPicturePath(orderItem.getPicturePath());
        resp.setPrice(orderItem.getPrice());
        resp.setSku(orderItem.getSku());
        resp.setSkuName(orderItem.getSkuName());
        resp.setWarehouseNo(orderItem.getWarehouseNo());
        resp.setWeight(orderItem.getWeight());
        resp.setType(orderItem.getSkuType());
        resp.setPdId(orderItem.getPdId());

        resp.setItemCategory(orderItem.getItemCategory());
        resp.setVolume(orderItem.getVolume());
        resp.setWeightNum(orderItem.getWeightNum());
        resp.setStorageLocation(orderItem.getStorageLocation());
        resp.setUnit(orderItem.getUnit());
        resp.setCategoryType(orderItem.getType());
        resp.setProductType(orderItem.getProductType());
        resp.setOriginalPrice(orderItem.getOriginalPrice());
        resp.setCategoryId(orderItem.getCategoryId());


        resp.setBuyerName(orderItem.getBuyerName());
        resp.setSkuCost(orderItem.getSkuCost());
        resp.setSkuPickUpSpecification(orderItem.getSkuPickUpSpecification());
        resp.setSupplierId(orderItem.getSupplierId());
        resp.setStatus(orderItem.getStatus());
        return resp;
    }


    public static InventoryQueryResp inventoryDomain2VO(InventoryVO inventoryVO) {
        InventoryQueryResp resp = new InventoryQueryResp();

        resp.setInvId(inventoryVO.getInvId().longValue());
        resp.setSku(inventoryVO.getSku());
        resp.setPdId(inventoryVO.getPdId());
        resp.setSkuName(inventoryVO.getPdName());
        resp.setSkuPic(inventoryVO.getPicturePath());

        resp.setVolume(inventoryVO.getVolume());
        resp.setWeightNum(inventoryVO.getWeightNum());

        resp.setWeight(inventoryVO.getWeight());
        resp.setUnit(inventoryVO.getUnit());
        resp.setType(inventoryVO.getSkuType());
        resp.setCategoryCategory(inventoryVO.getCategoryCategory());
        resp.setCategoryType(inventoryVO.getType());
        resp.setStorageLocation(inventoryVO.getStorageLocation());
        return resp;
    }
}
