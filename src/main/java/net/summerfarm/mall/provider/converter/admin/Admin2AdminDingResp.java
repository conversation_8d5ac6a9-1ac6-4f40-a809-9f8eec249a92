package net.summerfarm.mall.provider.converter.admin;

import net.summerfarm.mall.client.resp.AdminResp;
import net.summerfarm.mall.model.domain.Admin;

/**
 * <AUTHOR>
 */
public class Admin2AdminDingResp {

    public static AdminResp domain2VO(Admin admin) {
        AdminResp resp = new AdminResp();
        resp.setAdminChain(admin.getAdminChain());
        resp.setAdminGrade(admin.getAdminGrade());
        resp.setAdminId(admin.getAdminId());
        resp.setAdminSwitch(admin.getAdminSwitch());
        resp.setAdminType(admin.getAdminType());
        resp.setCloseOrderTime(admin.getCloseOrderTime());
        resp.setCloseOrderType(admin.getCloseOrderType());
        resp.setContract(admin.getContract());
        resp.setContractMethod(admin.getContractMethod());
        resp.setCreateTime(admin.getCreateTime());
        resp.setDepartment(admin.getDepartment());
        resp.setGender(admin.getGender());
        resp.setIsDisabled(admin.getIsDisabled());
        resp.setKp(admin.getKp());
        resp.setLoginFailTimes(admin.getLoginFailTimes());
        resp.setMId(admin.getmId());
        resp.setMName(admin.getmName());
        resp.setNameRemakes(admin.getNameRemakes());
        resp.setLoginTime(admin.getLoginTime());
        resp.setOperateId(admin.getOperateId());
        resp.setPassword(admin.getPassword());
        resp.setPhone(admin.getPhone());
        resp.setRealname(admin.getRealname());
        resp.setSalerId(admin.getSalerId());
        resp.setSalerName(admin.getSalerName());
        resp.setUserId(admin.getUserId());
        resp.setUsername(admin.getUsername());
        resp.setSkuSorting(admin.getSkuSorting());
        return resp;
    }


}
