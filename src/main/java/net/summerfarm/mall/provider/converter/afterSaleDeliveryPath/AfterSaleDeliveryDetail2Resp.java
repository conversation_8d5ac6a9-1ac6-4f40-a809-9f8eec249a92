package net.summerfarm.mall.provider.converter.afterSaleDeliveryPath;

import net.summerfarm.mall.client.resp.AfterSaleDeliveryDetailQueryResp;
import net.summerfarm.mall.model.domain.AfterSaleDeliveryDetail;

/**
 * <AUTHOR>
 */
public class AfterSaleDeliveryDetail2Resp {

    public static AfterSaleDeliveryDetailQueryResp domain2VO(AfterSaleDeliveryDetail deliveryDetail) {
        AfterSaleDeliveryDetailQueryResp resp = new AfterSaleDeliveryDetailQueryResp();
        resp.setAsDeliveryPathId(deliveryDetail.getAsDeliveryPathId());
        resp.setCreateTime(deliveryDetail.getCreateTime());
        resp.setId(deliveryDetail.getId());
        resp.setPdName(deliveryDetail.getPdName());
        resp.setQuantity(deliveryDetail.getQuantity());
        resp.setSku(deliveryDetail.getSku());
        resp.setStatus(deliveryDetail.getStatus());
        resp.setType(deliveryDetail.getType());
        resp.setUpdateTime(deliveryDetail.getUpdateTime());
        resp.setWeight(deliveryDetail.getWeight());
        return resp;
    }
}
