package net.summerfarm.mall.provider.converter.merchant;

import net.summerfarm.mall.client.resp.MerchantQueryResp;
import net.summerfarm.mall.client.resp.MerchantSubAccountResp;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.vo.MerchantSubAccountVO;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class Merchant2Resp {

    public static MerchantQueryResp domain2VO(Merchant merchant) {
        MerchantQueryResp resp = new MerchantQueryResp();
        resp.setAreaNo(merchant.getAreaNo());
        resp.setCompanyBrand(merchant.getCompanyBrand());
        resp.setDirect(merchant.getDirect());
        resp.setEnterpriseScale(merchant.getEnterpriseScale());
        resp.setMcontact(merchant.getMcontact());
        resp.setMerchantType(merchant.getMerchantType());
        resp.setMId(merchant.getmId());
        resp.setMname(merchant.getMname());
        resp.setPhone(merchant.getPhone());
        resp.setPoiNote(merchant.getPoiNote());
        resp.setSize(merchant.getSize());
        resp.setSkuShow(merchant.getSkuShow());
        resp.setType(merchant.getType());
        return resp;
    }

    public static List<MerchantQueryResp> listDomain2VO(List<Merchant> merchants) {
        List<MerchantQueryResp> merchantQueryResps = new ArrayList<>(merchants.size());
        merchants.stream().forEach(merchant -> {
            MerchantQueryResp resp = new MerchantQueryResp();
            resp.setAreaNo(merchant.getAreaNo());
            resp.setCompanyBrand(merchant.getCompanyBrand());
            resp.setDirect(merchant.getDirect());
            resp.setEnterpriseScale(merchant.getEnterpriseScale());
            resp.setMcontact(merchant.getMcontact());
            resp.setMerchantType(merchant.getMerchantType());
            resp.setMId(merchant.getmId());
            resp.setMname(merchant.getMname());
            resp.setPhone(merchant.getPhone());
            resp.setPoiNote(merchant.getPoiNote());
            resp.setSize(merchant.getSize());
            resp.setSkuShow(merchant.getSkuShow());
            resp.setType(merchant.getType());
            resp.setOpenId(merchant.getOpenid());
            if (Objects.isNull(merchant.getGrade())) {
                resp.setGrade(0);
            } else {
                resp.setGrade(merchant.getGrade());
            }
            merchantQueryResps.add(resp);
        });
        return merchantQueryResps;
    }

    public static MerchantSubAccountResp merchantSubAccountVO2Resp(MerchantSubAccountVO merchantSubAccountVO) {
        if (ObjectUtils.isEmpty(merchantSubAccountVO)){
            return null;
        }
        MerchantSubAccountResp merchantSubAccountResp = new MerchantSubAccountResp();
        merchantSubAccountResp.setAccountId(merchantSubAccountVO.getAccountId());
        merchantSubAccountResp.setMId(merchantSubAccountVO.getmId());
        merchantSubAccountResp.setContact(merchantSubAccountVO.getContact());
        merchantSubAccountResp.setPhone(merchantSubAccountVO.getPhone());
        merchantSubAccountResp.setUnionid(merchantSubAccountVO.getUnionid());
        merchantSubAccountResp.setOpenid(merchantSubAccountVO.getOpenid());
        merchantSubAccountResp.setMpOpenid(merchantSubAccountVO.getMpOpenid());
        merchantSubAccountResp.setPopView(merchantSubAccountVO.getPopView());
        merchantSubAccountResp.setFirstPopView(merchantSubAccountVO.getFirstPopView());
        merchantSubAccountResp.setCashAmount(merchantSubAccountVO.getCashAmount());
        merchantSubAccountResp.setCashUpdateTime(merchantSubAccountVO.getCashUpdateTime());
        merchantSubAccountResp.setLoginTime(merchantSubAccountVO.getLoginTime());
        merchantSubAccountResp.setLastOrderTime(merchantSubAccountVO.getLastOrderTime());
        merchantSubAccountResp.setStatus(merchantSubAccountVO.getStatus());
        merchantSubAccountResp.setDeleteFlag(merchantSubAccountVO.getDeleteFlag());
        merchantSubAccountResp.setMInfo(merchantSubAccountVO.getmInfo());
        merchantSubAccountResp.setRegisterTime(merchantSubAccountVO.getRegisterTime());
        merchantSubAccountResp.setAuditTime(merchantSubAccountVO.getAuditTime());
        merchantSubAccountResp.setAuditUser(merchantSubAccountVO.getAuditUser());
        merchantSubAccountResp.setType(merchantSubAccountVO.getType());
        merchantSubAccountResp.setWechatName(merchantSubAccountVO.getWechatName());
        merchantSubAccountResp.setWechatAvatar(merchantSubAccountVO.getWechatAvatar());
        merchantSubAccountResp.setChangeFlag(merchantSubAccountVO.getChangeFlag());
        merchantSubAccountResp.setDelFlag(merchantSubAccountVO.getDelFlag());
        merchantSubAccountResp.setEditFlag(merchantSubAccountVO.getEditFlag());
        return merchantSubAccountResp;
    }
}
