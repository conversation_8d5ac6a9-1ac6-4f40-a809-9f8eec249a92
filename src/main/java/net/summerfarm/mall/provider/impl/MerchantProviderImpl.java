package net.summerfarm.mall.provider.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.client.provider.MerchantProvider;
import net.summerfarm.mall.client.req.MerchantQueryReq;
import net.summerfarm.mall.client.req.merchant.SubAccountQueryReq;
import net.summerfarm.mall.client.req.merchant.TagManageReq;
import net.summerfarm.mall.client.resp.MerchantQueryResp;
import net.summerfarm.mall.client.resp.MerchantSubAccountResp;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.vo.MerchantSubAccountVO;
import net.summerfarm.mall.provider.converter.merchant.Merchant2Resp;
import net.summerfarm.mall.provider.converter.merchant.Req2TagManage;
import net.summerfarm.mall.service.MerchantSubAccountService;
import net.summerfarm.mall.wechat.service.WeChatService;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@Component
public class MerchantProviderImpl implements MerchantProvider {


    @Resource
    MerchantMapper merchantMapper;
    @Resource
    private WeChatService weChatService;

    @Resource
    private MerchantSubAccountService accountService;

    @Override
    public DubboResponse<MerchantQueryResp> getMerchant(MerchantQueryReq merchantQueryReq) {
        log.info("获取商户基本信息:{}", JSON.toJSONString(merchantQueryReq));
        MerchantQueryResp merchantQueryResp = new MerchantQueryResp();
        Merchant merchant =  merchantMapper.selectByMid(merchantQueryReq.getMId());
        if (!ObjectUtils.isEmpty(merchant)){
            merchantQueryResp = Merchant2Resp.domain2VO(merchant);
        }
        log.info("返回商户基本信息:{}", JSON.toJSONString(merchantQueryResp));
        return DubboResponse.getOK(merchantQueryResp);
    }

    @Override
    public DubboResponse<List<MerchantQueryResp>> batchGetMerchant(@Valid MerchantQueryReq merchantQueryReq) {
        log.info("MerchantProviderImpl[]batchGetMerchant[]merchantQueryReq:{}", JSON.toJSONString(merchantQueryReq));
        List<Merchant> merchantList = merchantMapper.selectMerchantList(merchantQueryReq.getMIds());
        if (CollectionUtils.isEmpty(merchantList)) {
            return DubboResponse.getOK(Collections.emptyList());
        }
        List<MerchantQueryResp> merchantQueryResps = Merchant2Resp.listDomain2VO(merchantList);
        return DubboResponse.getOK(merchantQueryResps);
    }

    @Override
    public DubboResponse<Integer> merchantSignTags(TagManageReq tagManageReq) {
        AjaxResult ajaxResult =  weChatService.tagsManage(Req2TagManage.tagManageReq2TagManage(tagManageReq));
        if (!AjaxResult.isSuccess(ajaxResult)){
            log.info("merchantSignTags请求失败:{}", JSON.toJSONString(ajaxResult));
            throw new ProviderException("请求失败！");
        }
        return DubboResponse.getOK((Integer)ajaxResult.getData());
    }

    @Override
    public DubboResponse<MerchantSubAccountResp> subAccountInfo(SubAccountQueryReq subAccountQueryReq) {
        if (ObjectUtils.isEmpty(subAccountQueryReq) || subAccountQueryReq.getAccountId() == null){
            return DubboResponse.getDefaultError("参数错误");
        }
        AjaxResult ajaxResult = accountService.selectAccountInfo(subAccountQueryReq.getAccountId());
        if (!AjaxResult.isSuccess(ajaxResult)){
            throw new ProviderException("查询错误请重试！");
        }
        return DubboResponse.getOK(Merchant2Resp.merchantSubAccountVO2Resp((MerchantSubAccountVO)ajaxResult.getData()));
    }
}
