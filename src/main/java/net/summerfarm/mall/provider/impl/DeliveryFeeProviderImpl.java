package net.summerfarm.mall.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.saas.provider.DeliveryFeeProvider;
import net.summerfarm.mall.client.saas.req.DeliveryFeeRuleQueryReq;
import net.summerfarm.mall.client.saas.req.SummerFarmDeliveryReq;
import net.summerfarm.mall.client.saas.resp.SummerFarmDeliveryResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/16
 */
@Slf4j
@DubboService
@Component
public class DeliveryFeeProviderImpl implements DeliveryFeeProvider {

    @Override
    public DubboResponse<SummerFarmDeliveryResp> queryDeliveryFee(SummerFarmDeliveryReq summerFarmDeliveryReq) {
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<SummerFarmDeliveryResp> querySummerFarmDeliveryFeeRule(DeliveryFeeRuleQueryReq deliveryFeeRuleQueryReq) {
        return DubboResponse.getOK();
    }
}
