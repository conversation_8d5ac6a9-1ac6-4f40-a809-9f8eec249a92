package net.summerfarm.mall.provider.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.enums.SaleStockChangeTypeEnum;
import net.summerfarm.mall.client.provider.OrderProvider;
import net.summerfarm.mall.client.req.AddOrderReq;
import net.summerfarm.mall.client.req.OrderBasicInfoQueryReq;
import net.summerfarm.mall.client.req.OrderReq;
import net.summerfarm.mall.client.req.UpdateDeliveryDateReq;
import net.summerfarm.mall.client.req.order.AutoCreateSampleReq;
import net.summerfarm.mall.client.req.order.PlaceOrderReq;
import net.summerfarm.mall.client.resp.AutoCreateSampleResp;
import net.summerfarm.mall.client.resp.OrderBasicInfoResp;
import net.summerfarm.mall.client.resp.OrderResp;
import net.summerfarm.mall.client.resp.PlaceOrderResultResp;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.enums.order.OrderItemErrorEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.bo.admin.AdminBriefInfoBO;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.BatchUpdateDeliveryDateVo;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.model.vo.OrderItemVO;
import net.summerfarm.mall.model.vo.OrderVO;
import net.summerfarm.mall.model.vo.neworder.PlaceOrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.provider.converter.orders.Order2Resp;
import net.summerfarm.mall.provider.converter.orders.PlaceOrderConverter;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.facade.OfcQueryFacade;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.WncDeliveryRuleQueryFacade;
import net.summerfarm.mall.service.facade.dto.*;
import net.summerfarm.mall.task.AsyncTaskService;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.ofc.client.resp.DeliveryDateQueryResp;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@Component
public class OrderProviderImpl implements OrderProvider {

    @Resource
    OrdersMapper ordersMapper;
    @Resource
    OrderItemMapper orderItemMapper;
    @Resource
    AreaMapper areaMapper;
    @Resource
    FollowUpRelationMapper followUpRelationMapper;
    @Resource
    AdminMapper adminMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;
    @Resource
    private PrepayInventoryService prepayInventoryService;
    @Resource
    @Lazy
    private OrderService orderService;
    @Resource
    private WmsAreaStoreFacade wmsAreaStoreFacade;
    @Resource
    private OrderNewService orderNewService;
    @Resource
    private WncDeliveryRuleQueryFacade wncDeliveryRuleQueryFacade;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private DeliveryPlanExtendMapper deliveryPlanExtendMapper;
    @Resource
    @Lazy
    private DeliveryService deliveryService;
    @Resource
    @Lazy
    private DeliverPlanRemarkSnapshotService deliverPlanRemarkSnapshotService;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private AsyncTaskService asyncTaskService;
    @Resource
    private TimingOrderRefundWhiteListMapper timingOrderRefundWhiteListMapper;
    @Resource
    private ConfigService configService;

    @Resource
    private AfterSaleOrderService afterSaleOrderService;

    private static final int MAX_SIZE = 50;

    @Override
    public DubboResponse<OrderResp> getOrderDetail(OrderReq orderReq) {
        OrderResp orderResp = new OrderResp();
        OrderVO orderVO =  ordersMapper.selectOrderVO(orderReq.getOrderNo());
        int i = orderItemMapper.selectAccurateDelivery(orderReq.getOrderNo());
        if (i > 0){
            orderVO.setAccuratePrice(BigDecimal.valueOf(30));
        }
        if (!ObjectUtils.isEmpty(orderVO)){
            orderResp = Order2Resp.dto2VO(orderVO);
        }

        if (!ObjectUtils.isEmpty(orderVO)){
            Area area =  areaMapper.selectByAreaNo(orderVO.getAreaNo());
            if (!ObjectUtils.isEmpty(area)){
                orderResp.setAreaName(area.getAreaName());
            }

            // 设置bd信息
            FollowUpRelation selectKeys2 = new FollowUpRelation();
            selectKeys2.setmId(orderVO.getmId());
            selectKeys2.setReassign(false);
            FollowUpRelation bd = followUpRelationMapper.selectOne(selectKeys2);
            if (Objects.nonNull(bd)) {
                Admin bdAdmin = adminMapper.selectByPrimaryKey(bd.getAdminId());
                if (!ObjectUtils.isEmpty(bdAdmin)){
                    orderResp.setBdName(bdAdmin.getRealname());
                    orderResp.setBdPhone(bdAdmin.getPhone());
                    orderResp.setBrandName(bdAdmin.getNameRemakes());
                }
            }
            // 大客户的品牌名称 -> 拿订单里的adminId去admin表里查询品牌名称
            if (null != orderVO.getAdminId() && orderVO.getAdminId() > 0){
                AdminBriefInfoBO adminBriefInfoBO = adminMapper.selectAdminBasicInfoByAdminId(orderVO.getAdminId());
                if (null != adminBriefInfoBO){
                    orderResp.setBrandName(adminBriefInfoBO.getNameRemakes());
                    orderResp.setPickType(Integer.valueOf(0).equals(adminBriefInfoBO.getSkuSorting()) ? 1 : 0);
                }

            }
            // 补充订单支付时间
            orderResp.setPayTime(paymentMapper.selectPayTimeByOrderNoForceMaster(orderReq.getOrderNo()));
        }
        return DubboResponse.getOK(orderResp);
    }

    @Override
    public DubboResponse<List<OrderBasicInfoResp>> getOrderBasicInfoByOrderNoList(OrderBasicInfoQueryReq orderBasicInfoQueryReq) {
        if (orderBasicInfoQueryReq == null || CollectionUtils.isEmpty(orderBasicInfoQueryReq.getOrderNoList())){
            return DubboResponse.getOK(Lists.newArrayList());
        }
        List<Orders> orders = ordersMapper.listOrderByOrderNoList(orderBasicInfoQueryReq.getOrderNoList());
        return DubboResponse.getOK(Order2Resp.modelList2Resp(orders));
    }

    @Override
    public DubboResponse<Boolean> closeOrder(@Valid OrderReq orderReq) {
        String orderNo = orderReq.getOrderNo();
        OrderVO orderVO = ordersMapper.selectByOrderyNo(orderNo);
        if (Objects.isNull(orderVO)) {
            throw new ProviderException("当前订单信息不存在！");
        }
        List<DeliveryPlanVO> planVOList = deliveryPlanMapper.selectByOrderNo(orderNo);

        //关单状态校验
        boolean flag = getCloseFlag(orderVO, planVOList);
        if(!flag){
            throw new ProviderException("该订单不可关单！");
        }

        //大客户返还预付
        if(Objects.equals(Global.BIG_MERCHANT,orderVO.getSize())){
            List<PrepayInventoryRecord> prepayInventoryRecords = prepayInventoryRecordMapper.selectRecordListByOrderNo(orderNo);
            if(!CollectionUtils.isEmpty(prepayInventoryRecords)){

                prepayInventoryRecords.forEach( x->{
                    boolean decrease = prepayInventoryService.increase(orderVO.getAdminId(), x.getSku(), orderNo, x.getAmount());
                    if(!decrease){
                        throw new ProviderException("取消订单返还预约商品数量失败！");
                    }
                });
            }
        }

        //处理库存
        List<OrderItemVO> itemList = orderItemMapper.selectByOrderNo(orderNo);
        itemList.sort(Comparator.comparing(OrderItem::getSku));

        Long contactId = CollectionUtils.isEmpty(planVOList) ?  null : planVOList.get(0).getContactId();
        //Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        UserBase user = UserInfoHolder.getUser();
        log.info("当前操作人是 >>> {}", JSON.toJSONString(user));

        if(OrderTypeEnum.TIMING.getId().equals(orderVO.getType())){
            for (OrderItem item : itemList) {
                planVOList.stream().forEach(plan -> {
                    if (!Objects.equals(plan.getStatus(),OrderStatusEnum.DELIVERING.getId())) {
                        return;
                    }
                    //释放库存
                    AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
                    areaStoreUnLockReq.setContactId(plan.getContactId());
                    areaStoreUnLockReq.setOrderType(SaleStockChangeTypeEnum.TIMING_PLAN_DEL.getTypeName());
                    areaStoreUnLockReq.setOrderNo(orderVO.getOrderNo());
                    areaStoreUnLockReq.setOrderSubNo(String.valueOf(plan.getId()));
                    areaStoreUnLockReq.setIdempotentNo(orderVO.getOrderNo() + plan.getId());
                    areaStoreUnLockReq.setOperatorNo(orderVO.getOrderNo());
                    areaStoreUnLockReq.setMerchantId(orderVO.getmId());
                    areaStoreUnLockReq.setSource(SourceEnum.XM_MALL_TIMING.getValue());
                    areaStoreUnLockReq.setOperatorName(Objects.isNull(user) ? "系统默认" : user.getNickname());
                    OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
                    orderUnLockSkuDetailReqDTO.setSkuCode(item.getSku());
                    orderUnLockSkuDetailReqDTO.setReleaseQuantity(plan.getQuantity());
                    areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(Collections.singletonList(orderUnLockSkuDetailReqDTO));
                    wmsAreaStoreFacade.storeUnLock(areaStoreUnLockReq);
                });
            }
        } else {
            //释放库存
            AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
            areaStoreUnLockReq.setContactId(contactId);
            areaStoreUnLockReq.setOrderType(SaleStockChangeTypeEnum.MANUAL_CLOSED.getTypeName());
            areaStoreUnLockReq.setOrderNo(orderVO.getOrderNo());
            areaStoreUnLockReq.setIdempotentNo(orderVO.getOrderNo());
            areaStoreUnLockReq.setOperatorNo(orderVO.getOrderNo());
            areaStoreUnLockReq.setMerchantId(orderVO.getmId());
            areaStoreUnLockReq.setSource(DistOrderSourceEnum.getDistOrderSourceByOrderType(orderVO.getType()));
            areaStoreUnLockReq.setOperatorName(Objects.isNull(user) ? "系统默认" : user.getNickname());
            List<OrderUnLockSkuDetailReqDTO> orderUnLockSkuDetailReqDTOS = new ArrayList<>();
            itemList.stream().forEach(item -> {
                OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
                orderUnLockSkuDetailReqDTO.setSkuCode(item.getSku());
                orderUnLockSkuDetailReqDTO.setReleaseQuantity(item.getAmount());
                orderUnLockSkuDetailReqDTOS.add(orderUnLockSkuDetailReqDTO);
            });
            areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(orderUnLockSkuDetailReqDTOS);
            wmsAreaStoreFacade.storeUnLock(areaStoreUnLockReq);
        }
        //log.info("ofc调用关单dubbo接口插入库存操作记录:{}", JSON.toJSONString(recordMap));
        //quantityChangeRecordService.insert(recordMap);

        //生成售后单
        afterSaleOrderService.closeOrderGenerateAfterSaleOrder(itemList,orderNo, user);

        //更新状态
        Orders ordersUpdate = new Orders();
        ordersUpdate.setOrderNo(orderNo);
        ordersUpdate.setStatus((short) OrderStatusEnum.MANUAL_CLOSED.getId());
        ordersMapper.updateByOrderNoSelective(ordersUpdate);

        orderItemMapper.updateStatusByOrderNo(orderNo, OrderStatusEnum.MANUAL_CLOSED.getId());

        if(!CollectionUtils.isEmpty(planVOList)){
            deliveryPlanMapper.updateStatusBatch(planVOList, OrderStatusEnum.MANUAL_CLOSED.getId());
        }

        Orders orders = ordersMapper.selectOne(orderNo);
        List<OrderItem> orderItems = orderItemMapper.selectOrderItem(orderNo);
        orderItems.forEach(el -> {
            orderService.reduceFruitSales(el.getId(), orders.getAreaNo());
        });
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    @Deprecated
    public DubboResponse<Map<String, Object>> orderPlaceOrderV2(PlaceOrderReq placeOrderReq) {
        return DubboResponse.getDefaultError("接口废弃");
    }


    @Override
    public DubboResponse<PlaceOrderResultResp> orderPlaceOrderV3(PlaceOrderReq placeOrderReq) {
        PlaceOrderVO placeOrderVO =  PlaceOrderConverter.placeOrderReq2VO(placeOrderReq);
        if (ObjectUtils.isEmpty(placeOrderVO)){
            return DubboResponse.getDefaultError("下单数据为空");
        }
        if (Objects.isNull(placeOrderVO.getMId())) {
            return DubboResponse.getDefaultError("请先登录");
        }
        PlaceOrderResultVO placeOrderResultVO;
        try {
            placeOrderResultVO = orderNewService.placeOrder(placeOrderVO);
        } catch (BizException e) {
            log.warn("下单异常：{}", e);
            if (Objects.nonNull(e.getErrorCode()) && Objects.equals(e.getErrorCode().getStatus(), 0)) {
                if (StringUtils.isNotBlank(e.getErrorCode().getCode()) && e.getErrorCode().getCode().contains("BIZ-")) {
                    //截取 BIZ-
                    String code = e.getErrorCode().getCode();
                    String str = code.substring(0, code.indexOf("-"));
                    return DubboResponse.getDefaultError("下单异常！" + code.substring(str.length()+1));
                }
                return DubboResponse.getDefaultError("下单异常！" + e.getErrorCode().getCode());
            }
            return DubboResponse.getDefaultError("下单异常！" + e.getMessage());
        } catch (Exception e) {
            log.warn("下单异常：{}", e);
            return DubboResponse.getDefaultError("下单异常！" + e.getMessage());
        }
        if (!CollectionUtils.isEmpty(placeOrderResultVO.getOrderItemErrorList())) {
            StringBuilder stringBuilder = new StringBuilder();
            placeOrderResultVO.getOrderItemErrorList().stream().forEach(e -> {
                if (Objects.equals(e.getReason(), OrderItemErrorEnum.LOW_STOCK.getCode())) {
                    stringBuilder.append(e.getPdName()).append("库存不足。");
                } else if (Objects.equals(e.getReason(), OrderItemErrorEnum.DELIVERY_DATE_CHANGE.getCode())) {
                    stringBuilder.append(e.getPdName()).append("配送时间发生变化。");
                } else if (Objects.equals(e.getReason(), OrderItemErrorEnum.NOT_ON_SALE.getCode())) {
                    stringBuilder.append(e.getPdName()).append("暂无上架。");
                }
            });
            log.warn("下单异常：{}", JSON.toJSONString(placeOrderResultVO));
            return DubboResponse.getDefaultError("下单异常！" + stringBuilder);
        }
        return DubboResponse.getOK(PlaceOrderConverter.placeOrderResultResp(placeOrderResultVO));
    }

    /**
     * 计算关单标识
     * @param orderVO 订单信息
     * @param planVOList 配送计划信息
     * @return t、可以关单 f、不可关单
     */
    private Boolean getCloseFlag(OrderVO orderVO, List<DeliveryPlanVO> planVOList) {
        if (!OrderTypeEnum.NORMAL.getId().equals(orderVO.getType())
                && !OrderTypeEnum.TIMING.getId().equals(orderVO.getType())
                && !OrderTypeEnum.HELP.getId().equals(orderVO.getType())) {
            log.info("订单类型异常,不可关单 >>> {}", JSON.toJSONString(orderVO));
            return false;
        }
        //已生成出库任务不可关单
        if(Objects.equals(1, orderVO.getOutStock())){
            log.info("已生成出库任务不可关单 >>> {}", JSON.toJSONString(orderVO));
            return false;
        }

        if (OrderStatusEnum.WAIT_DELIVERY.getId() != orderVO.getStatus() && OrderStatusEnum.DELIVERING.getId() != orderVO.getStatus()) {
            log.info("订单状态异常,不可关单 >>> {}", JSON.toJSONString(orderVO));
            return false;
        }

        //账期类型订单不可关单（直接售后录入账单）
//        if (Objects.equals(orderVO.getDirect(), 1)){
//            return false;
//        }

        //无配送计划，可直接关单
        if (CollectionUtils.isEmpty(planVOList)) {
            return true;
        }

        //存在已收货的配送计划，不可关单
        boolean f = planVOList.stream().anyMatch(el -> OrderStatusEnum.RECEIVED.getId() == el.getStatus());
        if (f) {
            log.info("存在已收货的配送计划，不可关单 >>> {}", JSON.toJSONString(orderVO));
            return false;
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DubboResponse<Boolean> addOrder(@Valid AddOrderReq addOrderReq) {
        String orderNo = addOrderReq.getOrderNo();

        //校验普通订单才能加单
        OrderVO order = ordersMapper.selectByOrderyNo(orderNo);
        if (Objects.isNull(order) || Objects.equals(order.getType(), OrderTypeEnum.TIMING.getId())) {
            return DubboResponse.getDefaultError("订单数据为空或普通订单才能加单！");
        }

        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orderNo);
        if (CollectionUtils.isEmpty(deliveryPlanVOS)) {
            return DubboResponse.getDefaultError("订单配送计划为空！");
        }
        DeliveryPlanVO deliveryPlanVO = deliveryPlanVOS.get(0);

        //查询当前地址信息
        Contact contact = contactMapper.selectByPrimaryKey(deliveryPlanVO.getContactId());

        //查询可配日期
        LocalDate nextDeliveryDate = wncDeliveryRuleQueryFacade.queryCloudDeliveryDate(DeliveryRuleQueryInput.builder()
                .contactId(contact.getContactId())
                .source(DistOrderSourceEnum.getSourceEnumByOrderType(order.getType()))
                .orderTime(LocalDateTime.now().minusMinutes(30))
                .merchantId(contact.getmId())
                .build());

        //更改订单状态
        Orders update = new Orders();
        update.setOrderNo(orderNo);
        update.setOutTimes(NumberUtils.INTEGER_ONE);
        ordersMapper.updateByOrderNoSelective(update);

        //修改配送时间
        DeliveryPlan deliveryPlan = new DeliveryPlan();
        deliveryPlan.setOrderNo(orderNo);
        deliveryPlan.setDeliveryTime(nextDeliveryDate);
        deliveryPlanMapper.updateDeliveryDate(deliveryPlanVO.getId(), nextDeliveryDate);
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DubboResponse<Boolean> updateDeliveryDate(@Valid UpdateDeliveryDateReq updateDeliveryDateReq) {
        OrderVO orders = ordersMapper.selectByOrderyNo(updateDeliveryDateReq.getOrderNo());
        if (orders == null) {
            return DubboResponse.getDefaultError("修改记录不存在");
        }

        // 先找出原配送计划
        DeliveryPlan select = new DeliveryPlan();
        select.setDeliveryTime(updateDeliveryDateReq.getOldDeliveryTime());
        select.setContactId(updateDeliveryDateReq.getContactId());
        select.setOrderNo(updateDeliveryDateReq.getOrderNo());
        DeliveryPlan deliveryPlan = deliveryPlanMapper.selectOne(select);
        if (deliveryPlan == null) {
            return DubboResponse.getDefaultError("修改记录不存在配送计划");
        }

        // 校验配送计划是否相同
        select.setDeliveryTime(updateDeliveryDateReq.getDeliveryTime());
        DeliveryPlan exist = deliveryPlanMapper.selectOne(select);
        if (exist != null) {
            return DubboResponse.getDefaultError("不能设置同一天配送计划");
        }

        //查询当前地址信息
        Contact contact = contactMapper.selectByPrimaryKey(deliveryPlan.getContactId());

        //校验配送时间
        if (!updateDeliveryDateReq.getDeliveryTime().isAfter(LocalDate.now())) {
            return DubboResponse.getDefaultError("配送时间不能为当前以及之前");
        }

        //省心送先删除原先的配送计划,再新增一条配送记录
        if (Objects.equals(orders.getType(), OrderTypeEnum.TIMING.getId())) {
            OrderItem orderItem = orderItemMapper.selectTiming(orders.getOrderNo());

            //截单后延迟配送需求 -- 20241223
            /*// 校验配送日
            DeliveryDateQueryResp deliveryDate = ofcQueryFacade.queryDeliveryDate(LocalDateTime.now(), contact.getmId(),
                    contact.getContactId(), updateDeliveryDateReq.getDeliveryTime(), updateDeliveryDateReq.getDeliveryTime(), OfcOrderSourceEnum.XM_MALL_TIMING, Collections.singletonList(orderItem.getSku()));
            if (Objects.isNull(deliveryDate) || !deliveryDate.getDeliveryDate().equals(updateDeliveryDateReq.getDeliveryTime())) {
                return DubboResponse.getDefaultError(updateDeliveryDateReq.getDeliveryTime().toString() +"的下一个配送日为:" + deliveryDate.getDeliveryDate());
            }

            //获取截单时间
            LocalTime closeTime = ofcQueryFacade.queryCloseTime(deliveryPlan.getContactId(), OfcOrderSourceEnum.XM_MALL_TIMING);
            if (Objects.isNull(closeTime)) {
                return DubboResponse.getDefaultError("当前地区截单时间为空！");
            }
            LocalDateTime closeDate = updateDeliveryDateReq.getOldDeliveryTime().minusDays(1).atTime(closeTime);
            if (Objects.nonNull(closeTime) && LocalDateTime.now().isAfter(closeDate.minusMinutes(2))) {
                return DubboResponse.getDefaultError("配送计划需在当天" + closeTime.minusMinutes(2) + "前修改！");
            }*/


            //新增白名单逻辑
            if (!timingOrderRefundWhiteListMapper.selectByOrderNo(orders.getOrderNo())){
                String beginTimeConfig = configService.getValue(ConfigValueEnum.TIMING_CHECK_BEGIN_TIME.getKey());
                LocalDate updateTime = LocalDate.parse(beginTimeConfig);
                if (orders.getOrderTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().isAfter(updateTime)){
                    if (updateDeliveryDateReq.getDeliveryTime().isAfter(orders.getOrderTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(90))) {
                        return DubboResponse.getDefaultError("最多可选择下单日期后的90个自然日");
                    }
                }
            }

            //已发起售后的省心送计划不可取消
            int dpCount = afterSaleOrderMapper.countBydeliveryPlanId(deliveryPlan.getId());
            if (dpCount >= 1) {
                return DubboResponse.getDefaultError("已售后，不能修改配送计划了");
            }

            //释放库存
            if (deliveryPlan.getStatus().intValue() == OrderStatusEnum.DELIVERING.getId()) {
                //释放库存 再占用库存
                storeUnLock(orderItem, deliveryPlan.getContactId(), deliveryPlan.getId(), deliveryPlan.getQuantity(), orders.getmId());
            }

            //修改配送计划状态
            DeliveryPlan delete = new DeliveryPlan();
            delete.setUpdateTime(LocalDateTime.now());
            delete.setStatus(OrderStatusEnum.CANCEL.getId());
            delete.setId(deliveryPlan.getId());
            deliveryPlanMapper.updateById(delete);

            //插入操作人
            addDeliveryPlanExtend(Collections.singletonList(deliveryPlan), DeliveryPlanExtendType.DELETE.getType());

            //新增省心送配送计划
            deliveryPlan.setDeliveryTime(updateDeliveryDateReq.getDeliveryTime());
            LocalDate tomorrow = deliveryService.queryTomorrow(orderItem.getSku(), contact,orders.getmId(), orders.getType());
            if (tomorrow == null) {
                return DubboResponse.getDefaultError("当前sku: "+ orderItem.getSku() + "，库存不足！");
            }
            if (deliveryPlan.getDeliveryTime().isBefore(tomorrow) || Objects.equals(tomorrow, deliveryPlan.getDeliveryTime())) {
                deliveryPlan.setStatus(OrderStatusEnum.DELIVERING.getId());
            } else {
                deliveryPlan.setStatus(OrderStatusEnum.WAIT_DELIVERY.getId());
            }
            deliveryPlan.setId(null);
            deliveryPlan.setAdminId(Objects.isNull(UserInfoHolder.getUser()) ? null : UserInfoHolder.getUser().getBizUserId());
            deliveryPlanMapper.insert(deliveryPlan);

            //插入配送计划快照
            deliverPlanRemarkSnapshotService.addDeliveryPlanNew(contact, deliveryPlan);

            //插入操作人
            addDeliveryPlanExtend(Collections.singletonList(deliveryPlan), DeliveryPlanExtendType.INSERT.getType());

            //占用库存
            if (Objects.equals(deliveryPlan.getStatus(), OrderStatusEnum.DELIVERING.getId())) {
                storeLock(orderItem, deliveryPlan.getContactId(), deliveryPlan.getId(), deliveryPlan.getQuantity(), orders.getmId());
            }
        } else {
            ArrayList<BatchUpdateDeliveryDateVo> messageList = new ArrayList<>();
            DeliveryPlan selectOriginal = new DeliveryPlan();
            selectOriginal.setOrderNo(orders.getOrderNo());
            DeliveryPlan originalDeliveryPlan = deliveryPlanMapper.selectOne(selectOriginal);
//            if(originalDeliveryPlan != null && Objects.equals(orders.getOutStock(),1)){
//                return DubboResponse.getDefaultError("已经生成出库任务，无法修改配送时间");
//            }

            //配送日期逻辑
            deliveryPlan.setDeliveryTime(updateDeliveryDateReq.getDeliveryTime());
            BatchUpdateDeliveryDateVo batchUpdateDeliveryDateVo = new BatchUpdateDeliveryDateVo();

            //查询openid和联系人信息
            BatchUpdateDeliveryDateVo merchantInfo = merchantMapper.getMerchantInfoByCId(String.valueOf(contact.getContactId()));
            if (Objects.nonNull(merchantInfo)) {
                batchUpdateDeliveryDateVo.setPhone(merchantInfo.getPhone());
                batchUpdateDeliveryDateVo.setOpenid(merchantInfo.getOpenid());
                batchUpdateDeliveryDateVo.setSendPhone(merchantInfo.getSendPhone());
            }

            //截单后延迟配送需求 -- 20241223
            /*try {
                deliveryDateJudge(contact, originalDeliveryPlan, deliveryPlan, batchUpdateDeliveryDateVo);
            } catch (BizException e) {
                return DubboResponse.getDefaultError(e.getMessage());
            }*/
            batchUpdateDeliveryDateVo.setOrderNo(orders.getOrderNo());
            batchUpdateDeliveryDateVo.setDeliveryTime(originalDeliveryPlan.getDeliveryTime());
            batchUpdateDeliveryDateVo.setNewDeliveryTime(updateDeliveryDateReq.getDeliveryTime());
            messageList.add(batchUpdateDeliveryDateVo);

            if(originalDeliveryPlan != null && originalDeliveryPlan.getOldDeliveryTime() == null){
                LocalDate oldDeliveryTime = originalDeliveryPlan.getDeliveryTime();
                deliveryPlanMapper.updateDeliveryTimeAndOldDate(orders.getOrderNo(), deliveryPlan.getDeliveryTime(), deliveryPlan.getContactId(), oldDeliveryTime);
            }else{
                deliveryPlanMapper.updateDeliveryTime(orders.getOrderNo(), deliveryPlan.getDeliveryTime(), deliveryPlan.getContactId());
            }
            deliverPlanRemarkSnapshotService.addDeliveryPlanNew(contact, originalDeliveryPlan);

            //发送信息
            asyncTaskService.sendBatchUpdateDeliveryDateMesaageAndWeChat(messageList);
        }
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    public DubboResponse<List<AutoCreateSampleResp>> queryOrderInfoByAutoCreateSampleTask(@Valid AutoCreateSampleReq autoCreateSampleReq) {
        //根据客户ID批量过滤--未下单过保温袋【SPU：737545156】、且当日有待配送订单
        //配送地址：取待配送订单相同地址，若有多条，则取创建时间最早的地址
        String sampleSpu = configService.getValue(Global.SAMPLE_SPU);
        sampleSpu = StringUtils.isNotBlank(sampleSpu) ? sampleSpu : "737545156";
        List<Long> mIdList = autoCreateSampleReq.getMIdList();
        List<AutoCreateSampleResp> autoCreateSampleResps = new ArrayList<>();

        //根据客户ID批量过滤--未下单过保温袋【SPU：737545156】 -- 超过50个分批查询
        List<DeliveryPlanVO> allDeliveryPlans = new ArrayList<>();
        List<List<Long>> partitionList = Lists.partition(mIdList, MAX_SIZE);
        for (List<Long> subMIds : partitionList) {
            Set<Long> purchaseOrderMIds = ordersMapper.getOrderRecordBySpu(subMIds, sampleSpu);
            if (!CollectionUtils.isEmpty(purchaseOrderMIds)) {
                subMIds = subMIds.stream().filter(mId -> !purchaseOrderMIds.contains(mId)).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(subMIds)) {
                log.info("OrderProvider[]queryOrderInfoByAutoCreateSampleTask[]subMIds is empty! purchaseOrderMIds:{}", JSON.toJSONString(purchaseOrderMIds));
                continue;
            }

            //当日有待配送订单 T+1 取待配送订单相同地址，若有多条，则取创建时间最早的地址
            List<DeliveryPlanVO> deliveryPlans = deliveryPlanMapper.getDeliveryPlanByMIds(subMIds, LocalDate.now().plusDays(1));
            if (CollectionUtils.isEmpty(deliveryPlans)) {
                log.info("OrderProvider[]queryOrderInfoByAutoCreateSampleTask[]deliveryPlans is empty! subMIds:{}, deliveryTime:{}",
                        JSON.toJSONString(subMIds), JSON.toJSONString(LocalDate.now().plusDays(1)));
                continue;
            }
            allDeliveryPlans.addAll(deliveryPlans);
        }

        if (CollectionUtils.isEmpty(allDeliveryPlans)) {
            log.info("OrderProvider[]queryOrderInfoByAutoCreateSampleTask[]allDeliveryPlans is empty!");
            return DubboResponse.getOK(autoCreateSampleResps);
        }

        //根据contactId排序然后再根据mid去重
        allDeliveryPlans.stream().sorted(Comparator.comparing(DeliveryPlanVO::getContactId)).collect(
                        Collectors.toMap(DeliveryPlanVO::getmId, k -> k, (oldValue, newValue) -> oldValue, LinkedHashMap::new))
                .values().forEach(deliveryPlanVO -> {
                    AutoCreateSampleResp autoCreateSampleResp = new AutoCreateSampleResp();
                    autoCreateSampleResp.setContactId(deliveryPlanVO.getContactId());
                    autoCreateSampleResp.setOrderNo(deliveryPlanVO.getOrderNo());
                    autoCreateSampleResp.setDeliveryDate(deliveryPlanVO.getDeliveryTime());
                    autoCreateSampleResp.setMContact(deliveryPlanVO.getContact());
                    autoCreateSampleResp.setMPhone(deliveryPlanVO.getPhone());
                    autoCreateSampleResp.setMId(deliveryPlanVO.getmId());
                    autoCreateSampleResp.setStoreNo(deliveryPlanVO.getOrderStoreNo());
                    autoCreateSampleResps.add(autoCreateSampleResp);
                });
        return DubboResponse.getOK(autoCreateSampleResps);
    }

    /**
     * @description: 添加配送计划操作日志
     * @author: lzh
     * @date: 2023/12/13 15:13
     * @param: [deliveryPlans, Type]
     * @return: void
     **/
    public void addDeliveryPlanExtend(List<DeliveryPlan> deliveryPlans,Integer Type) {
        if (CollectionUtils.isEmpty(deliveryPlans)) {
            return;
        }
        UserBase user = UserInfoHolder.getUser();
        List<DeliveryPlanExtend> planExtends = new ArrayList<>();
        for (DeliveryPlan deliveryPlan : deliveryPlans){
            DeliveryPlanExtend deliveryPlanExtend = new DeliveryPlanExtend();
            deliveryPlanExtend.setOrderNo(deliveryPlan.getOrderNo());
            deliveryPlanExtend.setDeliveryPlanId(deliveryPlan.getId());
            deliveryPlanExtend.setOperatorType(OperatorType.EMPLOYEE.getType());
            deliveryPlanExtend.setOperator(Objects.isNull(user) ? "系统默认" : user.getNickname());
            deliveryPlanExtend.setType(Type);
            deliveryPlanExtend.setCreateTime(LocalDateTime.now());
            planExtends.add(deliveryPlanExtend);
        }
        deliveryPlanExtendMapper.insertBatch(planExtends);
    }

    /**
     * @description: 占用库存
     * @author: lzh
     * @date: 2023/11/20 17:27
     * @param: [orderItem, contactId, deliveryId, quantity]
     * @return: void
     **/
    private void storeLock(OrderItem orderItem, Long contactId, Integer deliveryId, Integer quantity, Long mId) {
        UserBase user = UserInfoHolder.getUser();
        String orderNo = orderItem.getOrderNo();
        AreaStoreLockReq storeLockReq = new AreaStoreLockReq();
        storeLockReq.setOrderNo(orderNo);
        storeLockReq.setOrderSubNo(String.valueOf(deliveryId));
        storeLockReq.setMerchantId(mId);
        storeLockReq.setContactId(contactId);
        storeLockReq.setOrderType(SaleStockChangeTypeEnum.TIMING_PLAN_CREATE.getTypeName());
        storeLockReq.setOperatorNo(orderNo);
        storeLockReq.setIdempotentNo(orderNo + deliveryId);
        storeLockReq.setOperatorName(Objects.isNull(user) ? "系统默认" : user.getNickname());
        storeLockReq.setSource(SourceEnum.XM_MALL_TIMING.getValue());
        OrderLockSkuDetailReqDTO reqDTO = new OrderLockSkuDetailReqDTO();
        reqDTO.setSkuCode(orderItem.getSku());
        reqDTO.setOccupyQuantity(quantity);
        storeLockReq.setOrderLockSkuDetailReqDTOS(Collections.singletonList(reqDTO));
        wmsAreaStoreFacade.storeLock(storeLockReq);
    }

    /**
     * @description: 释放库存
     * @author: lzh
     * @date: 2023/11/20 17:20
     * @param: [orderItem, record]
     * @return: void
     **/
    private void storeUnLock(OrderItem orderItem, Long contactId, Integer deliveryId, Integer quantity, Long mId) {
        UserBase user = UserInfoHolder.getUser();
        String orderNo = orderItem.getOrderNo();
        AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
        areaStoreUnLockReq.setContactId(contactId);
        areaStoreUnLockReq.setOrderType(SaleStockChangeTypeEnum.MALL_TIMING_PLAN_DEL.getTypeName());
        areaStoreUnLockReq.setOrderNo(orderNo);
        areaStoreUnLockReq.setOrderSubNo(String.valueOf(deliveryId));
        areaStoreUnLockReq.setIdempotentNo(orderNo + deliveryId);
        areaStoreUnLockReq.setOperatorNo(orderNo);
        areaStoreUnLockReq.setSource(SourceEnum.XM_MALL_TIMING.getValue());
        areaStoreUnLockReq.setOperatorName(Objects.isNull(user) ? "系统默认" : user.getNickname());
        areaStoreUnLockReq.setMerchantId(mId);
        OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
        orderUnLockSkuDetailReqDTO.setSkuCode(orderItem.getSku());
        orderUnLockSkuDetailReqDTO.setReleaseQuantity(quantity);
        areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(Collections.singletonList(orderUnLockSkuDetailReqDTO));
        wmsAreaStoreFacade.storeUnLock(areaStoreUnLockReq);
    }
}
