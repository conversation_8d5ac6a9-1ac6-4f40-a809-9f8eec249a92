
package net.summerfarm.mall.provider.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.saas.provider.AreaInfoProvider;
import net.summerfarm.mall.client.saas.req.AreaInfoReq;
import net.summerfarm.mall.client.saas.resp.AreaInfoResp;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.payments.common.enums.CommonStatusEnum;
import net.summerfarm.mall.service.AreaService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description 获取所有运营编号
 * @date 2023/3/16 10:42:02
 */

@Slf4j
@DubboService
@Component
public class AreaInfoProviderImpl implements AreaInfoProvider {

    @Resource
    private AreaService areaService;

    @Override
    public DubboResponse<List<AreaInfoResp>> getAll(AreaInfoReq areaInfoReq) {
        log.info("AreaInfoProviderImpl[]getAll[]areaInfoReq:{}", JSON.toJSONString(areaInfoReq));
        Integer status;
        //默认获取开放区域信息
        if (Objects.isNull(areaInfoReq) || Objects.isNull(areaInfoReq.getStatus())) {
            status = CommonStatusEnum.SUCCESS.getStatus();
        } else {
            status = areaInfoReq.getStatus();
        }
        List<Area> areas = areaService.getListByStatus(status);
        if (CollectionUtils.isEmpty(areas)) {
            log.info("AreaInfoProviderImpl[]getAll[]areas is Empty");
            return DubboResponse.getOK();
        }
        List<AreaInfoResp> areaInfoResps = new ArrayList<>(areas.size());
        areas.stream().forEach(e -> {
            AreaInfoResp areaInfoResp = new AreaInfoResp();
            areaInfoResp.setAreaNo(e.getAreaNo());
            areaInfoResps.add(areaInfoResp);
        });
        log.info("AreaInfoProviderImpl[]getAll[]areaInfoResps:{}", JSON.toJSONString(areaInfoResps));
        return DubboResponse.getOK(areaInfoResps);
    }
}
