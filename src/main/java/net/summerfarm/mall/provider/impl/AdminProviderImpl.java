package net.summerfarm.mall.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.AdminProvider;
import net.summerfarm.mall.client.req.AdminReq;
import net.summerfarm.mall.client.resp.AdminResp;
import net.summerfarm.mall.mapper.AdminMapper;
import net.summerfarm.mall.model.domain.Admin;
import net.summerfarm.mall.provider.converter.admin.Admin2AdminDingResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@Component
public class AdminProviderImpl implements AdminProvider {

    @Resource
    AdminMapper adminMapper;

    @Override
    public DubboResponse<AdminResp> getAdminDetail(AdminReq adminReq) {
        AdminResp adminResp = new AdminResp();
        Admin admin =  adminMapper.selectAdminDetails(Long.valueOf(adminReq.getAdminId()));
        if (!ObjectUtils.isEmpty(admin)){
            adminResp = Admin2AdminDingResp.domain2VO(admin);
        }
        return DubboResponse.getOK(adminResp);
    }
}
