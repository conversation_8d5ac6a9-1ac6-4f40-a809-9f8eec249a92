package net.summerfarm.mall.provider.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.enums.SaleStockChangeTypeEnum;
import net.summerfarm.mall.client.provider.DeliveryPlanProvider;
import net.summerfarm.mall.client.req.*;
import net.summerfarm.mall.client.resp.DeliveryPlanQueryResp;
import net.summerfarm.mall.client.resp.OrderCompareCommonQueryResp;
import net.summerfarm.mall.client.resp.RecentDeliveryPlanResp;
import net.summerfarm.mall.enums.DistOrderSourceEnum;
import net.summerfarm.mall.enums.OperatorType;
import net.summerfarm.mall.enums.OrderStatusEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.delivery.DeliveryPlanQueryDTO;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.model.vo.OrderVO;
import net.summerfarm.mall.model.vo.deliveryplan.DeliveryPlanOperationVO;
import net.summerfarm.mall.model.vo.deliveryplan.DeliveryPlanRemoveVO;
import net.summerfarm.mall.provider.converter.deliveryPlan.DeliveryPlan2Resp;
import net.summerfarm.mall.service.DeliverPlanRemarkSnapshotService;
import net.summerfarm.mall.service.DeliveryPlanService;
import net.summerfarm.mall.service.OrderService;
import net.summerfarm.mall.service.QuantityChangeRecordService;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.WncDeliveryFenceQueryFacade;
import net.summerfarm.mall.service.facade.dto.AreaStoreUnLockReq;
import net.summerfarm.mall.service.facade.dto.FenceCloseTimeReq;
import net.summerfarm.mall.service.facade.dto.OrderUnLockSkuDetailReqDTO;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@Component
public class DeliveryPlanProviderImpl implements DeliveryPlanProvider {

    @Resource
    DeliveryPlanMapper deliveryPlanMapper;

    @Resource
    private OrderItemMapper orderItemMapper;

    @Resource
    private AdminMapper adminMapper;

    @Resource
    private MerchantSubAccountMapper merchantSubAccountMapper;
    @Resource
    private DeliverPlanRemarkSnapshotService deliverPlanRemarkSnapshotService;

    @Resource
    private OrdersMapper ordersMapper;

    @Resource
    private OrderService orderService;

    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;

    @Lazy
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;

    @Resource
    private DeliveryPlanService deliveryPlanService;

    @Resource
    private MerchantMapper merchantMapper;

    @Resource
    private WmsAreaStoreFacade wmsAreaStoreFacade;

    @Resource
    private WncDeliveryFenceQueryFacade wncDeliveryFenceQueryFacade;

    @Override
    public DubboResponse<DeliveryPlanQueryResp> getDeliveryPlan(DeliveryPlayQueryReq deliveryPlayQueryReq) {
        log.info("获取配送计划基本信息根据ID:{}", JSON.toJSONString(deliveryPlayQueryReq));
        DeliveryPlan deliveryPlan =  deliveryPlanMapper.selectById(deliveryPlayQueryReq.getId());
        DeliveryPlanQueryResp deliveryPlanQueryResp = new DeliveryPlanQueryResp();
        if (!ObjectUtils.isEmpty(deliveryPlan)){
            deliveryPlanQueryResp = DeliveryPlan2Resp.domain2VO(deliveryPlan);
            //获取备注信息
            DeliveryPlanRemarkSnapshot snapshot = deliverPlanRemarkSnapshotService.getByDeliveryPlanId(deliveryPlan.getId());
            if (snapshot != null) {
                deliveryPlanQueryResp.setAddressRemark(snapshot.getAddressRemark());
            }
        }
        this.deliveryPlanDataPostProcessor(Collections.singletonList(deliveryPlanQueryResp));
        log.info("返回配送计划基本信息根据ID:{}", JSON.toJSONString(deliveryPlanQueryResp));
        return DubboResponse.getOK(deliveryPlanQueryResp);
    }

    @Override
    public DubboResponse<List<DeliveryPlanQueryResp>> getDeliveryPlanListByOrderNo(DeliveryPlayQueryReq deliveryPlayQueryReq) {
        log.info("获取配送计划基本信息根据订单号:{}", JSON.toJSONString(deliveryPlayQueryReq));
        List<DeliveryPlanVO> deliveryPlanList =  deliveryPlanMapper.selectDeliveryPlanOnlyByOrderNo(deliveryPlayQueryReq.getOrderNo());
        List<DeliveryPlanQueryResp> deliveryPlanQueryRespList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(deliveryPlanList)){
            deliveryPlanQueryRespList = deliveryPlanList.stream().map(DeliveryPlan2Resp::domainDTO2VO).collect(Collectors.toList());
        }
        this.deliveryPlanDataPostProcessor(deliveryPlanQueryRespList);
        log.info("返回配送计划基本信息根据订单号:{}", JSON.toJSONString(deliveryPlanQueryRespList));
        return DubboResponse.getOK(deliveryPlanQueryRespList);
    }

    @Override
    public DubboResponse<PageInfo<OrderCompareCommonQueryResp>> listNormalOrderDataByDeliveryDate(DeliveryDateQueryReq deliveryDateQueryReq) {
        log.info("根据配送时间查询普通订单（包括省心送）:{}", JSON.toJSONString(deliveryDateQueryReq));
        List<Integer> statusList = Arrays.asList(OrderStatusEnum.DELIVERING.getId(), OrderStatusEnum.DRAWBACK.getId());
        int totalCount = deliveryPlanMapper.countDataByDeliveryDateAndStatusList(deliveryDateQueryReq.getDeliveryTime(), statusList);
        if (totalCount <= 0){
            return DubboResponse.getOK(PageInfo.emptyPageInfo());
        }
        PageInfo<OrderCompareCommonQueryResp> result = new PageInfo<>();
        result.setSize(totalCount);
        // ofc会先查询总数，pageSize参数会传0，这里直接返回可以少查询一次数据库
        if (deliveryDateQueryReq.getPageSize() <= 0){
            return DubboResponse.getOK(result);
        }
        List<DeliveryPlanVO> deliveryPlanList =  deliveryPlanMapper.listDataByDeliveryDateAndStatusList(
                deliveryDateQueryReq.getDeliveryTime(), statusList,
                (deliveryDateQueryReq.getPageIndex() - 1) * deliveryDateQueryReq.getPageSize(), deliveryDateQueryReq.getPageSize());
        if (CollectionUtils.isEmpty(deliveryPlanList)){
            return DubboResponse.getOK(result);
        }
        List<OrderItem> orderItemList = orderItemMapper.listDataByDeliveryDateAndStatusList(deliveryPlanList.stream().map(DeliveryPlanVO::getOrderNo).collect(Collectors.toList()));
        Map<String, List<OrderItem>> orderItemMap = orderItemList.stream().collect(Collectors.groupingBy(OrderItem::getOrderNo));
        result = new PageInfo<>(DeliveryPlan2Resp.buildNormalOrderDataList(deliveryPlanList, orderItemMap));
        return DubboResponse.getOK(result);
    }

    @Override
    public DubboResponse<Boolean> orderSelfPickup(OrderSelfPickupReq orderSelfPickupReq) {
        if (StringUtils.isEmpty(orderSelfPickupReq.getOrderNo())){
            return DubboResponse.getDefaultError("订单号不能为空！");
        }
        if (ObjectUtils.isEmpty(orderSelfPickupReq.getType())){
            return DubboResponse.getDefaultError("订单类型不能为空！");
        }
        deliveryPlanService.orderSelfPickup(orderSelfPickupReq.getType(),orderSelfPickupReq.getOrderNo(),orderSelfPickupReq.getContactId(),orderSelfPickupReq.getDeliveryTime());
        return DubboResponse.getOK(true);
    }

    /**
     * 交货计划数据处理器
     *
     * @param deliveryPlanQueryRespList 交货计划查询resp列表
     */
    private void deliveryPlanDataPostProcessor(List<DeliveryPlanQueryResp> deliveryPlanQueryRespList){
        if (CollectionUtils.isEmpty(deliveryPlanQueryRespList)){
            return;
        }
        Map<Integer, Admin> adminMap = new HashMap<>();
        Map<Long, MerchantSubAccount> accountMap = new HashMap<>();
        List<Integer> adminIdList = deliveryPlanQueryRespList.stream().map(DeliveryPlanQueryResp::getAdminId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adminIdList)){
            List<Admin> admins = adminMapper.listAdminByIds(adminIdList);
            adminMap = admins.stream().collect(Collectors.toMap(Admin::getAdminId, o -> o));
        }
        List<Long> accountIdList = deliveryPlanQueryRespList.stream().map(DeliveryPlanQueryResp::getAccountId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accountIdList)){
            List<MerchantSubAccount> merchantSubAccounts = merchantSubAccountMapper.listContactByAccountIds(accountIdList);
            accountMap = merchantSubAccounts.stream().collect(Collectors.toMap(MerchantSubAccount::getAccountId, o -> o));
        }
        for (DeliveryPlanQueryResp deliveryPlanQueryResp : deliveryPlanQueryRespList) {
            if (null != deliveryPlanQueryResp.getAdminId()) {
                Admin admin = adminMap.get(deliveryPlanQueryResp.getAdminId());
                if (null != admin){
                    deliveryPlanQueryResp.setAdminRealName(admin.getRealname());
                }
            }
            if (null != deliveryPlanQueryResp.getAccountId()){
                MerchantSubAccount merchantSubAccount = accountMap.get(deliveryPlanQueryResp.getAccountId());
                if (null != merchantSubAccount){
                    deliveryPlanQueryResp.setAccountContact(merchantSubAccount.getContact());
                }
            }
        }
    }

    @Override
    public DubboResponse<Boolean> deleteDeliveryPlan(@Valid DeliveryPlanDeleteReq deliveryPlanDeleteReq) {
        log.info("DeliveryPlanProviderImpl[]deleteDeliveryPlan[]deliveryPlanDeleteReq:{}", JSON.toJSONString(deliveryPlanDeleteReq));
        List<DeliveryPlan> deliveryPlans = deliveryPlanMapper.selectByDeliverTime(deliveryPlanDeleteReq.getOrderNo(), deliveryPlanDeleteReq.getDeliveryTime(), deliveryPlanDeleteReq.getContactId().longValue());
        if (CollectionUtils.isEmpty(deliveryPlans)) {
            throw new ProviderException("当前配送计划不存在！");
        }
        DeliveryPlan plan = deliveryPlans.get(0);
        OrderVO orders = ordersMapper.selectByOrderyNo(plan.getOrderNo());
        if (Objects.isNull(orders)) {
            throw new ProviderException("修改记录不存在！");
        }

        FenceCloseTimeReq req = new FenceCloseTimeReq();
        req.setContactId(plan.getContactId());
        req.setSource(DistOrderSourceEnum.getSourceEnumByOrderType(orders.getType()));
        LocalTime closeTime = wncDeliveryFenceQueryFacade.queryCloseTime(req);
        //LocalTime closeTime = warehouseLogisticsService.selectCloseTime(plan.getOrderStoreNo());
        if (null != closeTime){
            if (LocalTime.now().isAfter(closeTime.minusMinutes(2))) {
                throw new DefaultServiceException("配送计划需在当天"+closeTime.minusMinutes(2)+"前修改！");
            }
        }

        if (!plan.getDeliveryTime().isAfter(LocalDate.now())) {
            throw new ProviderException("配送时间不能为当前以及之前！");
        }

        if (orders.getType() == 1) {
            //已发起售后的省心送计划不可取消
            int dpCount = afterSaleOrderMapper.countBydeliveryPlanId(plan.getId());
            if (dpCount >= 1){
                throw new ProviderException("该配送计划已发起售后，不可取消！");
            }

            if (plan.getStatus().intValue() == OrderStatusEnum.DELIVERING.getId()) {
                OrderItem orderItem = orderItemMapper.selectOrderItem(plan.getOrderNo()).get(0);
                UserBase user = UserInfoHolder.getUser();
                log.info("当前操作人是 >>> {}", JSON.toJSONString(user));

                //释放库存
                AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
                areaStoreUnLockReq.setContactId(plan.getContactId());
                areaStoreUnLockReq.setOrderType(SaleStockChangeTypeEnum.TIMING_PLAN_DEL.getTypeName());
                areaStoreUnLockReq.setOrderNo(plan.getOrderNo());
                areaStoreUnLockReq.setOrderSubNo(String.valueOf(plan.getId()));
                areaStoreUnLockReq.setIdempotentNo(plan.getOrderNo() + plan.getId());
                areaStoreUnLockReq.setOperatorNo(plan.getOrderNo());
                areaStoreUnLockReq.setMerchantId(orders.getmId());
                areaStoreUnLockReq.setSource(SourceEnum.XM_MALL_TIMING.getValue());
                areaStoreUnLockReq.setOperatorName(Objects.isNull(user) ? "系统默认" : user.getNickname());
                OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
                orderUnLockSkuDetailReqDTO.setSkuCode(orderItem.getSku());
                orderUnLockSkuDetailReqDTO.setReleaseQuantity(plan.getQuantity());
                areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(Collections.singletonList(orderUnLockSkuDetailReqDTO));
                wmsAreaStoreFacade.storeUnLock(areaStoreUnLockReq);

                /*Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
                orderService.updateStock(orderItem.getSku(), -plan.getQuantity(), TIMING_PLAN_DEL,plan.getOrderNo(), recordMap,plan.getContactId());
                log.info("ofc调用关单dubbo接口省心送删除插入库存操作记录:{}", JSON.toJSONString(recordMap));
                quantityChangeRecordService.insert(recordMap);*/
            }
            plan.setOrderNo(plan.getOrderNo());
            plan.setUpdateTime(LocalDateTime.now());
            plan.setStatus(11);
            deliveryPlanMapper.updateById(plan);
        } else {
            throw new ProviderException("不正确的的订单类型！");
        }
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Transactional(rollbackOn = Exception.class)
    @Override
    public DubboResponse<Boolean> updateDeliveryPlanStore(DeliveryPlanUpdateStoreReq req) {
        List<DeliveryPlan> deliveryPlans = deliveryPlanMapper.selectByDeliverTime(req.getOrderNo(), req.getDeliveryTime(), req.getContactId());

        List<Integer> needUpdateStatusList = Arrays.asList(OrderStatusEnum.WAIT_DELIVERY.getId(), OrderStatusEnum.DELIVERING.getId());
        // 过滤出 状态为 2待配送，3待收货 的记录，才需要更新城配仓号
        deliveryPlans = deliveryPlans.stream().filter(e -> needUpdateStatusList.contains(e.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deliveryPlans) || deliveryPlans.size() != 1) {
            return DubboResponse.getDefaultError("配送计划不存在！");
        }

        for (DeliveryPlan plan : deliveryPlans) {
            DeliveryPlan updatePlan = new DeliveryPlan();
            updatePlan.setId(plan.getId());
            updatePlan.setOrderStoreNo(req.getNewStoreNo());
            deliveryPlanMapper.updateById(updatePlan);
        }

        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    public DubboResponse<List<DeliveryPlanQueryResp>> queryDeliveryPlan(DeliveryPlanQueryReq req) {
        if (req.getMinDeliveryTime() == null || req.getStatus() == null || req.getCity() == null){
            return DubboResponse.getDefaultError("参数异常");
        }

        List<DeliveryPlanQueryDTO> deliveryPlanList = deliveryPlanMapper.selectListByMinDeliveryTime(req);
        if (deliveryPlanList.isEmpty()) {
            return DubboResponse.getOK();
        }

        //查询merchant买家信息
        List<Long> mIdList = deliveryPlanList.stream().map(DeliveryPlanQueryDTO::getMId).collect(Collectors.toList());
        List<Merchant> merchantList = merchantMapper.selectMerchantList(mIdList);
        Map<Long, Merchant> merchantMap = new HashMap<>();
        merchantList.forEach(el -> merchantMap.put(el.getmId(), el));

        //构建rpc相应数据
        List<DeliveryPlanQueryResp> respList = deliveryPlanList.stream().map(el -> {
            DeliveryPlanQueryResp resp = new DeliveryPlanQueryResp();
            resp.setId(el.getId());
            resp.setOrderNo(el.getOrderNo());
            resp.setDeliveryTime(el.getDeliveryTime());
            resp.setContactId(el.getContactId());
            resp.setOrderStoreNo(el.getOrderStoreNo());
            resp.setStatus(el.getStatus());
            resp.setQuantity(el.getQuantity());
            resp.setInterceptFlag(el.getInterceptFlag());
            resp.setMId(el.getMId());
            resp.setAddTime(el.getAddTime());

            Merchant merchant = merchantMap.get(el.getMId());
            if (merchant != null) {
                resp.setMName(merchant.getMname());
            }
            return resp;
        }).collect(Collectors.toList());

        return DubboResponse.getOK(respList);
    }

    @Override
    public DubboResponse<List<RecentDeliveryPlanResp>> queryRecentTwoDeliveryPlan(DeliveryPlayQueryReq req) {
        List<RecentDeliveryPlanResp> deliveryPlanRes = deliveryPlanMapper.queryRecentTwoDeliveryPlan(req.getMId(), req.getOrderTime());
        return DubboResponse.getOK(deliveryPlanRes);
    }

    @Override
    public DubboResponse<Boolean> insertDeliveryPlan(@Valid DeliveryPlanInsertReq deliveryPlanInsertReq) {
        DeliveryPlanOperationVO deliveryPlanOperationVO = new DeliveryPlanOperationVO();
        DeliveryPlanVO insert = new DeliveryPlanVO();
        insert.setDeliveryTime(deliveryPlanInsertReq.getDeliveryTime());
        insert.setQuantity(deliveryPlanInsertReq.getQuantity());
        insert.setContactId(deliveryPlanInsertReq.getContactId());
        insert.setOrderNo(deliveryPlanInsertReq.getOrderNo());
        insert.setManage(Boolean.TRUE);
        insert.setAdminId(Objects.isNull(UserInfoHolder.getUser()) ? null : UserInfoHolder.getUser().getBizUserId());
        deliveryPlanOperationVO.setOperatorName(Objects.isNull(UserInfoHolder.getUserRealName()) ? "系统默认" : UserInfoHolder.getUserRealName());
        deliveryPlanOperationVO.setOperatorType(OperatorType.EMPLOYEE.getType());
        deliveryPlanOperationVO.setInsertPlanList(Collections.singletonList(insert));
        Orders orders = ordersMapper.selectByOrderNo(deliveryPlanInsertReq.getOrderNo());
        if (orders == null) {
            return DubboResponse.getDefaultError("当前订单信息不存在！");
        }
        CommonResult<List<DeliveryPlanRemoveVO>> listCommonResult = deliveryPlanService.insert(deliveryPlanOperationVO, orders.getmId(),
                null, deliveryPlanInsertReq.getOrderNo());
        if (!CollectionUtils.isEmpty(listCommonResult.getData())) {
            return DubboResponse.getDefaultError(listCommonResult.getData().get(0).getReason());
        }
        return DubboResponse.getOK(Boolean.TRUE);
    }
}
