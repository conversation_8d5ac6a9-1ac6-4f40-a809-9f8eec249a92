package net.summerfarm.mall.provider.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.AfterSaleDeliveryPathProvider;
import net.summerfarm.mall.client.req.AfterSaleDeliveryPathQueryReq;
import net.summerfarm.mall.client.req.AfterSaleDeliveryPathUpdateInfoReq;
import net.summerfarm.mall.client.req.AfterSaleDeliveryPathUpdateStoreReq;
import net.summerfarm.mall.client.resp.AfterSaleDeliveryPathQueryResp;
import net.summerfarm.mall.mapper.AfterSaleDeliveryDetailMapper;
import net.summerfarm.mall.mapper.AfterSaleDeliveryPathMapper;
import net.summerfarm.mall.model.domain.AfterSaleDeliveryDetail;
import net.summerfarm.mall.model.domain.AfterSaleDeliveryPath;
import net.summerfarm.mall.provider.converter.afterSaleDeliveryPath.AfterSaleDeliveryDetail2Resp;
import net.summerfarm.mall.provider.converter.afterSaleDeliveryPath.AfterSaleDeliveryPath2Resp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@Component
public class AfterSaleDeliveryPathProviderImpl implements AfterSaleDeliveryPathProvider {

    @Resource
    AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;

    @Resource
    AfterSaleDeliveryDetailMapper afterSaleDeliveryDetailMapper;

    @Override
    public DubboResponse<AfterSaleDeliveryPathQueryResp> getAfterSaleDeliveryPath(AfterSaleDeliveryPathQueryReq afterSaleDeliveryPathQueryReq) {
        log.info("获取售后配送基本信息:{}", JSON.toJSONString(afterSaleDeliveryPathQueryReq));
        AfterSaleDeliveryPath deliveryPath = new AfterSaleDeliveryPath();
        if (null != afterSaleDeliveryPathQueryReq.getId()) {
            deliveryPath = afterSaleDeliveryPathMapper.selectById(afterSaleDeliveryPathQueryReq.getId());
        }
        if (!StringUtils.isEmpty(afterSaleDeliveryPathQueryReq.getAfterSaleOrderNo())) {
            deliveryPath = afterSaleDeliveryPathMapper.findByAfterSaleOrderNo(afterSaleDeliveryPathQueryReq.getAfterSaleOrderNo());
        }
        if (Objects.isNull(deliveryPath) || Objects.isNull(deliveryPath.getId())) {
            return DubboResponse.getDefaultError("售后配送单信息不存在");
        }
        AfterSaleDeliveryPathQueryResp afterSaleDeliveryPathQueryResp = new AfterSaleDeliveryPathQueryResp();
        List<AfterSaleDeliveryDetail> afterSaleDeliveryDetailList = afterSaleDeliveryDetailMapper.selectDetail(deliveryPath.getId());
        if (!ObjectUtils.isEmpty(deliveryPath)) {
            afterSaleDeliveryPathQueryResp = AfterSaleDeliveryPath2Resp.domain2VO(deliveryPath);
        }
        afterSaleDeliveryPathQueryResp.setDeliveryDetail(afterSaleDeliveryDetailList.stream().map(AfterSaleDeliveryDetail2Resp::domain2VO).collect(Collectors.toList()));
        log.info("返回售后配送基本信息:{}", JSON.toJSONString(afterSaleDeliveryPathQueryResp));
        return DubboResponse.getOK(afterSaleDeliveryPathQueryResp);
    }

    @Transactional(rollbackOn = Exception.class)
    @Override
    public DubboResponse<Boolean> updateAfterSaleDeliveryPathStore(AfterSaleDeliveryPathUpdateStoreReq req) {
        AfterSaleDeliveryPath saleDeliveryPath = afterSaleDeliveryPathMapper.selectByAfterSaleOrderNo(req.getAfterSaleNo());
        if (saleDeliveryPath == null) {
            return DubboResponse.getDefaultError("售后数据不存在");
        }

        AfterSaleDeliveryPath update = new AfterSaleDeliveryPath();
        update.setId(saleDeliveryPath.getId());
        update.setOutStoreNo(req.getNewStoreNo());
        afterSaleDeliveryPathMapper.updateById(update);

        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    public DubboResponse<Boolean> updateAfterSaleDeliveryPathInfo(@Valid AfterSaleDeliveryPathUpdateInfoReq req) {
        AfterSaleDeliveryPath saleDeliveryPath = afterSaleDeliveryPathMapper.selectByAfterSaleOrderNo(req.getAfterSaleNo());
        if (saleDeliveryPath == null) {
            return DubboResponse.getDefaultError("售后数据不存在");
        }
        if (req.getDeliveryTime() == null) {
            return DubboResponse.getDefaultError("售后配送时间不能为空");
        }

        log.info("售后单号:{},即将更新售后配送时间，oldDeliveryTime:{}, oewDeliveryTime:{}", req.getAfterSaleNo(), saleDeliveryPath.getDeliveryTime(), req.getDeliveryTime());
        AfterSaleDeliveryPath update = new AfterSaleDeliveryPath();
        update.setId(saleDeliveryPath.getId());
        update.setDeliveryTime(req.getDeliveryTime());
        update.setUpdateTime(LocalDateTime.now());
        afterSaleDeliveryPathMapper.updateById(update);
        return DubboResponse.getOK(Boolean.TRUE);
    }
}
