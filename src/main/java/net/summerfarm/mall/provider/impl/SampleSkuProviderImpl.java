package net.summerfarm.mall.provider.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.SampleSkuProvider;
import net.summerfarm.mall.client.req.SampleSkuReq;
import net.summerfarm.mall.client.resp.SampleSkuResp;
import net.summerfarm.mall.mapper.InventoryMapper;
import net.summerfarm.mall.mapper.SampleApplyMapper;
import net.summerfarm.mall.mapper.SampleSkuMapper;
import net.summerfarm.mall.model.domain.SampleApply;
import net.summerfarm.mall.model.domain.SampleSku;
import net.summerfarm.mall.provider.converter.sampleSku.SampleSku2Resp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@Component
public class SampleSkuProviderImpl implements SampleSkuProvider {

    @Resource
    SampleSkuMapper sampleSkuMapper;
    @Resource
    SampleApplyMapper sampleApplyMapper;
    @Resource
    InventoryMapper inventoryMapper;

    @Override
    public DubboResponse<List<SampleSkuResp>> getSampleSku(SampleSkuReq sampleSkuReq) {
        log.info("获取样品sku基本信息:{}", JSON.toJSONString(sampleSkuReq));
        List<SampleSku> sampleSkuList =  sampleSkuMapper.selectBySampleId(sampleSkuReq.getSampleId());
        SampleApply sampleApply =  sampleApplyMapper.selectSampleById(sampleSkuReq.getSampleId());
        List<SampleSkuResp> sampleSkuRespList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(sampleSkuList)){
            for (SampleSku sampleSku : sampleSkuList){
                Integer warehouseNo =  inventoryMapper.selectWarehouseNo(sampleSku.getSku(),sampleApply.getAreaNo());
                sampleSku.setWarehouseNo(warehouseNo);
            }
            sampleSkuRespList = sampleSkuList.stream().map(SampleSku2Resp::domain2VO).collect(Collectors.toList());
        }
        log.info("返回样品sku基本信息:{}", JSON.toJSONString(sampleSkuRespList));
        return DubboResponse.getOK(sampleSkuRespList);
    }
}
