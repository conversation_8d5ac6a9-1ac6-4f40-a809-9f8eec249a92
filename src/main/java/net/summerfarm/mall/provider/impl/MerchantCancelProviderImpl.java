package net.summerfarm.mall.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.MerchantCancelProvider;
import net.summerfarm.mall.client.req.MerchantCancelInputReq;
import net.summerfarm.mall.client.resp.MerchantCancelResp;
import net.summerfarm.mall.model.input.MerchantCancelReq;
import net.summerfarm.mall.model.vo.MerchantCancelVO;
import net.summerfarm.mall.service.MerchantCancelService;
import net.summerfarm.mall.service.MerchantService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description 门店注销
 * @date 2023/4/23 16:32:01
 */
@Slf4j
@DubboService
@Component
public class MerchantCancelProviderImpl implements MerchantCancelProvider {

    @Resource
    private MerchantCancelService merchantCancelService;
    @Resource
    MerchantService merchantService;

    @Override
    public DubboResponse<MerchantCancelResp> insert(MerchantCancelInputReq merchantCancelInputReq) {
        if (Objects.isNull(merchantCancelInputReq)) {
            return DubboResponse.getDefaultError("入参不能为空！");
        }
        MerchantCancelReq merchantCancelReq = new MerchantCancelReq();
        merchantCancelReq.setMId(merchantCancelInputReq.getMId());
        merchantCancelReq.setCertificate(merchantCancelInputReq.getCertificate());
        merchantCancelReq.setRemake(merchantCancelInputReq.getRemake());
        merchantCancelReq.setCreator(merchantCancelInputReq.getCreator());
        merchantCancelReq.setResource(merchantCancelInputReq.getResource());
        merchantCancelReq.setMName(merchantCancelInputReq.getMName());
        MerchantCancelVO merchantCancelVO;
        try {
            merchantCancelVO = merchantCancelService.insert(merchantCancelReq);
        } catch (BizException e) {
            return DubboResponse.getError(e.getErrorCode().getCode(), e.getMessage());
        } catch (Exception e) {
            return DubboResponse.getDefaultError(e.getMessage());
        }
        if (Objects.isNull(merchantCancelVO)) {
            return DubboResponse.getDefaultError("新增注销申请异常！");
        }
        MerchantCancelResp merchantCancelResp = new MerchantCancelResp();
        merchantCancelResp.setId(merchantCancelVO.getId());
        merchantCancelResp.setCause(merchantCancelVO.getCause());
        return DubboResponse.getOK(merchantCancelResp);
    }

    @Override
    public DubboResponse<Boolean> updateStatus(MerchantCancelInputReq merchantCancelInputReq) {
        if (Objects.isNull(merchantCancelInputReq)) {
            return DubboResponse.getOK(Boolean.FALSE);
        }
        MerchantCancelReq merchantCancelReq = new MerchantCancelReq();
        merchantCancelReq.setId(merchantCancelInputReq.getId());
        merchantCancelReq.setStatus(merchantCancelInputReq.getStatus());
        merchantCancelReq.setUpdater(merchantCancelInputReq.getUpdater());
        Boolean updateStatus;
        try {
            updateStatus = merchantCancelService.updateStatus(merchantCancelReq);
        } catch (BizException e) {
            return DubboResponse.getError(e.getErrorCode().getCode(), e.getMessage());
        } catch (Exception e) {
            return DubboResponse.getDefaultError(e.getMessage());
        }
        return DubboResponse.getOK(updateStatus);
    }

    @Override
    public DubboResponse<List<String>> check(MerchantCancelInputReq merchantCancelInputReq) {
        if (Objects.isNull(merchantCancelInputReq)) {
            return DubboResponse.getOK(Collections.emptyList());
        }
        MerchantCancelReq merchantCancelReq = new MerchantCancelReq();
        merchantCancelReq.setMId(merchantCancelInputReq.getMId());
        List<String> check;
        try {
            check = merchantCancelService.check(merchantCancelReq);
        } catch (BizException e) {
            return DubboResponse.getError(e.getErrorCode().getCode(), e.getMessage());
        } catch (Exception e) {
            return DubboResponse.getDefaultError(e.getMessage());
        }
        return DubboResponse.getOK(check);
    }

    @Override
    public DubboResponse<Boolean> promptlyCancel(MerchantCancelInputReq merchantCancelInputReq) {
        if (Objects.isNull(merchantCancelInputReq)) {
            return DubboResponse.getOK(Boolean.FALSE);
        }
        MerchantCancelReq merchantCancelReq = new MerchantCancelReq();
        merchantCancelReq.setId(merchantCancelInputReq.getId());
        Boolean cancel;
        try {
            cancel = merchantCancelService.cancel(merchantCancelReq);
        } catch (BizException e) {
            return DubboResponse.getError(e.getErrorCode().getCode(), e.getMessage());
        } catch (Exception e) {
            return DubboResponse.getDefaultError(e.getMessage());
        }
        return DubboResponse.getOK(cancel);
    }

    @Override
    public DubboResponse<Boolean> transferCancel(MerchantCancelInputReq merchantCancelInputReq) {
        if (Objects.isNull(merchantCancelInputReq)) {
            return DubboResponse.getOK(Boolean.FALSE);
        }
        if (Objects.isNull(merchantCancelInputReq.getMId())) {
            return DubboResponse.getOK(Boolean.FALSE);
        }
        try {
            return DubboResponse.getOK(merchantService.cancelMerchant(merchantCancelInputReq.getMId(), true));
        } catch (BizException e) {
            return DubboResponse.getError(e.getErrorCode().getCode(), e.getMessage());
        } catch (Exception e) {
            return DubboResponse.getDefaultError(e.getMessage());
        }
    }
}
