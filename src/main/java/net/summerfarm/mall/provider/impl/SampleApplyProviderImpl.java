package net.summerfarm.mall.provider.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.SaleStockChangeTypeEnum;
import net.summerfarm.mall.client.provider.SampleApplyProvider;
import net.summerfarm.mall.client.req.*;
import net.summerfarm.mall.client.resp.OrderCompareCommonQueryResp;
import net.summerfarm.mall.client.resp.SampleApplyResp;
import net.summerfarm.mall.enums.SampleApplyStatus;
import net.summerfarm.mall.enums.SampleApplyStatusEnum;
import net.summerfarm.mall.mapper.AdminMapper;
import net.summerfarm.mall.mapper.SampleApplyMapper;
import net.summerfarm.mall.mapper.SampleApplyReviewMapper;
import net.summerfarm.mall.mapper.SampleSkuMapper;
import net.summerfarm.mall.model.bo.admin.AdminBriefInfoBO;
import net.summerfarm.mall.model.domain.DeliveryPlanRemarkSnapshot;
import net.summerfarm.mall.model.domain.SampleApply;
import net.summerfarm.mall.model.domain.SampleApplyReview;
import net.summerfarm.mall.model.domain.SampleSku;
import net.summerfarm.mall.provider.converter.deliveryPlan.DeliveryPlan2Resp;
import net.summerfarm.mall.provider.converter.sampleApply.SampleApply2Resp;
import net.summerfarm.mall.service.DeliverPlanRemarkSnapshotService;
import net.summerfarm.mall.service.SampleApplyService;
import net.summerfarm.mall.service.facade.OfcSampleApplyQueryFacade;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.dto.AreaStoreUnLockReq;
import net.summerfarm.mall.service.facade.dto.OrderUnLockSkuDetailReqDTO;
import net.summerfarm.util.DateUtil;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@Component
public class SampleApplyProviderImpl implements SampleApplyProvider {

    @Resource
    SampleApplyMapper sampleApplyMapper;

    @Resource
    private SampleSkuMapper sampleSkuMapper;

    @Resource
    private AdminMapper adminMapper;

    @Resource
    DeliverPlanRemarkSnapshotService deliverPlanRemarkSnapshotService;

    @Resource
    private SampleApplyService sampleApplyService;

    @Resource
    private OfcSampleApplyQueryFacade ofcSampleApplyQueryFacade;

    @Resource
    private WmsAreaStoreFacade wmsAreaStoreFacade;

    @Resource
    private SampleApplyReviewMapper sampleApplyReviewMapper;

    @Override
    public DubboResponse<SampleApplyResp> getSampleApply(SampleApplyReq sampleApplyReq) {

        log.info("获取样品基本信息:{}", JSON.toJSONString(sampleApplyReq));
        SampleApply sampleApply =  sampleApplyMapper.selectSampleById(sampleApplyReq.getSampleId());
        SampleApplyResp sampleApplyResp = new SampleApplyResp();
        if (!ObjectUtils.isEmpty(sampleApply)){
            sampleApplyResp = SampleApply2Resp.domain2VO(sampleApply);
            // 补充大客户信息
            AdminBriefInfoBO adminBriefInfoBO = adminMapper.selectAdminBasicInfoByMId(sampleApply.getMId());
            if (null != adminBriefInfoBO){
                sampleApplyResp.setAdminId(adminBriefInfoBO.getAdminId());
                sampleApplyResp.setNameRemakes(adminBriefInfoBO.getNameRemakes());
                sampleApplyResp.setSkuSorting(adminBriefInfoBO.getSkuSorting());
            }
            DeliveryPlanRemarkSnapshot sampleAppySnapshot = deliverPlanRemarkSnapshotService.getSampleAppySnapshot(sampleApplyResp.getSampleId());
            if (sampleAppySnapshot!=null){
                sampleApplyResp.setAddressRemark(sampleAppySnapshot.getAddressRemark());
            }
            SampleApplyReview sampleApplyReview = sampleApplyReviewMapper.isReview(sampleApplyResp.getSampleId(), null);
            if (null != sampleApplyReview){
                sampleApplyResp.setAuditTime(DateUtil.toLocalDateTime(sampleApplyReview.getAuditTime()));
            }
        }
        log.info("返回样品基本信息:{}", JSON.toJSONString(sampleApplyResp));
        return DubboResponse.getOK(sampleApplyResp);
    }

    @Override
    public DubboResponse<PageInfo<OrderCompareCommonQueryResp>> listSampleApplyOrderByDeliveryDate(DeliveryDateQueryReq deliveryDateQueryReq) {
        log.info("根据配送时间查询样品单请求参数:{}", JSON.toJSONString(deliveryDateQueryReq));
        List<Integer> statusList = Arrays.asList(SampleApplyStatus.PASS.getValue(), SampleApplyStatus.CANCEL.getValue());
        int totalCount = sampleApplyMapper.countDataByDeliveryDateAndStatusList(deliveryDateQueryReq.getDeliveryTime(), statusList);
        if (totalCount <= 0){
            return DubboResponse.getOK(PageInfo.emptyPageInfo());
        }
        PageInfo<OrderCompareCommonQueryResp> result = new PageInfo<>();
        result.setSize(totalCount);
        // ofc会先查询总数，pageSize参数会传0，这里直接返回可以少查询一次数据库
        if (deliveryDateQueryReq.getPageSize() <= 0){
            return DubboResponse.getOK(result);
        }
        List<SampleApply> sampleApplyList = sampleApplyMapper.listDataByDeliveryDateAndStatusList(
                deliveryDateQueryReq.getDeliveryTime(), statusList,
                (deliveryDateQueryReq.getPageIndex() - 1) * deliveryDateQueryReq.getPageSize(), deliveryDateQueryReq.getPageSize());
        if (CollectionUtils.isEmpty(sampleApplyList)){
            return DubboResponse.getOK(result);
        }
        List<SampleSku> sampleSkuList = sampleSkuMapper.selectBySampleIds(sampleApplyList.stream().map(SampleApply::getSampleId).collect(Collectors.toList()));
        Map<Integer, List<SampleSku>> sampleSkuMap = sampleSkuList.stream().collect(Collectors.groupingBy(SampleSku::getSampleId));
        result = new PageInfo<>(DeliveryPlan2Resp.buildSampleOrderDataList(sampleApplyList, sampleSkuMap));
        log.info("根据配送时间查询样品单请求结果:{}", JSON.toJSONString(result));
        return DubboResponse.getOK(result);
    }

    @Override
    public DubboResponse<Boolean> sampleApplySelfPickup(SampleApplySelfPickupReq sampleApplySelfPickupReq) {
        log.info("样品单自提:{}", JSON.toJSONString(sampleApplySelfPickupReq));
        if (ObjectUtils.isEmpty(sampleApplySelfPickupReq.getSampleId())){
            return DubboResponse.getDefaultError("样品单ID不能为空！");
        }
        sampleApplyService.sampleApplySelfPickup(sampleApplySelfPickupReq.getSampleId());
        return DubboResponse.getOK(true);
    }

    @Transactional(rollbackOn = Exception.class)
    @Override
    public DubboResponse<Boolean> updateSampleApplyStore(SampleApplyUpdateStoreReq req) {
        SampleApply sampleApply = sampleApplyMapper.selectSampleById(req.getSampleId());
        if (sampleApply == null){
            return DubboResponse.getDefaultError("样品数据不存在");
        }

        SampleApply update = new SampleApply();
        update.setSampleId(req.getSampleId());
        update.setStoreNo(req.getNewStoreNo());
        sampleApplyMapper.updateById(update);

        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public DubboResponse<Boolean> cancelSampleApply(@Valid SampleApplyCancelReq sampleApplyCancelReq) {
        // 参数校验
        Integer sampleId = sampleApplyCancelReq.getSampleId();
        SampleApply sampleApply = sampleApplyMapper.selectSampleById(sampleId);
        List<SampleSku> sampleSkuList = sampleSkuMapper.selectBySampleId(sampleId);
        if (CollectionUtils.isEmpty(sampleSkuList) || Objects.isNull(sampleApply)) {
            return DubboResponse.getDefaultError("参数有误！");
        }

        // 过配送日期前一天20：15无法取消
        if (LocalDate.now().isAfter(sampleApply.getDeliveryTime().minusDays(NumberUtils.INTEGER_ONE))
                || (LocalDate.now()).equals(sampleApply.getDeliveryTime().minusDays(NumberUtils.INTEGER_ONE))
                && !LocalTime.now().isBefore(LocalTime.of(20, 15, 00))) {
            return DubboResponse.getDefaultError("样品出库任务已生成无法取消");
        }

        //自提样品单不支持取消
        if (ofcSampleApplyQueryFacade.hasSampleApplySelfPicked(sampleId)) {
            return DubboResponse.getDefaultError("样品单已经自提，无法取消");
        }

        // 待反馈状态可直接取消
        if (!Objects.equals(SampleApplyStatusEnum.REVIEWING.getId(), sampleApply.getStatus())
                && !Objects.equals(SampleApplyStatusEnum.WAIT_HANDLE.getId(), sampleApply.getStatus())) {
            return DubboResponse.getDefaultError("只有待审核或待反馈状态申请才能取消");
        }

        // 取消样品申请
        SampleApply update = new SampleApply();
        update.setSampleId(sampleId);
        update.setStatus(SampleApplyStatusEnum.CANCEL.getId());
        update.setUpdateTime(new Date());
        sampleApplyMapper.updateById(update);

        //库存释放
        if(Objects.nonNull(sampleApplyReviewMapper.isReview(sampleId, SampleApplyStatusEnum.WAIT_HANDLE.getId()))){
            UserBase user = UserInfoHolder.getUser();
            AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
            areaStoreUnLockReq.setContactId(Long.valueOf(sampleApply.getContactId()));
            areaStoreUnLockReq.setOrderType(SaleStockChangeTypeEnum.DEMO_CANCEL.getTypeName());
            areaStoreUnLockReq.setOrderNo(String.valueOf(sampleId));
            areaStoreUnLockReq.setIdempotentNo(String.valueOf(sampleId));
            areaStoreUnLockReq.setOperatorNo(String.valueOf(sampleId));
            areaStoreUnLockReq.setMerchantId(sampleApply.getMId());
            areaStoreUnLockReq.setSource(SourceEnum.XM_SAMPLE_APPLY.getValue());
            areaStoreUnLockReq.setOperatorName(Objects.isNull(user) ? "系统默认" : user.getNickname());
            List<OrderUnLockSkuDetailReqDTO> orderReleaseSkuDetailReqDTOS = new ArrayList<>(sampleSkuList.size());
            sampleSkuList.stream().forEach(e -> {
                OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
                orderUnLockSkuDetailReqDTO.setSkuCode(e.getSku());
                orderUnLockSkuDetailReqDTO.setReleaseQuantity(e.getAmount());
                orderReleaseSkuDetailReqDTOS.add(orderUnLockSkuDetailReqDTO);
            });
            areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(orderReleaseSkuDetailReqDTOS);
            wmsAreaStoreFacade.storeUnLock(areaStoreUnLockReq);
        }
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    public DubboResponse<Boolean> updateSampleApplyInfo(@Valid SampleApplyUpdateInfoReq req) {
        SampleApply sampleApply = sampleApplyMapper.selectSampleById(req.getSampleId());
        if (sampleApply == null) {
            return DubboResponse.getDefaultError("当前样品单不存在！");
        }
        if (req.getDeliveryTime() == null) {
            return DubboResponse.getDefaultError("售后配送时间不能为空");
        }

        log.info("样品单号:{},即将更新售后配送时间，oldDeliveryTime:{}, oewDeliveryTime:{}", req.getSampleId(), sampleApply.getDeliveryTime(), req.getDeliveryTime());
        SampleApply update = new SampleApply();
        update.setSampleId(req.getSampleId());
        update.setDeliveryTime(req.getDeliveryTime());
        update.setUpdateTime(new Date());
        sampleApplyMapper.updateById(update);
        return DubboResponse.getOK(Boolean.TRUE);
    }
}
