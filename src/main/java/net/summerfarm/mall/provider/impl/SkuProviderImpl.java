package net.summerfarm.mall.provider.impl;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.SkuProvider;
import net.summerfarm.mall.client.req.SkuBaseInfoReq;
import net.summerfarm.mall.client.req.order.SkuSpuOrderQuery;
import net.summerfarm.mall.client.resp.MallPrice4SaasResp;
import net.summerfarm.mall.client.resp.MerchantSkuSpuOrderResp;
import net.summerfarm.mall.client.resp.SkuSpuOrderDetailResp;
import net.summerfarm.mall.enums.MajorDirectEnum;
import net.summerfarm.mall.enums.MajorPriceTypeEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.AreaSku;
import net.summerfarm.mall.model.domain.MajorPrice;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.dto.merchant.MerchantDTO;
import net.summerfarm.mall.model.vo.AreaSkuVO;
import net.summerfarm.mall.model.vo.InventoryVO;
import net.summerfarm.mall.repository.MerchantRepository;
import net.summerfarm.mall.repository.MerchantSubAccountRepository;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: <EMAIL>
 * @create: 2023/3/15
 */
@Slf4j
@DubboService
@Component
public class SkuProviderImpl implements SkuProvider {

    @Resource
    private AreaSkuMapper areaSkuMapper;

    @Resource
    private MajorPriceMapper majorPriceMapper;
    @Resource
    InventoryMapper inventoryMapper;
    @Resource
    OrdersMapper ordersMapper;
    @Resource
    MerchantRepository merchantRepository;

    @Override
    @Deprecated
    public DubboResponse<MallPrice4SaasResp> queryMallPriceInfo4Saas(SkuBaseInfoReq skuBaseInfoReq) {
        String sku = skuBaseInfoReq.getSku();
        Long adminId = skuBaseInfoReq.getAdminId();
        Integer areaNo = skuBaseInfoReq.getAreaNo();
        MallPrice4SaasResp price4SaasResp = new MallPrice4SaasResp();
        price4SaasResp.setSkuCode(sku);
        AreaSkuVO areaSku = areaSkuMapper.selectValidAndOnSale(areaNo, sku);
        if (areaSku == null) {
            log.warn("sku已下架或者已删除,sku:{},areaNo:{}", sku, areaNo);
            return DubboResponse.getOK(price4SaasResp);
        }
        price4SaasResp.setSkuId(areaSku.getSkuId());
        //报价单优先级：账期报价单、现结报价单
        MajorPrice billMajorPrice = majorPriceMapper.selectMajorPrice(adminId.intValue(),
                MajorDirectEnum.PERIOD.getType(), areaNo, sku);
        MajorPrice nowMajorPrice = majorPriceMapper.selectMajorPrice(adminId.intValue(),
                MajorDirectEnum.CASH.getType(), areaNo, sku);
        price4SaasResp.setValidType(1);

        if (billMajorPrice != null && Objects.equals(billMajorPrice.getPriceType(),
                MajorPriceTypeEnum.MALL_PRICE.getCode())) {
            billMajorPrice.setPrice(areaSku.getPrice());
        }

        if (nowMajorPrice != null && Objects.equals(nowMajorPrice.getPriceType(),
                MajorPriceTypeEnum.MALL_PRICE.getCode())) {
            nowMajorPrice.setPrice(areaSku.getPrice());
        }

        boolean isNowMajor = true;
        boolean majorExist = true;
        if (billMajorPrice == null && nowMajorPrice != null) {

        } else if (nowMajorPrice == null && billMajorPrice != null) {
            isNowMajor = false;
        } else if (billMajorPrice != null && nowMajorPrice != null) {
            isNowMajor = nowMajorPrice.getPrice().compareTo(billMajorPrice.getPrice()) > 0;
        } else {
            majorExist = false;
        }

        if (isNowMajor && majorExist) {
            price4SaasResp.setPrice(nowMajorPrice.getPrice());
            price4SaasResp.setValidTime(nowMajorPrice.getValidTime());
            price4SaasResp.setInvalidTime(nowMajorPrice.getInvalidTime());
            return DubboResponse.getOK(price4SaasResp);
        } else if (!isNowMajor && majorExist) {
            price4SaasResp.setPrice(billMajorPrice.getPrice());
            price4SaasResp.setValidTime(billMajorPrice.getValidTime());
            price4SaasResp.setInvalidTime(billMajorPrice.getInvalidTime());
            return DubboResponse.getOK(price4SaasResp);
        }
        price4SaasResp.setValidType(0);
        price4SaasResp.setPrice(areaSku.getPrice());
        // 如果没有创建报价或者都是商城价，使用原价
        return DubboResponse.getOK(price4SaasResp);
    }

    @Override
    public DubboResponse<MerchantSkuSpuOrderResp> querMerchantOrderSku(SkuSpuOrderQuery skuSpuOrderQuery) {
        if (skuSpuOrderQuery.getMId() == null || CollectionUtils.isEmpty(skuSpuOrderQuery.getSkus())) {
            throw new BizException("参数不能为空");
        }
        MerchantDTO merchant = merchantRepository.getMerchantByMid(skuSpuOrderQuery.getMId());
        if (merchant == null){
            throw new BizException("门店不存在");
        }
        MerchantSkuSpuOrderResp resp = new MerchantSkuSpuOrderResp();
        resp.setExit(false);

        //以sku的维度查询所有spu的sku
        List<String> querySkus = skuSpuOrderQuery.getSkus().stream().distinct().collect(Collectors.toList());
        if (skuSpuOrderQuery.getAllSpuSku() != null && skuSpuOrderQuery.getAllSpuSku()) {
            List<String> spuSkus = inventoryMapper.querySkusBySameSpuSku(querySkus);
            if (!CollectionUtils.isEmpty(spuSkus)) {
                querySkus.addAll(spuSkus);
            }
        }
        querySkus = querySkus.stream().distinct().collect(Collectors.toList());
        //查询门店下单的次数
        if (!CollectionUtils.isEmpty(querySkus)) {
            Orders orders = ordersMapper.exitByMidSkuOder(skuSpuOrderQuery.getMId(), querySkus);
            resp.setExit(orders != null);
        }
        if (skuSpuOrderQuery.getQuerySkyPrice() != null && skuSpuOrderQuery.getQuerySkyPrice()) {
            if (!CollectionUtils.isEmpty(querySkus)) {
                List<AreaSku> inventoryVOS = areaSkuMapper.selectAreaSkuByAreaNoAndSkus(merchant.getAreaNo() ,querySkus);
                List<SkuSpuOrderDetailResp> collect = inventoryVOS.stream().map(
                        it -> {
                            SkuSpuOrderDetailResp skuSpuOrderDetailResp = new SkuSpuOrderDetailResp();
                            skuSpuOrderDetailResp.setPrice(it.getPrice());
                            skuSpuOrderDetailResp.setSku(it.getSku());
                            return skuSpuOrderDetailResp;
                        }
                ).collect(Collectors.toList());
                resp.setSkus(collect);
            }
        }

        return DubboResponse.getOK(resp);
    }
}
