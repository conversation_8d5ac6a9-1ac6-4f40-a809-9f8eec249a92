package net.summerfarm.mall.provider.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.DeliveryEvaluationProvider;
import net.summerfarm.mall.client.req.DeliveryEvaluationQueryReq;
import net.summerfarm.mall.client.resp.DeliveryEvaluationQueryResp;
import net.summerfarm.mall.enums.DeliveryEvaluationTypeEnum;
import net.summerfarm.mall.mapper.DeliveryEvaluationMapper;
import net.summerfarm.mall.model.domain.DeliveryEvaluation;
import net.summerfarm.mall.provider.converter.deliveryEvaluation.DeliveryEvaluationConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: zhuyantao
 * @date: 2023/3/15 5:17 下午
 * @description
 */
@Slf4j
@DubboService
@Component
public class DeliveryEvaluationProviderImpl implements DeliveryEvaluationProvider {

    @Resource
    private DeliveryEvaluationMapper deliveryEvaluationMapper;
    @Override
    public DubboResponse<DeliveryEvaluationQueryResp> getDeliveryEvaluation(DeliveryEvaluationQueryReq deliveryEvaluationQueryReq) {
        log.info("获取配送评价:{}", JSON.toJSONString(deliveryEvaluationQueryReq));
        List<DeliveryEvaluation> list =  deliveryEvaluationMapper.selectList(DeliveryEvaluationConverter.Req2Domain(deliveryEvaluationQueryReq));
        if (CollectionUtils.isEmpty(list)){
            return DubboResponse.getOK();
        }
        list =  list.stream().filter(e -> e.getType().equals(DeliveryEvaluationTypeEnum.EVALUATION_DELIVERY.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)){
            return DubboResponse.getOK();
        }
        DeliveryEvaluationQueryResp deliveryEvaluation = DeliveryEvaluationConverter.domain2Resp(list.get(0));
        log.info("返回配送评价:{}", JSON.toJSONString(deliveryEvaluation));
        return DubboResponse.getOK(deliveryEvaluation);
    }
}
