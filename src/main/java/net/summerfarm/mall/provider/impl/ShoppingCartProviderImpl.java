package net.summerfarm.mall.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.ShoppingCartProvider;
import net.summerfarm.mall.client.req.ShoppingCartReq;
import net.summerfarm.mall.client.resp.ShoppingCartResp;
import net.summerfarm.mall.mapper.ShoppingCartMapper;
import net.summerfarm.mall.model.domain.ShoppingCart;
import net.summerfarm.mall.model.vo.ShoppingCartVO;
import net.summerfarm.mall.provider.converter.shoppingCart.ShoppingCartConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description
 * @date 2023/12/8 15:50:40
 */
@Slf4j
@DubboService
@Component
public class ShoppingCartProviderImpl implements ShoppingCartProvider {

    @Resource
    private ShoppingCartMapper shoppingCartMapper;

    @Override
    public DubboResponse<List<ShoppingCartResp>> getShoppingCart(@Valid ShoppingCartReq shoppingCartReq) {
        ShoppingCart shoppingCart = new ShoppingCart();
        shoppingCart.setMId(shoppingCartReq.getMId());
        shoppingCart.setAccountId(shoppingCartReq.getAccountId());
        List<ShoppingCartVO> shoppingCartVOS = shoppingCartMapper.getAll(shoppingCart);
        if (CollectionUtils.isEmpty(shoppingCartVOS)) {
            return DubboResponse.getOK(Collections.emptyList());
        }
        List<ShoppingCartResp> shoppingCartResps = ShoppingCartConverter.shoppingCartVOSToShoppingCartResp(shoppingCartVOS);
        return DubboResponse.getOK(shoppingCartResps);
    }
}
