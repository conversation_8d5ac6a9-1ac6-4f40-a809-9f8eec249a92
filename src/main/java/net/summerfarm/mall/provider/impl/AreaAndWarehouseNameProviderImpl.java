package net.summerfarm.mall.provider.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.AreaAndWarehouseNameProvider;
import net.summerfarm.mall.client.req.AreaAndWarehouseNameListQueryReq;
import net.summerfarm.mall.client.req.AreaAndWarehouseNameQueryReq;
import net.summerfarm.mall.client.req.SkuAndWarehouseNoMappingReq;
import net.summerfarm.mall.client.resp.AreaAndWarehouseNameQueryResp;
import net.summerfarm.mall.client.resp.SkuAndWarehouseNoMappingResp;
import net.summerfarm.mall.mapper.AreaMapper;
import net.summerfarm.mall.mapper.WarehouseInventoryMapper;
import net.summerfarm.mall.mapper.WarehouseLogisticsCenterMapper;
import net.summerfarm.mall.mapper.WarehouseStorageCenterMapper;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.mall.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * 运营区域，城配仓，仓库 查询接口
 */
@Slf4j
@DubboService
@Component
public class AreaAndWarehouseNameProviderImpl implements AreaAndWarehouseNameProvider {

    @Resource
    private AreaMapper areaMapper;

    /**
     * warehouse_logistics_center 城配仓
     */
    @Resource
    private WarehouseLogisticsCenterMapper warehouseLogisticsMapper;

    /**
     * warehouse_storage_center  库存仓
     */
    @Resource
    private WarehouseStorageCenterMapper warehouseStorageMapper;

    @Resource
    private WarehouseInventoryMapper warehouseInventoryMapper;


    @Override
    public DubboResponse<AreaAndWarehouseNameQueryResp> getAreaNameByAreaNo(AreaAndWarehouseNameQueryReq areaAndWarehouseNameQueryReq) {
        log.info("根据运营区域编号查询名称 >>> {}", JSON.toJSONString(areaAndWarehouseNameQueryReq));
        this.validateSingleReq(areaAndWarehouseNameQueryReq);
        Area area = areaMapper.selectByAreaNo(areaAndWarehouseNameQueryReq.getNo());
        if (null == area){
            return DubboResponse.getDefaultError("运营区域不存在");
        }
        AreaAndWarehouseNameQueryResp response = initResp(area.getAreaNo(), area.getAreaName());
        log.info("根据运营区域编号查询名称 >>> {}", JSON.toJSONString(response));
        return DubboResponse.getOK(response);
    }

    @Override
    public DubboResponse<List<AreaAndWarehouseNameQueryResp>> listAreaNameByAreaNos(AreaAndWarehouseNameListQueryReq areaAndWarehouseNameListQueryReq) {
        log.info("根据运营区域编号查询名称 >>> {}", JSON.toJSONString(areaAndWarehouseNameListQueryReq));
        this.validateListReq(areaAndWarehouseNameListQueryReq);
        List<Area> areas = areaMapper.listByAreaNoList(areaAndWarehouseNameListQueryReq.getNoList());
        if (CollectionUtils.isEmpty(areas)){
            return DubboResponse.getDefaultError("没查到对应的运营区域");
        }
        List<AreaAndWarehouseNameQueryResp> respList = new ArrayList<>(areas.size());
        areas.forEach(area -> {
            respList.add(initResp(area.getAreaNo(), area.getAreaName()));
        });
        log.info("根据运营区域编号查询名称 >>> {}", JSON.toJSONString(respList));
        return DubboResponse.getOK(respList);
    }

    @Override
    public DubboResponse<AreaAndWarehouseNameQueryResp> getStoreNameByStoreNo(AreaAndWarehouseNameQueryReq areaAndWarehouseNameQueryReq) {
        log.info("根据城配仓编号查询名称 >>> {}", JSON.toJSONString(areaAndWarehouseNameQueryReq));
        this.validateSingleReq(areaAndWarehouseNameQueryReq);
        List<WarehouseLogisticsCenter> warehouseLogisticsCenters = warehouseLogisticsMapper.listByStoreNoList(Collections.singletonList(areaAndWarehouseNameQueryReq.getNo()));
        if (CollectionUtils.isEmpty(warehouseLogisticsCenters)){
            return DubboResponse.getDefaultError("没查到对应的城配仓");
        }
        WarehouseLogisticsCenter warehouseLogisticsCenter = warehouseLogisticsCenters.get(0);
        AreaAndWarehouseNameQueryResp response = initResp(warehouseLogisticsCenter.getStoreNo(), warehouseLogisticsCenter.getStoreName());
        log.info("根据城配仓编号查询名称 >>> {}", JSON.toJSONString(response));
        return DubboResponse.getOK(response);
    }

    @Override
    public DubboResponse<List<AreaAndWarehouseNameQueryResp>> listStoreNameByStoreNos(AreaAndWarehouseNameListQueryReq areaAndWarehouseNameListQueryReq) {
        log.info("根据城配仓编号集合查询名称 >>> {}", JSON.toJSONString(areaAndWarehouseNameListQueryReq));
        this.validateListReq(areaAndWarehouseNameListQueryReq);
        List<WarehouseLogisticsCenter> warehouseLogisticsCenters = warehouseLogisticsMapper.listByStoreNoList(areaAndWarehouseNameListQueryReq.getNoList());
        if (CollectionUtils.isEmpty(warehouseLogisticsCenters)){
            return DubboResponse.getDefaultError("没查到对应的城配仓");
        }
        List<AreaAndWarehouseNameQueryResp> resultList = new ArrayList<>(warehouseLogisticsCenters.size());
        warehouseLogisticsCenters.forEach(warehouseLogisticsCenter -> {
            resultList.add(initResp(warehouseLogisticsCenter.getStoreNo(), warehouseLogisticsCenter.getStoreName()));
        });
        log.info("根据城配仓编号集合查询名称 >>> {}", JSON.toJSONString(resultList));
        return DubboResponse.getOK(resultList);
    }

    @Override
    public DubboResponse<List<AreaAndWarehouseNameQueryResp>> listWarehouseNameByWarehouseNos(AreaAndWarehouseNameListQueryReq areaAndWarehouseNameListQueryReq) {
        log.info("根据仓库编号集合查询名称 >>> {}", JSON.toJSONString(areaAndWarehouseNameListQueryReq));
        this.validateListReq(areaAndWarehouseNameListQueryReq);
        List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageMapper.listByWarehouseNoList(areaAndWarehouseNameListQueryReq.getNoList());
        if (CollectionUtils.isEmpty(warehouseStorageCenters)){
            return DubboResponse.getDefaultError("没查到对应的仓库");
        }
        List<AreaAndWarehouseNameQueryResp> resultList = new ArrayList<>(warehouseStorageCenters.size());
        warehouseStorageCenters.forEach(warehouseStorageCenter -> {
            resultList.add(initResp(warehouseStorageCenter.getWarehouseNo(), warehouseStorageCenter.getWarehouseName()));
        });
        log.info("根据仓库编号集合查询名称 >>> {}", JSON.toJSONString(resultList));
        return DubboResponse.getOK(resultList);
    }

    @Override
    public DubboResponse<List<SkuAndWarehouseNoMappingResp>> listWarehouseNoBySkuListAndStoreNo(SkuAndWarehouseNoMappingReq request) {
        log.info("通过sku列表和城配仓号查询仓库号 >>> {}", JSON.toJSONString(request));
        if (null == request){
            return DubboResponse.getDefaultError("传参不能为空");
        }
        if (null == request.getStoreNo()){
            return DubboResponse.getDefaultError("传参不能为空");
        }
        List<WarehouseInventoryMapping> warehouseInventoryMappings = warehouseInventoryMapper.listByStoreNoAndSku(request.getStoreNo(),
                request.getSkuIdList());
        List<SkuAndWarehouseNoMappingResp> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(warehouseInventoryMappings)){
            return DubboResponse.getOK(resultList);
        }
        warehouseInventoryMappings.forEach(warehouseInventoryMapping -> {
            SkuAndWarehouseNoMappingResp skuAndWarehouseNoMappingResp = new SkuAndWarehouseNoMappingResp();
            skuAndWarehouseNoMappingResp.setSkuId(warehouseInventoryMapping.getSku());
            skuAndWarehouseNoMappingResp.setStoreNo(warehouseInventoryMapping.getStoreNo());
            skuAndWarehouseNoMappingResp.setWarehouseNo(warehouseInventoryMapping.getWarehouseNo());
            resultList.add(skuAndWarehouseNoMappingResp);
        });
        return DubboResponse.getOK(resultList);

    }


    private void validateSingleReq(AreaAndWarehouseNameQueryReq queryReq){
        if (null == queryReq || null == queryReq.getNo() || 0 >= queryReq.getNo()){
            throw new ProviderException("编号不能为空");
        }
    }

    private void validateListReq(AreaAndWarehouseNameListQueryReq queryReq){
        if (null == queryReq || CollectionUtils.isEmpty(queryReq.getNoList())){
            throw new ProviderException("编号不能为空");
        }
    }

    /**
     * 初始化返回参数
     * @param no 编号
     * @param name 名称
     * @return AreaAndWarehouseNameQueryResp
     */
    private AreaAndWarehouseNameQueryResp initResp(Integer no, String name){
        AreaAndWarehouseNameQueryResp areaAndWarehouseNameQueryResp = new AreaAndWarehouseNameQueryResp();
        areaAndWarehouseNameQueryResp.setNo(no);
        areaAndWarehouseNameQueryResp.setName(name);
        return areaAndWarehouseNameQueryResp;
    }
}
