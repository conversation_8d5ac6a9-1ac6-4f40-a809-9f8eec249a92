package net.summerfarm.mall.provider.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.ProductProvider;
import net.summerfarm.mall.client.req.HelpOrderProductListQueryReq;
import net.summerfarm.mall.client.resp.HelpOrderProductInfoQueryResp;
import net.summerfarm.mall.common.util.PageInfoHelper;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.provider.converter.product.HelpOrderProductConvert;
import net.summerfarm.mall.service.InventoryService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-08-18
 * @description
 */
@Slf4j
@DubboService
@Component
public class ProductProviderImpl implements ProductProvider {

    @Resource
    private InventoryService inventoryService;

    @Override
    public DubboResponse<PageInfo<HelpOrderProductInfoQueryResp>> getHelpOrderProductList(HelpOrderProductListQueryReq req) {
        try {
            PageInfo<ProductInfoVO> productPageInfo = inventoryService.getHelpOrderProductList(req.getPageIndex(),req.getPageSize(),
                    req.getMId(),req.getPdName(),req.getAreaNo(),req.getType(),req.getActivityPrice(),req.getHelpOrderFlag(), req.getOrderType());
            PageInfo<HelpOrderProductInfoQueryResp>  pageInfo = null;
            if (Objects.nonNull(productPageInfo)&& CollectionUtil.isNotEmpty(productPageInfo.getList())){
                List<HelpOrderProductInfoQueryResp>  respList = HelpOrderProductConvert.productPageInfo2Resp(productPageInfo.getList());
                pageInfo = PageInfoHelper.copyPageInfo(productPageInfo,respList);
            }
            return DubboResponse.getOK(pageInfo);
        } catch (Exception e) {
            log.error("getHelpOrderProductList,param:{},e:{}", JSON.toJSON(req),e);
            throw new BizException("查询商品代下单商品列表异常");
        }
    }

}
