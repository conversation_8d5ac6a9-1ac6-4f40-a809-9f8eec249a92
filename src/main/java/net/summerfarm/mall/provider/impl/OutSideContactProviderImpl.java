package net.summerfarm.mall.provider.impl;


import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.OutSideContactProvider;
import net.summerfarm.mall.client.req.OutSideContactReq;
import net.summerfarm.mall.client.resp.OutSideContactResp;
import net.summerfarm.mall.mapper.OutsideContactMapper;
import net.summerfarm.mall.mapper.TmsDeliveryPlanMapper;
import net.summerfarm.mall.model.domain.OutsideContact;
import net.summerfarm.mall.model.domain.TmsDeliveryPlan;
import net.summerfarm.mall.provider.converter.outSideContact.OutSideContact2Resp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@Component
public class OutSideContactProviderImpl implements OutSideContactProvider {

    @Resource
    private TmsDeliveryPlanMapper tmsDeliveryPlanMapper;

    @Resource
    private OutsideContactMapper outsideContactMapper;


    @Override
    public DubboResponse<OutSideContactResp> getOrderDetail(OutSideContactReq outSideContactReq) {
        log.info("获取saas订单鲜沐地址详情参数:{}", JSON.toJSONString(outSideContactReq));
        OutSideContactResp outSideContactResp = new OutSideContactResp();
        TmsDeliveryPlan tmsDeliveryPlan =  tmsDeliveryPlanMapper.selectPlanByOuterNo(outSideContactReq.getCosfoOrderNo());
        if (!ObjectUtils.isEmpty(tmsDeliveryPlan)){
            OutsideContact outsideContact =  outsideContactMapper.selectOutsideContactById(tmsDeliveryPlan.getContactId());
            outSideContactResp = OutSideContact2Resp.dto2VO(outsideContact);
        }
        log.info("获取saas订单鲜沐地址详情返回:{}", JSON.toJSONString(outSideContactResp));
        return DubboResponse.getOK(outSideContactResp);
    }
}
