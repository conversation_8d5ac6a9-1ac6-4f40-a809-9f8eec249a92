package net.summerfarm.mall.provider.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.saas.model.AddressInfoVO;
import net.summerfarm.mall.client.saas.provider.AddressInfoProvider;
import net.summerfarm.mall.client.saas.req.AddressInfoReq;
import net.summerfarm.mall.client.saas.req.AreaNoByAddressReq;
import net.summerfarm.mall.client.saas.resp.AddressInfoResp;
import net.summerfarm.mall.client.saas.resp.AreaNoByAddressResp;
import net.summerfarm.mall.enums.CommonStatus;
import net.summerfarm.wnc.client.provider.fence.DeliveryFenceQueryProvider;
import net.summerfarm.wnc.client.req.AreaQueryReq;
import net.summerfarm.wnc.client.req.fence.CityAreaBatchQueryReq;
import net.summerfarm.wnc.client.req.fence.FenceQueryReq;
import net.summerfarm.wnc.client.resp.fence.CityAreaBelongFenceResp;
import net.summerfarm.wnc.client.resp.fence.FenceResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description 根据区域编号获取地址信息
 * @date 2023/3/15 17:23:11
 */
@Slf4j
@DubboService
@Component

public class AddressInfoProviderImpl implements AddressInfoProvider {

    @DubboReference
    private DeliveryFenceQueryProvider deliveryFenceQueryProvider;

    private static final int MAX_SIZE = 50;

    @Override
    public DubboResponse<AddressInfoResp> getAddressInfo(AddressInfoReq addressInfoReq) {
        log.info("AddressInfoProviderImpl[]getAddressInfo[]addressInfoReq:{}", JSON.toJSONString(addressInfoReq));
        //根据区域号获取围栏信息--支持批量获取
        FenceQueryReq fenceQueryReq = new FenceQueryReq();
        fenceQueryReq.setAreaNo(addressInfoReq.getAreaNo());
        fenceQueryReq.setStatus(CommonStatus.NO.getCode());
        DubboResponse<List<FenceResp>> dubboResponse = deliveryFenceQueryProvider.queryFenceListWithArea(fenceQueryReq);
        log.info("AddressInfoProviderImpl[]getAddressInfo[]queryFenceListWithArea:{}", JSON.toJSONString(dubboResponse));
        if (Objects.isNull(dubboResponse) || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            log.error("AddressInfoProviderImpl[]getAddressInfo[]queryFenceListWithArea error cause:{}", JSON.toJSONString(dubboResponse));
            throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        List<FenceResp> fenceRespList = dubboResponse.getData();
        if (CollectionUtils.isEmpty(fenceRespList)) {
            log.info("AddressInfoProviderImpl[]getAddressInfo[]fenceRespList is Empty!");
            return DubboResponse.getOK();
        }

        //获取当前区域编号--支持批量获取
        List<FenceResp> respList = fenceRespList.stream().filter(e -> Objects.equals(e.getAreaNo(), addressInfoReq.getAreaNo()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(respList)) {
            log.info("AddressInfoProviderImpl[]getAddressInfo[]respList is Empty!");
            return DubboResponse.getOK();
        }

        //组装返回信息
        List<AddressInfoVO> addressInfoResps = new ArrayList<>(respList.size());
        AddressInfoResp addressInfoResp = new AddressInfoResp();
        for (FenceResp fenceAreaResp : respList) {
            if (CollectionUtils.isEmpty(fenceAreaResp.getFenceAreaRespList())) {
                continue;
            }
            fenceAreaResp.getFenceAreaRespList().stream().forEach(e -> {
                AddressInfoVO addressInfoVO = new AddressInfoVO();
                addressInfoVO.setAreaName(e.getArea());
                addressInfoVO.setCityName(e.getCity());
                addressInfoVO.setProvinceName(e.getProvince());
                addressInfoResps.add(addressInfoVO);
            });
        }
        addressInfoResp.setAddressInfoVOS(addressInfoResps);
        log.info("AddressInfoProviderImpl[]getAddressInfo[]addressInfoResp:{}", JSON.toJSONString(addressInfoResp));
        return DubboResponse.getOK(addressInfoResp);
    }

    @Override
    public DubboResponse<AreaNoByAddressResp> getAreaNoByAddress(AreaNoByAddressReq areaNoByAddressReq) {
        log.info("AddressInfoProviderImpl[]getAreaNoByAddress[]areaNoByAddressReq:{}", JSON.toJSONString(areaNoByAddressReq));

        //封装参数
        List<AddressInfoVO> addressInfoVOS = areaNoByAddressReq.getAddressInfoVOS();
        CityAreaBatchQueryReq cityAreaBatchQueryReq = new CityAreaBatchQueryReq();
        List<AreaQueryReq> areaQueryReqs=  new ArrayList<>(addressInfoVOS.size());
        addressInfoVOS.stream().forEach(e -> {
            AreaQueryReq areaQueryReq = new AreaQueryReq();
            areaQueryReq.setArea(e.getAreaName());
            areaQueryReq.setCity(e.getCityName());
            areaQueryReqs.add(areaQueryReq);
        });
        if (CollectionUtils.isEmpty(areaQueryReqs)) {
            return DubboResponse.getOK();
        }

        List<CityAreaBelongFenceResp> belongFenceRespList = new ArrayList<>();
        if (areaQueryReqs.size() > MAX_SIZE) {
            for (int i = 0; i * MAX_SIZE < areaQueryReqs.size(); i++) {
                List<AreaQueryReq> areaQueryReqList = areaQueryReqs.subList(MAX_SIZE * i, Math.min(MAX_SIZE * (i + 1), areaQueryReqs.size()));
                cityAreaBatchQueryReq.setAreaQueryReqList(areaQueryReqList);
                log.info("AddressInfoProviderImpl[]getAreaNoByAddress[]cityAreaBatchQueryReq:{}", JSON.toJSONString(cityAreaBatchQueryReq));
                DubboResponse<List<CityAreaBelongFenceResp>> areaBelongFence = deliveryFenceQueryProvider.batchQueryCityAreaBelongFence(cityAreaBatchQueryReq);
                log.info("AddressInfoProviderImpl[]getAreaNoByAddress[]batchQueryCityAreaBelongFence:{}", JSON.toJSONString(areaBelongFence));
                if (Objects.isNull(areaBelongFence) || !DubboResponse.COMMON_SUCCESS_CODE.equals(areaBelongFence.getCode())){
                    log.warn("AddressInfoProviderImpl[]getAreaNoByAddress[]batchQueryCityAreaBelongFence error cause:{}", JSON.toJSONString(areaBelongFence));
                    continue;
                }

                List<CityAreaBelongFenceResp> fenceRespList = areaBelongFence.getData();
                if (CollectionUtils.isEmpty(fenceRespList)){
                    log.info("AddressInfoProviderImpl[]getAreaNoByAddress[]fenceRespList is Empty");
                    continue;
                }
                belongFenceRespList.addAll(fenceRespList);
            }
        } else {
            //根据省市区查询围栏信息
            cityAreaBatchQueryReq.setAreaQueryReqList(areaQueryReqs);
            DubboResponse<List<CityAreaBelongFenceResp>> areaBelongFence = deliveryFenceQueryProvider.batchQueryCityAreaBelongFence(cityAreaBatchQueryReq);
            log.info("AddressInfoProviderImpl[]getAreaNoByAddress[]batchQueryCityAreaBelongFence:{}", JSON.toJSONString(areaBelongFence));
            if (Objects.isNull(areaBelongFence) || !DubboResponse.COMMON_SUCCESS_CODE.equals(areaBelongFence.getCode())){
                log.error("AddressInfoProviderImpl[]getAreaNoByAddress[]batchQueryCityAreaBelongFence error cause:{}", JSON.toJSONString(areaBelongFence));
                throw new ProviderException(areaBelongFence == null ? ProviderErrorCode.DEFAULT_CODE : areaBelongFence.getMsg());
            }

            belongFenceRespList = areaBelongFence.getData();
        }

        if (CollectionUtils.isEmpty(belongFenceRespList)){
            log.info("AddressInfoProviderImpl[]getAreaNoByAddress[]belongFenceRespList is Empty");
            return DubboResponse.getOK();
        }

        //封装返回参数
        AreaNoByAddressResp areaNoByAddressResp = new AreaNoByAddressResp();
        Map<Integer, List<AddressInfoVO>> maps = new HashMap<>();
        for (CityAreaBelongFenceResp cityAreaBelongFenceResp : belongFenceRespList) {
            List<AddressInfoVO> addressInfoVOList = new ArrayList<>();
            FenceResp fenceResp = cityAreaBelongFenceResp.getFenceResp();

            if (Objects.isNull(fenceResp)) {
                continue;
            }
            fenceResp.getFenceAreaRespList().stream().forEach(e -> {
                //假如区域数据为空 则拿入参的区域信息
                if (StringUtils.isEmpty(e.getArea())) {
                    List<AddressInfoVO> infoVOList = addressInfoVOS.stream().filter(o -> Objects.equals(o.getCityName(), e.getCity())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(infoVOList)) {
                        infoVOList.stream().forEach(f -> {
                            AddressInfoVO addressInfoVO = new AddressInfoVO();
                            addressInfoVO.setCityName(e.getCity());
                            addressInfoVO.setAreaName(f.getAreaName());
                            addressInfoVO.setProvinceName(e.getProvince());
                            addressInfoVOList.add(addressInfoVO);
                        });
                    }
                } else {
                    AddressInfoVO addressInfoVO = new AddressInfoVO();
                    addressInfoVO.setCityName(e.getCity());
                    addressInfoVO.setAreaName(e.getArea());
                    addressInfoVO.setProvinceName(e.getProvince());
                    addressInfoVOList.add(addressInfoVO);
                }
            });

            //判断是否存在重复值
            if (CollectionUtils.isEmpty(maps.get(fenceResp.getAreaNo()))) {
                maps.put(fenceResp.getAreaNo(), addressInfoVOList);
            } else {
                List<AddressInfoVO> infoVOS = maps.get(fenceResp.getAreaNo());
                infoVOS.addAll(addressInfoVOList);
                maps.put(fenceResp.getAreaNo(), infoVOS);
            }
        }


        if (CollectionUtils.isEmpty(maps)){
            log.info("AddressInfoProviderImpl[]getAreaNoByAddress[]maps is Empty");
            return DubboResponse.getOK();
        }
        areaNoByAddressResp.setMaps(maps);
        log.info("AddressInfoProviderImpl[]getAreaNoByAddress[]areaNoByAddressResp:{}", JSON.toJSONString(areaNoByAddressResp));
        return DubboResponse.getOK(areaNoByAddressResp);
    }
}
