package net.summerfarm.mall.provider.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.ContactProvider;
import net.summerfarm.mall.client.req.ContactQueryReq;
import net.summerfarm.mall.client.resp.ContactQueryResp;
import net.summerfarm.mall.mapper.ContactMapper;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.model.domain.Contact;
import net.summerfarm.mall.model.dto.merchant.merchantQueryDTO;
import net.summerfarm.mall.provider.converter.contact.Contact2Resp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@Component
public class ContactProviderImpl implements ContactProvider {

    @Resource
    ContactMapper contactMapper;

    @Resource
    private MerchantMapper merchantMapper;


    @Override
    public DubboResponse<ContactQueryResp> getContact(ContactQueryReq contactQueryReq) {
        log.info("获取地址基本信息:{}", JSON.toJSONString(contactQueryReq));
        Contact contact =  contactMapper.selectByPrimaryKey(contactQueryReq.getContactId());
        ContactQueryResp contactQueryResp = new ContactQueryResp();
        if (!ObjectUtils.isEmpty(contact)){
            contactQueryResp = Contact2Resp.domain2VO(contact);
            if (null != contactQueryResp.getMId()){
                merchantQueryDTO merchantQueryDTO =  merchantMapper.selectMNameByMid(contactQueryResp.getMId());
                if (!ObjectUtils.isEmpty(merchantQueryDTO)){
                    contactQueryResp.setMerchantName(merchantQueryDTO.getMerchantName());
                    contactQueryResp.setAreaNo(merchantQueryDTO.getAreaNo());
                }
            }
        }
        log.info("返回地址基本信息:{}", JSON.toJSONString(contactQueryResp));
        return DubboResponse.getOK(contactQueryResp);
    }
}
