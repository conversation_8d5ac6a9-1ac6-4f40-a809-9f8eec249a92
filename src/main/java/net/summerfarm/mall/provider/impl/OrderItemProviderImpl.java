package net.summerfarm.mall.provider.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.OrderItemProvider;
import net.summerfarm.mall.client.req.InventoryQueryReq;
import net.summerfarm.mall.client.req.OrderItemReq;
import net.summerfarm.mall.client.resp.InventoryQueryResp;
import net.summerfarm.mall.client.resp.OrderItemResp;
import net.summerfarm.mall.mapper.InventoryMapper;
import net.summerfarm.mall.mapper.OrderItemMapper;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.model.vo.InventoryVO;
import net.summerfarm.mall.model.vo.OrderItemVO;
import net.summerfarm.mall.provider.converter.orderItem.OrderItem2Resp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@Component
public class OrderItemProviderImpl implements OrderItemProvider {

    @Resource
    OrderItemMapper orderItemMapper;
    @Resource
    InventoryMapper inventoryMapper;
    @Resource
    OrdersMapper ordersMapper;


    @Override
    public DubboResponse<List<OrderItemResp>> getOrderItem(OrderItemReq orderItemReq) {
        log.info("获取订单项基本信息:{}", JSON.toJSONString(orderItemReq));
        List<OrderItemResp> itemRespList = new ArrayList<>();
        List<OrderItemVO> orderItemList =  orderItemMapper.selectOrderItemVOByMaster(orderItemReq.getOrderNo());
        if (!CollectionUtils.isEmpty(orderItemList)){
            for (OrderItemVO orderItemVO : orderItemList){
                Integer warehouseNo =  inventoryMapper.selectWarehouseNo(orderItemVO.getSku(), ordersMapper.selectAreaNoByOrderNo(orderItemReq.getOrderNo()));
                orderItemVO.setWarehouseNo(warehouseNo);
            }
            itemRespList = orderItemList.stream().map(OrderItem2Resp::domain2VO).collect(Collectors.toList());
        }
        log.info("返回订单项基本信息:{}", JSON.toJSONString(itemRespList));
        return DubboResponse.getOK(itemRespList);
    }

    @Override
    public DubboResponse<List<InventoryQueryResp>> getInventoryList(InventoryQueryReq inventoryQueryReq) {
        log.info("获取sku基本信息:{}", JSON.toJSONString(inventoryQueryReq));
        List<InventoryQueryResp> inventoryQueryResps = new ArrayList<>();
        if (CollectionUtils.isEmpty(inventoryQueryReq.getSkuList()) && CollectionUtils.isEmpty(inventoryQueryReq.getSkuNoList())){
            return DubboResponse.getOK(Lists.newArrayList());
        }
        List<InventoryVO> inventoryVOList =  inventoryMapper.selectByPrimaryKeyList(inventoryQueryReq.getSkuList(), inventoryQueryReq.getSkuNoList());
        inventoryQueryResps = inventoryVOList.stream().map(OrderItem2Resp::inventoryDomain2VO).collect(Collectors.toList());
        log.info("返回sku基本信息:{}", JSON.toJSONString(inventoryQueryResps));
        return DubboResponse.getOK(inventoryQueryResps);
    }
}
