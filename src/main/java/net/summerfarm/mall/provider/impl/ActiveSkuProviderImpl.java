package net.summerfarm.mall.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.ActiveSkuProvider;
import net.summerfarm.mall.client.resp.ActiveSkuResp;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDTO;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDetailDTO;
import net.summerfarm.mall.service.ActivityService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 * @Date 2023/5/6 11:59
 */
@Slf4j
@DubboService
@Component
public class ActiveSkuProviderImpl implements ActiveSkuProvider {
    @Resource
    private ActivityService activityService;

    /**
     * 获取特价sku信息
     *
     * @param sku    sku
     * @param areaNo 区没有
     * @param mId    m id
     * @return {@link DubboResponse}<{@link ActiveSkuResp}>
     */
    @Override
    public DubboResponse<ActiveSkuResp> getActiveSkuDetail(String sku, Integer areaNo, Long mId) {
        ActivitySkuDTO activitySkuDTO = new ActivitySkuDTO();
        activitySkuDTO.setSku(sku);
        ActivitySkuDetailDTO activitySku = activityService.getActivitySku(activitySkuDTO, areaNo, mId,Boolean.FALSE,Boolean.FALSE);
        if (activitySku != null && activitySku.getActivityPrice() != null) {
            ActiveSkuResp resp = new ActiveSkuResp();
            resp.setActivityPrice(activitySku.getActivityPrice());
            resp.setSku(activitySku.getSku());
            return DubboResponse.getOK(resp);
        }
        return DubboResponse.getOK();
    }
}
