package net.summerfarm.mall.provider.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.after.service.impl.ExecutableAfterSale;
import net.summerfarm.mall.client.provider.AfterSaleOrderProvider;
import net.summerfarm.mall.client.req.AfterSaleOrderQueryReq;
import net.summerfarm.mall.client.req.AfterSaleOrderSaveReq;
import net.summerfarm.mall.client.req.DeliveryDateQueryReq;
import net.summerfarm.mall.client.req.afterSale.AfterSaleOrderReq;
import net.summerfarm.mall.client.req.afterSale.CalcAfterSaleCouponReq;
import net.summerfarm.mall.client.req.afterSale.DeliveryStatusQueryReq;
import net.summerfarm.mall.client.req.afterSale.HandleTypeQueryReq;
import net.summerfarm.mall.client.resp.AfterSaleOrderQueryResp;
import net.summerfarm.mall.client.resp.ExecutableAfterSaleResp;
import net.summerfarm.mall.client.resp.OrderCompareCommonQueryResp;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.tms.DistOrderDTO;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.model.vo.ofc.AfterSaleOrderCompareVO;
import net.summerfarm.mall.provider.converter.afterSaleOrder.AfterSaleOrder2Resp;
import net.summerfarm.mall.provider.converter.afterSaleOrder.AfterSaleOrderReq2VO;
import net.summerfarm.mall.provider.converter.deliveryPlan.DeliveryPlan2Resp;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.DeliverPlanRemarkSnapshotService;
import net.summerfarm.mall.service.OrderRelationService;
import net.summerfarm.mall.service.facade.OfcQueryFacade;
import net.summerfarm.mall.service.facade.TmsDistOrderDetailFacade;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.WncDeliveryFenceQueryFacade;
import net.summerfarm.mall.service.facade.dto.AreaStoreUnLockReq;
import net.summerfarm.mall.service.facade.dto.FenceCloseTimeReq;
import net.summerfarm.mall.service.facade.dto.OrderUnLockSkuDetailReqDTO;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@Component
public class AfterSaleOrderProviderImpl implements AfterSaleOrderProvider {

    @Resource
    AfterSaleOrderMapper afterSaleOrderMapper;

    @Resource
    private AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;

    @Resource
    private AfterSaleDeliveryDetailMapper afterSaleDeliveryDetailMapper;

    @Resource
    private AfterSaleOrderService afterSaleOrderService;

    @Resource
    private OrderItemMapper orderItemMapper;

    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;

    @Resource
    private OrdersMapper ordersMapper;

    @Resource
    private AreaMapper areaMapper;

    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;

    @Resource
    private AdminMapper adminMapper;

    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private OrdersCouponMapper ordersCouponMapper;

    @Resource
    private StockTaskMapper stockTaskMapper;

    @Resource
    private StockTaskItemMapper stockTaskItemMapper;

    @Resource
    private TmsDistOrderDetailFacade tmsDistOrderDetailFacade;

    @Resource
    private MerchantMapper merchantMapper;

    @Resource
    DeliverPlanRemarkSnapshotService deliverPlanRemarkSnapshotService;

    @Resource
    private WmsAreaStoreFacade areaStoreFacade;
    @Resource
    private OfcQueryFacade ofcQueryFacade;
    @Resource
    private OrderRelationService orderRelationService;
    @Resource
    private WncDeliveryFenceQueryFacade wncDeliveryFenceQueryFacade;

    @Override
    public DubboResponse<AfterSaleOrderQueryResp> getAfterSaleOrder(AfterSaleOrderQueryReq afterSaleOrderQueryReq) {
        log.info("获取售后基本信息:{}", JSON.toJSONString(afterSaleOrderQueryReq));
        AfterSaleOrderVO afterSaleOrderVO =  afterSaleOrderMapper.selectVoByAfterSaleOrderNo(afterSaleOrderQueryReq.getAfterSaleOrderNo());
        AfterSaleOrderQueryResp afterSaleOrderQueryResp = new AfterSaleOrderQueryResp();
        if (!ObjectUtils.isEmpty(afterSaleOrderVO)){
            afterSaleOrderQueryResp = AfterSaleOrder2Resp.domain2VO(afterSaleOrderVO);
        }
        String afterSaleOrderNo = afterSaleOrderQueryResp.getAfterSaleOrderNo();
        DeliveryPlanRemarkSnapshot snapshot = deliverPlanRemarkSnapshotService.getByAfterSale(afterSaleOrderNo);
        if (snapshot != null) {
            afterSaleOrderQueryResp.setAddressRemark(snapshot.getAddressRemark());
        }
        log.info("返回售后基本信息:{}", JSON.toJSONString(afterSaleOrderQueryResp));
        return DubboResponse.getOK(afterSaleOrderQueryResp);
    }

    @Override
    public DubboResponse<PageInfo<OrderCompareCommonQueryResp>> listAfterSaleOrderByDeliveryDate(DeliveryDateQueryReq deliveryDateQueryReq) {
        log.info("根据配送时间查询售后订单:{}", JSON.toJSONString(deliveryDateQueryReq));
        int totalCount = afterSaleDeliveryPathMapper.countByDeliveryDate(deliveryDateQueryReq.getDeliveryTime());
        if (totalCount <= 0){
            return DubboResponse.getOK(PageInfo.emptyPageInfo());
        }
        PageInfo<OrderCompareCommonQueryResp> result = new PageInfo<>();
        result.setSize(totalCount);
        // ofc会先查询总数，pageSize参数会传0，这里直接返回可以少查询一次数据库
        if (deliveryDateQueryReq.getPageSize() <= 0){
            return DubboResponse.getOK(result);
        }
        List<AfterSaleOrderCompareVO> afterSaleOrderCompareVOS = afterSaleDeliveryPathMapper.listByDeliveryDate(deliveryDateQueryReq.getDeliveryTime(),
                (deliveryDateQueryReq.getPageIndex() - 1) * deliveryDateQueryReq.getPageSize(), deliveryDateQueryReq.getPageSize());
        if (CollectionUtils.isEmpty(afterSaleOrderCompareVOS)){
            return DubboResponse.getOK(result);
        }
        List<AfterSaleDeliveryDetail> afterSaleDeliveryDetailList = afterSaleDeliveryDetailMapper.selectDetailByIds(afterSaleOrderCompareVOS.stream().map(AfterSaleOrderCompareVO::getAfterSaleDeliveryPathId).collect(Collectors.toList()));
        Map<Integer, List<AfterSaleDeliveryDetail>> afterSaleDeliveryDetailMap = afterSaleDeliveryDetailList.stream().collect(Collectors.groupingBy(AfterSaleDeliveryDetail::getAsDeliveryPathId));
        result = new PageInfo<>(DeliveryPlan2Resp.buildAfterSaleOrderDataList(afterSaleOrderCompareVOS, afterSaleDeliveryDetailMap));
        log.info("根据配送时间查询售后订单返回结果:{}", JSON.toJSONString(result));
        return DubboResponse.getOK(result);
    }

    @Override
    public DubboResponse<Boolean> save(@Valid AfterSaleOrderSaveReq afterSaleOrderSaveReq) {
        log.info("AfterSaleOrderProviderImpl[]save[]afterSaleOrderSaveReq:{}", JSON.toJSONString(afterSaleOrderSaveReq));
        AfterSaleOrder querySaleOrder = new AfterSaleOrder();
        querySaleOrder.setOrderNo(afterSaleOrderSaveReq.getOrderNo());
        List<AfterSaleOrderVO> orderVOList = afterSaleOrderMapper.selectByOrderNoNew(querySaleOrder);
        OrderItem orderItem = new OrderItem();
        orderItem.setOrderNo(afterSaleOrderSaveReq.getOrderNo());
        orderItem.setSku(afterSaleOrderSaveReq.getSku());
        OrderItemVO orderItemVO = orderItemMapper.selectByEntity(orderItem);
        if (Objects.isNull(orderItemVO)) {
            throw new ProviderException("订单数据不存在！");
        }
        AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();

        afterSaleOrderVO.setHandler(afterSaleOrderSaveReq.getAdminName());
        afterSaleOrderVO.setmId(orderItemVO.getmId());
        afterSaleOrderVO.setAddTime(LocalDateTime.now());
        afterSaleOrderVO.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        afterSaleOrderVO.setView(0);
        afterSaleOrderVO.setTimes(orderVOList.size() + 1);
        afterSaleOrderVO.setDeliveryId(null);
        afterSaleOrderVO.setAccountId(orderItemVO.getAccountId());
        afterSaleOrderVO.setApplyer(afterSaleOrderSaveReq.getAdminName());
        afterSaleOrderVO.setIsManage(true);
        afterSaleOrderVO.setSku(orderItemVO.getSku());
        afterSaleOrderVO.setOrderNo(orderItemVO.getOrderNo());
        afterSaleOrderVO.setRefundType("切仓库存不足");
        afterSaleOrderVO.setDeliveryed(0);
        afterSaleOrderVO.setSuitId(orderItemVO.getSuitId());
        afterSaleOrderVO.setQuantity(afterSaleOrderSaveReq.getRevokeQuantity());
        Merchant merchant = merchantMapper.selectByPrimaryKey(orderItemVO.getmId());
        if (Objects.nonNull(merchant) && Objects.nonNull(merchant.getDirect()) && Objects.equals(merchant.getDirect(), PayTypeEnum.BILL.getType())) {
            afterSaleOrderVO.setHandleType(3);
        } else {
            afterSaleOrderVO.setHandleType(2);
        }
        afterSaleOrderVO.setProductType(orderItemVO.getProductType());

        AjaxResult afterSaleMoney = afterSaleOrderService.getAfterSaleMoneyForAfterSale(afterSaleOrderVO);
        if (AjaxResult.isSuccess(afterSaleMoney)){
            afterSaleOrderVO.setHandleNum((BigDecimal)afterSaleMoney.getData());
        }else{
            throw new ProviderException("售后金额异常！");
        }
        try {
            String orderNo = afterSaleOrderVO.getOrderNo();
            Orders orders = ordersMapper.selectByOrderyNo(afterSaleOrderVO.getOrderNo());

            //发起未到货售后时，校验是否过了截单点
            if ( !Arrays.asList(new Integer[]{11,12,13,14}).contains(afterSaleOrderVO.getHandleType()) && afterSaleOrderVO.getDeliveryed().intValue() == 0 && !afterSaleOrderVO.getRefundType().equals("其他") && !orders.getType().equals(OrderTypeEnum.TIMING.getId())){
                AjaxResult result = checkOutTime(orders);
                if (!AjaxResult.isSuccess(result)) {
                    throw new ProviderException(result.getMsg());
                }
            }

            //查询大客户预付商品信息
            List<PrepayInventoryRecord> prepayInventoryRecords = prepayInventoryRecordMapper.selectRecordListNew(orderNo, afterSaleOrderVO.getSku(), true);

            InventoryVO inventoryVO = inventoryMapper.selectInventoryVOBySku(afterSaleOrderVO.getSku());
            //换货，补发售后金额为0 元  拦截退单、拦截录入账单的省心送为0元
            Boolean exGoodsRefund = true;
            if (Arrays.asList(new Integer[]{6, 7,11,12,13,14}).contains(afterSaleOrderVO.getHandleType())) {
                exGoodsRefund = false;
            }
            if (exGoodsRefund) {
                if (CollectionUtils.isEmpty(prepayInventoryRecords) && Objects.equals(inventoryVO.getType(), 0) && (afterSaleOrderVO.getHandleNum() == null || afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO) <= 0)) {
                    throw new ProviderException("售后金额不能小于零");
                }
            }

            BigDecimal totalPrice = orders.getTotalPrice();
            HashMap<String, String> map = new HashMap<>();
            Map<String, OrderRelation> orderNoMap = orderRelationService.queryMasterOrderNoByOrderNo(Collections.singletonList(orderNo));
            if (CollectionUtils.isEmpty(orderNoMap)){
                map.put("orderNo",orderNo);
            }else {
                map.put("orderNo",orderNoMap.get(orderNo).getMasterOrderNo());
            }
            // 获取使用的优惠券信息
            List<MerchantCouponVO> merchantCouponVOS = ordersCouponMapper.select(map);
            //使用售后券 且处理方式为返券 总金额 = 订单实付 + 券金额
            if (Objects.equals(afterSaleOrderVO.getHandleType(), AfterSaleHandleType.COUPON.getType()) &&
                    !CollectionUtils.isEmpty(merchantCouponVOS)) {
                for (MerchantCouponVO merchantCouponVO : merchantCouponVOS){
                    Integer grouping = merchantCouponVO.getGrouping();
                    //校验券类型 和券金额
                    if (Objects.equals(grouping, 1) && merchantCouponVO.getMoney() != null) {
                        BigDecimal money = merchantCouponVO.getMoney();
                        totalPrice = totalPrice.add(money);
                    }
                }
            }
            BigDecimal originHandleNum = afterSaleOrderVO.getHandleNum();
            if (afterSaleOrderVO.getRecoveryNum() != null) {
                originHandleNum = originHandleNum.subtract(afterSaleOrderVO.getRecoveryNum());
            }
            Integer deliveryId = afterSaleOrderVO.getDeliveryId();
            List<AfterSaleOrderVO> afterSaleOrders = selectByOrderNo(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getDeliveryId());

            if (exGoodsRefund) {
                //已售后的数量
                BigDecimal handleNum = BigDecimal.ZERO;
                //提交售后的金额
                BigDecimal afterHandleNum = BigDecimal.ZERO;
                for (AfterSaleOrderVO check : afterSaleOrders) {
                    if (Objects.equals(check.getDeliveryId(), deliveryId)) {
                        handleNum = handleNum.add(check.getHandleNum());
                    }
                    afterHandleNum = afterHandleNum.add(check.getHandleNum());

                }
                //售后金额大于可售后金额 提交报错
                if (totalPrice.subtract(afterHandleNum).compareTo(originHandleNum) < 0) {
                    throw new ProviderException("发起售后的总金额大于订单金额");
                }

                if (afterSaleOrderVO.getType() == null || afterSaleOrderVO.getType() != 3) {
                    if (handleNum.add(originHandleNum).compareTo(totalPrice) > 0) {
                        throw new ProviderException( "退款金额异常");
                    }
                }

            }
            //查询已提交的售后订单
            afterSaleOrderVO.setDeliveryId(afterSaleOrderVO.getDeliveryId());
            log.info("AfterSaleOrderProviderImpl[]save[]newSave[]start[]afterSaleOrderVO:{}", JSON.toJSONString(afterSaleOrderVO));
            AjaxResult result = afterSaleOrderService.newSave(afterSaleOrderVO);
            if (Objects.isNull(result) || !AjaxResult.DEFAULT_SUCCESS.equalsIgnoreCase(result.getCode())) {
                log.error("AfterSaleOrderProviderImpl[]save[]newSave[]error[]result:{}", JSON.toJSONString(result));
                throw new ProviderException(Objects.isNull(result) ? "新增售后单失败！" : result.getMsg());
            }
        } catch (InterruptedException e) {
            throw new ProviderException("新增售后单失败！");
        }
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    public DubboResponse<Boolean> closeAfterSale(@Valid @NotBlank(message = "售后单不能为空!") String afterSaleNo) {
        log.info("AfterSaleOrderProviderImpl[]save[]afterSaleNo:{}", afterSaleNo);
        AfterSaleOrderVO afterSaleOrderVO = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleNo);
        if (Objects.isNull(afterSaleOrderVO)) {
            throw new ProviderException("售后单不存在！");
        }
        AfterSaleDeliveryPath afterSaleDeliveryPath = afterSaleDeliveryPathMapper.selectPathByNo(afterSaleNo);
        if (Objects.equals(afterSaleOrderVO.getStatus(), 11)) {
            throw new ProviderException("已取消，不可再关闭售后单");
        }
        Orders orders = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        if (Objects.isNull(orders)) {
            throw new ProviderException("订单不存在");
        }

        if (afterSaleDeliveryPath != null) {
            //关单时间判断
            LocalTime closeTime = ofcQueryFacade.queryCloseTime(afterSaleDeliveryPath.getConcatId(),
                    DistOrderSourceEnum.getOfcSourceEnumAfterSaleByOrderType(orders.getType()));
            closeTime = null != closeTime ? closeTime : LocalTime.of(20,00);
            if (isNotWarehouseIntoOrder(afterSaleNo)){
                LocalDateTime afterSaleCloseTime = afterSaleOrderVO.getAddTime().toLocalDate().atTime(closeTime);
                if(LocalDateTime.now().isAfter(afterSaleCloseTime)){
                    throw new ProviderException("已过截单时间，不可关闭");
                }
            }else {
                //关单时间判断
                LocalDate deliveryTime = afterSaleDeliveryPath.getDeliveryTime().minusDays(1);
                LocalDate deliveryTimeNow = afterSaleDeliveryPath.getDeliveryTime();
                LocalDateTime localDateTime = deliveryTime.atTime(closeTime);
                if(LocalDateTime.now().isAfter(localDateTime)){
                    LocalTime localTime = localDateTime.toLocalTime();
                    String format = localTime.format(DateTimeFormatter.ofPattern("HH点mm分"));
                    throw new ProviderException("配送日前一天的" + format +"前可选");
                }
                //配送时间小于当前时间
                if (LocalDate.now().isAfter(deliveryTimeNow) || Objects.equals(deliveryTimeNow, LocalDate.now())) {
                    throw new ProviderException("当前售后单已不可关闭");
                }
            }

        }
        log.info("关闭凭证更新售后单号:" + afterSaleOrderVO.getAfterSaleOrderNo());
        //否者 就失败售后单
        AfterSaleOrder updateOrder = new AfterSaleOrder();
        updateOrder.setAfterSaleOrderNo(afterSaleNo);
        //updateOrder.setCloser(UserInfoHolder.getUser().getUsername());
        updateOrder.setCloseTime(LocalDateTime.now());
        updateOrder.setStatus(11);
        afterSaleOrderMapper.updateByAfterSaleOrderNo(updateOrder);
        AfterSaleProof update = new AfterSaleProof();
        update.setAfterSaleOrderNo(afterSaleNo);
        update.setStatus(11);
        afterSaleProofMapper.updateByAfterSale(update);
        //处理补发 退货退款 换货
        if (afterSaleDeliveryPath != null) {
            Integer deliveryPathId = afterSaleDeliveryPath.getId();
            //补发sku信息
            AfterSaleDeliveryDetail afterSaleDeliveryDetail = afterSaleDeliveryPathMapper.selectDetail(deliveryPathId);
            //返还冻结
            if (afterSaleDeliveryDetail != null) {

                String sku = afterSaleDeliveryDetail.getSku();
                Integer quantity = afterSaleDeliveryDetail.getQuantity();
                //Integer outStoreNo = afterSaleDeliveryPath.getOutStoreNo();
                log.info("取消售后信息 sku={},quantity={}", sku, quantity);
                /*Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
                //加虚拟
                areaStoreService.updateOnlineStockByStoreNo(true, quantity, sku, outStoreNo, SaleStockChangeTypeEnum.CANCEL_AFTER_ORDER, null, recordMap, NumberUtils.INTEGER_ZERO);
                //减冻结库存
                areaStoreService.updateLockStockByStoreNo(-quantity, sku, outStoreNo, SaleStockChangeTypeEnum.CANCEL_AFTER_ORDER, null, recordMap);
                quantityChangeRecordService.insert(recordMap);*/

                //库存释放 新模型
                AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
                areaStoreUnLockReq.setContactId(afterSaleDeliveryPath.getConcatId());
                areaStoreUnLockReq.setOrderType(net.summerfarm.enums.SaleStockChangeTypeEnum.CANCEL_AFTER_ORDER.getTypeName());
                areaStoreUnLockReq.setOrderNo(afterSaleNo);
                areaStoreUnLockReq.setIdempotentNo(afterSaleNo);
                areaStoreUnLockReq.setOperatorNo(afterSaleNo);
                List<OrderUnLockSkuDetailReqDTO> orderReleaseSkuDetailReqDTOS = new ArrayList<>();
                OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
                orderUnLockSkuDetailReqDTO.setSkuCode(sku);
                orderUnLockSkuDetailReqDTO.setReleaseQuantity(quantity);
                orderReleaseSkuDetailReqDTOS.add(orderUnLockSkuDetailReqDTO);
                areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(orderReleaseSkuDetailReqDTOS);
                areaStoreUnLockReq.setMerchantId(afterSaleOrderVO.getmId());
                areaStoreUnLockReq.setSource(DistOrderSourceEnum.getDistOrderAfterSaleSourceByOrderType(orders.getType()));
                areaStoreFacade.storeUnLock(areaStoreUnLockReq);
            }
            //更新状态
            afterSaleDeliveryPathMapper.updateDeliveryPath(afterSaleNo, 0);
        }
        return DubboResponse.getOK(Boolean.TRUE);
    }

    /**
     * 判断售后单sku是否为代销不入库订单：t、代销不入库 f、非代销不入库
     * @param afterSaleNo 售后单号
     * @return true代销不入库，false非代销不入库
     */
    public boolean isNotWarehouseIntoOrder(String afterSaleNo) {
        AfterSaleOrderVO afterSaleOrderVO = afterSaleOrderMapper.queryByAfterSaleOrderNo(afterSaleNo);
        if (null != afterSaleOrderVO.getSubType() && InventoryEnums.SubType.SELF_NOT_INTO_WAREHOUSE.getSubType().equals(afterSaleOrderVO.getSubType())){
            return Boolean.TRUE;
        }else {
            return Boolean.FALSE;
        }
    }

    @Override
    public DubboResponse<Boolean> afterSaleHandle(AfterSaleOrderReq afterSaleOrderReq) {
        log.info("审核售后数据为:"+JSON.toJSONString(afterSaleOrderReq));
        AjaxResult result = null;
        try {
             result = afterSaleOrderService.newHandle(AfterSaleOrderReq2VO.afterSaleOrderReq2VO(afterSaleOrderReq));
        } catch (Exception e) {
            log.error("审核异常：{}", e);
            return DubboResponse.getDefaultError("审核异常");
        }
        if (!AjaxResult.isSuccess(result)){
            return DubboResponse.getDefaultError(result.getMsg());
        }
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    public DubboResponse<ExecutableAfterSaleResp> afterSaleDeliveryStatus(DeliveryStatusQueryReq deliveryStatusQueryReq) {
        log.info("获取已到货/未到货售后数据为:"+JSON.toJSONString(deliveryStatusQueryReq));
        if (Objects.isNull(deliveryStatusQueryReq)) {
            return DubboResponse.getDefaultError("参数不能为空！");
        }
        AjaxResult result = null;
        try {
            result = afterSaleOrderService.deliveryStatus(deliveryStatusQueryReq.getOrderNo(),deliveryStatusQueryReq.getIsManage(),deliveryStatusQueryReq.getDeliveryId(), null);
        } catch (Exception e) {
            log.error("获取已到货/未到货售后异常：{}", e);
            return DubboResponse.getDefaultError("获取已到货/未到货售后异常");
        }
        if (!AjaxResult.isSuccess(result)){
            return DubboResponse.getDefaultError(result.getMsg());
        }
        return DubboResponse.getOK(AfterSaleOrder2Resp.executableAfterSale2Resp((ExecutableAfterSale)result.getData()));
    }

    @Override
    public DubboResponse<ExecutableAfterSaleResp> afterSaleGetHandleType(HandleTypeQueryReq handleTypeQueryReq) {
        log.info("获取售后服务类型数据为:"+JSON.toJSONString(handleTypeQueryReq));
        if (Objects.isNull(handleTypeQueryReq)) {
            return DubboResponse.getDefaultError("参数不能为空！");
        }
        AjaxResult result = null;
        try {
            result = afterSaleOrderService.getHandleType(handleTypeQueryReq.getOrderNo(),handleTypeQueryReq.getSku(),handleTypeQueryReq.getDeliveryed(),handleTypeQueryReq.getIsManage());
        } catch (Exception e) {
            log.error("获取售后服务类型异常：{}", e);
            return DubboResponse.getDefaultError("获取售后服务类型异常");
        }
        if (!AjaxResult.isSuccess(result)){
            return DubboResponse.getDefaultError(result.getMsg());
        }
        return DubboResponse.getOK(AfterSaleOrder2Resp.executableAfterSale2Resp((ExecutableAfterSale)result.getData()));
    }

    @Override
    public DubboResponse<Integer> afterSaleManageCalculateQuantity(AfterSaleOrderReq afterSaleOrderReq) {
        log.info("获取最大售后数量数据为:"+JSON.toJSONString(afterSaleOrderReq));
        if (Objects.isNull(afterSaleOrderReq)) {
            return DubboResponse.getDefaultError("参数不能为空！");
        }
        AjaxResult result = null;
        try {
            result = afterSaleOrderService.getMaxQuantity(AfterSaleOrderReq2VO.afterSaleOrderReq2VO(afterSaleOrderReq));
        } catch (Exception e) {
            log.error("获取最大售后数量异常：{}", e);
            return DubboResponse.getDefaultError("获取最大售后数量异常");
        }
        if (!AjaxResult.isSuccess(result)){
            return DubboResponse.getDefaultError(result.getMsg());
        }
        return DubboResponse.getOK((Integer) result.getData());
    }

    @Override
    public DubboResponse<BigDecimal> afterSaleManageAfterSaleMoney(AfterSaleOrderReq afterSaleOrderReq) {
        log.info("获取最大售后金额数据为:"+JSON.toJSONString(afterSaleOrderReq));
        if (Objects.isNull(afterSaleOrderReq)) {
            return DubboResponse.getDefaultError("参数不能为空！");
        }
        if(null == afterSaleOrderReq.getDeliveryed()){
            return DubboResponse.getDefaultError("售后类型不可为空!");
        }
        AjaxResult result = null;
        try {
            result = afterSaleOrderService.getAfterSaleMoneyForAfterSale(AfterSaleOrderReq2VO.afterSaleOrderReq2VO(afterSaleOrderReq));
        } catch (Exception e) {
            log.error("获取最大售后金额异常：{}", e);
            return DubboResponse.getDefaultError("获取最大售后金额异常");
        }
        if (!AjaxResult.isSuccess(result)){
            return DubboResponse.getDefaultError(result.getMsg());
        }
        return DubboResponse.getOK((BigDecimal) result.getData());
    }

    @Override
    public DubboResponse<Boolean> afterSaleBatchSave(List<AfterSaleOrderReq> afterSaleOrderReqList) {
        log.info("批量生成售后单数据为:"+JSON.toJSONString(afterSaleOrderReqList));
        if (CollectionUtils.isEmpty(afterSaleOrderReqList)) {
            return DubboResponse.getDefaultError("参数不能为空！");
        }
        AjaxResult result = null;
        try {
            result = afterSaleOrderService.batchSave(AfterSaleOrderReq2VO.afterSaleOrderReq2List(afterSaleOrderReqList));
        } catch (Exception e) {
            log.error("批量生成售后单异常：{}",result.getMsg(), e);
            return DubboResponse.getDefaultError(result.getMsg());
        }
        if (!AjaxResult.isSuccess(result)){
            return DubboResponse.getDefaultError(result.getMsg());
        }
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<Boolean> afterSaleAudit(AfterSaleOrderReq afterSaleOrderReq) {
        log.info("商城审批数据为:"+JSON.toJSONString(afterSaleOrderReq));
        if (Objects.isNull(afterSaleOrderReq)) {
            return DubboResponse.getDefaultError("参数不能为空！");
        }
        AjaxResult result = null;
        try {
            result = afterSaleOrderService.newAudit(AfterSaleOrderReq2VO.afterSaleOrderReq2VO(afterSaleOrderReq));
        } catch (Exception e) {
            log.error("商城审批异常：{}", e);
            return DubboResponse.getDefaultError("商城审批异常");
        }
        if (!AjaxResult.isSuccess(result)){
            return DubboResponse.getDefaultError(result.getMsg());
        }
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<Boolean> afterSaleManageSave(AfterSaleOrderReq afterSaleOrderReq) {
        log.info("提交售后数据为:"+JSON.toJSONString(afterSaleOrderReq));
        if (Objects.isNull(afterSaleOrderReq)) {
            return DubboResponse.getDefaultError("参数不能为空！");
        }
        AjaxResult result = null;
        try {
            result = afterSaleOrderService.newSave(AfterSaleOrderReq2VO.afterSaleOrderReq2VO(afterSaleOrderReq));
        } catch (InterruptedException e) {
            log.error("保存售后数据异常：{}", e);
            return DubboResponse.getDefaultError("保存售后数据异常");
        }
        if (!AjaxResult.isSuccess(result)){
            return DubboResponse.getDefaultError(result.getMsg());
        }
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<Map<String, String>> afterSaleCoupon(CalcAfterSaleCouponReq calcAfterSaleCouponReq) {
        log.info("获取售后券数据为:"+JSON.toJSONString(calcAfterSaleCouponReq));
        if (Objects.isNull(calcAfterSaleCouponReq)) {
            return DubboResponse.getDefaultError("参数不能为空！");
        }
        AjaxResult result = null;
        try {
            result = afterSaleOrderService.calcAfterSaleCoupon(calcAfterSaleCouponReq.getOrderNo(), calcAfterSaleCouponReq.getSku(), calcAfterSaleCouponReq.getQuantity(), calcAfterSaleCouponReq.getSuitId(), calcAfterSaleCouponReq.getDeliveryed(),calcAfterSaleCouponReq.getType(),calcAfterSaleCouponReq.getDeliveryId()
                    ,calcAfterSaleCouponReq.getHandleType(),calcAfterSaleCouponReq.getAfterSaleOrderNo());
        } catch (Exception e) {
            log.error("获取售后券异常：{}", e);
            return DubboResponse.getDefaultError("获取售后券异常");
        }
        if (!AjaxResult.isSuccess(result)){
            return DubboResponse.getDefaultError(result.getMsg());
        }
        Map<String,String> data = (Map<String,String>)result.getData();
        return DubboResponse.getOK(data);
    }

    @Override
    public DubboResponse<List<AfterSaleOrderQueryResp>> queryAfterSaleReissueOrder(AfterSaleOrderQueryReq afterSaleOrderQueryReq) {
        if (null == afterSaleOrderQueryReq.getMId()){
            throw new ProviderException("mId不能为空");
        }
        if (null == afterSaleOrderQueryReq.getSku()){
            throw new ProviderException("sku不能为空");
        }
        if (null == afterSaleOrderQueryReq.getAddTime()){
            throw new ProviderException("日期不能为空");
        }
        LocalDateTime startTime = LocalDateTime.of(afterSaleOrderQueryReq.getAddTime(), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(afterSaleOrderQueryReq.getAddTime(), LocalTime.MAX);
        List<AfterSaleOrderVO> afterSaleOrders = afterSaleOrderMapper.selectAfterSaleReissueOrder(startTime,endTime,afterSaleOrderQueryReq.getSku(),afterSaleOrderQueryReq.getMId());
        return DubboResponse.getOK(AfterSaleOrder2Resp.afterSaleOrderList2Resp(afterSaleOrders));
    }

    /**
     * 校验截单时间
     * @param orders 订单编号
     * @return
     */
    private AjaxResult checkOutTime(Orders orders){
        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orders.getOrderNo());
        if (CollectionUtils.isEmpty(deliveryPlanVOS) || deliveryPlanVOS.size() > 1) {
            return AjaxResult.getErrorWithMsg("配送计划不正常");
        }
        DeliveryPlanVO deliveryPlanVO = deliveryPlanVOS.get(0);
        LocalDate deliveryTime = deliveryPlanVO.getDeliveryTime().minusDays(1);
        LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(22, 00, 00));
        Integer adminId = orders.getAdminId();
        //大客户截单 信息
        if(Objects.nonNull(adminId)){
            Admin admin = adminMapper.selectByPrimaryKey(adminId);
            String closeOrderTime = admin.getCloseOrderTime();
            if(!org.springframework.util.StringUtils.isEmpty(closeOrderTime)){
                LocalDateTime afterTime = LocalDateTime.of(deliveryTime, LocalTime.parse(closeOrderTime, BaseDateUtils.DEFAULT_LOCAL_TIME));
                if(LocalDateTime.now().isAfter(afterTime)){
                    return AjaxResult.getErrorWithMsg("配送日前一天的" + closeOrderTime + "前可选");
                }
            }
        }
        //获取当前配送仓的截单时间
        Integer orderStoreNo = deliveryPlanVO.getOrderStoreNo();
        //WarehouseLogisticsCenter center = warehouseLogisticsService.selectByStoreNo(orderStoreNo);

        FenceCloseTimeReq req = new FenceCloseTimeReq();
        req.setSource(DistOrderSourceEnum.getSourceEnumByOrderType(orders.getType()));
        req.setContactId(deliveryPlanVO.getContactId());
        LocalTime closeTime = wncDeliveryFenceQueryFacade.queryCloseTime(req);
        if(Objects.nonNull(closeTime)){
            //获取城配仓对应是否有加单
            List<Area> areas = areaMapper.selectSupportAddOrder(orderStoreNo);
            //截单时间
            localDateTime = deliveryTime.atTime(closeTime);
            //存在加单往后延长30分钟
            if(!CollectionUtils.isEmpty(areas)){
                localDateTime = localDateTime.plusMinutes(30);
            }
        }
        //时间校验
        if(LocalDateTime.now().isAfter(localDateTime)){
            LocalTime localTime = localDateTime.toLocalTime();
            String format = localTime.format(DateTimeFormatter.ofPattern("HH点mm分"));
            return AjaxResult.getErrorWithMsg("配送日前一天的" + format +"前可选");
        }
        return AjaxResult.getOK();
    }

    private List<AfterSaleOrderVO> selectByOrderNo(String orderNo, Integer deliveryId) {
        AfterSaleOrder querySaleOrder = new AfterSaleOrder();
        querySaleOrder.setOrderNo(orderNo);
        querySaleOrder.setDeliveryId(deliveryId);
        //获取到订单sku所有的售后信息
        List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderMapper.selectByOrderNoNew(querySaleOrder);
        //获取到订单售后单详情信息
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.selectByOrderNo(orderNo, null);
        if (!CollectionUtils.isEmpty(afterSaleProofs) && !CollectionUtils.isEmpty(afterSaleOrderVOS)) {
            Map<String, List<AfterSaleProof>> saleProofMap = afterSaleProofs.stream().
                    collect(Collectors.groupingBy(AfterSaleProof::getAfterSaleOrderNo));
            //遍历售后单信息填充 数量信息
            afterSaleOrderVOS.stream().forEach(afterSaleOrderVO -> {
                List<AfterSaleProof> afterSaleProofList = saleProofMap.get(afterSaleOrderVO.getAfterSaleOrderNo());
                handleVO(afterSaleOrderVO, afterSaleProofList);
            });
        }
        return afterSaleOrderVOS;
    }

    public void handleVO(AfterSaleOrderVO afterSaleOrder, List<AfterSaleProof> result) {
        AfterSaleProof afterSaleProof;
        if (!CollectionUtils.isEmpty(result) && afterSaleOrder != null) {
            if (result.size() > 1) {
                List<AfterSaleProof> collect = result.stream().filter(x -> !Objects.equals(x.getStatus(), AfterSaleOrderStatus.RE_COMMIT.getStatus()))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)) {
                    afterSaleProof = collect.get(NumberUtils.INTEGER_ZERO);
                } else {
                    afterSaleProof = result.get(NumberUtils.INTEGER_ZERO);
                }
            } else {
                afterSaleProof = result.get(NumberUtils.INTEGER_ZERO);
            }
            afterSaleOrder.setRefundType(afterSaleProof.getRefundType());
            afterSaleOrder.setApplyer(afterSaleProof.getApplyer());
            afterSaleOrder.setRecoveryNum(afterSaleProof.getRecoveryNum());
            afterSaleOrder.setHandleType(afterSaleProof.getHandleType());
            afterSaleOrder.setQuantity(afterSaleProof.getQuantity());
            afterSaleOrder.setHandler(afterSaleProof.getHandler());
            afterSaleOrder.setProofPic(afterSaleProof.getProofPic());
            afterSaleOrder.setHandleRemark(afterSaleProof.getHandleRemark());
            afterSaleOrder.setExtraRemark(afterSaleProof.getExtraRemark());
            afterSaleOrder.setAfterSaleType(afterSaleProof.getAfterSaleType());
            afterSaleOrder.setAuditer(afterSaleProof.getAuditer());
            afterSaleOrder.setHandleNum(afterSaleProof.getHandleNum());
            afterSaleOrder.setStatus(afterSaleProof.getStatus());
            afterSaleOrder.setApplyRemark(afterSaleProof.getApplyRemark());
            afterSaleOrder.setUpdatetime(afterSaleProof.getUpdatetime());
            afterSaleOrder.setAuditeRemark(afterSaleProof.getAuditeRemark());
            //判断是否为省心送退款
            if (afterSaleOrder.getDeliveryId() != null){
                DeliveryPlan deliveryPlan = deliveryPlanMapper.selectById(afterSaleOrder.getDeliveryId());
                if (deliveryPlan == null){
                    return;
                }
                OrderVO orderVO = ordersMapper.selectByOrderyNo(deliveryPlan.getOrderNo());
                if (orderVO == null){
                    return;
                }
                if (deliveryPlan.getInterceptFlag() == 1 ){
                    afterSaleOrder.setOrderDeliveryStatus(DeliveryStatusEnum.INTERCEPT.getStatus());
                }else {
                    Integer source = TmsSourceEnum.getDistOrderSource(orderVO.getType());
                    DistOrderDTO distOrderDTO = tmsDistOrderDetailFacade.queryDistOrderDetail(deliveryPlan.getOrderNo(), deliveryPlan.getContactId(),
                            deliveryPlan.getDeliveryTime(), source);
                    if (distOrderDTO != null && Objects.nonNull(distOrderDTO.getStatus())){
                        afterSaleOrder.setOrderDeliveryStatus(TmsDistOrderStatusEnum.getDistOrderStatus(distOrderDTO.getStatus()));
                    }else {
                        afterSaleOrder.setOrderDeliveryStatus(DeliveryStatusEnum.TO_BE_WIRED.getStatus());
                    }
                }

                //校验截单时间
                /*WarehouseLogisticsCenter wlc = warehouseLogisticsCenterMapper.selectByStoreNo(deliveryPlan.getOrderStoreNo());
                Date orderTime = orderVO.getOrderTime();
                SimpleDateFormat hms = new SimpleDateFormat("HH:mm:ss");
                String closeTime = wlc.getCloseTime();
                String orderFormat = hms.format(orderTime);
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                SimpleDateFormat ds = new SimpleDateFormat("yyyy-MM-dd ");
                try {
                    Date orderHms = hms.parse(orderFormat);
                    Date closeHms = hms.parse(closeTime);
                    //如果超过截单时间，日期加一天
                    if(closeHms.compareTo(orderHms) <0){
                        Calendar calendar = new GregorianCalendar();
                        calendar.setTime(orderTime);
                        calendar.add(Calendar.DATE,1); //把日期往后增加一天,整数  往后推,负数往前移动
                        orderTime=calendar.getTime();
                    }
                    Date startTime = df.parse(ds.format(orderTime) +wlc.getCloseTime());
                    Date date = new Date();
                    int i = date.compareTo(startTime);
                    if (i<0){
                        afterSaleOrder.setDeliveryStatus(DeliveryStatusEnum.NOT_YET.getStatus());
                    }
                } catch (ParseException e) {
                    e.printStackTrace();
                }*/
            }else {
                //配置原订单配送状态
                List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNoIntercept(afterSaleOrder.getOrderNo());
                if(!CollectionUtils.isEmpty(deliveryPlanVOS)){
                    DeliveryPlanVO deliveryPlanVO = deliveryPlanVOS.get(0);
                    OrderVO orderVO = ordersMapper.selectByOrderyNo(deliveryPlanVO.getOrderNo());
                    if (orderVO == null){
                        return;
                    }
                    if (deliveryPlanVO.getInterceptFlag() == 1){
                        afterSaleOrder.setOrderDeliveryStatus(DeliveryStatusEnum.INTERCEPT.getStatus());
                    }else {
                        Integer source = TmsSourceEnum.getDistOrderSource(orderVO.getType());
                        DistOrderDTO distOrderDTO = tmsDistOrderDetailFacade.queryDistOrderDetail(deliveryPlanVO.getOrderNo(), deliveryPlanVO.getContactId(),
                                deliveryPlanVO.getDeliveryTime(), source);
                        if (distOrderDTO != null && Objects.nonNull(distOrderDTO.getStatus())){
                            afterSaleOrder.setOrderDeliveryStatus(TmsDistOrderStatusEnum.getDistOrderStatus(distOrderDTO.getStatus()));
                        }else {
                            afterSaleOrder.setOrderDeliveryStatus(DeliveryStatusEnum.TO_BE_WIRED.getStatus());
                        }
                    }

                    //校验截单时间-- 改成tms查询
                    /*WarehouseLogisticsCenter wlc = warehouseLogisticsCenterMapper.selectByStoreNo(deliveryPlanVO.getOrderStoreNo());
                    Date orderTime = orderVO.getOrderTime();
                    SimpleDateFormat hms = new SimpleDateFormat("HH:mm:ss");
                    String closeTime = wlc.getCloseTime();
                    String orderFormat = hms.format(orderTime);
                    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    SimpleDateFormat ds = new SimpleDateFormat("yyyy-MM-dd ");
                    try {
                        Date orderHms = hms.parse(orderFormat);
                        Date closeHms = hms.parse(closeTime);
                        //如果超过截单时间，日期加一天
                        if(closeHms.compareTo(orderHms) <0){
                            Calendar   calendar = new GregorianCalendar();
                            calendar.setTime(orderTime);
                            calendar.add(Calendar.DATE,1); //把日期往后增加一天,整数  往后推,负数往前移动
                            orderTime=calendar.getTime();
                        }
                        Date startTime = df.parse(ds.format(orderTime) +wlc.getCloseTime());
                        Date date = new Date();
                        int i = date.compareTo(startTime);
                        if (i<0){

                            afterSaleOrder.setDeliveryStatus(DeliveryStatusEnum.NOT_YET.getStatus());
                        }
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }*/
                }else {
                    afterSaleOrder.setOrderDeliveryStatus(DeliveryStatusEnum.NOT_YET.getStatus());
                }
            }
        }
    }
}
