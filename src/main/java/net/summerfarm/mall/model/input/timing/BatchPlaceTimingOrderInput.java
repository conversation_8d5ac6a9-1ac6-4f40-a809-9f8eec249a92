package net.summerfarm.mall.model.input.timing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.validation.annotation.IsPhone;
import net.summerfarm.mall.model.domain.DeliveryPlan;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.List;

/**
 * 省心送批量下单传参
 */
@Data
@ApiModel(value = "省心送批量下单参数VO类")
public class BatchPlaceTimingOrderInput implements Serializable {

    private static final long serialVersionUID = -7095204812474330131L;

    @ApiModelProperty(value = "省心送id")
    private List<PlaceTimingOrderDetailInput> placeTimingOrderDetails;

    @ApiModelProperty(value = "门店优惠券id")
    private Integer merchantCouponId;

    @ApiModelProperty(value = "红包id")
    private Integer rpCouponId;

}
