package net.summerfarm.mall.model.input.timing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * 省心送批量下单传参
 */
@Data
@ApiModel(value = "省心送批量下单参数VO类")
public class PlaceTimingOrderDetailInput implements Serializable {

    private static final long serialVersionUID = 3597521172231351100L;

    @ApiModelProperty(value = "省心送规则id")
    private Integer timingRuleId;

    @ApiModelProperty(value = "数量")
    @Min(value = 1,message = "less.than.min")
    @Max(value = 1000000,message = "more.than.max")
    private Integer quantity;
}
