package net.summerfarm.mall.model.input.product;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;
import net.summerfarm.mall.common.util.DateUtils;
import net.xianmu.common.input.BasePageInput;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @author: <EMAIL>
 * @create: 2023/10/25
 */
@Data
public class ProductSearchInput extends BasePageInput implements Serializable {

    /**
     * 前台类目id
     */
    private Integer frontCategoryId;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * mId
     */
    private Long mId;

    /**
     * 查询pdName或sku queryStr
     */
    private String queryStr;

    /**
     * 凑单用字段（需要用于列表页搜索接口）,0 搜全部商品, 1 只搜代销不入仓商品, 2 搜非代销不入仓的商品
     * @see net.summerfarm.mall.enums.order.AddOnEnum
     */
    private Integer addOn;

    /**
     * sku集合
     */
    private List<String> skus;

    /**
     * sku查询列表（上面那个字段被大客户报价单污染了）
     */
    private List<String> skuQueryList;

    /**
     *  运营服务区域编号
     */
    private Integer areaNo;

    /**
     * 配送时间
     */
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate deliveryTime;
}
