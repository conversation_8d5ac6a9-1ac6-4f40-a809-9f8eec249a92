package net.summerfarm.mall.model.input.frequentSkuPool;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 常购清单 - 取消推荐
 * <AUTHOR>
 * @Date 2025/6/30 11:41
 * @Version 1.0
 */
@Data
public class FrequentSkuPoolCancelRecommendInput implements Serializable {


    private static final long serialVersionUID = 8989950851646033139L;
    /**
     * sku编码数组
     */
    private List<String> skuList;

}
