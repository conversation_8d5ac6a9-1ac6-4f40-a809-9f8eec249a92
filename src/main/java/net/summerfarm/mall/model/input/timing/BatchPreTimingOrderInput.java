package net.summerfarm.mall.model.input.timing;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 省心送批量下预下单 传参
 * <AUTHOR>
 * @Date 2025/7/3 11:28
 * @Version 1.0
 */
@Data
public class BatchPreTimingOrderInput implements Serializable {

    private static final long serialVersionUID = -2953497995693884791L;

    /**
     * 省心送预下单明细
     */
    private List<PreTimingOrderDetailInput> preTimingOrderDetails;
    /**
     * 红包id
     */
    private Integer rpCouponId;
    /**
     * 商户卡券id
     */
    private Integer merchantCouponId;
}
