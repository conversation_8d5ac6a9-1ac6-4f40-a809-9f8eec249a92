package net.summerfarm.mall.model.input.timing;

import lombok.Data;
import net.summerfarm.common.util.validation.annotation.IsPhone;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/7/3 11:28
 * @Version 1.0
 */
@Data
public class PreTimingOrderDetailInput implements Serializable {

    private static final long serialVersionUID = 958641936774275731L;

    /**
     * 省心送规则id
     */
    private Integer timingRuleId;

    /**
     * 数量
     */
    private Integer quantity;
}
