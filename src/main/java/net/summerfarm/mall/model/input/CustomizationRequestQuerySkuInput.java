package net.summerfarm.mall.model.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class CustomizationRequestQuerySkuInput implements Serializable {
    /**
     * spuid
     */
    @NotNull(message = "spuid不能为空")
    private Long pdId;
    /**
     * 规格
     * eg:颜色1个
     */
    @NotNull(message = "规格不能为空")
    private String weightLike;
}
