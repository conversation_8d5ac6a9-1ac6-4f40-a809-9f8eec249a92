package net.summerfarm.mall.model.input;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class CustomizationRequestEnabledInput implements Serializable {

    /**
     * 主键Id
     */
    @NotNull(message = "id不能为空")
    private Long id;
    /**
     * 3-客户通过,4-客户不通过
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 不通过原因
     */
    private String refuseReason;

}
