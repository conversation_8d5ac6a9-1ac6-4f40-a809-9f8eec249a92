package net.summerfarm.mall.model.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 定制需求保存请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@ApiModel(description = "定制需求保存请求参数")
public class CustomizationRequestInput implements Serializable {
    /**
     * spuid
     */
    @NotNull(message = "spuid不能为空")
    private Long pdId;
    /**
     * 颜色数量
     */
    @NotNull(message = "颜色数量不能为空")
    private Integer colorCount;

    /**设计参考图-客户上传**/
    private String referenceImage;

    /**logo原文件-客户上传**/
    private String logoImage;

    /**logo大小**/
    private String logoSize;

    /**样杯-客户上传**/
    private String sampleImage;

    /**客户备注**/
    private String storeRemark;

}
