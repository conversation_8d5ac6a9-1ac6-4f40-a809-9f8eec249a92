package net.summerfarm.mall.model.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 定制需求保存请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@ApiModel(description = "定制需求保存请求参数")
public class CustomizationRequestsInput implements Serializable {
    /**
     * spuid
     */
    @NotNull(message = "spuid不能为空")
    private Long pdId;


    @ApiModelProperty(value = "颜色数量")
    private Integer colorCount;

    @ApiModelProperty(value = "设计参考图-客户上传")
    @Size(max = 1024, message = "设计参考图URL长度不能超过1024个字符")
    private String referenceImage;


    @ApiModelProperty(value = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

}
