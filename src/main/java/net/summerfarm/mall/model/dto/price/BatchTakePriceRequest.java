package net.summerfarm.mall.model.dto.price;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量查询到手价请求参数
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(description = "批量查询到手价请求参数")
public class BatchTakePriceRequest {

    @ApiModelProperty(value = "门店ID", required = true, example = "12345")
    @NotNull(message = "门店ID不能为空")
    private Long mId;

    @ApiModelProperty(value = "SKU列表", required = true, notes = "最多支持20个SKU")
    @NotEmpty(message = "SKU列表不能为空")
    @Size(max = 20, message = "SKU列表最多支持20个")
    private List<BatchTakePriceItem> skuList;

    @ApiModelProperty(value = "加密签名", required = true, notes = "用于验证请求合法性")
    @NotEmpty(message = "签名不能为空")
    private String signature;

    @ApiModelProperty(value = "是否获取最低阶梯价", example = "1")
    private Integer minLadderPrice;

    

    /**
     * SKU项目
     */
    @Data
    @ApiModel(description = "SKU项目")
    public static class BatchTakePriceItem {
        
        @ApiModelProperty(value = "SKU编码", required = true, example = "863080734120")
        @NotEmpty(message = "SKU编码不能为空")
        private String sku;

        @ApiModelProperty(value = "数量", required = true, example = "2")
        @NotNull(message = "数量不能为空")
        private Integer quantity;
    }
}
