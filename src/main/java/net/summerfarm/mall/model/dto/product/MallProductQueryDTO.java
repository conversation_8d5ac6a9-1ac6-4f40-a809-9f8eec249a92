package net.summerfarm.mall.model.dto.product;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 商城商品查询 - 查询条件 【特殊定制条件】
 * <AUTHOR>
 * @Date 2025/4/28 15:16
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MallProductQueryDTO {

    /**
     * mId
     */
    private Long mId;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;
}
