package net.summerfarm.mall.model.dto.price;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 批量查询到手价响应结果
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(description = "批量查询到手价响应结果")
public class BatchTakePriceResponse {

    @ApiModelProperty(value = "门店ID")
    private Long mId;

    @ApiModelProperty(value = "门店名称")
    private String merchantName;

    @ApiModelProperty(value = "是否大客户")
    private Boolean isMajorMerchant;

    @ApiModelProperty(value = "业务线：0=鲜沐;1=pop")
    private Integer businessLine;

    @ApiModelProperty(value = "SKU价格列表")
    private List<SkuPriceInfo> skuPrices;

    @ApiModelProperty(value = "查询时间戳")
    private Long queryTime;

    /**
     * SKU价格信息
     */
    @Data
    @ApiModel(description = "SKU价格信息")
    public static class SkuPriceInfo {
        
        @ApiModelProperty(value = "SKU编码")
        private String sku;

        @ApiModelProperty(value = "数量")
        private Integer quantity;

        @ApiModelProperty(value = "原价")
        private BigDecimal originalPrice;

        @ApiModelProperty(value = "到手价")
        private BigDecimal takeActualPrice;

        @ApiModelProperty(value = "优惠金额")
        private BigDecimal discountAmount;

        @ApiModelProperty(value = "是否有效", notes = "false表示SKU不存在或不可购买")
        private Boolean isValid;

        @ApiModelProperty(value = "错误信息", notes = "当isValid为false时的错误描述")
        private String errorMessage;

        @ApiModelProperty(value = "商品名称")
        private String productName;

        @ApiModelProperty(value = "规格")
        private String specification;

        @ApiModelProperty(value = "单位")
        private String unit;
    }
}
