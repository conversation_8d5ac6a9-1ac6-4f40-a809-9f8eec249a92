package net.summerfarm.mall.model.dto.order;

import lombok.Data;
import net.summerfarm.mall.model.domain.Area;

import java.io.Serializable;

/**
 * 用户上下文参数类
 * 用于替代从RequestHolder获取的用户登录态信息，使接口可以独立测试
 * 
 * <AUTHOR>
 */
@Data
public class UserContextParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户ID
     */
    private Long mId;

    /**
     * 账户ID
     */
    private Long accountId;

    /**
     * 商户名称
     */
    private String mname;

    /**
     * 是否大客户
     */
    private Boolean majorMerchant;

    /**
     * 大客户管理员ID
     */
    private Integer adminId;

    /**
     * 合作模式：1、账期 2、现结
     */
    private Integer direct;

    /**
     * 展示模式：1、定量 2、全量
     */
    private Integer skuShow;

    /**
     * 微信OpenID
     */
    private String openId;

    /**
     * 服务区状态：1服务区内 2服务区外
     */
    private Integer server;

    /**
     * 业务线：0=鲜沐;1=pop
     */
    private Integer businessLine;

    /**
     * 用户区域信息
     */
    private Area area;

    /**
     * 客户规模
     */
    private String size;

    /**
     * 是否代下单：0、否 1、是
     */
    private Integer helpOrder;

    /**
     * 超时加单：0、否 1、是
     */
    private Integer outTimes;

    /**
     * 联系人ID
     */
    private Long contactId;

    
}
