package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 定制需求表
 * 
 * <AUTHOR>
 * @LocalDateTime 2025-01-07
 */
@Data
@ApiModel(description = "定制需求表")
public class CustomizationRequest implements Serializable {

    @ApiModelProperty(value = "主键Id")
    private Long id;

    @ApiModelProperty(value = "客户名称")
    private String storeName;

    @ApiModelProperty(value = "门店设计账号")
    private String accountName;

    @ApiModelProperty(value = "客户id")
    private Long mId;

    @ApiModelProperty(value = "第一笔订单编号")
    private String initiateOrderNo;

    @ApiModelProperty(value = "颜色数量")
    private Integer colorCount;

    @ApiModelProperty(value = "提交日期-下单时间")
    private LocalDateTime designSubmitTime;

    @ApiModelProperty(value = "客户拒绝时间")
    private LocalDateTime refuseTime;

    @ApiModelProperty(value = "关闭时间")
    private LocalDateTime closeTime;

    @ApiModelProperty(value = "客户通过时间")
    private LocalDateTime agreeTime;

    @ApiModelProperty(value = "设计参考图-客户上传")
    private String referenceImage;

    @ApiModelProperty(value = "log原文件-客户上传")
    private String logoImage;

    @ApiModelProperty(value = "样杯-客户上传")
    private String sampleImage;

    @ApiModelProperty(value = "设计效果图-设计师上传")
    private String designImage;

    @ApiModelProperty(value = "状态，0-定制需求待生成(订单未支付),1-待设计师设计(订单已支付),2-设计师发起确认,3-客户通过,4-客户不通过,5-关闭(订单取消/退款)")
    private Integer status;

    @ApiModelProperty(value = "不通过原因")
    private String refuseReason;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "沟通记录 json格式")
    private String communicationNotes;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime upLocalDateTimeTime;
}
