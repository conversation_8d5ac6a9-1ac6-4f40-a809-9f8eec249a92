package net.summerfarm.mall.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 定制需求sku关联表
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@ApiModel(description = "定制需求sku关联表")
public class CustomizationRequestSkuMapping implements Serializable {

    @ApiModelProperty(value = "主键Id")
    private Long id;

    @ApiModelProperty(value = "定制需求id")
    private Long customizationRequestId;

    @ApiModelProperty(value = "商品名称及规格")
    private String productName;

    @ApiModelProperty(value = "sku")
    private String sku;

    @ApiModelProperty(value = "模版sku")
    private String sourceSku;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;
}
