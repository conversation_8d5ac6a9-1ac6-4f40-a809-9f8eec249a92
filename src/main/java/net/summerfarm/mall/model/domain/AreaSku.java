package net.summerfarm.mall.model.domain;

import net.summerfarm.mall.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class AreaSku {
    private Integer id;

    private String sku;

    private Integer areaNo;

    private Integer quantity;

    private BigDecimal originalPrice;

    private BigDecimal price;

    private String ladderPrice;

    private Boolean onSale;

    private Integer priority;

    private LocalDateTime updateTime;

    private LocalDateTime addTime;

    private Integer pdPriority;

    private Boolean share;

    private Integer cornerStatus;

    private Integer openSale;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime openSaleTime;

    private Integer closeSale;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime closeSaleTime;

    public Integer getSalesMode() {
        return salesMode;
    }

    public void setSalesMode(Integer salesMode) {
        this.salesMode = salesMode;
    }

    public Integer getLimitedQuantity() {
        return limitedQuantity;
    }

    public void setLimitedQuantity(Integer limitedQuantity) {
        this.limitedQuantity = limitedQuantity;
    }

    private Integer salesMode;

    private Integer limitedQuantity;

    private Integer mType;

    public Boolean getShare() {
        return share;
    }

    public void setShare(Boolean share) {
        this.share = share;
    }

    public Integer getPdPriority() {
        return pdPriority;
    }

    public void setPdPriority(Integer pdPriority) {
        this.pdPriority = pdPriority;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Boolean getOnSale() {
        return onSale;
    }

    public void setOnSale(Boolean onSale) {
        this.onSale = onSale;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public LocalDateTime getAddTime() {
        return addTime;
    }

    public void setAddTime(LocalDateTime addTime) {
        this.addTime = addTime;
    }

    public Integer getCornerStatus() {
        return cornerStatus;
    }

    public void setCornerStatus(Integer cornerStatus) {
        this.cornerStatus = cornerStatus;
    }

    public Integer getOpenSale() {
        return openSale;
    }

    public void setOpenSale(Integer openSale) {
        this.openSale = openSale;
    }

    public LocalDateTime getOpenSaleTime() {
        return openSaleTime;
    }

    public void setOpenSaleTime(LocalDateTime openSaleTime) {
        this.openSaleTime = openSaleTime;
    }

    public Integer getCloseSale() {
        return closeSale;
    }

    public void setCloseSale(Integer closeSale) {
        this.closeSale = closeSale;
    }

    public LocalDateTime getCloseSaleTime() {
        return closeSaleTime;
    }

    public void setCloseSaleTime(LocalDateTime closeSaleTime) {
        this.closeSaleTime = closeSaleTime;
    }

    public String getLadderPrice() {
        return ladderPrice;
    }

    public void setLadderPrice(String ladderPrice) {
        this.ladderPrice = ladderPrice;
    }

    public Integer getmType() {
        return mType;
    }

    public void setmType(Integer mType) {
        this.mType = mType;
    }

    @Override
    public String toString() {
        return "AreaSku{" +
                "id=" + id +
                ", sku='" + sku + '\'' +
                ", areaNo=" + areaNo +
                ", quantity=" + quantity +
                ", originalPrice=" + originalPrice +
                ", price=" + price +
                ", ladderPrice='" + ladderPrice + '\'' +
                ", onSale=" + onSale +
                ", priority=" + priority +
                ", updateTime=" + updateTime +
                ", addTime=" + addTime +
                ", pdPriority=" + pdPriority +
                ", share=" + share +
                ", cornerStatus=" + cornerStatus +
                ", openSale=" + openSale +
                ", openSaleTime=" + openSaleTime +
                ", closeSale=" + closeSale +
                ", closeSaleTime=" + closeSaleTime +
                ", salesMode=" + salesMode +
                ", limitedQuantity=" + limitedQuantity +
                ", mType=" + mType +
                '}';
    }
}