package net.summerfarm.mall.model.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-06-27 14:30:21
 * @version 1.0
 *
 */
@Data
public class OrderItemExtra {
	/**
	 * id
	 */
	private Long id;

	/**
	 * 订单项id
	 */
	private Long orderItemId;

	/**
	 * 买手id（xianmudb.pop_buyer表id）
	 */
	private Long buyerId;

	/**
	 * 买手所对应的admin_id(admin表id)
	 */
	private Long buyerAdminId;

	/**
	 * 买手名称(花名)
	 */
	private String buyerName;

	/**
	 * 供应商id
	 */
	private Long supplierId;

	/**
	 * 平台佣金比例。举例：如果佣金比例是52.54%，那存的数据就是52.54
	 */
	private BigDecimal platformCommissionRatio;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	/**
	 * sku成本
	 */
	private BigDecimal cost;

	/**
	 * 提货毛重（类似于规格）
	 */
	private BigDecimal specifications;

	/**
	 * 供应商报价类型：0-默认类型，1-按斤报价，2-按件报价
	 * @see net.summerfarm.mall.enums.InventoryQuoteTypeEnum
	 */
	private Integer quoteType;

	/**
	 * 自动补差售后量阈值，自动补差售后量阈值，单位等同于售后单位
	 */
	private Integer minAutoAfterSaleThreshold;

	/**
	 * 自动售后标识
	 * @see net.summerfarm.mall.enums.AutoAfterSaleFlag
	 */
	private Integer autoAfterSaleFlag;

	/**
	 * 自动售后退款金额（元）
	 */
	private BigDecimal autoAfterSaleAmount;

	/**
	 * 当前订单该sku的总到货重量（单位：公斤）
	 */
	private BigDecimal skuTotalWeight;

	/**
	 * 自动售后退款数量
	 */
	private BigDecimal autoAfterSaleQuantity;

	/**
	 * 整件成本（单位：元）
	 */
	private BigDecimal totalUnitCost;

	/**
	 * 分销商加价金额（单位：元）
	 */
	private BigDecimal markupAmount;

	/**
	 * 对应的代下单订单itemId （pop订单记录）
	 */
	private Long helpOrderItemId;

}