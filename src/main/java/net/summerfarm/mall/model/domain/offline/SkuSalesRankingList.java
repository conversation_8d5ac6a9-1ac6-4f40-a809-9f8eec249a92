package net.summerfarm.mall.model.domain.offline;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SkuSalesRankingList implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 日期标签，格式为YYYYMMDD
     */
    private String dayTag;

    /**
     * 客户类型（鲜沐）；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他
     */
    private String merchantType;

    /**
     * sku销售排行榜（top20）
     */
    private String skuList;

    private static final long serialVersionUID = 1L;
}