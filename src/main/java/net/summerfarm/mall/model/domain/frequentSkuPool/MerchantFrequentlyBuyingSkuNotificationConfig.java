package net.summerfarm.mall.model.domain.frequentSkuPool;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class MerchantFrequentlyBuyingSkuNotificationConfig implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 门店id
     */
    private Long mId;

    /**
     * 特价提醒 0：不提醒  1：提醒（默认）
     */
    private Integer specialOffer;

    /**
     * 到货提醒 0：不提醒  1：提醒（默认）
     */
    private Integer goodsArrived;

    private static final long serialVersionUID = 1L;
}