package net.summerfarm.mall.model.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import net.summerfarm.mall.model.domain.RechargeRecord;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-08-29
 * @description
 */
@Data
@ApiModel(description = "余额变动记录model")
public class RechargeRecordVO extends RechargeRecord {
    private String recorder;

    private LocalDateTime queryStartTime;

    private LocalDateTime queryEndTime;
}
