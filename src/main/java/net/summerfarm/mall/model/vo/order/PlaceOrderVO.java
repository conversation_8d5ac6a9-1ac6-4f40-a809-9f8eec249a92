package net.summerfarm.mall.model.vo.order;

import java.time.LocalDate;
import lombok.Data;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.model.domain.AreaSku;
import net.summerfarm.mall.model.domain.Contact;
import net.summerfarm.mall.model.domain.Recharge;
import net.summerfarm.mall.model.domain.SkuDiscount;
import net.summerfarm.mall.model.domain.Trolley;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDetailDTO;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.input.order.OrderItemInput;
import net.summerfarm.mall.model.vo.AreaSkuVO;
import net.summerfarm.mall.model.vo.CartTimingVO;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryRes;

/**
 * @Package: net.summerfarm.mall.model.vo
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/4/14
 */
@Data
public class PlaceOrderVO {

    /**
     * 立即下单数据
     */
    private List<Trolley> orderNow;

    /**
     * 选中下单的商品数据
     */
    private List<OrderItemInput> orderItemList;

    /**
     * 下单账号
     */
    private Long accountId;

    /**
     * 配送地址
     */
    private Long contactId;

    /**
     * 店铺名
     */
    private String mname;

    /**
     * 下单用户openId
     */
    private String openId;

    /**
     * 是否超时加单
     */
    private Integer outTimes;

    /**
     * 精准送
     */
    private String timeFrameName;

    /**
     * 使用优惠券
     */
    private Set<Integer> merchantCouponId;

    /**
     * 使用的优惠券id, 0未定义,1普通商品优惠券,2普通运费优惠券,3精准送优惠券,4红包,5商品兑换券
     */
    private Map<Integer, Integer> usedCouponIds;

    /**
     * 是否代下单
     */
    private Integer helpOrder;

    /**
     * 客户size
     */
    private String size;

    /**
     * 代下单操作人ID
     */
    private Integer operateId;

    /**
     * 代下单用：订单编号
     */
    private String orderNo;

    /**
     * 代下单用：mId
     */
    private Long mId;

    /**
     * 代下单用：下单备注
     */
    private String remark;

    /**
     * 下单流程中使用：用户区域
     */
    private Area area;

    /**
     * 下单流程中使用：是否是大客户
     */
    private Boolean majorMerchant;

    /**
     * 大客户用：大客户adminId
     */
    private Integer adminId;

    /**
     * 大客户用：合作模式：1、账期 2、现结
     */
    private Integer direct;

    /**
     * 大客户用：是否展示：1、定量 2、全量
     */
    private Integer skuShow;

    /**
     * 大客户用：是否在服务区：1服务区内 2服务区外
     */
    private Integer server;

    /**
     * 是否提前截单：0、否 1、是
     */
    private Integer closeOrderType;

    /**
     * 提前接单时间
     */
    private String closeOrderTime;

    /**
     * 地址信息
     */
    private Contact contact;

    /**
     * 拓展购买活动ID
     */
    private Long expandActivityId;

    /**
     * 换购活动范围id
     */
    private Long bizId;

    /**
     * 下单支付类型
     */
    private Integer payType;

    /**
     * 充值流水
     */
    private List<Recharge> recharges;

    /**
     * 代下单申请人
     */
    private Integer applicant;

    /**
     * 代下单申请人
     */
    private String updater;

    /**
     * 此标记为true代表获取明细并且不均摊优惠,false则均摊优惠用于预下单和下单
     */
    private boolean takePriceFlag;

    public boolean getTakePriceFlag() {
        return takePriceFlag;
    }

    public void setTakePriceFlag(Boolean takePriceFlag) {
        this.takePriceFlag = Optional.ofNullable(takePriceFlag).orElse(false);
    }

    /**
     * 使用优惠券是否需要领取 0 表示领取过，1表示需要领取
     */
    private Integer effectiveNum;

    /**
     * 优惠券ID-不包含红包-到手价计算
     */
    private Integer couponId;

    /**
     * 发放设置ID
     */
    private Integer couponSenderId;

    /**
     * 判断是否为下单计算 0为到手价或者预下单计算卡券，1为下单计算使用具体前端给的
     */
    private Integer isTakePrice;

    /**
     * 省心送获取到手价 0非省心送，1省心送
     */
    private Integer timingSkuPrice;

    /**
     * 省心送id
     */
    private Integer timingRuleId;

    /**
     * 搭配购
     */
    private Map<String, SkuDiscount> skuDiscountMap;

    /**
     * 搭配购是否可用
     */
    private Map<String, Boolean> collocationUsableMap;

    /**
     * areaSkuMap
     */
    private Map<String, AreaSku> areaSkuMap;

    /**
     * 活动价信息map
     */
    private Map<String, ActivitySkuDetailDTO> activitySkuDetailMap;

    /**
     * 库存、履约时效信息
     */
    private Map<String, AreaStoreQueryRes> storeMap;

    /**
     * 按履约时效的拆单信息
     */
    private Map<LocalDate, OrderResultVO> subOrderResultMap;

    /**
     * 按履约时效的拆单信息(提交订单的时候比较子订单信息使用)
     */
    private Map<LocalDate, OrderResultVO> subPlaceOrderMap;

    /**
     * 是否子包裹第一条数据
     */
    private Boolean firstFlag;

    /**
     * 是否获取最低价格的阶梯价展示，1表示获取，0或者null表示不获取（为空或者为0走不获取最低价逻辑）
     */
    private Integer minLadderPrice;

    /**
     * 业务线:0=鲜沐;1=pop
     */
    private Integer businessLine;

    /**
     * 线上兼容 -- 是否获取老的配送规则 1表示不获取（改版后默认都是走新的规则），0或者null表示获取
     */
    private Integer deliveryRulesType;

    /**
     * 销售主体名称
     */
    private String sellingEntityName;

    /**
     * pop下单来源子订单号 （pop订单自动代下单使用）
     */
    private String sourcePopSubOrderNo;

    /**
     * pop下单来源子订单号 （pop订单自动代下单使用）
     */
    private Map<String, String> popSkuMapping;

    /**
     * 指定配送时间 （pop订单自动代下单使用）
     */
    private LocalDate assignDeliveryDate;

}
