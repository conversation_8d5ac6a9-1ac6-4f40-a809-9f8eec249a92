package net.summerfarm.mall.model.vo;

import lombok.Data;
import net.summerfarm.mall.model.domain.OrderPreferential;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Data
public class AfterSaleSimplifyOrderVO implements Serializable {

    private String orderNo;

    private Date orderTime;

    private Integer type;

    //总价
    private BigDecimal total;

    private Long accountId;

    private String address;

    private BigDecimal coupon=BigDecimal.ZERO;

    private BigDecimal rpCoupon = BigDecimal.ZERO;

    private BigDecimal deliveryCoupon=BigDecimal.ZERO;

    private LocalDate deliveryTime;

    /**
     * 原价(应付价)
     */
    private BigDecimal originPrice;

    private List<AfterSaleSimplifyItemVO> orderItems;

    private String mName;

    private List<OrderPreferential> preferentials;


}
