package net.summerfarm.mall.model.vo.timing;

import lombok.Data;
import net.summerfarm.mall.model.vo.TimingProductVO;

import java.math.BigDecimal;

/**
 * 返回的购买的省心送商品确认页面信息（合并预下单）
 * @Date: 2025-07-07
 * @Author: zach
 */
@Data
public class TimingPreOrderSingleVO {
    /**
     * 购买商品的件数
     */
    private Integer quantity;
    /**
     * 省心送规则里的单价
     */
    private BigDecimal price;

    /**
     * 省心送购买的对应的商品的VO
     */
    private TimingProductVO timingProductVO;

    /**
     * 活动优惠金额
     */
    private BigDecimal activityFee;

}


