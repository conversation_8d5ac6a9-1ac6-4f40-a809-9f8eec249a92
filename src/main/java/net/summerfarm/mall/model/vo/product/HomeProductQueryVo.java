package net.summerfarm.mall.model.vo.product;

import lombok.Data;
import net.summerfarm.mall.model.vo.MerchantSubject;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * 首页商品查询参数
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2022-07-26
 */
@Data
public class HomeProductQueryVo {

    /**
     * 当前登录用户信息
     */
    private MerchantSubject merchantSubject;

    /**
     * 商品编号列表
     */
    private List<String> skuList;

    /**
     * sku查询列表（上面那个字段被大客户报价单污染了）
     */
    private List<String> skuQueryList;

    /**
     * 不展示商品编号列表
     */
    private Set<String> notShowSkuList;

    /**
     * 大客户专享sku（全量查询时使用）
     */
    private List<String> mSkuList;

    private Integer frontCategoryId;

    /**
     * 查询字符串
     */
    private String queryStr;

    /**
     * 是否展示
     */
    private Integer show;

    private Long mId;

    private String pdName;
    private Integer adminId;
    private Integer direct;

    /**
     * 商品二级性质
     */
    private List<Integer> subTypeList;

    // 运营服务区
    private Integer areaNo;

    /**
     * 指定配送日期
     */
    private LocalDate deliveryTime;
}
