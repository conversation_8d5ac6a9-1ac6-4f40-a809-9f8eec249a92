package net.summerfarm.mall.model.vo.price;

import lombok.Data;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class SkuPreferentialVO {

    /**
     * 优惠总金额
     */
    private BigDecimal amount;

    /**
     * 优惠类型:0活动，2满减，3阶梯价，4黄金卡，9优惠券，13红包，16-直播专享价，17-直播、商城同享价 18兑换券
     */
    private Integer type;

    /**
     * 优惠类型细化-满减类型：1、阶梯满减 2、每满元减 3、每满件减
     */
    private Integer refinementType;

    /**
     * 展示使用：优惠等级，比如类型为满减-满100-10，这里为100
     */
    private BigDecimal ruleLevel;

    /**
     * 展示使用：优惠金额，比如类型为满减-满100-10，这里为10
     */
    private BigDecimal value;


    public SkuPreferentialVO(OrderPreferentialTypeEnum typeEnum, BigDecimal amount){
        this.type = typeEnum.ordinal();
        this.amount = amount;
    }

    public SkuPreferentialVO(Integer type, BigDecimal amount){
        this.type = type;
        this.amount = amount;
    }

}
