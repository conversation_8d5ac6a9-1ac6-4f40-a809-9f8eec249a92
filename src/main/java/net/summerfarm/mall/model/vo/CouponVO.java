package net.summerfarm.mall.model.vo;

import net.summerfarm.mall.model.domain.Coupon;

/**
 * Created by wjd on 2018/9/19.
 */
public class CouponVO extends Coupon {

    private Integer marketRuleId;

    private Integer ruleLevel;

    public CouponVO(Coupon coupon) {
        this.setId(coupon.getId());
        this.setName(coupon.getName());
        this.setCode(coupon.getCode());
        this.setMoney(coupon.getMoney());
        this.setThreshold(coupon.getThreshold());
        this.setType(coupon.getType());
        this.setVaildDate(coupon.getVaildDate());
        this.setVaildTime(coupon.getVaildTime());
        this.setNewHand(coupon.getNewHand());
        this.setReamrk(coupon.getReamrk());
        this.setAddTime(coupon.getAddTime());
        this.setAgioType(coupon.getAgioType());
        this.setStatus(coupon.getStatus());
        this.setGrouping(coupon.getGrouping());
        this.setSku(coupon.getSku());
        this.setCategoryId(coupon.getCategoryId());
        this.setActivityScope(coupon.getActivityScope());
    }

    public Integer getMarketRuleId() {
        return marketRuleId;
    }

    public void setMarketRuleId(Integer marketRuleId) {
        this.marketRuleId = marketRuleId;
    }

    public Integer getRuleLevel() {
        return ruleLevel;
    }

    public void setRuleLevel(Integer ruleLevel) {
        this.ruleLevel = ruleLevel;
    }
}
