package net.summerfarm.mall.model.vo.frequentSkuPool;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 在购物车中的常购商品 vo
 * <AUTHOR>
 * @Date 2025/7/1 10:26
 * @Version 1.0
 */
@Data
@AllArgsConstructor
public class FrequentSkuInShoppingCartVO implements Serializable {
    private static final long serialVersionUID = 8214733550463231624L;

    /**
     * 在购物车中的常购商品sku编码数组
     */
    private List<String> skuList;

    /**
     * 这些sku的总数量
     */
    private Integer totalQuantity;

    public static FrequentSkuInShoppingCartVO empty() {
        return new FrequentSkuInShoppingCartVO(Collections.emptyList(), 0);
    }
}
