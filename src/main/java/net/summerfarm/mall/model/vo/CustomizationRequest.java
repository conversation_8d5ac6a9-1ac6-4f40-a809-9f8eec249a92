package net.summerfarm.mall.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 定制需求表
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@ApiModel(description = "定制需求表")
public class CustomizationRequestsVO implements Serializable {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 商品名称及规格
     */
    private String productName;

    @ApiModelProperty(value = "sku")
    private String sku;

    @ApiModelProperty(value = "模版sku")
    @NotNull(message = "模版sku不能为空", groups = {Add.class})
    @Size(max = 50, message = "模版sku长度不能超过50个字符", groups = {Add.class, Update.class})
    private String sourceSku;

    @ApiModelProperty(value = "客户名称")
    @NotNull(message = "客户名称不能为空", groups = {Add.class})
    @Size(max = 255, message = "客户名称长度不能超过255个字符", groups = {Add.class, Update.class})
    private String storeName;

    @ApiModelProperty(value = "门店设计账号")
    @Size(max = 255, message = "门店设计账号长度不能超过255个字符", groups = {Add.class, Update.class})
    private String accountName;

    @ApiModelProperty(value = "客户id")
    @NotNull(message = "客户id不能为空", groups = {Add.class})
    private Long mId;

    @ApiModelProperty(value = "第一笔订单编号")
    @NotNull(message = "第一笔订单编号不能为空", groups = {Add.class})
    @Size(max = 50, message = "第一笔订单编号长度不能超过50个字符", groups = {Add.class, Update.class})
    private String initiateOrderNo;

    @ApiModelProperty(value = "颜色数量")
    private Integer colorCount;

    @ApiModelProperty(value = "提交日期-下单时间")
    private Date designSubmitTime;

    @ApiModelProperty(value = "客户拒绝时间")
    private Date refuseTime;

    @ApiModelProperty(value = "关闭时间")
    private Date closeTime;

    @ApiModelProperty(value = "客户通过时间")
    private Date agreeTime;

    @ApiModelProperty(value = "设计参考图-客户上传")
    private String referenceImage;

    @ApiModelProperty(value = "设计效果图-设计师上传")
    private String designImage;

    /**
     * 状态，0-定制需求待生成(订单未支付),1-待设计师设计(订单已支付),2-设计师发起确认,3-客户通过,4-客户不通过,5-关闭(订单取消/退款)")
     */
    private Integer status;

    @ApiModelProperty(value = "不通过原因")
    @Size(max = 500, message = "不通过原因长度不能超过500个字符", groups = {Add.class, Update.class})
    private String refuseReason;

    @ApiModelProperty(value = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符", groups = {Add.class, Update.class})
    private String remark;

}
