package net.summerfarm.mall.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 定制需求返回对象
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@ApiModel(description = "定制需求返回对象")
public class CustomizationRequestVO implements Serializable {

    @ApiModelProperty(value = "主键Id")
    private Long id;

    @ApiModelProperty(value = "商品名称及规格")
    private String productName;

    @ApiModelProperty(value = "sku")
    private String sku;

    @ApiModelProperty(value = "模版sku")
    private String sourceSku;

    @ApiModelProperty(value = "客户名称")
    private String storeName;

    @ApiModelProperty(value = "门店设计账号")
    private String accountName;

    @ApiModelProperty(value = "客户id")
    private Long mId;

    @ApiModelProperty(value = "第一笔订单编号")
    private String initiateOrderNo;

    @ApiModelProperty(value = "颜色数量")
    private Integer colorCount;

    @ApiModelProperty(value = "提交日期-下单时间")
    private Date designSubmitTime;

    @ApiModelProperty(value = "客户拒绝时间")
    private Date refuseTime;

    @ApiModelProperty(value = "关闭时间")
    private Date closeTime;

    @ApiModelProperty(value = "客户通过时间")
    private Date agreeTime;

    @ApiModelProperty(value = "设计参考图-客户上传")
    private String referenceImage;

    @ApiModelProperty(value = "设计效果图-设计师上传")
    private String designImage;

    @ApiModelProperty(value = "状态码")
    private Integer status;

    @ApiModelProperty(value = "状态描述")
    private String statusDescription;

    @ApiModelProperty(value = "不通过原因")
    private String refuseReason;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "沟通记录 json格式")
    private String communicationNotes;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
