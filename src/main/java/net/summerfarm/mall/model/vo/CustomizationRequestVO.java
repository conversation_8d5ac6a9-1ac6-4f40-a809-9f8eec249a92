package net.summerfarm.mall.model.vo;
import lombok.Data;

import java.io.Serializable;

/**
 * 定制需求返回对象
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
public class CustomizationRequestVO implements Serializable {

    /**主键Id**/
    private Long id;

    /**颜色数量**/
    private Integer colorCount;

    /**设计参考图-客户上传**/
    private String referenceImage;

    /**设计效果图-设计师上传**/
    private String designImage;

    /**杯样-客户上传**/
    private String sampleImage;

    /**状态:0-定制需求待生成(订单未支付),1-待设计师设计(订单已支付),2-设计师发起确认,3-客户通过,4-客户不通过,5-关闭(订单取消/退款)**/
    private Integer status;

    /**客户备注/门店备注**/
    private String storeRemark;

}
