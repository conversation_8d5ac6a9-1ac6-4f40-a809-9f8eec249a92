package net.summerfarm.mall.model.vo.timing;

import lombok.Data;
import net.summerfarm.mall.model.vo.neworder.FloorPriceFailVO;
import net.summerfarm.mall.model.vo.neworder.OrderItemErrorVO;
import net.summerfarm.mall.model.vo.neworder.SpuErrorVO;
import net.summerfarm.mall.model.vo.neworder.SubOrderFailVO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 省心送批量下单返回参数
 * @author: zhangchi
 * @create: 2025-07-08
 */
@Data
public class PlaceTimingOrderResultVO implements Serializable {

    private static final long serialVersionUID = 1131075257649842289L;
    /**
     * 主订单编号
     */
    private String masterOrderNo;

    /**
     * 子订单号
     */
    private List<String> subOrderNos;


}
