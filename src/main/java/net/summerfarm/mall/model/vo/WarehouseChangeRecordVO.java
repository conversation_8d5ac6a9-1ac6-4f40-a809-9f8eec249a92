package net.summerfarm.mall.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.mall.model.domain.WarehouseChangeRecord;

@Data
public class WarehouseChangeRecordVO extends WarehouseChangeRecord {

    @ApiModelProperty(value = "数量")
    private Integer quantity;

    public WarehouseChangeRecordVO() {

    }

    public WarehouseChangeRecordVO(Integer warehouseNo,Integer storeNo, String sku, String adminName, String typeName, Integer quantity) {
        this.quantity=quantity;
        super.setWarehouseNo(warehouseNo);
        super.setStoreNo(storeNo);
        super.setSku(sku);
        super.setRecorder(adminName);
        super.setTypeName(typeName);
    }
}