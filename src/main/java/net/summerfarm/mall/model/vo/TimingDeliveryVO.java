package net.summerfarm.mall.model.vo;

import java.math.BigDecimal;
import lombok.Data;
import net.summerfarm.mall.model.domain.DeliveryPlan;
import net.summerfarm.mall.model.domain.OrderItem;
import net.summerfarm.mall.model.domain.TimingOrder;
import net.summerfarm.mall.common.util.DateUtils;

import java.time.LocalDateTime;

/**
 * @Package: net.summerfarm.mall.model.vo
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/4/11
 */
@Data
public class TimingDeliveryVO {

    private String orderNo;

    //商品名称
    private String pdName;

    //sku
    private String sku;

    //商品图片
    private String picPath;

    //购买总数量
    private Integer quantity;

    //已配送数量
    private Integer deliveredQuantity;

    //未配送数量
    private Integer waitDeliveryQuantity;

    //基础配送单位
    private Integer deliveryUnit;

    //单次配送数量上限
    private Integer deliveryUpperLimit;

    //未配送次数
    private Integer deliveryTimes;

    //已配送次数
    private Integer deliveredTimes;

    private Integer totalDeliveryTimes;

    //最近配送计划
    private DeliveryPlan nextDeliveryPlan;

    private LocalDateTime deliveryStartTime;

    private LocalDateTime deliveryEndTime;

    //库存
    private Integer stockQuantity;

    private Short orderStatus;

    //已经售后数量
    private Integer afterQuantity;

    private Integer orderSaleType;

    private LocalDateTime orderTime;

    /**
     * 订单金额
     */
    private BigDecimal totalPrice;

    /**
     * 子账号id
     */
    private Long accountId;

    /**
     * 待设置配送计划的数量
     */
    private Integer waitSetPlanQuantity;

    //购买总数量
    private Integer amount;

    public TimingDeliveryVO(){}

    public TimingDeliveryVO(String orderNo) {
        this.orderNo = orderNo;
    }
    public TimingDeliveryVO(String orderNo, Short orderStatus) {
        this.orderNo = orderNo;
        this.orderStatus = orderStatus;
    }

    public void init(OrderItem orderItem) {
        if (null == orderItem) {
            return;
        }
        this.sku = orderItem.getSku();
        this.quantity = orderItem.getAmount();
        this.amount = orderItem.getAmount();
        this.pdName = orderItem.getPdName();
        this.waitDeliveryQuantity = orderItem.getAmount();
        this.picPath = orderItem.getPicturePath();
    }

    public void init(TimingOrder timingOrder) {
        this.totalDeliveryTimes = timingOrder.getDiscountDeliveryTimes();
        this.deliveryUpperLimit = timingOrder.getDeliveryUpperLimit();
        this.deliveryStartTime = DateUtils.date2LocalDateTime(timingOrder.getDeliveryStartTime());
        this.deliveryEndTime = DateUtils.date2LocalDateTime(timingOrder.getDeliveryEndTime());
        this.deliveredTimes = 0;
        this.deliveryUnit = timingOrder.getDeliveryUnit();
        this.deliveryTimes = timingOrder.getDiscountDeliveryTimes();
        this.deliveredQuantity = 0;
    }

//    public String getOrderNo() {
//        return orderNo;
//    }
//
//    public void setOrderNo(String orderNo) {
//        this.orderNo = orderNo;
//    }
//
//    public String getPicPath() {
//        return picPath;
//    }
//
//    public void setPicPath(String picPath) {
//        this.picPath = picPath;
//    }
//
//    public Integer getQuantity() {
//        return quantity;
//    }
//
//    public void setQuantity(Integer quantity) {
//        this.quantity = quantity;
//    }
//
//    public Integer getDeliveredQuantity() {
//        return deliveredQuantity;
//    }
//
//    public void setDeliveredQuantity(Integer deliveredQuantity) {
//        this.deliveredQuantity = deliveredQuantity;
//    }
//
//    public Integer getWaitDeliveryQuantity() {
//        return waitDeliveryQuantity;
//    }
//
//    public void setWaitDeliveryQuantity(Integer waitDeliveryQuantity) {
//        this.waitDeliveryQuantity = waitDeliveryQuantity;
//    }
//
//    public Integer getDeliveryUnit() {
//        return deliveryUnit;
//    }
//
//    public void setDeliveryUnit(Integer deliveryUnit) {
//        this.deliveryUnit = deliveryUnit;
//    }
//
//    public Integer getDeliveryTimes() {
//        return deliveryTimes;
//    }
//
//    public void setDeliveryTimes(Integer deliveryTimes) {
//        this.deliveryTimes = deliveryTimes;
//    }
//
//    public Integer getDeliveredTimes() {
//        return deliveredTimes;
//    }
//
//    public void setDeliveredTimes(Integer deliveredTimes) {
//        this.deliveredTimes = deliveredTimes;
//    }
//
//    public DeliveryPlan getNextDeliveryPlan() {
//        return nextDeliveryPlan;
//    }
//
//    public void setNextDeliveryPlan(DeliveryPlan nextDeliveryPlan) {
//        this.nextDeliveryPlan = nextDeliveryPlan;
//    }
//
//    public String getPdName() {
//        return pdName;
//    }
//
//    public void setPdName(String pdName) {
//        this.pdName = pdName;
//    }
//
//    public String getSku() {
//        return sku;
//    }
//
//    public void setSku(String sku) {
//        this.sku = sku;
//    }
//
//    public LocalDateTime getDeliveryStartTime() {
//        return deliveryStartTime;
//    }
//
//    public void setDeliveryStartTime(LocalDateTime deliveryStartTime) {
//        this.deliveryStartTime = deliveryStartTime;
//    }
//
//    public LocalDateTime getDeliveryEndTime() {
//        return deliveryEndTime;
//    }
//
//    public void setDeliveryEndTime(LocalDateTime deliveryEndTime) {
//        this.deliveryEndTime = deliveryEndTime;
//    }
//
//    public Integer getTotalDeliveryTimes() {
//        return totalDeliveryTimes;
//    }
//
//    public void setTotalDeliveryTimes(Integer totalDeliveryTimes) {
//        this.totalDeliveryTimes = totalDeliveryTimes;
//    }
//
//    public Integer getStockQuantity() {
//        return stockQuantity;
//    }
//
//    public void setStockQuantity(Integer stockQuantity) {
//        this.stockQuantity = stockQuantity;
//    }
//
//    public Integer getDeliveryUpperLimit() {
//        return deliveryUpperLimit;
//    }
//
//    public void setDeliveryUpperLimit(Integer deliveryUpperLimit) {
//        this.deliveryUpperLimit = deliveryUpperLimit;
//    }
//
//    public Short getOrderStatus() {
//        return orderStatus;
//    }
//
//    public void setOrderStatus(Short orderStatus) {
//        this.orderStatus = orderStatus;
//    }
//
//    public Integer getAfterQuantity() {
//        return afterQuantity;
//    }
//
//    public void setAfterQuantity(Integer afterQuantity) {
//        this.afterQuantity = afterQuantity;
//    }
//
//    public Integer getOrderSaleType() {
//        return orderSaleType;
//    }
//
//    public void setOrderSaleType(Integer orderSaleType) {
//        this.orderSaleType = orderSaleType;
//    }
}
