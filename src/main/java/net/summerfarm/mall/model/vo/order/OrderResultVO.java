package net.summerfarm.mall.model.vo.order;

import java.time.LocalDateTime;
import lombok.Data;
import net.summerfarm.mall.model.domain.DiscountCardUseRecord;
import net.summerfarm.mall.model.domain.MarketRuleHistory;
import net.summerfarm.mall.model.domain.OrderPreferential;
import net.summerfarm.mall.model.dto.delivery.DeliveryFeeRuleDetailDTO;
import net.summerfarm.mall.model.dto.delivery.DeliveryFeeRuleInfoDTO;
import net.summerfarm.mall.model.dto.delivery.FreeDeliveryDifferenceDTO;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import net.summerfarm.mall.model.vo.OrderItemVO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Supplier;
import net.summerfarm.mall.model.vo.neworder.DeliveryFreeRuleVO;
import net.summerfarm.mall.model.vo.neworder.FloorPriceFailVO;
import net.summerfarm.mall.model.vo.neworder.SubOrderFailVO;

/**
 * <AUTHOR>
 * @Description 下单、预下单结果实体类
 */
@Data
public class OrderResultVO implements Serializable {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单类型
     */
    private Integer type;

    /**
     * 区域
     */
    private Integer areaNo;

    /**
     * 订单配送类型,0 次日达, 1 隔日达
     * 次日达排序在前
     */
    private Integer orderDeliveryType;

    /**
     * 日配 0
     */
    private Integer isEveryDayFlag;

    /**
     * 凑单用字段（需要用于列表页搜索接口）,0 搜全部商品, 1 只搜代销不入仓商品, 2 搜非代销不入仓的商品
     */
    private Integer addOn;

    /**
     * 是否含有代销不入仓的商品, 0 否, 1 是
     */
    private Integer hasSaleNotInStore;

    /**
     * 订单项
     */
    private List<OrderItemCalcDTO> itemList;
    /**
     * 优惠记录
     */
    private List<OrderPreferential> preferentialList;

    /**
     * 预付商品使用记录
     */
    private Map<String, Integer> prepayUseMap;

    /**
     * 优惠卡使用记录
     */
    private List<DiscountCardUseRecord> discountCardUseRecords;

    /**
     * 满返、满减使用记录
     */
    private List<MarketRuleHistory> marketRuleHistoryList;

    /**
     * 可用普通优惠券
     */
    private List<MerchantCouponVO> usableNormalCoupon;

    /**
     * 可用红包
     */
    private List<MerchantCouponVO> usableRedPackCoupon;

    /**
     * 可用精准送优惠券
     */
    private List<MerchantCouponVO> usableAccurateCoupon;

    /**
     * 使用的优惠券ID
     */
    private Set<Integer> usedMerchantCouponId;

    /**
     * 运费券
     */
    private Integer usedDeliveryCouponId;

    /**
     * 是否超时加单：0、否 1、是
     */
    private Integer outTimes;

    /**
     * 超时加单费用
     */
    private BigDecimal outTimesFee;

    /**
     * 使用的精准送
     */
    private String usedTimeFrame;

    /**
     * 精准送订单项
     */
    private OrderItemCalcDTO timeFrameItem;

    /**
     * 运费规则配置的运费
     */
    private BigDecimal ruleDeliveryFee;

    /**
     * 未使用优惠券的运费
     */
    private BigDecimal originalDeliveryFee;

    /**
     * 运费(实付运费)
     */
    private BigDecimal deliveryFee;

    /**
     * 用户原始运费
     */
    @Deprecated
    private BigDecimal areaDeliveryFee;

    /**
     * 配送日期，根据配送时间拆包裹
     */
    private LocalDate deliveryDate;

    /**
     * 发货日期
     */
    private LocalDate shipmentDate;

    /**
     * 订单状态
     */
    private Short orderStatus;

    /**
     * 商品总价（未优惠）
     */
    private BigDecimal itemOriginalPrice;

    /**
     * 订单实付总价
     */
    private BigDecimal actualTotalPrice;

    /**
     * 订单原价总价
     */
    private BigDecimal originTotalPrice;

    /**
     * 是否满足免运规则,0 否, 1 是
     */
    private Integer satisfyFreeDelivery;

    /**
     * 精准送费用
     */
    private BigDecimal precisionDeliveryFee;

    /**
     * 凑单免运规则
     */
    private List<DeliveryFreeRuleVO> freeDeliveryRule;

    /**
     * 命中配送规则快照
     */
    private DeliveryFeeRuleDetailDTO hitRuleDetailSnapshot;

    /**
     * 是否满足起送门槛 ture 满足起送门槛 false 未达到起送门槛
     */
    private Boolean isSatisfyDeliveryThreshold;

    /**
     * 起送门槛最低差额
     */
    private BigDecimal deliveryThresholdMinDifference;

    /**
     * 起送费
     */
    private BigDecimal startPrice;

    /**
     * 免运差额列表
     */
    private List<FreeDeliveryDifferenceDTO> freeDeliveryDifferenceList;

    /**
     * 免运规则--可能为空 deliveryFee为0 且差额最小的那条规则
     */
    private FreeDeliveryDifferenceDTO freeDelivery;

    /**
     * 匹配到层级运费规则
     */
    private DeliveryFeeRuleInfoDTO matchDeliveryFeeRule;

    /**
     * 失败子订单信息（不满足免运门槛订单）
     */
    private List<SubOrderFailVO> subOrderFailVOS;

    /**
     * 均摊后价格低于低价的订单项
     */
    private List<FloorPriceFailVO> floorPriceFailVOS;

    /**
     * 添加优惠信息
     *
     * @param supplier 优惠
     */
    public void addOrderPreferential(Supplier<OrderPreferential> supplier) {
        if (preferentialList == null) {
            preferentialList = new ArrayList<>();
        }
        preferentialList.add(supplier.get());
    }

    public void addOrderPreferential(OrderPreferential orderPreferential) {
        addOrderPreferential(() -> orderPreferential);
    }


    /**
     * 添加预付使用数量
     *
     * @param sku       sku
     * @param useAmount 使用数量
     */
    public void addPrepayRecord(String sku, Integer useAmount) {
        if (prepayUseMap == null) {
            prepayUseMap = new HashMap<>(4);
        }
        prepayUseMap.put(sku, useAmount);
    }

    /**
     * 添加满减、满返使用记录
     *
     * @param supplier 记录
     */
    public void addMarketRuleHistory(Supplier<MarketRuleHistory> supplier) {
        if (marketRuleHistoryList == null) {
            marketRuleHistoryList = new ArrayList<>();
        }
        marketRuleHistoryList.add(supplier.get());
    }

    /**
     * 添加使用的券ID
     *
     * @param merchantCouponId 券ID
     */
    public void addUsedMerchantCouponId(Integer merchantCouponId) {
        if (usedMerchantCouponId == null) {
            usedMerchantCouponId = new HashSet<>();
        }
        usedMerchantCouponId.add(merchantCouponId);
    }

    /**
     * 添加可用优惠券
     *
     * @param couponVOList 优惠券
     */
    public void addUsableNormalCoupon(List<MerchantCouponVO> couponVOList) {
        if (usableNormalCoupon == null) {
            usableNormalCoupon = new ArrayList<>();
        }
        usableNormalCoupon.addAll(couponVOList);
    }

    /**
     * 添加不满足免运门槛的订单信息
     *
     * @param supplier
     */
    public void addSubOrderFailVO(Supplier<SubOrderFailVO> supplier) {
        if (subOrderFailVOS == null) {
            subOrderFailVOS = new ArrayList<>();
        }
        subOrderFailVOS.add(supplier.get());
    }

    /**
     * 均摊后价格低于低价的订单项
     *
     * @param supplier
     */
    public void addFloorPriceFailVO(Supplier<FloorPriceFailVO> supplier) {
        if (floorPriceFailVOS == null) {
            floorPriceFailVOS = new ArrayList<>();
        }
        floorPriceFailVOS.add(supplier.get());
    }
}
