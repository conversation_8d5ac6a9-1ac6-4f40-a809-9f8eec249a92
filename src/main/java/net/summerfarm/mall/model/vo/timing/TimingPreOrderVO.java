package net.summerfarm.mall.model.vo.timing;

import lombok.Data;
import net.summerfarm.mall.model.vo.MerchantCouponVO;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

/**
 * 返回的购买的省心送商品确认页面信息（合并预下单）
 * @Date: 2025-07-07
 * @Author: zach
 */
@Data
public class TimingPreOrderVO {
    /**
     * 未扣除优惠卡优惠的金额
     */
    private BigDecimal originTotalPrice;
    /**
     * 购买使用的权益卡名称
     */
    private List<String> discountCardName;
    /**
     * 权益卡优惠总金额
     */
    private BigDecimal discountCardTotalFee;
    /**
     * 总计优惠
     */
    private BigDecimal preferentialTotal;
    /**
     * 用户应付金额
     */
    private BigDecimal goodsTotalPrice;
    /**
     * 省心送可用优惠券
     */
    private List<MerchantCouponVO> timingRuleCoupon;

    /**
     * 红包优惠
     */
    private HashMap<String,Object> rp;

    /**
     * 省心送预下单 - 单个订单明细
     */
    private List<TimingPreOrderSingleVO> timingPreOrderDetails;
}


