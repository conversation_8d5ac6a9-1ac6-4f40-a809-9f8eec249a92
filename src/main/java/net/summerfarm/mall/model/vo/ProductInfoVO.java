package net.summerfarm.mall.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.MarketControlPriceHideEnum;
import net.summerfarm.mall.enums.ProductCategoryTypeEnum;
import net.summerfarm.mall.model.domain.LadderPrice;
import net.summerfarm.mall.model.domain.MarketRule;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Package: net.summerfarm.mall.model.vo
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/4/10
 */
@Data
public class ProductInfoVO {

    private Long pdId;

    private Integer priority;

    private String activityLogo;

    private String sku;

    private String skuName;

    //销售模式
    private int salesMode;

    //限购数量
    private int limitedQuantity;

    //库存数量
    private int quantity;

    //商品名称
    private String pdName;

    //商品类别
    private int categoryId;

    //详情
    private String pddetail;

    //单位
    private String unit;

    private String picturePath;

    private String skuPic;

    private String weight;

    private Double salePrice;

    private Double costPrice;

    private String introduction;

    private String maturity;

    private LadderPrice minLadderPrice;

    private String productionDate;

    private String ladderPrice;

    private String originalPrice;

    private String detailPicture;

    private Integer baseSaleUnit;

    //最小起售
        private Integer baseSaleQuantity;

    private Integer spuBaseSaleQuantity;

    //品牌名称
    private String name;

    //大客户价格
    private Double price;

    private Integer direct;

    // SKU 类型，0：自营，1：代仓。参考 inventory.type(InventoryTypeEnum)
    private int type;

    /**
     * SKU 子类型
     *
     * @see net.summerfarm.mall.enums.InventoryEnums.SubType
     */
    private Integer subType;

    private String storageMethod;

    //保质期
    private String info;

    //商品平均价(带单位)
    private String avgPrice;

    //是否展示平均价 0不展示 1展示
    private Integer showAvg;

    //产地
    private String origin;


    private String slogan;

    private String otherSlogan;

    private Integer rebateType;

    private Double rebateNumber;

    private Double mPrice;

    private Integer arrivalNotice; //1 有提醒

    /**
     * 均价分子
     */
    private BigDecimal avgNumerator;

    /**
     * 均价单位
     */
    private String avgUnit;
    /**
     * 买手ID
     */
    private Long buyerId;

    /**
     * 买手名称
     */
    private String buyerName;

    /**
     * 供应商报价类型：0-默认类型，1-按斤报价，2-按件报价
     */
    private Integer quoteType;

    /**
     * 净重
     */
    private BigDecimal netWeightNum;
    /**
     * 净重单位
     */
    private String netWeightUnit;
    /**
     * 毛重
     */
    private BigDecimal weightNum;

    /**
     * 视频链接
     */
    private String videoUrl;
    /**
     * 售后规则详情
     */
    private String afterSaleRuleDetail;
    @Deprecated
    @ApiModelProperty(value = "是否展示预告")
    private Boolean showAdvance;
    @Deprecated
    @ApiModelProperty(value = "预告消息")
    private String advance;

    @ApiModelProperty(value = "活动原价")
    private BigDecimal activityOriginPrice;

    private List<MarketRule> marketRuleList;

    private List<ProductsPropertyValueVO> keyValueList;

    @ApiModelProperty(value = "预付款数量")
    private Integer prePayAmount;

    private Integer fixNum;

    private Float score;

    /**
     * 所属品牌
     */
    private String brandName;

    /**
     * 在品牌下的排名
     */
    private Integer rankId;

    /**
     * 商品类型
     */
    private Integer extType;

    /**
     * 保质期时长
     */
    private Integer qualityTime;

    /**
     * 保质期单位
     */
    private String qualityTimeUnit;

    /**
     * 门店可见该商品；0：是，1：否；
     */
    private Integer mallShow;
    /**
     * 虚拟库存
     */
    private Integer onlineQuantity;

    /**
     * 预留库存最大值
     */
    @Deprecated
    private Integer reserveMaxQuantity;
    /**
     * 预留库存最小值
     */
    @Deprecated
    private Integer reserveMinQuantity;

    /**
     * 预留库存剩余数量(可以为负值) 展示 大于最小值 取剩余数量 小于最小值 取最小值
     */
    @Deprecated
    private Integer reserveUseQuantity;
    /**
     * 阶梯价数组
     */
    @Deprecated
    private List<LadderPriceVO> ladderPrices;
    /**
     * 配送门槛
     */
    private Integer threshold;
    /**
     * 配送周期
     */
    private Integer deliveryPeriod;
    /**
     * 起送数量
     */
    private Integer deliveryUnit;
    /**
     * 省心送id
     */
    private Integer timingRuleId;
    /**
     * 配送开始时间
     */
    private Date deliveryStart;
    /**
     * 配送结束时间
     */
    private Date deliveryEnd;
    /**
     * 单次配送上限
     */
    private Integer deliveryUpperLimit;
    /**
     * 是否自动计算配送次数
     */
    private Boolean autoCalculate;
    private Integer soldOut;

    /**
     * 0、不展示平均价 1、展示平均价
     */
    private Integer averagePriceFlag;

    /**
     * 是否活动sku
     */
    private Boolean activitySku;

    /**
     * 是否省心送sku
     */
    private Boolean timingRuleSku;

    /**
     * 活动限购数量
     */
    private Integer activityLimitedQuantity;

    /**
     * 剩余限购数量
     */
    private Integer remainingQuantity;

    /**
     * 拓展购买剩余限购数量
     */
    private Integer expandRemainingQuantity;

    /**
     * 拓展购买活动价格
     */
    private BigDecimal expandActivityPrice;

    /**
     * 拓展购买活动商品优惠金额
     */
    private BigDecimal expandActivityDiscountPrice;

    /**
     * 商品类型，0 普通 、1 赠品 、2 换购
     *
     * @see net.summerfarm.mall.enums.ProductTypeEnum
     */
    @Getter
    @Setter
    private Integer productType;

    /**
     * 一年内购买次数
     */
    private Integer purchases;

    /**
     * 1置顶
     */
    private Integer sortTop;

    /**
     * 换购活动唯一id,格式为：infoId-scopeConfigId,通过“-”拼接
     */
    private String exchangeBuyId;

    private int sortQuantity;

    private int sales;

    private Integer platform;

    private Integer isSupportTiming;

    private Integer purchaseCeiling;

    /**
     * 类目类型: 2: 乳制品，3:非乳制品，4:水果，参考：ProductCategoryTypeEnum
     */
    private Integer cateType;

    /**
     * 控价品是否隐藏实付价--默认不隐藏
     *
     * @see MarketControlPriceHideEnum
     */
    private Integer priceHide;

    /**
     * 隐藏面价  1-不隐藏  2-隐藏（默认）
     */
    private Integer facePriceHide;


    /**
     * 隐藏控价线（和隐藏面价搭配使用）
     */
    private BigDecimal priceControlLine;
    /**
     * 是否包材
     */
    private boolean packagingMaterial;

    /**
     * 活动阶梯价数组
     */
    private List<LadderPriceVO> activityLadderPrices;

    /**
     * 活动剩余库存
     */
    private Integer activityActualQuantity;

    /**
     * es score
     */
    private float esScore;

    /**
     * es maxScore
     */
    private float maxScore;

    /**
     * 当前品是否在门店的常购清单
     * true: 在常购清单中 false: 不在常购清单中
     */
    private boolean inMerchantFrequentSkuPool;

    /**
     * 优先取SKU名称，如果为空，则兜底为SPU名称+SKU规格信息
     *
     * @return
     */
    public ProductInfoVO resetSkuName() {
        if (StringUtils.isBlank(skuName)) {
            skuName = pdName + " " + weight;
        }
        return this;
    }

    /**
     * 优先取SKU头图，如果为空，则兜底为SPU橱窗图
     *
     * @return
     */
    public ProductInfoVO resetSkuPic() {
        if (StringUtils.isBlank(skuPic)) {
            skuPic = picturePath;
        }
        return this;
    }

    /**
     * 优先取SKU名称，如果为空，则兜底为SPU名称+SKU规格信息
     * && 优先取SKU头图，如果为空，则兜底为SPU橱窗图
     *
     * @return
     */
    public ProductInfoVO resetSkuNameAndSkuPic() {
        resetSkuName();
        resetSkuPic();
        return this;
    }

    /**
     * 对商品属性键值对列表进行排序
     * 水果类商品使用专门的排序规则,其他商品使用通用排序规则
     *
     * @return 当前对象(支持链式调用)
     */
    public ProductInfoVO sortKeyValueList() {
        if (keyValueList == null || null == this.cateType) {
            return this;
        }

        // 根据商品类型选择对应的属性排序规则
        List<String> sortOrder = isFruitCategoryType(this)
                ? Global.FRUIT_PROPERTY_SORT
                : Global.NON_FRUIT_PROPERTY_SORT;

        // 构建基于属性名称的比较器
        Comparator<ProductsPropertyValueVO> comparator = Comparator.comparing(
                ProductsPropertyValueVO::getName,
                Comparator.comparing(sortOrder::indexOf)
        );

        // 原地排序并更新列表
        synchronized (this) {
            // 防止并发更新。
            keyValueList.sort(comparator);
        }

        return this;
    }

    public static boolean isFruitCategoryType(ProductInfoVO productInfoVO) {
        return null != productInfoVO && null != productInfoVO.cateType && ProductCategoryTypeEnum.FRUIT.getCode() == productInfoVO.cateType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ProductInfoVO that = (ProductInfoVO) o;
        return salesMode == that.salesMode && limitedQuantity == that.limitedQuantity && quantity == that.quantity && categoryId == that.categoryId && type == that.type && sortQuantity == that.sortQuantity && sales == that.sales && Objects.equals(pdId, that.pdId) && Objects.equals(priority, that.priority) && Objects.equals(activityLogo, that.activityLogo) && Objects.equals(sku, that.sku) && Objects.equals(skuName, that.skuName) && Objects.equals(pdName, that.pdName) && Objects.equals(pddetail, that.pddetail) && Objects.equals(unit, that.unit) && Objects.equals(picturePath, that.picturePath) && Objects.equals(skuPic, that.skuPic) && Objects.equals(weight, that.weight) && Objects.equals(salePrice, that.salePrice) && Objects.equals(costPrice, that.costPrice) && Objects.equals(introduction, that.introduction) && Objects.equals(maturity, that.maturity) && Objects.equals(minLadderPrice, that.minLadderPrice) && Objects.equals(productionDate, that.productionDate) && Objects.equals(ladderPrice, that.ladderPrice) && Objects.equals(originalPrice, that.originalPrice) && Objects.equals(detailPicture, that.detailPicture) && Objects.equals(baseSaleUnit, that.baseSaleUnit) && Objects.equals(baseSaleQuantity, that.baseSaleQuantity) && Objects.equals(name, that.name) && Objects.equals(price, that.price) && Objects.equals(direct, that.direct) && Objects.equals(storageMethod, that.storageMethod) && Objects.equals(info, that.info) && Objects.equals(avgPrice, that.avgPrice) && Objects.equals(showAvg, that.showAvg) && Objects.equals(origin, that.origin) && Objects.equals(slogan, that.slogan) && Objects.equals(otherSlogan, that.otherSlogan) && Objects.equals(rebateType, that.rebateType) && Objects.equals(rebateNumber, that.rebateNumber) && Objects.equals(mPrice, that.mPrice) && Objects.equals(arrivalNotice, that.arrivalNotice) && Objects.equals(showAdvance, that.showAdvance) && Objects.equals(advance, that.advance) && Objects.equals(activityOriginPrice, that.activityOriginPrice) && Objects.equals(marketRuleList, that.marketRuleList) && Objects.equals(keyValueList, that.keyValueList) && Objects.equals(prePayAmount, that.prePayAmount) && Objects.equals(fixNum, that.fixNum) && Objects.equals(score, that.score) && Objects.equals(brandName, that.brandName) && Objects.equals(rankId, that.rankId) && Objects.equals(extType, that.extType) && Objects.equals(qualityTime, that.qualityTime) && Objects.equals(qualityTimeUnit, that.qualityTimeUnit) && Objects.equals(mallShow, that.mallShow) && Objects.equals(onlineQuantity, that.onlineQuantity) && Objects.equals(reserveMaxQuantity, that.reserveMaxQuantity) && Objects.equals(reserveMinQuantity, that.reserveMinQuantity) && Objects.equals(reserveUseQuantity, that.reserveUseQuantity) && Objects.equals(ladderPrices, that.ladderPrices) && Objects.equals(threshold, that.threshold) && Objects.equals(deliveryPeriod, that.deliveryPeriod) && Objects.equals(deliveryUnit, that.deliveryUnit) && Objects.equals(timingRuleId, that.timingRuleId) && Objects.equals(deliveryStart, that.deliveryStart) && Objects.equals(deliveryEnd, that.deliveryEnd) && Objects.equals(deliveryUpperLimit, that.deliveryUpperLimit) && Objects.equals(autoCalculate, that.autoCalculate) && Objects.equals(soldOut, that.soldOut) && Objects.equals(averagePriceFlag, that.averagePriceFlag) && Objects.equals(activitySku, that.activitySku) && Objects.equals(timingRuleSku, that.timingRuleSku) && Objects.equals(activityLimitedQuantity, that.activityLimitedQuantity) && Objects.equals(remainingQuantity, that.remainingQuantity) && Objects.equals(expandRemainingQuantity, that.expandRemainingQuantity) && Objects.equals(expandActivityPrice, that.expandActivityPrice) && Objects.equals(expandActivityDiscountPrice, that.expandActivityDiscountPrice) && Objects.equals(productType, that.productType) && Objects.equals(purchases, that.purchases) && Objects.equals(sortTop, that.sortTop) && Objects.equals(exchangeBuyId, that.exchangeBuyId) && Objects.equals(platform, that.platform) && Objects.equals(isSupportTiming, that.isSupportTiming) && Objects.equals(purchaseCeiling, that.purchaseCeiling);
    }

    @Override
    public int hashCode() {
        return Objects.hash(pdId, priority, activityLogo, sku, skuName, salesMode, limitedQuantity, quantity, pdName, categoryId, pddetail, unit, picturePath, skuPic, weight, salePrice, costPrice, introduction, maturity, minLadderPrice, productionDate, ladderPrice, originalPrice, detailPicture, baseSaleUnit, baseSaleQuantity, name, price, direct, type, storageMethod, info, avgPrice, showAvg, origin, slogan, otherSlogan, rebateType, rebateNumber, mPrice, arrivalNotice, showAdvance, advance, activityOriginPrice, marketRuleList, keyValueList, prePayAmount, fixNum, score, brandName, rankId, extType, qualityTime, qualityTimeUnit, mallShow, onlineQuantity, reserveMaxQuantity, reserveMinQuantity, reserveUseQuantity, ladderPrices, threshold, deliveryPeriod, deliveryUnit, timingRuleId, deliveryStart, deliveryEnd, deliveryUpperLimit, autoCalculate, soldOut, averagePriceFlag, activitySku, timingRuleSku, activityLimitedQuantity, remainingQuantity, expandRemainingQuantity, expandActivityPrice, expandActivityDiscountPrice, productType, purchases, sortTop, exchangeBuyId, sortQuantity, sales, platform, isSupportTiming, purchaseCeiling);
    }
}
