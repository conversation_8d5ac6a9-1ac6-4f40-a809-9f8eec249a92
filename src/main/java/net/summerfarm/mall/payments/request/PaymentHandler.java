package net.summerfarm.mall.payments.request;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.model.domain.Refund;
import net.summerfarm.mall.model.input.payment.PaymentInput;
import net.summerfarm.mall.model.input.payment.PaymentOrderInput;
import net.summerfarm.mall.model.vo.payment.PayResultVO;
import net.xianmu.common.result.CommonResult;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;

/**
 * 支付处理
 * 特别说明「适用于以下所有带V2的支付相关接口说明」：
 *  1、考虑到线上支付风险V2版本接口适用于主子订单、支付单场景
 *  2、V2版本接口和原接口有大量重复代码，考虑到后续演化路径「全部支付场景迁移到v2版本」，故未对重复代码做封装
 */
public interface PaymentHandler {
    /**
     * 拉起支付
     *
     * @return 支付结果
     */
    String pay(PaymentOrderInput paymentOrderInput);

    /**
     * 退款
     *
     * @param afterSaleOrderNo 售后订单号
     * @return 退款操作结果
     */
    AjaxResult refundByAfterSaleOrderNo(String afterSaleOrderNo) throws UnrecoverableKeyException, CertificateException, IOException, KeyStoreException, NoSuchAlgorithmException, KeyManagementException;

    /**
     * 同步订单支付结果并更新订单状态
     * @param payOrderNo 付款单号
     * @return
     */
    void syncPaymentResult(String payOrderNo);

    AjaxResult performRefund(Refund refund);

    /**
     * 查询退款情况
     * @param refundNo 退款编号
     * @return 退款情况
     */
    AjaxResult queryRefund(String refundNo);

    /**
     * 关单接口
     * @param payOrderNo 支付订单号
     */
    void closeOrder(String payOrderNo);

    /**
     * 支付接口、支持主子单场景
     * @param input 支付参数
     * @return 发起支付结果
     */
    CommonResult<PayResultVO> payV2(PaymentInput input);

    /**
     * 同步订单支付结果并更新订单状态
     * @param masterPaymentNo 付款单号
     */
    void syncPaymentResultV2(String masterPaymentNo);

    /**
     * 关单接口，关闭支付单
     * @param masterPaymentNo 支付订单号
     */
    void closeOrderV2(String masterPaymentNo);
}
