package net.summerfarm.mall.payments.request;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.contexts.OrderPayStatusConstant;
import net.summerfarm.mall.model.domain.Payment;
import net.summerfarm.mall.model.domain.Statement;
import net.summerfarm.mall.model.vo.payment.PayResultVO;
import net.summerfarm.mall.payments.common.enums.CommonStatusEnum;
import net.summerfarm.mall.payments.common.pojo.*;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-07-07
 * @description
 */
public interface PaymentRequestService {
    /**
     * 支付请求处理
     *
     * @param payOrderInfo 支付信息
     * @return 支付结果
     */
    String payRequest(PaymentOrderInfo payOrderInfo);

    /**
     * 支付请求处理V2
     * @param paymentOrderInfoV2 支付信息
     * @return 支付结果
     */
    PayResultVO payRequestV2(PaymentOrderInfoV2 paymentOrderInfoV2);

    /**
     * 退款处理
     *
     * @param refundOrderInfo 退款信息
     * @return 处理结果
     */
    AjaxResult refundRequest(RefundOrderInfo refundOrderInfo) throws IOException, KeyStoreException, CertificateException, NoSuchAlgorithmException, UnrecoverableKeyException, KeyManagementException;

    /**
     * 查询指定日期的账单
     *
     * @param accountId 帐号id
     * @param date      日期
     * @return 账单
     */
    default Statement queryStatement(int accountId, String date) {
        return null;
    }

    /**
     * 查询订单支付情况
     *
     * @param queryOrderInfo 支付查询
     * @return QueryOrderInfo
     */
    void syncPayResult(QueryOrderInfo queryOrderInfo);

    /**
     * 查询订单支付情况
     *
     * @param orderInfoV2 支付查询
     * @return QueryOrderInfo
     */
    void syncPayResultV2(QueryOrderInfoV2 orderInfoV2);

    /**
     * 查询退款情况
     *
     * @param refundOrderInfo @return 退款情况
     * @return
     */
    default AjaxResult queryRefund(RefundOrderInfo refundOrderInfo) {return AjaxResult.getOK();};

    /**
     * 关闭顶单
     *
     * @param queryOrderInfo 订单信息
     */
    default CommonStatusEnum closeOrder(BasePayOrderInfo queryOrderInfo) {return CommonStatusEnum.SUCCESS; };
}
