package net.summerfarm.mall.payments.request.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.mq.PayNotifySuccessData;
import net.summerfarm.mall.common.mq.PayNotifySuccessDataV2;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.contexts.OrderPayStatusConstant;
import net.summerfarm.mall.enums.payment.PaymentEnums;
import net.summerfarm.mall.mapper.PaymentMapper;
import net.summerfarm.mall.model.domain.MasterPayment;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.domain.Payment;
import net.summerfarm.mall.model.vo.payment.PayResultVO;
import net.summerfarm.mall.payments.common.pojo.*;
import net.summerfarm.mall.payments.request.PaymentRequestService;
import net.summerfarm.mall.service.MasterPaymentService;
import net.summerfarm.mall.service.PaymentService;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-06-28
 * @description
 */
@Slf4j
@Service("periodPaymentRequestService")
public class PeriodPaymentRequestServiceImpl implements PaymentRequestService {
    @Resource
    private PaymentService paymentService;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private MasterPaymentService masterPaymentService;

    @Override
    public String payRequest(PaymentOrderInfo payOrderInfo) {
        Payment record = new Payment();
        record.setOrderNo(payOrderInfo.getPayOrderNo());
        record.setPayType("线下支付");
        record.setTransactionNumber(payOrderInfo.getOrderNo());
        record.setMoney(payOrderInfo.getPayTotalPrice());
        record.setEndTime(new Date());
        record.setStatus(1);
        record.setCompanyAccountId(payOrderInfo.getCompanyAccountId());
        paymentService.insertPayment(record);

        //账期支付直接凋成功接口
        PayNotifySuccessData payNotifySuccessData=new PayNotifySuccessData();
        payNotifySuccessData.setPayment(record);
        payNotifySuccessData.setOrder(payOrderInfo.getOrders());
        MQData mqData = new MQData();
        mqData.setType(MType.PAYMENT_NOTIFY_SUCCESS.name());
        mqData.setData(JSONObject.toJSONString(payNotifySuccessData));
        //producer.sendOrderlyDataToQueue(MQTopicConstant.MALL_PAYMENT_LIST, JSONObject.toJSONString(mqData), payOrderInfo.getOrders().getOrderNo());
        mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST,null,JSONObject.toJSONString(mqData), payOrderInfo.getOrders().getOrderNo());
        return "{\"returncode\":\"" + "MAJOR" + "\"}";
    }

    @Override
    public PayResultVO payRequestV2(PaymentOrderInfoV2 orderInfoV2) {
        //插入主单支付信息
        MasterPayment masterPayment = new MasterPayment();
        masterPayment.setPayType("线下支付");
        masterPayment.setMasterOrderNo(orderInfoV2.getOrderNo());
        masterPayment.setMoney(orderInfoV2.getMasterOrder().getTotalPrice());
        masterPayment.setEndTime(LocalDateTime.now());
        masterPayment.setStatus(0);
        masterPayment.setCompanyAccountId(null);
        masterPayment.setOnlinePayEndTime(LocalDateTime.now());
        masterPaymentService.upsertMasterAndPayment(masterPayment, null);

        //插入子单支付信息
        Date now = new Date();
        for (Orders orders : orderInfoV2.getOrdersList()) {
            Payment payment = new Payment();
            payment.setPayType("线下支付");
            payment.setOrderNo(orders.getOrderNo());
            payment.setMoney(orders.getTotalPrice());
            payment.setEndTime(now);
            payment.setStatus(0);
            payment.setCompanyAccountId(null);
            payment.setOnlinePayEndTime(now);
            paymentService.insertPayment(payment);
        }

        //发送支付成功消息
        masterPayment.setStatus(1);

        //账期支付直接凋成功接口
        PayNotifySuccessDataV2 successDataV2 = new PayNotifySuccessDataV2();
        successDataV2.setMasterPayment(masterPayment);
        successDataV2.setMasterOrder(orderInfoV2.getMasterOrder());
        MQData mqData = new MQData();
        mqData.setType(MType.MASTER_PAYMENT_NOTIFY_SUCCESS.name());
        mqData.setData(JSONObject.toJSONString(successDataV2));
        mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST,null,JSONObject.toJSONString(mqData), orderInfoV2.getOrderNo());

        PayResultVO payResultVO = new PayResultVO();
        payResultVO.setReturncode("MAJOR");
        return payResultVO;
    }

    @Override
    public AjaxResult refundRequest(RefundOrderInfo refundOrderInfo) {
        //账期无退款操作，为保证后续流程正常执行，直接返回成功
        return AjaxResult.getOK();
    }

    @Override
    public void syncPayResult(QueryOrderInfo queryOrderInfo) {
        Payment selectKey = new Payment();
        selectKey.setOrderNo(queryOrderInfo.getOrderNo());
        Payment payRecord = paymentMapper.selectOne(selectKey);
        if (!ObjectUtils.isEmpty(payRecord) && payRecord.getStatus() == 1) {
            redisTemplate.opsForValue().set(OrderPayStatusConstant.ORDER_PAY_SUCCESS_STATUS + payRecord.getOrderNo(), "SUCCESS", 5, TimeUnit.SECONDS);
        }
    }

    @Override
    public void syncPayResultV2(QueryOrderInfoV2 orderInfoV2) {
        MasterPayment masterPayment = masterPaymentService.selectByMasterPaymentNo(orderInfoV2.getOrderNo());
        if (!ObjectUtils.isEmpty(masterPayment) && PaymentEnums.PaymentStatus.SUCCESS.getStatus().equals(masterPayment.getStatus())) {
            redisTemplate.opsForValue().set(OrderPayStatusConstant.ORDER_PAY_SUCCESS_STATUS + orderInfoV2.getOrderNo(), "SUCCESS", 30, TimeUnit.SECONDS);
        }
    }
}
