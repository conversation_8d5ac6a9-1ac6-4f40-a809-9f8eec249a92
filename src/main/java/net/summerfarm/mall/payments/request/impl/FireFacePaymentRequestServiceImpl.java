package net.summerfarm.mall.payments.request.impl;

import com.alibaba.fastjson.JSONObject;
import com.lianok.docking.b2b.response.ApiHlAppletB2bPreAppletB2bResponse;
import com.lianok.docking.notification.OrderNotification;
import com.lianok.docking.pay.request.ApiHlOrderPayDetailsRequest;
import com.lianok.docking.pay.request.ApiHlOrderRefundDetailsRequest;
import com.lianok.docking.pay.request.ApiHlOrderRefundOperationRequest;
import com.lianok.docking.pay.response.ApiHlOrderPayDetailsResponse;
import com.lianok.docking.pay.response.ApiHlOrderRefundDetailsResponse;
import com.lianok.docking.pay.response.ApiHlOrderRefundOperationResponse;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.HandleEventStatus;
import net.summerfarm.mall.common.delayqueue.OrderCancelItem;
import net.summerfarm.mall.common.exceptions.PayBusyException;
import net.summerfarm.mall.common.mq.DelayData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.util.FireFaceUtils;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.common.util.WeChatUtils;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.mapper.MasterPaymentMapper;
import net.summerfarm.mall.mapper.PaymentMapper;
import net.summerfarm.mall.mapper.RefundHandleEventMapper;
import net.summerfarm.mall.model.domain.MasterPayment;
import net.summerfarm.mall.model.domain.Payment;
import net.summerfarm.mall.model.domain.Refund;
import net.summerfarm.mall.model.vo.payment.PayResultVO;
import net.summerfarm.mall.payments.common.config.PaymentConfig;
import net.summerfarm.mall.payments.common.enums.*;
import net.summerfarm.mall.payments.common.pojo.*;
import net.summerfarm.mall.payments.common.pojo.fireface.FireFacePayConfig;
import net.summerfarm.mall.payments.common.pojo.fireface.FireFacePaySignData;
import net.summerfarm.mall.payments.common.utils.CmbPayUtil;
import net.summerfarm.mall.payments.notify.FireFaceNotifyService;
import net.summerfarm.mall.payments.request.PaymentRequestService;
import net.summerfarm.mall.service.MasterOrderService;
import net.summerfarm.mall.service.MasterPaymentService;
import net.summerfarm.mall.service.OrderService;
import net.summerfarm.mall.service.PaymentService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.lianok.core.entity.ResponseResultBase;
import com.lianok.docking.LianokService;
import com.lianok.docking.b2b.request.ApiHlAppletB2bPreAppletB2bRequest;
import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Objects;

import static net.summerfarm.enums.RefundReponseEnum.P;
import static net.summerfarm.enums.RefundReponseEnum.S;

/**
 * @description:
 * @author: George
 * @date: 2025-01-13
 **/
@Service("fireFacePaymentRequestService")
@lombok.extern.slf4j.Slf4j
public class FireFacePaymentRequestServiceImpl implements PaymentRequestService {

    @Resource
    private MasterPaymentService masterPaymentService;
    @Resource
    private PaymentService paymentService;
    @Resource
    private FireFaceNotifyService fireFaceNotifyService;
    @Resource
    private RefundHandleEventMapper refundHandleEventMapper;
    @Resource
    private PaymentConfig paymentConfig;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private MasterPaymentMapper masterPaymentMapper;
    @Resource
    private MasterOrderService masterOrderService;
    @Resource
    private OrderService orderService;

    /**
     * 通用支付请求 主要用于调用支付接口流程
     * @param <T>
     * @param payOrderInfo
     * @return
     */
    public <T extends BasePayOrderInfo> ResponseResultBase<ApiHlAppletB2bPreAppletB2bResponse> commonPayRequest(T payOrderInfo) {
        // 如果之前存在过B2B支付行为 则业务异常返回
        b2bOnlyOnceBizVerify(payOrderInfo);
        // 构建请求参数
        ApiHlAppletB2bPreAppletB2bRequest request = buildFireFacePayRequest(payOrderInfo);

        // 火脸支付配置
        FireFacePayConfig payConfig = payOrderInfo.getFireFacePayConfig();
        LianokService service = FireFaceUtils.buildLianokService(payConfig.getAuthCode(), payConfig.getSecret());
        ResponseResultBase<ApiHlAppletB2bPreAppletB2bResponse> response = FireFaceUtils.executeRequest(service, request, ApiHlAppletB2bPreAppletB2bResponse.class);
        if (response == null || !Objects.equals(response.getCode(), Integer.valueOf(FireFacePaymentEnum.responseCode.SUCCESS.getCode()))) {
            throw new ProviderException("支付请求失败");
        }
        if (response.getData() == null) {
            throw new ProviderException("支付请求失败");
        }
        // 这里添加一个订单延迟取消事件（火脸不支持自定义过期时间 默认20min 且支付单号是订单号只能将订单取消掉）
        addDelayCancelEvent(payOrderInfo);
        return response;
    }

    private <T extends BasePayOrderInfo> void b2bOnlyOnceBizVerify(T payOrderInfo) {
        String payOrderNo = payOrderInfo.getPayOrderNo();
        Integer paymentBizType = payOrderInfo.getPaymentBizType();
        try {
            if (Objects.equals(paymentBizType, PaymentBizTypeEnum.ORDER_PAY.getCode())) {
                Payment query = new Payment();
                query.setOrderNo(payOrderNo);
                query.setPayType(BasePayTypeEnum.B2B_WECHAT_FIRE_FACE.getTypeStr());
                Payment payment = paymentMapper.selectOne(query);
                if (payment == null) {
                    return;
                }
                LocalDateTime orderTime = payOrderInfo.getOrderTime();
                checkPayBusyTime(orderTime);
                orderService.cancelOrder(payOrderNo);
            }
            if (Objects.equals(paymentBizType, PaymentBizTypeEnum.MASTER_ORDER_PAY.getCode())) {
                MasterPayment masterPayment = masterPaymentMapper.selectByMasterPaymentNo(payOrderNo);
                if (masterPayment== null || !Objects.equals(masterPayment.getPayType(), BasePayTypeEnum.B2B_WECHAT_FIRE_FACE.getTypeStr())) {
                    return;
                }
                LocalDateTime createTime = masterPayment.getCreateTime();
                checkPayBusyTime(createTime);
                masterOrderService.closeOrder(payOrderNo, RequestHolder.getMId(), RequestHolder.isMajor(), RequestHolder.isMajorDirect());
            }
        } catch (PayBusyException e) {
            throw e;
        } catch (Exception e) {
            log.error("校验b2b支付单流程异常", e);
            return;
        }
        throw new BizException("支付超时，点【再来一单】继续支付");
    }

    private void checkPayBusyTime(LocalDateTime firstPayTime) {
        if (firstPayTime == null) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        long diffSeconds = firstPayTime.until(now, ChronoUnit.SECONDS);
        if (diffSeconds < paymentConfig.getCheckPayBusySeconds()) {
            throw new PayBusyException("支付频繁，请稍后再试！");
        }
    }

    private <T extends BasePayOrderInfo> void addDelayCancelEvent(T payOrderInfo) {
        //添加事件到延迟队列
        String payOrderNo = payOrderInfo.getPayOrderNo();
        Long delayTime = paymentConfig.getFireFaceDelayCancelPayOrderTime();
        OrderCancelItem orderCancelItem = new OrderCancelItem("cancelorder" + payOrderNo,
                LocalDateTime.now(),
                delayTime, RequestHolder.getMId(), RequestHolder.getMpOpenId(),
                RequestHolder.getMerchantAreaNo(), payOrderNo);
        DelayData delayData = new DelayData();
        String type = Objects.equals(payOrderInfo.getPaymentBizType(), PaymentBizTypeEnum.ORDER_PAY.getCode()) ? MType.ORDER_TIMEOUT_CLOSE.name() : MType.MASTER_ORDER_TIMEOUT_CLOSE.name();
        delayData.setType(type);
        delayData.setData(JSONObject.toJSONString(orderCancelItem));
        mqProducer.sendDelay(MQTopicConstant.MALL_DELAY_LIST, null,
                JSONObject.toJSONString(delayData), delayTime);
    }

    private <T extends BasePayOrderInfo> ApiHlAppletB2bPreAppletB2bRequest buildFireFacePayRequest(T payOrderInfo) {
        // 创建 signData 信息
        FireFacePaySignData data = new FireFacePaySignData();
        data.setDescription("鲜沐农场直销商品");
        data.setEnv(FireFacePaymentEnum.env.PRO.getEnv());
        PaymentAttachInfoDTO attachInfoDTO = PaymentAttachInfoDTO.builder().companyAccountId(payOrderInfo.getCompanyAccountId()).build();
        data.setAttach(JSON.toJSONString(attachInfoDTO));

        // 构建金额信息
        FireFacePaySignData.AmountInfo amountInfo = new FireFacePaySignData().new AmountInfo();
        amountInfo.setOrderAmount(CmbPayUtil.yuanToFen(payOrderInfo.getPayTotalPrice()));
        data.setAmount(amountInfo);

        // 获取用户secretKey
        String sessionKey = getUserSessionKey(payOrderInfo);
        if (StringUtils.isEmpty(sessionKey)) {
            throw new ProviderException("获取用户信息失败");
        }

        // 构建请求对象
        ApiHlAppletB2bPreAppletB2bRequest request = new ApiHlAppletB2bPreAppletB2bRequest();
        FireFacePayConfig payConfig = payOrderInfo.getFireFacePayConfig();
        request.setMerchantNo(payConfig.getMerchantNo());
        request.setOperatorAccount(payConfig.getOperatorAccount());
        request.setBusinessOrderNo(payOrderInfo.getPayOrderNo());
        String notifyUrl = getNotifyUrl(payOrderInfo);
        request.setNotifyUrl(notifyUrl);
        request.setSignData(JSON.toJSONString(data));
        request.setSessionKey(sessionKey);
        return request;
    }

    private <T extends BasePayOrderInfo> String getNotifyUrl(T payOrderInfo) {
        //  这里将账户信息放在url中 没有其他字段可以放了
        return Global.DOMAIN_NAME +
                NotifyUrl.FIRE_FACE_PAY_NOTIFY_URL +
                payOrderInfo.getCompanyAccountId();
    }

    private <T extends BasePayOrderInfo> String getUserSessionKey(T payOrderInfo) {
        FireFacePayConfig fireFacePayConfig = payOrderInfo.getFireFacePayConfig();
        JSONObject jsonObject = WeChatUtils.code2Session(fireFacePayConfig.getAppId(), fireFacePayConfig.getAppSecret(), payOrderInfo.getJsCode());
        if (jsonObject == null) {
           return null;
        }
        return jsonObject.getString("session_key");
    }

    @Override
    public String payRequest(PaymentOrderInfo payOrderInfo) {
        // 调用支付接口
        ResponseResultBase<ApiHlAppletB2bPreAppletB2bResponse> payBaseResponse = commonPayRequest(payOrderInfo);
        //插入支付信息
        upsertPayment(payOrderInfo);
        // 返回支付结果
        return buildPayResultStr(payBaseResponse.getData());
    }

    @Override
    public PayResultVO payRequestV2(PaymentOrderInfoV2 paymentOrderInfoV2) {
        // 调用支付接口
        ResponseResultBase<ApiHlAppletB2bPreAppletB2bResponse> payBaseResponse = commonPayRequest(paymentOrderInfoV2);
        //插入支付信息
        upsertMasterPayment(paymentOrderInfoV2);
        // 返回支付结果
        return buildPayResultVO(payBaseResponse.getData());
    }

    /**
     * 插入支付信息
     * @param payOrderInfo
     */
    private void upsertPayment(PaymentOrderInfo payOrderInfo) {
        paymentService.insertPayment(null, payOrderInfo.getPayOrderNo(), payOrderInfo.getPayTypeStr(), payOrderInfo.getCompanyAccountId(), null, null);
    }

    /**
     * 构建支付结果字符串
     * @param responseData
     * @return
     */
    private String buildPayResultStr(ApiHlAppletB2bPreAppletB2bResponse responseData) {
        return JSONObject.toJSONString(buildPayResultVO(responseData));
    }

    /**
     * 插入支付信息
     * @param paymentOrderInfoV2
     */
    private void upsertMasterPayment(PaymentOrderInfoV2 paymentOrderInfoV2) {
        MasterPayment masterPayment = new MasterPayment();
        masterPayment.setMasterOrderNo(paymentOrderInfoV2.getOrderNo());
        masterPayment.setPayType(paymentOrderInfoV2.getPayTypeStr());
        masterPayment.setCompanyAccountId(paymentOrderInfoV2.getCompanyAccountId());
        masterPaymentService.upsertMasterAndPayment(masterPayment, paymentOrderInfoV2.getOrdersList());
    }

    /**
     * 构建支付结果
     * @param responseData
     * @return
     */
    private PayResultVO buildPayResultVO(ApiHlAppletB2bPreAppletB2bResponse responseData) {
        PayResultVO payResultVO = new PayResultVO();
        payResultVO.setSignature(responseData.getSignature());
        payResultVO.setPaySign(responseData.getPaySig());
        payResultVO.setSignData(responseData.getSignData());
        payResultVO.setReturncode("SUCCESS");
        return payResultVO;
    }

    @Override
    public void syncPayResult(QueryOrderInfo orderInfo) {
        ResponseResultBase<ApiHlOrderPayDetailsResponse> responseResultBase = commonSyncPayResult(orderInfo);
        commonOnSuccess(responseResultBase);
    }

    @Override
    public void syncPayResultV2(QueryOrderInfoV2 orderInfoV2) {
        ResponseResultBase<ApiHlOrderPayDetailsResponse> responseResultBase = commonSyncPayResult(orderInfoV2);
        commonOnSuccess(responseResultBase);
    }

    private <T extends BasePayOrderInfo> void commonOnSuccess(ResponseResultBase<ApiHlOrderPayDetailsResponse> responseResultBase) {
        if (responseResultBase == null || responseResultBase.getData() == null) {
            return;
        }
        ApiHlOrderPayDetailsResponse data = responseResultBase.getData();
        Integer orderStatus = data.getOrderStatus();
        if (Objects.equals(orderStatus, FireFacePaymentEnum.orderStatus.SUCCESS.getStatus())) {
            log.info("订单:{}主动查询支付成功，即将执行成功操作", data.getBusinessOrderNo());
            OrderNotification orderNotification = buildOrderNotification(data);
            fireFaceNotifyService.onSuccess(orderNotification);
            log.info("订单:{}主动查询支付成功，执行成功操作完成", data.getBusinessOrderNo());
            return;
        }
        log.info("订单:{}主动查询非支付成功", data.getBusinessOrderNo());
    }

    private <T extends BasePayOrderInfo> OrderNotification buildOrderNotification(ApiHlOrderPayDetailsResponse response) {
        OrderNotification orderNotification = new OrderNotification();
        orderNotification.setBusinessOrderNo(response.getBusinessOrderNo());
        orderNotification.setTopChannelOrderNo(response.getTopChannelOrderNo());
        orderNotification.setTotalAmount(response.getTotalAmount());
        orderNotification.setPayTime(response.getPayTime());
        return orderNotification;
    }


    /**
     * 同步支付结果
     * @param payOrderInfo
     * @param <T>
     */
    private <T extends BasePayOrderInfo> ResponseResultBase<ApiHlOrderPayDetailsResponse> commonSyncPayResult(T payOrderInfo) {
        ApiHlOrderPayDetailsRequest request = buildFireFacePayQueryRequest(payOrderInfo);
        FireFacePayConfig payConfig = payOrderInfo.getFireFacePayConfig();
        LianokService service = FireFaceUtils.buildLianokService(payConfig.getAuthCode(), payConfig.getSecret());
        ResponseResultBase<ApiHlOrderPayDetailsResponse> response = FireFaceUtils.executeRequest(service, request, ApiHlOrderPayDetailsResponse.class);
        if (response == null || !Objects.equals(response.getCode(), Integer.valueOf(FireFacePaymentEnum.responseCode.SUCCESS.getCode()))) {
            log.error("订单:{}查询支付详情请求失败", payOrderInfo.getPayOrderNo());
            return null;
        }
        if (response.getData() == null) {
            log.error("订单:{}查询支付详情请求失败", payOrderInfo.getPayOrderNo());
            return null;
        }
        return response;
    }

    private <T extends BasePayOrderInfo> ApiHlOrderPayDetailsRequest buildFireFacePayQueryRequest(T payOrderInfo) {
        FireFacePayConfig fireFacePayConfig = payOrderInfo.getFireFacePayConfig();
        ApiHlOrderPayDetailsRequest request = new ApiHlOrderPayDetailsRequest();
        request.setMerchantNo(fireFacePayConfig.getMerchantNo());
        request.setBusinessOrderNo(payOrderInfo.getPayOrderNo());
        return request;
    }

    @Override
    public CommonStatusEnum closeOrder(BasePayOrderInfo queryOrderInfo) {
        return PaymentRequestService.super.closeOrder(queryOrderInfo);
    }

    @Override
    public AjaxResult refundRequest(RefundOrderInfo refundOrderInfo) throws IOException, KeyStoreException, CertificateException, NoSuchAlgorithmException, UnrecoverableKeyException, KeyManagementException {
        log.info("开始处理火脸退款请求.refundOrderInfo:{}", JSON.toJSONString(refundOrderInfo));

        // 0.校验退款时间（微信限制10分钟以内的订单偶发退款失败）
        Payment query = new Payment();
        query.setOrderNo(refundOrderInfo.getOrderNo());
        Payment payment = paymentMapper.selectOne(query);
        Date onlinePayEndTime = payment.getOnlinePayEndTime();
        // 如果在十分钟内则不允许退款
        Integer refundQuiteTimeInMinutes = paymentConfig.getRefundQuiteTimeInMinutes();
        if (onlinePayEndTime != null && System.currentTimeMillis() - onlinePayEndTime.getTime() < refundQuiteTimeInMinutes * 60 * 1000) {
            log.info("{}支付间隙过短，暂不支持退款", refundOrderInfo.getRefund().getRefundNo());
            return AjaxResult.getErrorWithMsg("支付间隙过短，暂不支持退款");
        }

        // 1.预处理退款事件
        refundHandleEventMapper.updateStatusByRefundNo(HandleEventStatus.IN_HANDLING.ordinal(), refundOrderInfo.getRefund().getRefundNo());

        // 2.发起退款
        ApiHlOrderRefundOperationResponse response = this.doRefund(refundOrderInfo);

        // 3.构建退款返回响应
        Integer refundState = response == null ? null : response.getRefundStatus();
        return this.buildResultByRefundStatus(refundState);
    }


    private ApiHlOrderRefundOperationResponse doRefund(RefundOrderInfo refundOrderInfo){
        FireFacePayConfig payConfig = refundOrderInfo.getFireFacePayConfig();
        Refund refund = refundOrderInfo.getRefund();
        LianokService service = FireFaceUtils.buildLianokService(payConfig.getAuthCode(), payConfig.getSecret());
        ApiHlOrderRefundOperationRequest request = new ApiHlOrderRefundOperationRequest();
        /** 业务订单号，保持唯一 */
        request.setBusinessOrderNo(refundOrderInfo.getPayOrderNo());
        request.setBusinessRefundNo(refund.getRefundNo());
        request.setMerchantNo(payConfig.getMerchantNo());
        request.setOperatorAccount(payConfig.getOperatorAccount());
        request.setRefundPassword(payConfig.getRefundPassword());
        request.setRefundAmount(refund.getRefundFee().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        request.setNotifyUrl(Global.DOMAIN_NAME +
                NotifyUrl.FIRE_FACE_REFUND_NOTIFY_URL +
                refundOrderInfo.getCompanyAccountId());
        ResponseResultBase<ApiHlOrderRefundOperationResponse> response = FireFaceUtils.executeRequest(service, request, ApiHlOrderRefundOperationResponse.class);
        if (response == null || !Objects.equals(response.getCode(), Integer.valueOf(FireFacePaymentEnum.responseCode.SUCCESS.getCode()))) {
            log.error("退款:{}查询退款详情请求失败", refundOrderInfo.getPayOrderNo());
            return null;
        }
        return response.getData();
    }


    private AjaxResult buildResultByRefundStatus(Integer refundState){
        AjaxResult refundResult = AjaxResult.getError(ResultConstant.DEFAULT_FAILED);
        if(refundState == null) {
            return refundResult;
        }
        switch (refundState) {
            case 0:
            case 1:
                refundResult = AjaxResult.getOK("退款正在处理中", P);
                break;
            case 2:
                refundResult = AjaxResult.getOK("退款成功", S);
                break;
            case 3:
            case 4:
                refundResult = AjaxResult.getError("退款失败");
                break;
            default:
                break;
        }
        return refundResult;
    }

    @Override
    public AjaxResult queryRefund(RefundOrderInfo refundOrderInfo) {
        FireFacePayConfig payConfig = refundOrderInfo.getFireFacePayConfig();
        Refund refund = refundOrderInfo.getRefund();
        LianokService service = FireFaceUtils.buildLianokService(payConfig.getAuthCode(), payConfig.getSecret());
        ApiHlOrderRefundDetailsRequest request = new ApiHlOrderRefundDetailsRequest();
        /** 业务订单号，保持唯一 */
        request.setBusinessRefundNo(refund.getRefundNo());
        request.setMerchantNo(payConfig.getMerchantNo());
        ResponseResultBase<ApiHlOrderRefundDetailsResponse> response = FireFaceUtils.executeRequest(service, request, ApiHlOrderRefundDetailsResponse.class);
        Integer refundState = null;
        if (response == null || !Objects.equals(response.getCode(), Integer.valueOf(FireFacePaymentEnum.responseCode.SUCCESS.getCode()))) {
            log.error("退款:{}查询退款详情请求失败", refundOrderInfo.getPayOrderNo());
        } else {
            ApiHlOrderRefundDetailsResponse data = response.getData();
            refundState = data == null ? null : data.getRefundStatus();
        }
        return this.buildResultByRefundStatus(refundState);
    }

}
