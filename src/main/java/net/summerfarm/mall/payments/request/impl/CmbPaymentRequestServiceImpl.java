package net.summerfarm.mall.payments.request.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.HandleEventStatus;
import net.summerfarm.mall.Conf;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.util.IPUtil;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.contexts.MQDelayConstant;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.contexts.SpringContextUtil;
import net.summerfarm.mall.mapper.RefundHandleEventMapper;
import net.summerfarm.mall.model.domain.MasterPayment;
import net.summerfarm.mall.model.domain.Refund;
import net.summerfarm.mall.model.domain.Statement;
import net.summerfarm.mall.model.vo.payment.PayResultVO;
import net.summerfarm.mall.payments.PaymentStrategy;
import net.summerfarm.mall.payments.common.delayqueue.PaymentDelayQueueItem;
import net.summerfarm.mall.payments.common.enums.CmbPayUrl;
import net.summerfarm.mall.payments.common.enums.CommonStatusEnum;
import net.summerfarm.mall.payments.common.enums.NotifyUrl;
import net.summerfarm.mall.payments.common.enums.PayTypeStrEnum;
import net.summerfarm.mall.payments.common.pojo.*;
import net.summerfarm.mall.payments.common.pojo.cmb.CmbPayConfig;
import net.summerfarm.mall.payments.common.utils.CmbPayUtil;
import net.summerfarm.mall.payments.common.utils.RestTemplateUtil;
import net.summerfarm.mall.payments.notify.CmbNotifyService;
import net.summerfarm.mall.payments.request.PaymentRequestService;
import net.summerfarm.mall.service.MasterPaymentService;
import net.summerfarm.mall.service.PaymentService;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static net.summerfarm.enums.RefundReponseEnum.P;
import static net.summerfarm.enums.RefundReponseEnum.S;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-06-28
 * @description
 */
@Slf4j
@Service("cmbPaymentRequestService")
public class CmbPaymentRequestServiceImpl implements PaymentRequestService {
    @Resource
    private PaymentService paymentService;
    @Resource
    private CmbNotifyService cmbNotifyService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    PaymentStrategy paymentStrategy;
    @Resource
    private RefundHandleEventMapper refundHandleEventMapper;
    @Resource
    private RestTemplateUtil restTemplateUtil;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private MasterPaymentService masterPaymentService;

    /**
     * 订单id重复errorCode(招行的小程序/公众号互相切换支付)
     */
    public static final String ORDER_ID_DUPLICATION_ERROR_CODE = "ORDERID_DUPLICATION";
    public static final String ORDER_ID_DUPLICATION_RESP_MSG = "商户订单号重复:PCMBDAB";
    public static final String ORDER_ID_DUPLICATION_TIPS = "请勿小程序/公众号切换支付，请重新创建订单支付";

    private static final List<String> CODE_LIST = new ArrayList<>();

    static {
        CODE_LIST.add("FORWARD_AMOUNT_LESS");
        CODE_LIST.add("MCH_EXSRFN_AMOUNT_LESS");
        CODE_LIST.add("SERVMCH_FORWARD_AMOUNT_LESS");
    }

    @Override
    public Statement queryStatement(int accountId, String date) {

        CmbPayConfig payConfig = paymentStrategy.getCmbPayConfig(accountId);
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("billDate", date);
        Map<String, String> requestParams = initParamMap(payConfig, paramMap, false);
        // 组request头部Map
        Map<String, String> apiHeader = initHeaderMap(payConfig, requestParams.get("sign"));

        // 2. 发起请求
        Map<String, String> result = restTemplateUtil.postForEntity(CmbPayUrl.STATEMENT_URL, requestParams, apiHeader);
        if (!result.get("returnCode").equals("SUCCESS")) {
            throw new DefaultServiceException(result.get("respMsg"));
        }

        String url = (String) JSONObject.parseObject(result.get("biz_content")).get("fileDownloadUrl");
        return extractStatement(url, date);

    }

    private Statement extractStatement(String url, String date) {
        HttpGet request = new HttpGet(url);
        CloseableHttpResponse resp = null;
        try {
            resp = HttpClients.createDefault().execute(request);
            InputStream in = resp.getEntity().getContent();
            XSSFWorkbook wb = new XSSFWorkbook(in);
            Sheet sheet = wb.getSheetAt(0);
            Double payAMount = Double.parseDouble(sheet.getRow(8).getCell(2).toString());
            Double refundAmount = Double.parseDouble(sheet.getRow(9).getCell(2).toString());
            Statement statement = new Statement();
            statement.setPayAmount(payAMount);
            statement.setRefundAmount(refundAmount * -1);
            log.info("招行{}账单：{}", date, statement);
            return statement;
        } catch (IOException e) {
            log.error("IOException", e);
            return new Statement();
        } finally {
            if (Objects.nonNull(resp)) {
                try {
                    resp.close();
                } catch (IOException e) {
                    log.error("关闭异常", e);
                }
            }
        }
    }

    @Override
    public String payRequest(PaymentOrderInfo payOrderInfo) {
        // 1. 参数构建
        CmbPayConfig payConfig = payOrderInfo.getCmbPayConfig();

        //公共请求参数：版本号、编码方式、签名方法
        String requestUrl = CmbPayUrl.QR_CODE;
        Map<String, String> paramMap = new HashMap<>(16);
        paramMap.put("orderId", payOrderInfo.getPayOrderNo());
        paramMap.put("tradeScene", "OFFLINE");
        paramMap.put("body", "鲜沐农场直销水果");
        paramMap.put("notifyUrl", Global.DOMAIN_NAME + NotifyUrl.CMD_NOTIFY_URL);
        paramMap.put("txnAmt", CmbPayUtil.yuanToFen(payOrderInfo.getPayTotalPrice()));
        paramMap.put("mchReserved", payOrderInfo.getCompanyAccountId().toString());
        if (PayTypeStrEnum.WEIX.getType().equals(payOrderInfo.getOriginPayType()) && payOrderInfo.getIsMpPay()) {
            requestUrl = CmbPayUrl.ONLINE_PAY;

            paramMap.put("subAppId", Conf.MP_APP_ID);
            paramMap.put("tradeType", "JSAPI");
            //该字段会在回调中返回
            paramMap.put("subOpenId", RequestHolder.isMiniProgramLogin() ? RequestHolder.getMpOpenId() : RequestHolder.getOpenId());
            paramMap.put("spbillCreateIp", IPUtil.getIpAddress(RequestHolder.getRequest()));
        } else {
            paramMap.put("payValidTime", "1770");
        }

        Map<String, String> requestParams = initParamMap(payConfig, paramMap, true);
        // 组request头部Map
        Map<String, String> apiHeader = initHeaderMap(payConfig, requestParams.get("sign"));

        // 2. 发起请求
        Map<String, String> result = restTemplateUtil.postForEntity(requestUrl, requestParams, apiHeader);
        log.info("用户订单{}支付，参数：{}，响应：{}", payOrderInfo.getPayOrderNo(), JSONObject.toJSONString(requestParams), result);

        // 3. 处理响应
        checkSign(payConfig, result);
        //插入支付信息
        String bizContent = result.get("biz_content");
        if (StringUtils.isBlank(bizContent) || "FAIL".equals(result.get("returnCode")) || "FAIL".equals(result.get("respCode"))) {
            String errCode = result.get("errCode");
            String respMsg = result.get("respMsg");
            if (ORDER_ID_DUPLICATION_ERROR_CODE.equals(errCode) && ORDER_ID_DUPLICATION_RESP_MSG.equals(respMsg)) {
                return "{\"returncode\":\"" + "FAIL"
                        + "\",\"errmsg\":\"" + result.get("errCode")
                        + ":" + ORDER_ID_DUPLICATION_TIPS + "\"}";
            }
        }

        if (StringUtils.isNotBlank(bizContent)) {
            JSONObject contentMap = JSON.parseObject(bizContent);
            paymentService.insertPayment(contentMap.getString("cmbOrderId"), payOrderInfo.getPayOrderNo(), payOrderInfo.getPayTypeStr(), payOrderInfo.getCompanyAccountId(), null, payOrderInfo.getOriginPayType());

            JSONObject rStr = new JSONObject();
            rStr.put("returncode", result.get("returnCode"));
            if (PayTypeStrEnum.WEIX.getType().equals(payOrderInfo.getOriginPayType()) && payOrderInfo.getIsMpPay()) {
                JSONObject payData = contentMap.getJSONObject("payData");
                rStr.putAll(payData);
            } else {
                String qrCode = contentMap.getString("qrCode");
                if (!SpringContextUtil.isProduct()) {
                    qrCode = qrCode.replace("https://qr.95516.com", "http://payment-uat.cs.cmburl.cn");
                }
                rStr.put("qrCode", qrCode);
                rStr.put("txnTime", contentMap.get("txnTime"));
            }

            //延迟反查策略
            try {
                PaymentDelayQueueItem item = new PaymentDelayQueueItem(payOrderInfo.getPayOrderNo(), 10 * 1000L);
                MQData payData = new MQData();
                payData.setType(MType.PAYMENT_STATUS_QUERY.name());
                payData.setData(JSONObject.toJSONString(item));
                //producer.sendDelayDataToQueue(MQTopicConstant.MALL_PAYMENT_DELAY_LIST, JSONObject.toJSONString(payData), MQDelayConstant.THREE_DELAY_LEVEL);
                mqProducer.sendDelay(MQTopicConstant.MALL_PAYMENT_DELAY_LIST,null, JSONObject.toJSONString(payData), MQDelayConstant.THREE_DELAY_LEVEL_LONG);
                //2分钟内反查
                stringRedisTemplate.opsForValue().set("DELAY_PAYMENT:" + payOrderInfo.getPayOrderNo(), "", 2, TimeUnit.MINUTES);
            } catch (Exception e) {
                throw new DefaultServiceException(1, "严重！支付错误...");
            }
            return rStr.toJSONString();
        }

        return "{\"returncode\":\"" + result.get("returnCode")
                + "\",\"errmsg\":\"" + result.get("errCode")
                + ":" + result.get("respMsg") + "\"}";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayResultVO payRequestV2(PaymentOrderInfoV2 payOrderInfo) {
        // 1. 参数构建
        CmbPayConfig payConfig = payOrderInfo.getCmbPayConfig();

        //公共请求参数：版本号、编码方式、签名方法
        String requestUrl = CmbPayUrl.QR_CODE;
        Map<String, String> paramMap = new HashMap<>(16);
        paramMap.put("orderId", payOrderInfo.getPayOrderNo());
        paramMap.put("tradeScene", "OFFLINE");
        paramMap.put("body", "鲜沐农场直销水果");
        paramMap.put("notifyUrl", Global.DOMAIN_NAME + NotifyUrl.CMD_NOTIFY_URL_V2);
        paramMap.put("txnAmt", CmbPayUtil.yuanToFen(payOrderInfo.getPayTotalPrice()));
        paramMap.put("mchReserved", payOrderInfo.getCompanyAccountId().toString());
        //剩余可支付时间，不应小于60s
        paramMap.put("payValidTime", "1770");
        //小程序微信支付处理
        if (PayTypeStrEnum.WEIX.getType().equals(payOrderInfo.getOriginPayType()) && payOrderInfo.getIsMpPay()) {
            requestUrl = CmbPayUrl.ONLINE_PAY;

            paramMap.put("subAppId", Conf.MP_APP_ID);
            paramMap.put("tradeType", "JSAPI");
            //该字段会在回调中返回
            paramMap.put("subOpenId", RequestHolder.isMiniProgramLogin() ? RequestHolder.getMpOpenId() : RequestHolder.getOpenId());
            paramMap.put("spbillCreateIp", IPUtil.getIpAddress(RequestHolder.getRequest()));
        }

        Map<String, String> requestParams = initParamMap(payConfig, paramMap, true);
        // 组request头部Map
        Map<String, String> apiHeader = initHeaderMap(payConfig, requestParams.get("sign"));

        // 2. 发起请求
        Map<String, String> result = restTemplateUtil.postForEntity(requestUrl, requestParams, apiHeader);
        log.info("用户订单{}支付，参数：{}，响应：{}", payOrderInfo.getPayOrderNo(), JSONObject.toJSONString(requestParams), result);

        // 3. 校验签名、处理响应
        checkSign(payConfig, result);
        String bizContent = result.get("biz_content");

        PayResultVO payResultVO = new PayResultVO();
        payResultVO.setReturncode(result.get("returnCode"));
        if (StringUtils.isBlank(bizContent) || "FAIL".equals(result.get("returnCode")) || "FAIL".equals(result.get("respCode"))) {
            payResultVO.setReturncode("FAIL");
            payResultVO.setErrmsg(result.get("errCode") + ":" + result.get("respMsg"));
            String errCode = result.get("errCode");
            String respMsg = result.get("respMsg");
            if (ORDER_ID_DUPLICATION_ERROR_CODE.equals(errCode) && ORDER_ID_DUPLICATION_RESP_MSG.equals(respMsg)) {
                payResultVO.setErrmsg(ORDER_ID_DUPLICATION_TIPS);
            }
            payResultVO.setPrepayFail(true);
            return payResultVO;
        }

        //提取支付相关参数
        JSONObject contentMap = JSON.parseObject(bizContent);
        if (PayTypeStrEnum.WEIX.getType().equals(payOrderInfo.getOriginPayType()) && payOrderInfo.getIsMpPay()) {
            JSONObject payData = contentMap.getJSONObject("payData");
            payResultVO.setAppId(payData.getString("appId"));
            payResultVO.setPaySign(payData.getString("paySign"));
            payResultVO.setTimeStamp(payData.getString("timeStamp"));
            payResultVO.setNonceStr(payData.getString("nonceStr"));
            payResultVO.setSignType(payData.getString("signType"));
            payResultVO.setPackageStr(payData.getString("package"));
        } else {
            String qrCode = contentMap.getString("qrCode");
            if (!SpringContextUtil.isProduct()) {
                qrCode = qrCode.replace("https://qr.95516.com", "http://payment-uat.cs.cmburl.cn");
            }
            payResultVO.setQrCode(qrCode);
            payResultVO.setTxnTime(contentMap.getString("txnTime"));
        }

        //upsert主子表支付信息
        MasterPayment masterPayment = new MasterPayment();
        masterPayment.setTransactionNumber(contentMap.getString("cmbOrderId"));
        masterPayment.setMasterOrderNo(payOrderInfo.getPayOrderNo());
        masterPayment.setPayType(payOrderInfo.getPayTypeStr());
        masterPayment.setCompanyAccountId(payOrderInfo.getCompanyAccountId());
        masterPayment.setBocPayType(payOrderInfo.getOriginPayType());
        masterPaymentService.upsertMasterAndPayment(masterPayment, payOrderInfo.getOrdersList());

        //招行支付主动反查
        try {
            PaymentDelayQueueItem item = new PaymentDelayQueueItem(payOrderInfo.getPayOrderNo(), 10 * 1000L);
            MQData payData = new MQData();
            payData.setType(MType.MASTER_PAYMENT_STATUS_QUERY.name());
            payData.setData(JSONObject.toJSONString(item));
            mqProducer.sendDelay(MQTopicConstant.MALL_PAYMENT_DELAY_LIST, null, JSONObject.toJSONString(payData), MQDelayConstant.THREE_DELAY_LEVEL_LONG);
            stringRedisTemplate.opsForValue().set("DELAY_PAYMENT:" + payOrderInfo.getPayOrderNo(), "", 2, TimeUnit.MINUTES);
        } catch (Exception e) {
            throw new DefaultServiceException(1, "严重！支付错误...");
        }

        return payResultVO;
    }

    @Override
    public AjaxResult refundRequest(RefundOrderInfo refundOrderInfo) {
        refundHandleEventMapper.updateStatusByRefundNo(HandleEventStatus.IN_HANDLING.ordinal(), refundOrderInfo.getRefund().getRefundNo());
        //1. 参数构建
        CmbPayConfig payConfig = refundOrderInfo.getCmbPayConfig();
        Refund refund = refundOrderInfo.getRefund();

        //公共请求参数：版本号、编码方式、签名方法
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("orderId", refund.getRefundNo());
        paramMap.put("origOrderId", refundOrderInfo.getPayOrderNo());
        paramMap.put("txnAmt", CmbPayUtil.yuanToFen(refundOrderInfo.getPayTotalPrice()));
        String refundFee = refund.getRefundFee().toString();
        paramMap.put("refundAmt", refundFee.substring(0, refundFee.indexOf(".")));
        //该字段会在回调中返回
        paramMap.put("mchReserved", refundOrderInfo.getCompanyAccountId().toString());
        paramMap.put("notifyUrl", Global.DOMAIN_NAME + NotifyUrl.CMD_REFUND_URL);
        Map<String, String> requestParams = initParamMap(payConfig, paramMap, true);

        // 组request头部Map
        Map<String, String> apiHeader = initHeaderMap(payConfig, requestParams.get("sign"));

        //2. 发起请求
        log.info("招行退款请求，订单号：{}，参数：{}", refund.getRefundNo(), JSONObject.toJSONString(requestParams));
        Map<String, String> result = restTemplateUtil.postForEntity(CmbPayUrl.REFUND, requestParams, apiHeader);
        log.info("招行退款请求，订单号：{}，参数：{}，响应：{}", refund.getRefundNo(), JSONObject.toJSONString(requestParams), result);

        //3. 处理响应
        checkSign(payConfig, result);

        //失败处理
        if ("FAIL".equals(result.get("returnCode")) || "FAIL".equals(result.get("respCode"))) {
            if (CODE_LIST.stream().anyMatch(el -> Objects.equals(result.get("errCode"), el))) {
                throw new DefaultServiceException(0, "资金确认中，请稍后重新发起退款。");
            }
            throw new DefaultServiceException(1, "退款请求异常，" + result.get("errCode") + "：" + result.get("respMsg"));
        }

        //请求成功处理
        String bizContent = result.get("biz_content");
        JSONObject jsonObject = JSONObject.parseObject(bizContent);
        String refundState = jsonObject.getString("refundState");

        AjaxResult refundResult = AjaxResult.getError(ResultConstant.DEFAULT_FAILED);
        switch (refundState) {
            case "S":
                refundResult = AjaxResult.getOK("退款成功", S);
                break;
            case "F":
                refundResult = AjaxResult.getError("退款失败");
                break;
            case "P":
                refundResult = AjaxResult.getOK("退款正在处理中", P);
                break;
            default:
                break;
        }

        return refundResult;
    }

    @Override
    public void syncPayResult(QueryOrderInfo orderInfo) {
        // 1.参数构建
        CmbPayConfig payConfig = orderInfo.getCmbPayConfig();

        //公共请求参数：版本号、编码方式、签名方法
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("orderId", orderInfo.getPayOrderNo());
        Map<String, String> requestParams = initParamMap(payConfig, paramMap, true);

        // 组request头部Map
        Map<String, String> apiHeader = initHeaderMap(payConfig, requestParams.get("sign"));

        //2. 发起请求
        Map<String, String> result = restTemplateUtil.postForEntity(CmbPayUrl.ORDER_QUERY, requestParams, apiHeader);
        log.info("支付结果查询请求，payOrderNo：{}，参数：{}，响应：{}", orderInfo.getPayOrderNo(), JSONObject.toJSONString(requestParams), JSONObject.toJSONString(result));

        //3. 处理响应
        cmbNotifyService.cmbPayNotify(result, false);
    }

    @Override
    public void syncPayResultV2(QueryOrderInfoV2 orderInfoV2) {
        // 1.参数构建
        CmbPayConfig payConfig = orderInfoV2.getCmbPayConfig();

        //公共请求参数：版本号、编码方式、签名方法
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("orderId", orderInfoV2.getPayOrderNo());
        Map<String, String> requestParams = initParamMap(payConfig, paramMap, true);

        // 组request头部Map
        Map<String, String> apiHeader = initHeaderMap(payConfig, requestParams.get("sign"));

        //2. 发起请求
        Map<String, String> result = restTemplateUtil.postForEntity(CmbPayUrl.ORDER_QUERY, requestParams, apiHeader);
        log.info("支付结果查询请求，payOrderNo：{}，参数：{}，响应：{}", orderInfoV2.getPayOrderNo(), JSONObject.toJSONString(requestParams), JSONObject.toJSONString(result));

        //3. 处理响应
        cmbNotifyService.cmbPayNotifyV2(result, false);
    }

    @Override
    public AjaxResult queryRefund(RefundOrderInfo refundOrderInfo) {
        //1. 参数构建
        CmbPayConfig payConfig = refundOrderInfo.getCmbPayConfig();
        Refund refund = refundOrderInfo.getRefund();

        //公共请求参数：版本号、编码方式、签名方法
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("orderId", refund.getRefundNo());
        Map<String, String> requestParams = initParamMap(payConfig, paramMap, true);

        // 组request头部Map
        Map<String, String> apiHeader = initHeaderMap(payConfig, requestParams.get("sign"));

        //2. 发送请求
        Map<String, String> result = restTemplateUtil.postForEntity(CmbPayUrl.REFUND_QUERY, requestParams, apiHeader);
        log.info("退款结果查询请求，refundNo：{}，参数：{}，响应：{}", refund.getRefundNo(), JSONObject.toJSONString(requestParams), result);

        JSONObject bizContent = JSONObject.parseObject(result.get("biz_content"));

        //3. 处理响应数据
        cmbNotifyService.cmbRefundNotify(result, false);

        if ("P".equals(bizContent.getString("tradeState"))) {
            return AjaxResult.getOK("退款中", P);
        } else if ("S".equals(bizContent.getString("tradeState"))) {
            return AjaxResult.getOK("退款完成", S);
        } else if ("F".equals(bizContent.getString("tradeState"))) {
            return AjaxResult.getError();
        } else {
            return AjaxResult.getError("未知状态异常");
        }
    }

    @Override
    public CommonStatusEnum closeOrder(BasePayOrderInfo basePayOrderInfo) {
        //1.构建参数
        CmbPayConfig cmbPayConfig = basePayOrderInfo.getCmbPayConfig();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("origOrderId", basePayOrderInfo.getPayOrderNo());
        Map<String, String> requestParam = initParamMap(cmbPayConfig, paramMap, true);

        //header
        Map<String, String> header = initHeaderMap(cmbPayConfig, requestParam.get("sign"));

        //2. 发送请求
        Map<String, String> result = restTemplateUtil.postForEntity(CmbPayUrl.CLOSE_ORDER, requestParam, header);
        log.info("请求招行支付关单，payOrderNo：{}，参数：{}，响应：{}", basePayOrderInfo.getPayOrderNo(), JSONObject.toJSONString(requestParam), result);

        //3.处理返回
        checkSign(cmbPayConfig, result);

        if ("FAIL".equals(result.get("returnCode"))) {
            throw new DefaultServiceException(1, "关单请求参数错误，payOrderNo：" + basePayOrderInfo.getPayOrderNo());
        }
        if ("FAIL".equals(result.get("respCode"))) {
            return CommonStatusEnum.FAIL;
        }
        if ("SYSTERM_ERROR".equals(result.get("respCode"))) {
            return CommonStatusEnum.HANDLING;
        }
        return CommonStatusEnum.SUCCESS;
    }

    /**
     * 初始化请求参数
     *
     * @param payConfig 支付配置
     * @param useUserId
     * @return 请求参数
     */
    private Map<String, String> initParamMap(CmbPayConfig payConfig, Map<String, String> paramMap, Boolean useUserId) {
        paramMap.put("merId", payConfig.getMerId());
        if (useUserId) {
            paramMap.put("userId", payConfig.getUserId());
        }
        ObjectMapper objectMapper = new ObjectMapper();
        String valueAsString = null;
        try {
            valueAsString = objectMapper.writeValueAsString(paramMap);
        } catch (IOException e) {
            throw new DefaultServiceException(1, "参数处理错误");
        }

        Map<String, String> requestParams = new TreeMap<>();
        requestParams.put(CmbPayUtil.CmbPayConstant.VERSION, CmbPayUtil.CmbPayConstant.VERSION_VALUE);
        requestParams.put(CmbPayUtil.CmbPayConstant.ENCODING, CmbPayUtil.CmbPayConstant.ENCODING_VALUE);
        requestParams.put(CmbPayUtil.CmbPayConstant.SIGN_METHOD, CmbPayUtil.CmbPayConstant.SIGN_METHOD_VALUE);
        requestParams.put(CmbPayUtil.CmbPayConstant.BIZ_CONTENT, valueAsString);

        //签名
        String sign = CmbPayUtil.SM2Util.sm2Sign(requestParams, payConfig.getPriKey());
        requestParams.put(CmbPayUtil.CmbPayConstant.SIGN, sign);

        return requestParams;
    }

    /**
     * 初始化请求头
     *
     * @param payConfig 支付配置
     * @return 请求头
     */
    private Map<String, String> initHeaderMap(CmbPayConfig payConfig, String sign) {
        long timestamp = System.currentTimeMillis() / 1000;
        Map<String, String> md5Map = new TreeMap<>();
        md5Map.put("appid", payConfig.getAppId());
        md5Map.put("secret", payConfig.getSecret());
        md5Map.put("sign", sign);
        md5Map.put("timestamp", "" + timestamp);
        String apiSign = CmbPayUtil.getMD5Content(md5Map);

        Map<String, String> apiHeader = new HashMap<>(4);
        apiHeader.put("appid", payConfig.getAppId());
        apiHeader.put("timestamp", "" + timestamp);
        apiHeader.put("apisign", apiSign);
        return apiHeader;
    }

    /**
     * 签名校验
     *
     * @param payConfig 配置
     * @param result    响应数据
     */
    private void checkSign(CmbPayConfig payConfig, Map<String, String> result) {
        boolean rsaCheck;
        String resultSign;
        resultSign = result.remove("sign");
        rsaCheck = CmbPayUtil.SM2Util.sm2Check(result, resultSign, payConfig.getPubKey());
        if (!rsaCheck) {
            throw new DefaultServiceException(1, "签名验证失败");
        }
    }
}
