package net.summerfarm.mall.payments.request.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.jaxb.JaxbUtil;
import net.summerfarm.enums.HandleEventStatus;
import net.summerfarm.enums.RefundStatusEnum;
import net.summerfarm.mall.Conf;
import net.summerfarm.mall.common.config.DynamicConfig;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.mq.PayNotifySuccessData;
import net.summerfarm.mall.common.mq.PayNotifySuccessDataV2;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.contexts.SpringContextUtil;
import net.summerfarm.mall.contexts.WechatConstant;
import net.summerfarm.mall.mapper.CompanyAccountMapper;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.mapper.RefundHandleEventMapper;
import net.summerfarm.mall.mapper.RefundMapper;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.WxAccountInfoVO;
import net.summerfarm.mall.model.vo.payment.PayResultVO;
import net.summerfarm.mall.payments.common.enums.CommonStatusEnum;
import net.summerfarm.mall.payments.common.enums.NotifyUrl;
import net.summerfarm.mall.payments.common.pojo.*;
import net.summerfarm.mall.payments.common.utils.RestTemplateUtil;
import net.summerfarm.mall.payments.notify.WeixNotifyService;
import net.summerfarm.mall.payments.request.PaymentRequestService;
import net.summerfarm.mall.service.MasterPaymentService;
import net.summerfarm.mall.service.PaymentService;
import net.summerfarm.mall.service.facade.AuthWechatFacade;
import net.summerfarm.mall.wechat.api.constant.ApiConsts;
import net.summerfarm.mall.wechat.model.OrderQuery;
import net.summerfarm.mall.wechat.utils.RequestHandler;
import net.summerfarm.mall.wechat.utils.Sha1Util;
import net.summerfarm.mall.wechat.utils.TenpayUtil;
import net.summerfarm.mall.wechat.utils.XMLUtil;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContexts;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-06-28
 * @description
 */
@Slf4j
@Service("weixPaymentRequestService")
public class WeixPaymentRequestServiceImpl implements PaymentRequestService {
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private PaymentService paymentService;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private RefundHandleEventMapper refundHandleEventMapper;
    @Resource
    private WeixNotifyService weixNotifyService;
    @Resource
    private CompanyAccountMapper companyAccountMapper;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private RestTemplateUtil restTemplateUtil;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private MasterPaymentService masterPaymentService;
    @Resource
    private AuthWechatFacade authWechatFacade;
    @Resource
    private DynamicConfig dynamicConfig;

    @Override
    public String payRequest(PaymentOrderInfo payOrderInfo) {
        //支付相关参数
        WeixPayInfo weixPayInfo = payOrderInfo.getWeixPayInfo();
        boolean isMpPay = payOrderInfo.getIsMpPay();
        String openId = weixPayInfo.getOpenid();
        String weixAppId = weixPayInfo.getWeixAppId();
        String weixAppSecret = weixPayInfo.getWeixAppSecret();
        String mchId = weixPayInfo.getMchId();
        String mchKey = weixPayInfo.getMchKey();
        String subMchId = weixPayInfo.getSubMchId();
        boolean isSubApp = weixPayInfo.getIsSubApp();
        String sub_appid = weixPayInfo.getSubAppId();
        String sub_openid = RequestHolder.getMpOpenId();

        log.info("用户app{}，用户sub{}", openId, sub_openid);

        // 提交支付url
        HttpServletRequest request = RequestHolder.getRequest();
        String payurl = request.getParameter("payurl");
        String noncestr = Sha1Util.getNonceStr();
        String timestamp = Sha1Util.getTimeStamp();
        String order_price = TenpayUtil.toFen(payOrderInfo.getPayTotalPrice().doubleValue() + "");// 订单金额
        // 微信金额 以分为单位.如果不注意.页面的报错.
        // 订单名称
        String product_name = "鲜沐农场直销水果";
        // 获取jsapi_ticket
        //String ticket = (String) redisTemplate.opsForValue().get(WechatConstant.MALL_WECHAT_JSAPI_TICKET);
        String ticket = authWechatFacade.queryChannelTicket(WxOfficialAccountsChannelEnum.XM_MALL.channelCode);
        RequestHandler reqHandler = new RequestHandler(request, RequestHolder.getResponse());
        // 初始化 RequestHandler 类可以在微信的文档中找到.还有相关工具类
        reqHandler.init(weixAppId, weixAppSecret, mchKey, mchId);
        reqHandler.setParameter("mch_id", mchId); // 商户号
        log.info("使用服务号" + mchId);
        // 执行统一下单接口 获得预支付id
        reqHandler.setParameter("appid", weixAppId);
        // 子商户号
        if (isSubApp) {
            reqHandler.setParameter("sub_mch_id", subMchId);
            if (isMpPay) {
                reqHandler.setParameter("sub_appid", sub_appid); // 子商户号
                reqHandler.setParameter("sub_openid", sub_openid); // 子商户号
            }
        }
        reqHandler.setParameter("nonce_str", noncestr);
        reqHandler.setParameter("body", product_name);
        reqHandler.setParameter("out_trade_no", payOrderInfo.getPayOrderNo());
        // 商品金额,以分为单位
        reqHandler.setParameter("total_fee", order_price);
        // 用户的公网ip

        reqHandler.setParameter("spbill_create_ip", Conf.MALL_IP);
        reqHandler.setParameter("notify_url", Global.DOMAIN_NAME + NotifyUrl.WEIX_NOTIFY_URL);
        // 交易类型公众号支付
        reqHandler.setParameter("trade_type", "JSAPI");
        // 这个必填.
        reqHandler.setParameter("openid", openId);
        //设置订单的过期时间，以确保用户在规定的时间内完成支付
        // 将过期时间转换为格式化字符串，例如："yyyyMMddHHmmss"
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        Date orderTime = payOrderInfo.getOrders().getOrderTime();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(orderTime);
        // 增加29分钟30秒
        calendar.add(Calendar.MINUTE, 29);
        calendar.add(Calendar.SECOND, 30);
        String timeExpire = dateFormat.format(calendar.getTime());
        reqHandler.setParameter("time_expire", timeExpire);
        // 设置附加数据
        PaymentAttachInfoDTO attachInfoDTO = PaymentAttachInfoDTO.builder().companyAccountId(payOrderInfo.getCompanyAccountId()).build();
        reqHandler.setParameter("attach", JSONObject.toJSONString(attachInfoDTO));
        // 这里只是在组装数据.还没到执行到统一下单接口.统一下单接口的数据传递格式是xml的.所以需要组装.
        // 统一下单接口提交 xml格式
        String requestUrl = null;
        try {
            requestUrl = reqHandler.getRequestURL();
        } catch (UnsupportedEncodingException e) {
            throw new DefaultServiceException(1, "拉起微信支付异常");
        }
        //链接参数
        log.info("支付订单{}，{}", payOrderInfo.getPayOrderNo(), requestUrl);
        String result = restTemplateUtil.postForXml(ApiConsts.UNIFIEDORDER_API, requestUrl);
        Map<String, String> orderMap = XMLUtil.xmlToMap(result);
        log.info("支付订单{}，返回数据：{}", payOrderInfo.getPayOrderNo(), JSONObject.toJSONString(orderMap));

        // 判断签名是否正确
        String returnstr;
        if ("FAIL".equals(orderMap.get("return_code"))) {
            String errmsg = orderMap.get("return_msg");
            returnstr = "{\"returncode\":\"" + orderMap.get("return_code")
                    + "\",\"errmsg\":\"" + errmsg + "\"}";
            log.error("用户订单{}支付时请求签名错误，返回码:{}", payOrderInfo.getPayOrderNo(), returnstr);

            //测试环境模拟支付
            if ((SpringContextUtil.isDev() || SpringContextUtil.isQa()) && LocalTime.now().getMinute() % 2 == 0) {
                //1.模拟支付成功  2.调用支付成功回调方法
                returnstr = "{\"returncode\":\"" + orderMap.get("return_code")
                        + "\",\"errmsg\":\"" + "模拟支付已完成，请进入订单页查看" + "\"}";

                Map<String, String> resultMap = new HashMap<>();
                resultMap.put("total_fee", TenpayUtil.toFen(payOrderInfo.getPayTotalPrice() + ""));
                resultMap.put("transaction_id", payOrderInfo.getPayOrderNo());
                resultMap.put("openid", openId);
                resultMap.put("time_end", DateUtils.date2String(new Date(), DateUtils.NUMBER_DATE_FORMAT));
                resultMap.put("bank_type", "XMBC");
                resultMap.put("trade_type", "MOCK_PAY");

                // 当支付信息没有重复，对支付成功信息更新
                Payment payment = new Payment();
                if (Conf.MP_APP_ID.equals(resultMap.get("appid"))) {
                    payment.setPayType(Global.MINI_PROGRAM_PAY);
                } else {
                    payment.setPayType(Global.WECHAT_PAY);
                }
                if (payOrderInfo.getCompanyAccountId() != null) {
                    payment.setCompanyAccountId(payOrderInfo.getCompanyAccountId());
                } else {
                    payment.setCompanyAccountId(1);
                }
                payment.setOrderNo(payOrderInfo.getPayOrderNo());
                payment.setTransactionNumber(resultMap.get("transaction_id"));
                payment.setMoney(TenpayUtil.toYuan(resultMap.get("total_fee")));
                payment.setEndTime(new Date());
                payment.setTradeType(resultMap.get("trade_type"));
                payment.setBankType(resultMap.get("bank_type"));
                payment.setStatus(1);
                Orders orders = ordersMapper.selectByOrderNo(payOrderInfo.getOrderNo());

                //调用支付成功
                PayNotifySuccessData payNotifySuccessData=new PayNotifySuccessData();
                payNotifySuccessData.setPayment(payment);
                payNotifySuccessData.setOrder(orders);
                MQData mqData = new MQData();
                mqData.setType(MType.PAYMENT_NOTIFY_SUCCESS.name());
                mqData.setData(JSONObject.toJSONString(payNotifySuccessData));
                //producer.sendOrderlyDataToQueue(MQTopicConstant.MALL_PAYMENT_LIST, JSONObject.toJSONString(mqData), orders.getOrderNo());
                mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST,null,JSONObject.toJSONString(mqData), orders.getOrderNo());
            }

            return returnstr;
        }

        if ("FAIL".equals(orderMap.get("result_code"))) {
            returnstr = "{\"returncode\":\"" + orderMap.get("result_code")
                    + "\",\"errmsg\":\"" + orderMap.get("err_code") + "\"}";
            log.error("用户订单{}支付时请求签名错误，返回码:{}", payOrderInfo.getOrderNo(), returnstr);
            return returnstr;
        }

        // 交易信息正确得到的预支付id
        String prepay_id = orderMap.get("prepay_id");
        SortedMap<String, String> params = new TreeMap<>();
        //小程序服务商替特约商拉起支付与公众号不同
        params.put("appId", isMpPay && isSubApp ? sub_appid : weixAppId);
        params.put("timeStamp", timestamp);
        params.put("nonceStr", noncestr);
        params.put("package", "prepay_id=" + prepay_id);
        params.put("signType", "MD5");
        // 生成支付签名,这个签名 给 微信支付的调用使用
        String paySign = reqHandler.createSign(params);
        // 这个签名主要是给加载微信js使用.和上面的不同
        request.setAttribute("paySign", paySign);
        //小程序服务商替特约商拉起支付与公众号不同
        request.setAttribute("appId", isMpPay ? sub_appid : weixAppId);
        // 时间戳
        request.setAttribute("timeStamp", timestamp);
        // 随机字符串
        request.setAttribute("nonceStr", noncestr);
        // 加密格式
        request.setAttribute("signType", "MD5");
        // 订单号
        request.setAttribute("out_trade_no", payOrderInfo.getOrderNo());
        // 预支付id,就这样的格式.
        request.setAttribute("package", "prepay_id=" + prepay_id);

        String signValue = "jsapi_ticket=" + ticket + "&noncestr="
                + noncestr + "&timestamp=" + timestamp + "&url=" + payurl;
        String signature = Sha1Util.getSha1((signValue));

        request.setAttribute("signature", signature);
        returnstr = "{\"returncode\":\"SUCCESS\",\"appId\":\""
                + weixAppId + "\",\"paySign\":\"" + paySign
                + "\",\"timeStamp\":\"" + timestamp + "\","
                + "\"nonceStr\":\"" + noncestr
                + "\",\"signType\":\"MD5\",\"out_trade_no\":\""
                + payOrderInfo.getOrderNo() + "\"," + "\"package\":\"prepay_id="
                + prepay_id + "\",\"signature\":\"" + signature + "\"}";
        log.info("微信订单{}请求支付成功", payOrderInfo.getPayOrderNo());

        //插入支付信息
        paymentService.insertPayment(null, payOrderInfo.getPayOrderNo(), payOrderInfo.getPayTypeStr(), payOrderInfo.getCompanyAccountId(), null, null);

        return returnstr;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayResultVO payRequestV2(PaymentOrderInfoV2 orderInfoV2) {
        log.info("用户app{}，用户sub{}，服务号{}", orderInfoV2.getWeixPayInfo().getOpenid(), RequestHolder.getMpOpenId(), orderInfoV2.getWeixPayInfo().getMchId());

        //组装微信支付参数
        String noncestr = Sha1Util.getNonceStr();
        String timestamp = Sha1Util.getTimeStamp();
        HttpServletRequest request = RequestHolder.getRequest();
        RequestHandler reqHandler = initRequestParams(orderInfoV2, request, noncestr);

        //使用xml格式调用支付接口
        String requestUrl;
        try {
            requestUrl = reqHandler.getRequestURL();
        } catch (UnsupportedEncodingException e) {
            throw new DefaultServiceException(1, "拉起微信支付异常");
        }
        String result = restTemplateUtil.postForXmlV2(ApiConsts.UNIFIEDORDER_API, requestUrl);
        Map<String, String> orderMap = XMLUtil.xmlToMap(result);
        log.info("支付订单{}，requestUrl：{}，返回数据：{}", orderInfoV2.getPayOrderNo(), requestUrl, JSONObject.toJSONString(orderMap));

        // 处理预支付请求结果
        PayResultVO payResultVO = new PayResultVO();
        if ("FAIL".equals(orderMap.get("return_code"))) {
            String errmsg = orderMap.get("return_msg");
            log.error("用户订单{}支付时请求签名错误，errmsg:{}", orderInfoV2.getPayOrderNo(), errmsg);

            payResultVO.setReturncode(orderMap.get("return_code"));
            payResultVO.setErrmsg(errmsg);

            //测试环境模拟支付逻辑，生产环境禁止调用
//            if (!SpringContextUtil.isProduct()) {
//                boolean mockResult = mockPaySuccess(orderInfoV2);
//                if (mockResult) {
//                    payResultVO.setErrmsg("模拟支付已完成，请进入订单页查看");
//                }
//            }

            payResultVO.setPrepayFail(true);
            return payResultVO;
        }

        if ("FAIL".equals(orderMap.get("result_code"))) {
            payResultVO.setReturncode(orderMap.get("result_code"));
            payResultVO.setErrmsg(orderMap.get("err_code"));
            log.error("用户订单{}支付时请求签名错误，result_code：{}，err_code：{}", orderInfoV2.getOrderNo(), orderMap.get("result_code"), orderMap.get("err_code"));
            payResultVO.setPrepayFail(true);
            return payResultVO;
        }
        log.info("微信订单{}请求支付成功", orderInfoV2.getPayOrderNo());

        //插入支付信息
        MasterPayment masterPayment = new MasterPayment();
        masterPayment.setMasterOrderNo(orderInfoV2.getOrderNo());
        masterPayment.setPayType(orderInfoV2.getPayTypeStr());
        masterPayment.setCompanyAccountId(orderInfoV2.getCompanyAccountId());
        masterPaymentService.upsertMasterAndPayment(masterPayment, orderInfoV2.getOrdersList());

        // 交易信息正确得到的预支付id
        String prepayId = orderMap.get("prepay_id");
        String paySign = getPaySign(orderInfoV2, timestamp, noncestr, prepayId, reqHandler);
        //String ticket = (String) redisTemplate.opsForValue().get(WechatConstant.MALL_WECHAT_JSAPI_TICKET);
        String channelCode = orderInfoV2.getPopFlag() ? WxOfficialAccountsChannelEnum.POP_MALL.channelCode : WxOfficialAccountsChannelEnum.XM_MALL.channelCode;
        String ticket = authWechatFacade.queryChannelTicket(channelCode);
        String signValue = "jsapi_ticket=" + ticket + "&noncestr="
                + noncestr + "&timestamp=" + timestamp + "&url=" + request.getParameter("payurl");
        String signature = Sha1Util.getSha1((signValue));

        payResultVO.setReturncode("SUCCESS");
        payResultVO.setAppId(orderInfoV2.getWeixPayInfo().getWeixAppId());
        payResultVO.setPaySign(paySign);
        payResultVO.setTimeStamp(timestamp);
        payResultVO.setNonceStr(noncestr);
        payResultVO.setSignType("MD5");
        payResultVO.setPaymentNo(orderInfoV2.getPayOrderNo());
        payResultVO.setPackageStr("prepay_id=" + prepayId);
        payResultVO.setSignature(signature);

        return payResultVO;
    }

    /**
     * 初始化微信支付参数
     * @param orderInfoV2  订单信息
     * @param request 请求
     * @param noncestr 随机字符串
     * @return 请求处理器
     */
    private RequestHandler initRequestParams(PaymentOrderInfoV2 orderInfoV2, HttpServletRequest request, String noncestr) {
        WeixPayInfo weixPayInfo = orderInfoV2.getWeixPayInfo();

        RequestHandler reqHandler = new RequestHandler(request, RequestHolder.getResponse());
        reqHandler.init(weixPayInfo.getWeixAppId(), weixPayInfo.getWeixAppSecret(), weixPayInfo.getMchKey(), weixPayInfo.getMchId());
        reqHandler.setParameter("mch_id", weixPayInfo.getMchId()); // 商户号
        reqHandler.setParameter("appid", weixPayInfo.getWeixAppId());
        if (weixPayInfo.getIsSubApp()) {
            reqHandler.setParameter("sub_mch_id", weixPayInfo.getSubMchId());
            if (orderInfoV2.getIsMpPay()) {
                reqHandler.setParameter("sub_appid", weixPayInfo.getSubAppId()); // 子商户号
                reqHandler.setParameter("sub_openid", RequestHolder.getMpOpenId()); // 子商户号
            }
        }
        boolean popFlag = orderInfoV2.getPopFlag();
        reqHandler.setParameter("nonce_str", noncestr);
        reqHandler.setParameter("body", popFlag ? "顺鹿达销售水果" : "鲜沐农场直销水果");
        reqHandler.setParameter("out_trade_no", orderInfoV2.getPayOrderNo());
        reqHandler.setParameter("total_fee", TenpayUtil.toFen(orderInfoV2.getPayTotalPrice().doubleValue() + ""));
        reqHandler.setParameter("spbill_create_ip", Conf.MALL_IP);
        String notifyUrl = popFlag ? dynamicConfig.getPopMallDomain() : Global.DOMAIN_NAME;
        reqHandler.setParameter("notify_url", notifyUrl + NotifyUrl.WEIX_NOTIFY_URL_V2);
        reqHandler.setParameter("trade_type", "JSAPI");
        reqHandler.setParameter("openid", weixPayInfo.getOpenid());
        LocalDateTime orderTime = orderInfoV2.getMasterOrder().getOrderTime();
        LocalDateTime lastPayTime = orderTime.plusMinutes(29).plusSeconds(30);
        reqHandler.setParameter("time_expire", lastPayTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        PaymentAttachInfoDTO attachInfoDTO = PaymentAttachInfoDTO.builder().companyAccountId(orderInfoV2.getCompanyAccountId()).build();
        reqHandler.setParameter("attach", JSONObject.toJSONString(attachInfoDTO));
        return reqHandler;
    }

    private String getPaySign(PaymentOrderInfoV2 orderInfoV2, String timestamp, String noncestr, String prepayId, RequestHandler reqHandler) {
        WeixPayInfo weixPayInfo = orderInfoV2.getWeixPayInfo();

        SortedMap<String, String> params = new TreeMap<>();
        params.put("appId", orderInfoV2.getIsMpPay() && weixPayInfo.getIsSubApp() ? weixPayInfo.getSubAppId() : weixPayInfo.getWeixAppId());
        params.put("timeStamp", timestamp);
        params.put("nonceStr", noncestr);
        params.put("package", "prepay_id=" + prepayId);
        params.put("signType", "MD5");
        return reqHandler.createSign(params);
    }

    /**
     * 模拟支付成功处理「生产环境严禁调用」
     * @param orderInfoV2 订单信息
     * @return 支付结果
     */
    private boolean mockPaySuccess(PaymentOrderInfoV2 orderInfoV2) {
        boolean mockFlag = (SpringContextUtil.isDev() || SpringContextUtil.isQa()) && LocalTime.now().getMinute() % 2 == 0;
        if (!mockFlag) {
            return false;
        }

        WeixPayInfo weixPayInfo = orderInfoV2.getWeixPayInfo();

        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("total_fee", TenpayUtil.toFen(orderInfoV2.getPayTotalPrice() + ""));
        resultMap.put("transaction_id", orderInfoV2.getPayOrderNo());
        resultMap.put("openid", weixPayInfo.getOpenid());
        resultMap.put("time_end", DateUtils.date2String(new Date(), DateUtils.NUMBER_DATE_FORMAT));
        resultMap.put("bank_type", "XMBC");
        resultMap.put("trade_type", "MOCK_PAY");

        // 当支付信息没有重复，对支付成功信息更新
        MasterPayment masterPayment = new MasterPayment();
        if (Conf.MP_APP_ID.equals(resultMap.get("appid"))) {
            masterPayment.setPayType(Global.MINI_PROGRAM_PAY);
        } else {
            masterPayment.setPayType(Global.WECHAT_PAY);
        }
        if (orderInfoV2.getCompanyAccountId() != null) {
            masterPayment.setCompanyAccountId(orderInfoV2.getCompanyAccountId());
        } else {
            masterPayment.setCompanyAccountId(1);
        }
        masterPayment.setMasterOrderNo(orderInfoV2.getPayOrderNo());
        masterPayment.setTransactionNumber(resultMap.get("transaction_id"));
        masterPayment.setMoney(TenpayUtil.toYuan(resultMap.get("total_fee")));
        masterPayment.setEndTime(LocalDateTime.now());
        masterPayment.setTradeType(resultMap.get("trade_type"));
        masterPayment.setBankType(resultMap.get("bank_type"));
        masterPayment.setStatus(1);

        //调用支付成功
        PayNotifySuccessDataV2 successDataV2 = new PayNotifySuccessDataV2();
        successDataV2.setMasterPayment(masterPayment);
        successDataV2.setMasterOrder(orderInfoV2.getMasterOrder());
        MQData mqData = new MQData();
        mqData.setType(MType.MASTER_PAYMENT_NOTIFY_SUCCESS.name());
        mqData.setData(JSONObject.toJSONString(successDataV2));
        mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST,null,JSONObject.toJSONString(mqData), orderInfoV2.getOrderNo());

        return true;
    }

    @Override
    public AjaxResult refundRequest(RefundOrderInfo refundOrderInfo) throws IOException, KeyStoreException, CertificateException, NoSuchAlgorithmException, UnrecoverableKeyException, KeyManagementException {
        WeixPayInfo weixPayInfo = refundOrderInfo.getWeixPayInfo();
        String appId = weixPayInfo.getWeixAppId();
        String appSecret = weixPayInfo.getWeixAppSecret();
        log.info("退款渠道：{}", refundOrderInfo.getPayTypeStr());

        //微信服务商号
        String mchId = weixPayInfo.getMchId();
        String mchKey = weixPayInfo.getMchKey();
        String certPath = weixPayInfo.getCertPath();
        String subMchId = weixPayInfo.getSubMchId();

        Refund refund = refundOrderInfo.getRefund();
        String refundNo = refund.getRefundNo();
        BigDecimal totalFee = refund.getMasterTotalFee();// 订单金额
        String orderNo = refund.getMasterOrderNo();
        BigDecimal refundFee = refund.getRefundFee();
        String noncestr = Sha1Util.getNonceStr();// 生成随机字符串
        RequestHandler reqHandler = new RequestHandler(null, null);
        // 初始化 RequestHandler 类
        reqHandler.init(appId, appSecret, mchKey, mchId);

        reqHandler.setParameter("mch_id", mchId); // 商户号

        if (StringUtils.isNotBlank(subMchId)) {
            reqHandler.setParameter("sub_mch_id", subMchId); // 子商户号
        }

        // 执行退款接口
        reqHandler.setParameter("appid", appId);
        reqHandler.setParameter("nonce_str", noncestr); // 随机字符串
        //reqHandler.setParameter("op_user_id", Conf.MCHID); // 操作员号
        reqHandler.setParameter("out_trade_no", orderNo); // 商家订单号
        reqHandler.setParameter("out_refund_no", refundNo); // 商家退款号
        log.info("totalfee的值为：{}refund_fee:{}", totalFee.intValue(), refundFee.intValue());
        reqHandler.setParameter("total_fee", totalFee.intValue() + ""); // 订单金额
        reqHandler.setParameter("refund_fee", refundFee.intValue() + ""); // 退款金额(全额)
        reqHandler.setParameter("notify_url",Global.DOMAIN_NAME + NotifyUrl.WEIX_REFUND_URL);
        String requestUrl = reqHandler.getRequestURL();
        KeyStore keyStore;
        keyStore = KeyStore.getInstance("PKCS12");//指定读取证书格式为PKCS12
        //读取本机存放的PKCS12证书文件
        FileInputStream instream;
        instream = new FileInputStream(new File(certPath));
        //指定PKCS12的密码(默认商户ID)
        SSLContext sslcontext;
        //先写死，后面再改
        keyStore.load(instream, mchId.toCharArray());
        instream.close();
        sslcontext = SSLContexts.custom().loadKeyMaterial(keyStore, mchId.toCharArray()).build();

        //指定TLS版本
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslcontext, null, null, SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
        //设置httpclient的SSLSocketFactory
        CloseableHttpClient httpclient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
        HttpPost httpPost = new HttpPost(ApiConsts.REFUND_API);//退款接口
        StringEntity reqEntity = new StringEntity(requestUrl);
        // 设置类型
        reqEntity.setContentType("application/x-www-form-urlencoded");
        httpPost.setEntity(reqEntity);
        CloseableHttpResponse response1 = httpclient.execute(httpPost);
        HttpEntity entity = response1.getEntity();
        log.info("退款退款退款,orderNO{},ss{}",orderNo,JSONObject.toJSONString(entity));
        AjaxResult result = null;
        if (entity != null) {
            // 获取返回值
            BufferedReader in = new BufferedReader(new InputStreamReader(entity.getContent()));
            String line;
            StringBuffer strBuf = new StringBuffer();
            while ((line = in.readLine()) != null) {
                strBuf.append(line).append("\n");
            }
            in.close();
            String sb = strBuf.toString().trim();
            log.info("微信退款返回结果：{}", sb);
            Map<String, String> RefundMap;
            RefundMap = XMLUtil.xmlToMap(sb);     // String格式的xml数据

            //处理返回数据
            if (RefundMap.get("return_code").equals("FAIL")) {
                result = AjaxResult.getError(RefundMap.get("return_code"), RefundMap.get("return_msg"));
                // 判断交易是否正确
            } else if (RefundMap.get("result_code").equals("FAIL")) {
                result = AjaxResult.getError(RefundMap.get("result_code"), RefundMap.get("err_code_des"));
                // 1.在退款表中保存交易错误码
                refund.setEndTime(new Date());
                refund.setErrCode(RefundMap.get("err_code"));
                refund.setErrCodeDes(RefundMap.get("err_code_des"));
                refundMapper.updateByPrimaryKeySelective(refund);

            } else {
                result = AjaxResult.getOK();
                // 退款成功信息更新
                String transactionNumber = RefundMap.get("refund_id");
                BigDecimal cash_fee = TenpayUtil.toYuan(RefundMap.get("cash_fee"));
                BigDecimal cash_refund_fee = TenpayUtil.toYuan(RefundMap.get("cash_refund_fee"));
                String refund_channel = RefundMap.get("refund_channel");
                // 1.在退款表中更新退款成功信息
                refund.setStatus((byte) RefundStatusEnum.SUCCESS.ordinal());
                refund.setTransactionNumber(transactionNumber);
                refund.setEndTime(new Date());
                refund.setCashFee(cash_fee);
                refund.setTotalFee(new BigDecimal(RefundMap.get("total_fee")));
                refund.setCashRefundFee(cash_refund_fee);
                refund.setRefundChannel(refund_channel);
                refund.setErrCode("");
                refund.setErrCodeDes("");
                try {
                    if(StringUtils.isNotBlank(RefundMap.get("success_time"))){
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        Date date = sdf.parse(RefundMap.get("success_time"));
                        refund.setOnlineRefundEndTime(date);
                    }
                } catch (ParseException e) {
                    log.error("日期转换异常", e);
                }
                refundMapper.updateByPrimaryKeySelective(refund);
                refundHandleEventMapper.updateStatusByRefundNo(HandleEventStatus.SUCCESS.ordinal(), refundNo);
                log.info("已更新refund:{}", refund);
            }
        }
        EntityUtils.consume(entity);
        response1.close();
        httpclient.close();
        log.info(result == null ? null : result.toString());
        return result;
    }

    @Override
    public Statement queryStatement(int accountId, String date) {
        String appId;
        String appSecret;
        String mchId;
        String mchKey;

        appId = Conf.MP_APP_ID;
        appSecret = Conf.MP_APP_SECRET;
        
        CompanyAccount companyAccount = companyAccountMapper.selectByPrimaryKey(accountId);
        //微信服务商号
        String subMchId = null;
        if (companyAccount != null) {
            WxAccountInfoVO wxAccountInfoVO = JSON.parseObject(companyAccount.getWxAccountInfo(), WxAccountInfoVO.class);
            mchKey = wxAccountInfoVO.getWxMchKey();
            //父id
            if (Objects.equals(companyAccount.getId(), 5)) {
                mchId = wxAccountInfoVO.getWxMchId();
            } else {
                mchId = wxAccountInfoVO.getWxFatherMchId();
                subMchId = wxAccountInfoVO.getWxMchId();
            }
        } else {
            throw new DefaultServiceException(1, "微信配置异常");
        }
        String noncestr = Sha1Util.getNonceStr();// 生成随机字符串
        RequestHandler reqHandler = new RequestHandler(null, null);
        // 初始化 RequestHandler 类
        reqHandler.init(appId, appSecret, mchKey, mchId);

        reqHandler.setParameter("mch_id", mchId); // 商户号

        if (StringUtils.isNotBlank(subMchId)) {
            reqHandler.setParameter("sub_mch_id", subMchId); // 子商户号
        }


        reqHandler.setParameter("appid", appId);
        reqHandler.setParameter("nonce_str", noncestr); // 随机字符串
        reqHandler.setParameter("bill_date", date);
        reqHandler.setParameter("bill_type", "ALL");
        String requestUrl;
        try {
            requestUrl = reqHandler.getRequestURL();
        } catch (UnsupportedEncodingException e) {
            throw new DefaultServiceException(e.getMessage());
        }
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(ApiConsts.DOWNLOAD_BILL_API);//退款接口
        StringEntity reqEntity;
        try {
            reqEntity = new StringEntity(requestUrl);
        } catch (UnsupportedEncodingException e) {
            log.error("UnsupportedEncodingException", e);
            throw new DefaultServiceException(e.getMessage());
        }
        // 设置类型
        reqEntity.setContentType("application/x-www-form-urlencoded");
        httpPost.setEntity(reqEntity);
        CloseableHttpResponse response1 = null;
        Statement statement = null;
        try {
            response1 = httpClient.execute(httpPost);
            HttpEntity entity = response1.getEntity();
            statement = extractStatement(entity, date);
        } catch (IOException e) {
            log.error("IOException", e);
            throw new DefaultServiceException(e.getMessage());
        } finally {
            if (Objects.nonNull(response1)) {
                try {
                    response1.close();
                } catch (IOException e) {
                    log.error("关闭异常", e);
                }
            }
        }
        return statement;
    }

    private Statement extractStatement(HttpEntity entity, String date) {
        BufferedReader in;
        try {
            in = new BufferedReader(new InputStreamReader(entity.getContent()));
        } catch (IOException e) {
            log.error("IOException", e);
            throw new DefaultServiceException(e.getMessage());
        }
        String line;
        String lastLine = null;
        while (true) {
            try {
                if ((line = in.readLine()) == null) {
                    break;
                }
                if (line.equals("<return_msg><![CDATA[No Bill Exist]]></return_msg>")) {
                    log.warn("微信支付：{}当日无账单存在", date);
                    Statement statement = new Statement();
                    statement.setPayAmount(0.);
                    statement.setRefundAmount(0.);
                    return statement;
                }
                log.debug(line);
            } catch (IOException e) {
                log.error("IOException", e);
                throw new DefaultServiceException(e.getMessage());
            }
            lastLine = line;
        }

        log.info("lastLine = {}", lastLine);
        List<Double> list = Arrays.stream(lastLine.split(","))
                .map(i -> Double.parseDouble(i.substring(1))).collect(Collectors.toList());
        Statement statement = new Statement();
        statement.setPayAmount(list.get(5));
        statement.setRefundAmount(list.get(6));
        log.info("微信支付{}账单：{}", date, statement);
        return statement;
    }

    @Override
    public void syncPayResult(QueryOrderInfo queryOrderInfo) {
        WeixPayInfo weixPayInfo = queryOrderInfo.getWeixPayInfo();

        OrderQuery orderQuery = new OrderQuery();
        orderQuery.setAppid(Conf.APP_Id);
        orderQuery.setMch_id(weixPayInfo.getMchId());
        if (StringUtils.isNotBlank(weixPayInfo.getSubMchId())) {
            orderQuery.setSub_mch_id(weixPayInfo.getSubMchId());
        }

        // 生成随机字符串
        String noncestr = Sha1Util.getNonceStr();
        orderQuery.setNonce_str(noncestr);
        orderQuery.setOut_trade_no(queryOrderInfo.getPayOrderNo());
        orderQuery.setSign(TenpayUtil.getSign(orderQuery, weixPayInfo.getMchKey()));
        //bean to xml
        String requestUrl = JaxbUtil.beanToXml(orderQuery);
        log.info("orderQuery:" + requestUrl);
        // 订单查询接口提交 xml格式
        String result = restTemplateUtil.postForXml(ApiConsts.ORDERQUERY_API, requestUrl);
        log.info("result:" + result);
        Map<String, String> queryMap = XMLUtil.xmlToMap(result);

        //处理回调
        if (Objects.equals("SUCCESS", queryMap.get("trade_state"))) {
            weixNotifyService.handleNotify(queryMap);
        }
    }

    @Override
    public void syncPayResultV2(QueryOrderInfoV2 orderInfoV2) {
        WeixPayInfo weixPayInfo = orderInfoV2.getWeixPayInfo();
        Boolean isMpPay = orderInfoV2.getIsMpPay();
        boolean popFlag = orderInfoV2.getPopFlag();
        OrderQuery orderQuery = new OrderQuery();
        orderQuery.setAppid(isMpPay & popFlag ? Conf.POP_MP_APP_ID : Conf.APP_Id);
        orderQuery.setMch_id(weixPayInfo.getMchId());
        if (StringUtils.isNotBlank(weixPayInfo.getSubMchId())) {
            orderQuery.setSub_mch_id(weixPayInfo.getSubMchId());
        }

        // 生成随机字符串
        String noncestr = Sha1Util.getNonceStr();
        orderQuery.setNonce_str(noncestr);
        orderQuery.setOut_trade_no(orderInfoV2.getPayOrderNo());
        orderQuery.setSign(TenpayUtil.getSign(orderQuery, weixPayInfo.getMchKey()));
        //bean to xml
        String requestUrl = JaxbUtil.beanToXml(orderQuery);
        log.info("orderQuery:" + requestUrl);
        // 订单查询接口提交 xml格式
        String result = restTemplateUtil.postForXml(ApiConsts.ORDERQUERY_API, requestUrl);
        log.info("result:" + result);
        Map<String, String> queryMap = XMLUtil.xmlToMap(result);

        //处理回调
        if (Objects.equals("SUCCESS", queryMap.get("trade_state"))) {
            weixNotifyService.handleNotifyV2(queryMap);
        }
    }

    @Override
    public CommonStatusEnum closeOrder(BasePayOrderInfo basePayOrderInfo) {
        WeixPayInfo weixPayInfo = basePayOrderInfo.getWeixPayInfo();
        Boolean isMpPay = Optional.ofNullable(basePayOrderInfo.getIsMpPay()).orElse(Boolean.FALSE);
        boolean popFlag = basePayOrderInfo.getPopFlag();
        OrderQuery closeOrderReq = new OrderQuery();
        closeOrderReq.setAppid(isMpPay & popFlag ? Conf.POP_MP_APP_ID : Conf.APP_Id);
        closeOrderReq.setMch_id(weixPayInfo.getMchId());
        if (StringUtils.isNotBlank(weixPayInfo.getSubMchId())) {
            closeOrderReq.setSub_mch_id(weixPayInfo.getSubMchId());
        }
        closeOrderReq.setOut_trade_no(basePayOrderInfo.getPayOrderNo());
        // 生成随机字符串
        String noncestr = Sha1Util.getNonceStr();
        closeOrderReq.setNonce_str(noncestr);
        closeOrderReq.setSign(TenpayUtil.getSign(closeOrderReq, weixPayInfo.getMchKey()));

        String requestUrl = JaxbUtil.beanToXml(closeOrderReq);
        log.info("closeOrderReq:" + requestUrl);
        String result = restTemplateUtil.postForXml(ApiConsts.CLOSEORDER_API, requestUrl);
        log.info("result:" + result);

        return CommonStatusEnum.SUCCESS;
    }
}
