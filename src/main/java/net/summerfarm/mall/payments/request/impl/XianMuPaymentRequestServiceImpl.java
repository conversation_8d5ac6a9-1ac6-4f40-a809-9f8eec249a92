package net.summerfarm.mall.payments.request.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.HandleEventStatus;
import net.summerfarm.enums.RechargeRecordType;
import net.summerfarm.enums.RefundStatusEnum;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.mq.PayNotifySuccessData;
import net.summerfarm.mall.common.mq.PayNotifySuccessDataV2;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.contexts.OrderPayStatusConstant;
import net.summerfarm.mall.enums.payment.PaymentEnums;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.payment.PayResultVO;
import net.summerfarm.mall.payments.common.pojo.*;
import net.summerfarm.mall.payments.request.PaymentRequestService;
import net.summerfarm.mall.service.MasterPaymentService;
import net.summerfarm.mall.service.PaymentService;
import net.summerfarm.mall.service.RechargeRecordService;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-06-28
 * @description
 */
@Slf4j
@Service("xianMuPaymentRequestService")
public class XianMuPaymentRequestServiceImpl implements PaymentRequestService {
    @Resource
    private RechargeRecordService rechargeRecordService;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private PaymentService paymentService;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private RefundHandleEventMapper refundHandleEventMapper;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private MasterPaymentService masterPaymentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String payRequest(PaymentOrderInfo payOrderInfo) {
        Orders orders = payOrderInfo.getOrders();
        Merchant merchant = merchantMapper.selectOneByMid(orders.getmId());
        if (merchant.getRechargeAmount().compareTo(payOrderInfo.getPayTotalPrice()) < 0) {
            return "{\"returncode\":\"" + payOrderInfo.getOrderNo()
                    + "\",\"errmsg\":\"" + "余额不足" + "\"}";
        }

        //扣减余额
        Payment payment = new Payment();
        payment.setPayType(Global.XIANMU_CARD);
        payment.setOrderNo(payOrderInfo.getPayOrderNo());
        payment.setMoney(payOrderInfo.getPayTotalPrice());
        payment.setEndTime(new Date());
        payment.setStatus(1);
        payment.setCompanyAccountId(null);
        payment.setOnlinePayEndTime(new Date());
        //调价支付记录
        String tansactionNumber = rechargeRecordService.insert(orders.getmId(),
                orders.getAccountId(), RechargeRecordType.CONSUMPTION.getId(),
                orders.getOrderNo(), BigDecimal.ZERO.subtract(payOrderInfo.getPayTotalPrice()));
        payment.setTransactionNumber(tansactionNumber);
        paymentService.insertPayment(payment);

        //成功
        PayNotifySuccessData payNotifySuccessData=new PayNotifySuccessData();
        payNotifySuccessData.setPayment(payment);
        payNotifySuccessData.setOrder(orders);
        MQData mqData = new MQData();
        mqData.setType(MType.PAYMENT_NOTIFY_SUCCESS.name());
        mqData.setData(JSONObject.toJSONString(payNotifySuccessData));
        //producer.sendOrderlyDataToQueue(MQTopicConstant.MALL_PAYMENT_LIST, JSONObject.toJSONString(mqData), orders.getOrderNo());
        mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST,null,JSONObject.toJSONString(mqData), orders.getOrderNo());
        log.info("余额订单{}请求支付成功", payOrderInfo.getPayOrderNo());

        //鲜沐卡支付防重复
        redisTemplate.opsForValue().set(OrderPayStatusConstant.XM_PAY_REPEAT_CHECK + payOrderInfo.getPayOrderNo(), "", 10, TimeUnit.MINUTES);

        return "{\"returncode\":\"" + "XIANMUCARD" + "\"}";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayResultVO payRequestV2(PaymentOrderInfoV2 orderInfoV2) {
        PayResultVO payResultVO = new PayResultVO();

        MasterOrder masterOrder = orderInfoV2.getMasterOrder();
        Merchant merchant = merchantMapper.selectOneByMid(masterOrder.getMId());
        if (merchant.getRechargeAmount().compareTo(orderInfoV2.getPayTotalPrice()) < 0) {
            payResultVO.setReturncode(orderInfoV2.getOrderNo());
            payResultVO.setErrmsg("余额不足");
            payResultVO.setPrepayFail(true);
            return payResultVO;
        }

        //插入主单支付信息
        MasterPayment masterPayment = new MasterPayment();
        masterPayment.setPayType(Global.XIANMU_CARD);
        masterPayment.setMasterOrderNo(orderInfoV2.getOrderNo());
        masterPayment.setMoney(orderInfoV2.getMasterOrder().getTotalPrice());
        masterPayment.setEndTime(LocalDateTime.now());
        masterPayment.setStatus(0);
        masterPayment.setCompanyAccountId(null);
        masterPayment.setOnlinePayEndTime(LocalDateTime.now());
        masterPaymentService.upsertMasterAndPayment(masterPayment, null);

        //按订单扣减余额
        Date now = new Date();
        for (Orders orders : orderInfoV2.getOrdersList()) {
            Payment payment = new Payment();
            payment.setPayType(Global.XIANMU_CARD);
            payment.setOrderNo(orders.getOrderNo());
            payment.setMoney(orders.getTotalPrice());
            payment.setEndTime(now);
            payment.setStatus(0);
            payment.setCompanyAccountId(null);
            payment.setOnlinePayEndTime(now);

            //插入支付流水
            String transactionNumber = rechargeRecordService.insert(orders.getmId(),
                    orders.getAccountId(), RechargeRecordType.CONSUMPTION.getId(),
                    orders.getOrderNo(), BigDecimal.ZERO.subtract(orders.getTotalPrice()));
            payment.setTransactionNumber(transactionNumber);
            paymentService.insertPayment(payment);
        }

        //发送支付成功消息
        masterPayment.setStatus(1);

        PayNotifySuccessDataV2 successDataV2 = new PayNotifySuccessDataV2();
        successDataV2.setMasterOrder(masterOrder);
        successDataV2.setMasterPayment(masterPayment);
        MQData mqData = new MQData();
        mqData.setType(MType.MASTER_PAYMENT_NOTIFY_SUCCESS.name());
        mqData.setData(JSONObject.toJSONString(successDataV2));
        mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST, null, JSONObject.toJSONString(mqData), orderInfoV2.getOrderNo());
        log.info("余额订单{}请求支付成功", orderInfoV2.getPayOrderNo());

        //鲜沐卡支付防重复
        redisTemplate.opsForValue().set(OrderPayStatusConstant.XM_PAY_REPEAT_CHECK + orderInfoV2.getPayOrderNo(), "", 10, TimeUnit.MINUTES);

        payResultVO.setReturncode("XIANMUCARD");
        return payResultVO;
    }

    @Override
    public AjaxResult refundRequest(RefundOrderInfo refundOrderInfo) {
        Refund refund = refundOrderInfo.getRefund();
        log.info("鲜沐卡退款， 退款号：{}，金额：{}", refund.getRefundNo(), refund.getRefundFee());

        String orderNo = refundOrderInfo.getOrderNo();
        Orders orders = ordersMapper.selectByOrderNo(orderNo);

        //返还余额
        rechargeRecordService.insert(orders.getmId(), orders.getAccountId(), RechargeRecordType.REFUND.getId(), refund.getRefundNo(), refund.getRefundFee().divide(new BigDecimal(100)));

        // 1.在退款表中更新退款成功信息
        refund.setStatus((byte) RefundStatusEnum.SUCCESS.ordinal());
        refund.setEndTime(new Date());
        refund.setOnlineRefundEndTime(new Date());
        refund.setCashFee(refundOrderInfo.getPayTotalPrice());
        //refund.setTotalFee(refundOrderInfo.getPayTotalPrice());
        refund.setRefundFee(refund.getRefundFee());
        refund.setCashRefundFee(refund.getRefundFee().divide(BigDecimal.valueOf(100)));
        refund.setRefundChannel("");
        refund.setErrCode("");
        refund.setErrCodeDes("");
        refundMapper.updateByPrimaryKeySelective(refund);
        int i = refundHandleEventMapper.updateStatusByRefundNo(HandleEventStatus.SUCCESS.ordinal(), refund.getRefundNo());
        if (i == 0){
            throw new BizException("退款已处理成功，请勿重复处理");
        }

        return AjaxResult.getOK();
    }

    @Override
    public void syncPayResult(QueryOrderInfo queryOrderInfo) {
        Payment selectKey = new Payment();
        selectKey.setOrderNo(queryOrderInfo.getOrderNo());
        Payment payRecord = paymentMapper.selectOne(selectKey);
        if (!ObjectUtils.isEmpty(payRecord) && payRecord.getStatus() == 1) {
            redisTemplate.opsForValue().set(OrderPayStatusConstant.ORDER_PAY_SUCCESS_STATUS + payRecord.getOrderNo(), "SUCCESS", 5, TimeUnit.SECONDS);
        }
    }

    @Override
    public void syncPayResultV2(QueryOrderInfoV2 orderInfoV2) {
        MasterPayment masterPayment = masterPaymentService.selectByMasterPaymentNo(orderInfoV2.getOrderNo());
        if (!ObjectUtils.isEmpty(masterPayment) && PaymentEnums.PaymentStatus.SUCCESS.getStatus().equals(masterPayment.getStatus())) {
            redisTemplate.opsForValue().set(OrderPayStatusConstant.ORDER_PAY_SUCCESS_STATUS + orderInfoV2.getOrderNo(), "SUCCESS", 30, TimeUnit.SECONDS);
        }
    }
}
