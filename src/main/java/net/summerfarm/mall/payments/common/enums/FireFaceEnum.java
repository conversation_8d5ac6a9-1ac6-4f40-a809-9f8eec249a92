package net.summerfarm.mall.payments.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public class FireFaceEnum {
    
    @Getter
    @AllArgsConstructor
    public enum domain {
        
        PRO("https://open.lianok.com/open/v1/api/biz/do"),
        ;

        private String domain;
    }

    @Getter
    @AllArgsConstructor
    public enum env {

            PRO(0),
            SANDBOX(1),
            ;

            private final Integer env;
    }

    @Getter
    @AllArgsConstructor
    public enum responseCode {
        SUCCESS("0"),

        PAY_NOTIFY_SUCCESS("200"),

        PAY_NOTIFY_PROCESS_SUCCESS("SUCCESS")
        ;

        private final String code;
    }

    @Getter
    @AllArgsConstructor
    public enum orderStatus {
        PENDING_PAYMENT(0, "待支付"),
        IN_PROGRESS(1, "支付中"),
        SUCCESS(2, "支付成功"),
        CLOSED(3, "交易关闭"),
        PARTIAL_REFUND(4, "部分退款"),
        FULL_REFUND(5, "全部退款");

        private final Integer status;
        private final String desc;
    }


    @Getter
    @AllArgsConstructor
    public enum RefundStatus {
        // 待退款
        PENDING_REFUND(0, "待退款"),
        // 退款中
        REFUNDING(1, "退款中"),
        // 退款成功
        REFUND_SUCCESS(2, "退款成功"),
        // 退款失败
        REFUND_FAILED(3, "退款失败"),
        // 退款关闭
        REFUND_CLOSED(4, "退款关闭");

        private final Integer status;
        private final String desc;
    }
}
