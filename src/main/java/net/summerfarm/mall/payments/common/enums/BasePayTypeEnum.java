package net.summerfarm.mall.payments.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BasePayTypeEnum {
    /**
     * 0-微信支付
     */
    WEIX(0, "微信支付"),
    /**
     * 1-鲜沐卡支付
     */
    XIANMU(1, "鲜沐卡"),
    @Deprecated
    /**
     * 2-中银支付
     */
    BOC(2, "中银支付"),
    /**
     * 3-账期支付
     */
    PERIOD(3, "线下支付"),
    /**
     * 4-招行支付
     */
    CMB(4, "招行支付"),

    /**
     * 5-微信B2B支付
     */
    B2B_WECHAT_FIRE_FACE(5, "微信B2B支付"),
    /**
     * 6-智付间联
     */
    DIN_PAY(6, "智付间联"),
    ;

    private final Integer type;

    private final String typeStr;
}
