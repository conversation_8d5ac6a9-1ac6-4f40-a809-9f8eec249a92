package net.summerfarm.mall.payments.common.pojo.fireface;

import lombok.Data;

/**
 * @description:
 * @author: George
 * @date: 2025-01-13
 **/
@Data
public class FireFaceNotifyDTO {

    /**
     * 授权码
     * 用于标识本次操作的授权凭证。
     */
    private String authCode;

    /**
     * 响应状态码
     * 表示接口调用的结果状态，如 200 表示成功。
     */
    private String code;

    /**
     * 响应消息
     * 返回接口调用的提示信息或错误描述。
     */
    private String message;

    /**
     * 请求时间
     * 表示接口请求发起的时间。
     */
    private String requestTime;

    /**
     * 资源标识
     * 指明具体调用的资源或接口。
     */
    private String resource;

    /**
     * 响应体内容
     * 返回的具体业务数据内容，可能为 JSON 格式字符串。
     */
    private String respBody;

    /**
     * 响应签名
     * 用于校验接口返回数据的完整性和真实性。
     */
    private String sign;
}
