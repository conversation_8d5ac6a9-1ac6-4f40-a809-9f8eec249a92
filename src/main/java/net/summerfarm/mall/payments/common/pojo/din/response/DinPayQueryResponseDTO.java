package net.summerfarm.mall.payments.common.pojo.din.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 支付查询响应DTO
 * @author: George
 * @date: 2025-02-12
 **/
@Data
public class DinPayQueryResponseDTO {

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 支付客户端类型
     */
    private String paymentType;

    /**
     * 支付方式
     */
    private String paymentMethods;

    /**
     * 商户编号
     */
    private String merchantId;

    /**
     * 请求订单号
     */
    private String orderNo;

    /**
     * 交易金额
     */
    private BigDecimal payAmount;

    /**
     * 币种类型
     */
    private String currency;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 退款状态
     */
    private String refundStatus;

    /**
     * 订单备注
     */
    private String orderDesc;

    /**
     * 上游订单号
     */
    private String channelNumber;

    /**
     * 上游交易单号
     */
    private String outTransactionOrderId;

    /**
     * 上游子商户号(U/A/T)
     */
    private String subMerchantNo;

    /**
     * 商户公众号sub_appid
     */
    private String appid;

    /**
     * 用户openid
     */
    private String openid;

    /**
     * 微信子商户subopenid或支付宝子商户用户buyer_id
     */
    private String subopenid;

    /**
     * 用户付款银行
     */
    private String bankType;

    /**
     * 支付卡类型
     */
    private String onlineCardType;

    /**
     * 上游返回:现金支付金额
     */
    private BigDecimal cashFee;

    /**
     * 上游返回:现金券金额
     */
    private BigDecimal couponFee;

    /**
     * 订单入账面额
     */
    private BigDecimal orderCreditAmount;

    /**
     * 实际支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 渠道结算金额
     */
    private BigDecimal channelSettlementAmount;

    /**
     * 交易手续费
     */
    private BigDecimal orderFee;

    /**
     * 商户实际入账发生额
     */
    private BigDecimal merchantCreditAmount;

    /**
     * 渠道支付宝费率活动标识
     */
    private String chargeFlag;

    /**
     * 商户费率
     */
    private BigDecimal merchantFee;

    /**
     * 平台商补贴的手续费
     */
    private BigDecimal feeAccountAmt;

    /**
     * 实收手续费
     */
    private BigDecimal receiverFee;

    /**
     * 后收手续费
     */
    private BigDecimal offlineFee;

    /**
     * 订单完成时间
     */
    private String orderPayDate;

    /**
     * 营销参数规则
     */
    private String marketingRules;

    /**
     * 分账规则及状态
     */
    private String splitRules;

    /**
     * 微信交易类型
     */
    private String wxTradeType;

    /**
     * 银联返回的付款方附加数据
     */
    private String upAddData;

    /**
     * 银联二维码返回的保留数据
     */
    private String resvData;

    /**
     * 交易资金渠道和优惠信息-支付宝
     */
    private String fundBillList;

    /**
     * 优惠信息详情
     */
    private String promotionDetail;

    /**
     * 支付宝优惠信息详情
     */
    private List<VoucherDetail> voucherDetailList;

    /**
     * 管控类型
     */
    private String controlType;

    /**
     * 管控状态
     */
    private String controlStatus;

    /**
     * 内部类：支付宝优惠信息详情
     */
    public static class VoucherDetail {
        private String amount;
        private String merchantContribute;
        private String name;
        private String otherContribute;
        private String type;
        private String voucherId;
    }
}
