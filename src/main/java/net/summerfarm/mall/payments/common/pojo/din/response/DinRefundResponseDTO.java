package net.summerfarm.mall.payments.common.pojo.din.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: George
 * @date: 2025-02-17
 **/
@Data
public class DinRefundResponseDTO {

    /**
     * 接口名称
     * 实例值：AppPayRefund
     * 说明：交易退款：AppPayRefund
     */
    private String interfaceName;

    /**
     * 商户编号
     * 实例值：D10000000000002
     * 说明：支付系统分配的商户号
     */
    private String merchantId;

    /**
     * 交易请求订单号
     * 实例值：p_2024051011121314
     * 说明：要退款的交易订单的商户请求订单号与”上游订单号”二选一
     */
    private String payOrderNo;

    /**
     * 上游退款订单号
     * 实例值：202405101112130001
     * 说明：上游退款请求订单号
     */
    private String refundChannelNumber;

    /**
     * 退款订单号
     * 实例值：t_2024051011121314
     * 说明：商户退款请求订单号，商户号下唯一
     */
    private String refundOrderNo;

    /**
     * 退款金额
     * 实例值：0.01
     * 说明：退款金额，以元为单位，最小金额为0.01
     */
    private BigDecimal refundAmount;

    /**
     * 币种类型
     * 实例值：CNY
     * 说明：CNY:人民币
     */
    private String currency;

    /**
     * 管控类型
     * 实例值：CONTROL
     * 说明：CONTROL：管控退款
     *       UNCONTROL：非管控退款
     */
    private String controlType;
}
