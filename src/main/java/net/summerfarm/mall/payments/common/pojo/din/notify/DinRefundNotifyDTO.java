package net.summerfarm.mall.payments.common.pojo.din.notify;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: George
 * @date: 2025-02-17
 **/
@Data
public class DinRefundNotifyDTO {

    /**
     * 接口名称
     * 实例值：AppPayRefund
     * 说明：交易退款
     */
    private String interfaceName;

    /**
     * 商户编号
     * 实例值：D10000000000002
     * 说明：支付系统分配的商户号
     */
    private String merchantId;

    /**
     * 客户端类型
     * 实例值：WXPAY
     * 说明：ALIPAY：支付宝
     *       WXPAY:微信
     *       UNIONPAY:银联
     */
    private String paymentType;

    /**
     * 支付类型
     * 实例值：SCAN
     * 说明：SWIPE:条码(被扫)
     *       SCAN:二维码(主扫)
     *       PUBLIC:公众号
     *       APPLET:小程序
     *       WAP:H5支付
     */
    private String paymentMethods;

    /**
     * 交易请求订单号
     * 实例值：p_2024051011121314
     * 说明：要退款的交易订单的商户请求订单号
     */
    private String payOrderNo;

    /**
     * 退款订单号
     * 实例值：t_2024051011121314
     * 说明：商户退款请求订单号，商户号下唯一
     */
    private String refundOrderNo;

    /**
     * 上游退款订单号
     * 实例值：202405101112130001
     * 说明：上游退款请求订单号
     */
    private String refundChannelNumber;

    /**
     * 退款订单状态
     * 实例值：SUCCESS
     * 说明：BEFORERECEIVE:等待处理
     *       RECEIVE: 接收成功
     *       INIT：初始化
     *       DOING：处理中
     *       SUCCESS：成功
     *       FAIL：失败
     *       CLOSE：关闭
     */
    private String refundOrderStatus;

    /**
     * 退款金额
     * 实例值：0.01
     * 说明：退款金额，以元为单位，最小金额为0.01
     */
    private BigDecimal refundAmount;

    /**
     * 币种类型
     * 实例值：CNY
     * 说明：CNY:人民币
     */
    private String currency;

    /**
     * 退款完成时间
     * 实例值：2024-05-10 11:12:13
     * 说明：退款完成时间
     */
    private String refundOrderCompleteDate;

    /**
     * 通知时间
     * 实例值：2024-05-10 11:12:13
     * 说明：通知时间
     */
    private String refundNotifyDate;

    /**
     * 上游退款支付订单号
     * 实例值：202405101112130001
     * 说明：上游退款支付订单号
     */
    private String refundChannelOrderNum;

    /**
     * 退款面额
     * 实例值：0.01
     * 说明：退款请求金额，以元为单位，最小金额为0.01
     */
    private BigDecimal refundOrderAmount;

    /**
     * 用户实际退款到账金额
     * 实例值：0.01
     * 说明：订单金额，以元为单位，最小金额为0.01
     */
    private BigDecimal refundUserAmount;

    /**
     * 退还手续费
     * 实例值：0.01
     * 说明：本次退款成功,退还手续费,单位:元
     */
    private BigDecimal refundFee;

    /**
     * 退还平台商补贴的手续费
     * 实例值：0.01
     * 说明：本次退款成功,退还平台商补贴的手续费,单位:元
     */
    private BigDecimal refundFeeAccountAmt;

    /**
     * 退还实收手续费
     * 实例值：0.01
     * 说明：成功时返回
     */
    private BigDecimal refundReceiverFee;

    /**
     * 退还后收手续费
     * 实例值：0.01
     * 说明：成功时返回
     */
    private BigDecimal refundOfflineFee;

    /**
     * 退款营销规则串
     * 实例值：明串示例:{refundMarketingMerchantNo:”D10000000000001”,refundMarketingAmount:10.00}
     * 说明：营销参数规则,JSON格式字符串，SM4加密返回,详见3.6营销参数规则说明
     */
    private String refundMarketingRules;

    /**
     * 退款分账规则串
     * 实例值：[{"refundAmount":2,"splitBillMerchantNo":"W0xxxxxxxx","splitBillOrderNum":"1556013197418CBoywwwq","refundStatus":"FAILED"}]
     * 说明：退款分账规则串及状态，见附录3.7
     */
    private String refundSplitRules;

    /**
     * 退款优惠详情
     * 实例值：微信/支付宝等卡券优惠详情串
     * 说明：微信/支付宝等卡券优惠详情串
     */
    private String refundPromotionDetail;

    /**
     * 退款原因/备注
     * 实例值：退款
     * 说明：若商户传入，会在下发给用户的退款账单消息中体现退款原因
     */
    private String refundDesc;

    /**
     * 通道上游业务结果描述
     * 实例值：结果信息描述
     * 说明：结果信息描述
     */
    private String retReasonDesc;

    /**
     * 银联二维码平台付款方附加数据
     * 实例值：详见4.2.3.2
     * 说明：详见4.2.3.2
     */
    private String upAddData;

    /**
     * 管控类型
     * 实例值：CONTROL
     * 说明：CONTROL：管控退款
     *       UNCONTROL：非管控退款
     */
    private String controlType;
}
