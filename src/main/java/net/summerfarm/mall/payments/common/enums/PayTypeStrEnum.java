package net.summerfarm.mall.payments.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-08-09
 * @description
 */
@Getter
@AllArgsConstructor
public enum PayTypeStrEnum {
    /**
     * 支付宝
     */
    ZFBA("ZFBA", "支付宝"),
    /**
     * 微信
     */
    WEIX("WEIX", "微信"),
    /**
     * 银联
     */
    UPAY("UPAY", "银联");

    private final String type;

    private final String desc;
}
