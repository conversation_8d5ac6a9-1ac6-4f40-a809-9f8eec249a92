package net.summerfarm.mall.payments.common.enums;


import net.summerfarm.mall.contexts.SpringContextUtil;

/**
 * 接口请求url
 */
public class CmbPayUrl {
    /**
     * 招行支付域名
     */
    public static String CMB_DOMAIN = SpringContextUtil.isProduct() ? "https://api.cmbchina.com" : "https://api.cmburl.cn:8065";

    /**
     * 微信统一下单
     */
    public static String ONLINE_PAY = CMB_DOMAIN + "/polypay/v1.0/mchorders/onlinepay";

    /**
     * 支付宝native
     */
    public static  String ZFBA_NATIVE = CMB_DOMAIN + "/polypay/v1.0/mchorders/zfbqrcode";

    /**
     * 订单二维码申请
     */
    public static String QR_CODE = CMB_DOMAIN + "/polypay/v1.0/mchorders/orderqrcodeapply";

    /**
     * 支付结果查询
     */
    public static String ORDER_QUERY = CMB_DOMAIN + "/polypay/v1.0/mchorders/orderquery";

    /**
     * 退款申请
     */
    public static String REFUND = CMB_DOMAIN + "/polypay/v1.0/mchorders/refund";

    /**
     * 退款结果查询
     */
    public static String REFUND_QUERY = CMB_DOMAIN + "/polypay/v1.0/mchorders/refundquery";

    /**
     * 对账单下载地址获取
     */
    public static String STATEMENT_URL = CMB_DOMAIN + "/polypay/v1.0/mchorders/statementurl";

    /**
     * 秘钥设置
     */
    public static String KEY_SET = CMB_DOMAIN + "/polypay/v1.0/mchkey/keyset";

    /**
     * 关单
     */
    public static String CLOSE_ORDER = CMB_DOMAIN + "/polypay/v1.0/mchorders/close";
}
