package net.summerfarm.mall.payments.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-12-10
 **/
@Getter
@AllArgsConstructor
public enum PaymentBizTypeEnum {

    ORDER_PAY(0, "订单支付"),
    MASTER_ORDER_PAY(1, "主订单支付"),
    ;

    private Integer code;

    private String desc;

    public static PaymentBizTypeEnum getByCode(Integer code) {
        for (PaymentBizTypeEnum value : PaymentBizTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
