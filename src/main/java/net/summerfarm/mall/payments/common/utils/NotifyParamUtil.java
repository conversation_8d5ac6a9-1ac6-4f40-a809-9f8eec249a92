package net.summerfarm.mall.payments.common.utils;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-07-05
 * @description
 */
public class NotifyParamUtil {
    public static String resultParam(HttpServletRequest request) throws IOException {
        //获取回调参数 S
        InputStream inStream = request.getInputStream();
        ByteArrayOutputStream outSteam = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        while ((len = inStream.read(buffer)) != -1) {
            outSteam.write(buffer, 0, len);
        }
        outSteam.close();
        inStream.close();
        return new String(outSteam.toByteArray(), StandardCharsets.UTF_8);
    }
}
