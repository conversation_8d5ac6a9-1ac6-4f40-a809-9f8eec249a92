package net.summerfarm.mall.payments.common.pojo.din.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-02-17
 **/
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DinRefundQueryRequestDTO {

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 退款单号
     */
    private String refundOrderNo;

}
