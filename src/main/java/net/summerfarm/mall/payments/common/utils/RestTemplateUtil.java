package net.summerfarm.mall.payments.common.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description
 * @date 2023/5/9 14:41:15
 */
@Component
@Slf4j
public class RestTemplateUtil {

    @Resource
    private RestTemplate restTemplate;

    /**
     * @description: 发送post请求 通过map参数
     * @author: lzh
     * @date: 2023/5/9 14:44
     * @param: [url, requestParams, apiHeader]
     * @return: java.util.Map<java.lang.String,java.lang.String>
     **/
    public Map<String, String> postForEntity(String url, Map<String, String> requestParams, Map<String, String> apiHeader) {
        log.info("RestTemplateUtil[]postForEntity[]start[]url:{}, requestParams:{}, apiHeader:{}", url, JSONObject.toJSONString(requestParams), JSONObject.toJSONString(apiHeader));
        //restTemplate.getMessageConverters().add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        HttpHeaders headers = new HttpHeaders();

        // 以json的方式提交
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("appid", apiHeader.get("appid"));
        headers.add("timestamp", apiHeader.get("timestamp"));
        headers.add("apisign", apiHeader.get("apisign"));

        // 将请求头部和参数合成一个请求
        String requestBody = JSONObject.toJSONString(requestParams);
        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

        // 执行HTTP请求
        return (Map<String, String>) restTemplate.postForEntity(url, requestEntity, Map.class).getBody();
    }

    /**
     * @description: 发送post请求通过xml封装参数
     * @author: lzh
     * @date: 2023/5/11 16:26
     * @param: [url, xml]
     * @return: java.lang.String
     **/
    public String postForXml(String url, String xml) {
        log.info("RestTemplateUtil[]postForXml[]start[]url:{}, xml:{}", url, xml);
        //使用restTemplate（springboot 封装的用于发送请求的对象）如果不设置请求头，编码格式默认为ISO8859-1，会导致签名算法验证通过，但是微信仍然会返回签名错误的提示
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/xml; charset=UTF-8");
        headers.setContentType(type);
        HttpEntity<String> requestEntity = new HttpEntity<>(xml, headers);
        ResponseEntity<String> responseEntity =  restTemplate.postForEntity(url,requestEntity,String.class);
        String s1 = "";
        try {
            s1 = new String(Objects.requireNonNull(responseEntity.getBody()).getBytes("ISO8859-1"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("RestTemplateUtil[]postForXml[]error[]cause:{}", e.getMessage());
            throw new DefaultServiceException(1, "拉起微信支付异常");
        }
        return s1;
    }

    /**
     * @description: 发送post请求通过xml封装参数
     * @author: lzh
     * @date: 2023/5/11 16:26
     * @param: [url, xml]
     * @return: java.lang.String
     **/
    public String postForXmlV2(String url, String xml) {
        log.info("RestTemplateUtil[]postForXml[]start[]url:{}, xml:{}", url, xml);
        //使用restTemplate（springboot 封装的用于发送请求的对象）如果不设置请求头，编码格式默认为ISO8859-1，会导致签名算法验证通过，但是微信仍然会返回签名错误的提示
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/xml; charset=UTF-8");
        headers.setContentType(type);
        HttpEntity<String> requestEntity = new HttpEntity<>(xml, headers);
        ResponseEntity<String> responseEntity =  restTemplate.postForEntity(url,requestEntity,String.class);
        String s1 = "";
        try {
            s1 = new String(Objects.requireNonNull(responseEntity.getBody()).getBytes("UTF-8"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("RestTemplateUtil[]postForXml[]error[]cause:{}", e.getMessage());
            throw new DefaultServiceException(1, "拉起微信支付异常");
        }
        return s1;
    }

}
