package net.summerfarm.mall.payments.common.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.wechat.utils.MD5Util;
import org.apache.tomcat.util.codec.binary.Base64;
import org.bouncycastle.asn1.ASN1EncodableVector;
import org.bouncycastle.asn1.ASN1Integer;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.DERSequence;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;
import org.bouncycastle.jcajce.spec.SM2ParameterSpec;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.ECParameterSpec;
import org.bouncycastle.jce.spec.ECPrivateKeySpec;
import org.bouncycastle.jce.spec.ECPublicKeySpec;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

/**
 * <AUTHOR>
 * @description 招行支付处理工具类
 */

@Slf4j
public class CmbPayUtil {
    /**
     * 发起请求
     *
     * @param url         地址
     * @param requestParams 请求参数
     * @param apiHeader   请求头
     * @return 请求结果
     */
    public static Map<String, String> postForEntity(String url, Map<String, String> requestParams, Map<String, String> apiHeader) {
        RestTemplate client = new RestTemplate();
        client.getMessageConverters().add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        HttpHeaders headers = new HttpHeaders();

        // 以json的方式提交
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("appid", apiHeader.get("appid"));
        headers.add("timestamp", apiHeader.get("timestamp"));
        headers.add("apisign", apiHeader.get("apisign"));

        // 将请求头部和参数合成一个请求
        String requestBody = JSONObject.toJSONString(requestParams);
        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);
        // 执行HTTP请求

        return (Map<String, String>) client.postForEntity(url, requestEntity, Map.class).getBody();
    }

    public static String getMD5Content(Map<String, String> requestParam){
        String content = SignatureUtil.getSignContent(requestParam);
        return MD5Util.getMD5(content.getBytes()).toLowerCase();
    }

    /**
     * 元转分
     *
     * @param yuan 元
     * @return fen
     */
    public static String yuanToFen(BigDecimal yuan) {
        BigDecimal fen = yuan.multiply(BigDecimal.valueOf(100));
        return fen.toString().substring(0, fen.toString().indexOf("."));
    }

    /**
     * 签名工具
     */
    public static class SignatureUtil {

        public SignatureUtil() {
        }

        public static String rsaSign(Map<String, String> requestParams, String privateKey, String charset) {
            String content = SignatureUtil.getSignContent(requestParams);
            try {
                PrivateKey priKey = getPrivateKeyFromPKCS8("RSA", new ByteArrayInputStream(privateKey.getBytes()));
                Signature signature = Signature.getInstance("SM2withSM3");
                signature.initSign(priKey);
                if (null == charset || charset.length() == 0) {
                    signature.update(content.getBytes());
                } else {
                    signature.update(content.getBytes(charset));
                }

                byte[] signed = signature.sign();
                return new String(Base64.encodeBase64(signed));
            } catch (Exception var6) {
                log.error("签名异常：", var6);
                throw new RuntimeException("RSAcontent = " + content + "; charset = " + charset, var6);
            }
        }

        public static PrivateKey getPrivateKeyFromPKCS8(String algorithm, InputStream ins) throws Exception {
            if (ins != null && null != algorithm && algorithm.length() != 0) {
                KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
                byte[] encodedKey = Base64.decodeBase64(copyToString(ins, StandardCharsets.UTF_8));
                return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(encodedKey));
            } else {
                return null;
            }
        }

        public static String copyToString(InputStream in, Charset charset) throws IOException {
            if (in == null) {
                return "";
            } else {
                StringBuilder out = new StringBuilder();
                InputStreamReader reader = new InputStreamReader(in, charset);
                char[] buffer = new char[4096];

                int bytesRead;
                while ((bytesRead = reader.read(buffer)) != -1) {
                    out.append(buffer, 0, bytesRead);
                }

                return out.toString();
            }
        }

        public static String getSignContent(Map<String, String> sortedParams) {
            StringBuffer content = new StringBuffer();
            List<String> keys = new ArrayList<String>(sortedParams.keySet());
            Collections.sort(keys);
            int index = 0;
            for (int i = 0; i < keys.size(); i++) {
                String key = keys.get(i);
                String value = sortedParams.get(key);
                if (null != key && null != value) {
                    content.append((index == 0 ? "" : "&") + key + "=" + value);
                    index++;
                }
            }
            return content.toString();
        }

        public static boolean rsaCheck(Map<String, String> requestParam, String sign, String publicKey) {
            String content = getSignContent(requestParam);
            return rsaCheck(content, sign, publicKey, StandardCharsets.UTF_8.name());
        }

        public static boolean rsaCheck(String content, String sign, String publicKey, String charset) {
            try {
                PublicKey pubKey = getPublicKeyFromX509("RSA", new ByteArrayInputStream(publicKey.getBytes()));
                Signature signature = Signature.getInstance("SM2withSM3");
                signature.initVerify(pubKey);
                if (null == charset || charset.length() == 0) {
                    signature.update(content.getBytes());
                } else {
                    signature.update(content.getBytes(charset));
                }

                return signature.verify(Base64.decodeBase64(sign.getBytes()));
            } catch (Exception var6) {
                throw new RuntimeException("RSAcontent = " + content + ",sign=" + sign + ",charset = " + charset, var6);
            }
        }

        public static PublicKey getPublicKeyFromX509(String algorithm, InputStream ins) throws Exception {
            KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
            byte[] encodedKey = Base64.decodeBase64(copyToString(ins, StandardCharsets.UTF_8));
            return keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
        }


    }

    /**
     * 固定参数
     */
    public static class CmbPayConstant {

        public static final String VERSION = "version";

        public static final String ENCODING = "encoding";

        public static final String SIGN = "sign";

        public static final String SIGN_METHOD = "signMethod";

        public static final String VERSION_VALUE = "0.0.1";

        public static final String ENCODING_VALUE = "UTF-8";

        public static final String SIGN_METHOD_VALUE = "02";

        public static final String BIZ_CONTENT = "biz_content";

        public static final String UTF_8 = "UTF-8";
    }

    public static class SM2Util {

        private static final String SM2_KEY_TITLE = "3059301306072a8648ce3d020106082a811ccf5501822d03420004";

        public static final String USER_ID = "1234567812345678";


        public static String sm2Sign(Map<String, String> requestParams, String privateKey){
            String content = SignatureUtil.getSignContent(requestParams);
            return sm2Sign(content, privateKey);
        }

        public static String sm2Sign(String content, String privateKey) {
            try {
                //init privateKey
                BCECPrivateKey bcecPrivateKey = BCUtil.getPrivatekeyFromD(new BigInteger(privateKey, 16));

                byte[] sign = BCUtil.signSm3WithSm2(content.getBytes(), USER_ID.getBytes(), bcecPrivateKey);

                return encodeBase64(signRawToAsn1(sign));
            } catch (Exception ex) {
                log.error("异常：",ex.getMessage(), ex);
                return null;
            }
        }

        public static boolean sm2Check(Map<String, String> requestParams, String rawSign, String publicKey){
            String content = SignatureUtil.getSignContent(requestParams);
            return sm2Check(content, rawSign, publicKey);
        }

        /**
         * @param content
         * @param rawSign
         * @param publicKey
         * @return
         */
        public static boolean sm2Check(String content, String rawSign, String publicKey) {
            try {
                //init PublicKey
                Sm2Vo sm2Vo = parseBase64TRawKey(publicKey);
                if (null == sm2Vo) {
                    return false;
                }
                BCECPublicKey bcecPublicKey = BCUtil.getPublickeyFromXY(new BigInteger(sm2Vo.getSm2_x(), 16), new BigInteger(sm2Vo.getSm2_y(), 16));

                byte[] sign = signAsn12Raw(decodeBase64(rawSign));

                return BCUtil.verifySm3WithSm2(content.getBytes(), USER_ID.getBytes(), sign, bcecPublicKey);

            } catch (Exception ex) {
                log.error("异常：",ex.getMessage(), ex);
                return false;
            }
        }

        /**
         * BASE64格式公钥转换为裸公钥
         *
         * @param sm2Key
         * @return
         */
        private static Sm2Vo parseBase64TRawKey(String sm2Key) {
            if (null == sm2Key) {
                return null;
            }

            String sm2_asn1 = Hex.toHexString(decodeBase64(sm2Key));
            if (!sm2_asn1.startsWith(SM2_KEY_TITLE)) {
                return null;
            }

            String sm2_xy = sm2_asn1.substring(SM2_KEY_TITLE.length());
            String sm2_x = sm2_xy.substring(0, sm2_xy.length() / 2);
            String sm2_y = sm2_xy.substring(sm2_xy.length() / 2);

            return new Sm2Vo(SM2_KEY_TITLE, sm2_x, sm2_y);
        }

        /**
         * 将字节数组转换为Base64格式字符串
         *
         * @param data
         * @return
         */
        public static String encodeBase64(byte[] data) {
            return java.util.Base64.getEncoder().encodeToString(data);
        }

        /**
         * 将Base64格式字符串转为字节数组
         *
         * @param data
         * @return
         */
        public static byte[] decodeBase64(String data) {
            return java.util.Base64.getDecoder().decode(data);
        }

        /**
         * 将BC SM2 RAW签名值转化为ASN1格式签名值
         *
         * @param bcCipTxt
         * @return
         * @throws Exception
         */
        private static byte[] signRawToAsn1(byte[] bcCipTxt) throws Exception {

            byte[] netSignCipTxt = new byte[73];

            byte[] signR = new byte[32];
            byte[] signS = new byte[32];

            System.arraycopy(bcCipTxt, 0, signR, 0, 32);
            System.arraycopy(bcCipTxt, 32, signS, 0, 32);

            //signR补位
            int wPos = 4;
            netSignCipTxt[0] = 0x30;
            netSignCipTxt[2] = 0x02;
            if ((signR[0] & 0xFF) >= 128) {
                netSignCipTxt[wPos - 1] = 0x21;
                netSignCipTxt[wPos] = 0x00;
                wPos += 1;
            } else {
                netSignCipTxt[wPos - 1] = 0x20;
            }
            System.arraycopy(signR, 0, netSignCipTxt, wPos, 32);
            wPos += 32;

            //signS补位
            netSignCipTxt[wPos] = 0x02;
            wPos += 1;
            if ((signS[0] & 0xFF) >= 128) {
                netSignCipTxt[wPos] = 0x21;
                wPos += 1;
                netSignCipTxt[wPos] = 0x00;
                wPos += 1;
            } else {
                netSignCipTxt[wPos] = 0x20;
                wPos += 1;
            }
            System.arraycopy(signS, 0, netSignCipTxt, wPos, 32);
            wPos += 32;

            if (70 == wPos) {
                netSignCipTxt[1] = 0x44;
            } else if (71 == wPos) {
                netSignCipTxt[1] = 0x45;
            } else if (72 == wPos) {
                netSignCipTxt[1] = 0x46;
            } else {
                throw new Exception("signRawToAsn1 Error!");
            }

            byte[] resultBytes = new byte[wPos];
            System.arraycopy(netSignCipTxt, 0, resultBytes, 0, wPos);

            return resultBytes;
        }

        /**
         * 将ASN1格式签名值转化为BC SM2 RAW 签名值
         *
         * @param signature Asn1格式签名值
         * @return byte[] Raw签名值
         */
        private static byte[] signAsn12Raw(byte[] signature) throws Exception {

            byte[] resultBytes = new byte[64];

            //截取signR
            int wPos = 3;
            if ((signature[wPos] & 0xFF) == 32) {
                wPos += 1;
            } else if ((signature[wPos] & 0xFF) == 33) {
                wPos += 2;
            } else {
                throw new Exception("signR length Error!");
            }
            System.arraycopy(signature, wPos, resultBytes, 0, 32);
            wPos += 32;

            //截取signS
            wPos += 1;
            if ((signature[wPos] & 0xFF) == 32) {
                wPos += 1;
            } else if ((signature[wPos] & 0xFF) == 33) {
                wPos += 2;
            } else {
                throw new Exception("signS length Error!");
            }
            System.arraycopy(signature, wPos, resultBytes, 32, 32);


            return resultBytes;
        }


        public static class Sm2Vo {

            //标准公钥头
            private String sm2_h;
            //裸公钥X
            private String sm2_x;
            //裸公钥Y
            private String sm2_y;

            public Sm2Vo(String sm2_h, String sm2_x, String sm2_y) {
                this.sm2_h = sm2_h;
                this.sm2_x = sm2_x;
                this.sm2_y = sm2_y;
            }

            public String getSm2_h() {
                return sm2_h;
            }

            public void setSm2_h(String sm2_h) {
                this.sm2_h = sm2_h;
            }

            public String getSm2_x() {
                return sm2_x;
            }

            public void setSm2_x(String sm2_x) {
                this.sm2_x = sm2_x;
            }

            public String getSm2_y() {
                return sm2_y;
            }

            public void setSm2_y(String sm2_y) {
                this.sm2_y = sm2_y;
            }
        }
    }

    public static class BCUtil {
        private final static int RS_LEN = 32;
        private static final X9ECParameters x9ECParameters = GMNamedCurves.getByName("sm2p256v1");
        private static final ECDomainParameters ecDomainParameters = new ECDomainParameters(x9ECParameters.getCurve(),x9ECParameters.getG(),x9ECParameters.getN());
        private static final ECParameterSpec ecParameterSpec = new ECParameterSpec(x9ECParameters.getCurve(),x9ECParameters.getG(),x9ECParameters.getN());

        static {
            if (Security.getProvider("BC") == null) {
                Security.addProvider(new BouncyCastleProvider());
            }
        }

        /**
         *
         * @param msg
         * @param userId
         * @param privateKey
         * @return r||s，直接拼接byte数组的rs
         */
        public static byte[] signSm3WithSm2(byte[] msg, byte[] userId, PrivateKey privateKey){
            return rsAsn1ToPlainByteArray(signSm3WithSm2Asn1Rs(msg, userId, privateKey));
        }

        /**
         *
         * @param msg
         * @param userId
         * @param privateKey
         * @return rs in <b>asn1 format</b>
         */
        public static byte[] signSm3WithSm2Asn1Rs(byte[] msg, byte[] userId, PrivateKey privateKey){
            try {
                SM2ParameterSpec parameterSpec = new SM2ParameterSpec(userId);
                Signature signer = Signature.getInstance("SM3withSM2", "BC");
                signer.setParameter(parameterSpec);
                signer.initSign(privateKey, new SecureRandom());
                signer.update(msg, 0, msg.length);
                byte[] sig = signer.sign();
                return sig;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        /**
         *
         * @param msg
         * @param userId
         * @param rs r||s，直接拼接byte数组的rs
         * @param publicKey
         * @return
         */
        public static boolean verifySm3WithSm2(byte[] msg, byte[] userId, byte[] rs, PublicKey publicKey){
            return verifySm3WithSm2Asn1Rs(msg, userId, rsPlainByteArrayToAsn1(rs), publicKey);
        }

        /**
         *
         * @param msg
         * @param userId
         * @param rs in <b>asn1 format</b>
         * @param publicKey
         * @return
         */
        public static boolean verifySm3WithSm2Asn1Rs(byte[] msg, byte[] userId, byte[] rs, PublicKey publicKey){
            try {
                SM2ParameterSpec parameterSpec = new SM2ParameterSpec(userId);
                Signature verifier = Signature.getInstance("SM3withSM2", "BC");
                verifier.setParameter(parameterSpec);
                verifier.initVerify(publicKey);
                verifier.update(msg, 0, msg.length);
                return verifier.verify(rs);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        /**
         * BC的SM3withSM2签名得到的结果的rs是asn1格式的，这个方法转化成直接拼接r||s
         * @param rsDer rs in asn1 format
         * @return sign result in plain byte array
         */
        private static byte[] rsAsn1ToPlainByteArray(byte[] rsDer){
            ASN1Sequence seq = ASN1Sequence.getInstance(rsDer);
            byte[] r = bigIntToFixexLengthBytes(ASN1Integer.getInstance(seq.getObjectAt(0)).getValue());
            byte[] s = bigIntToFixexLengthBytes(ASN1Integer.getInstance(seq.getObjectAt(1)).getValue());
            byte[] result = new byte[RS_LEN * 2];
            System.arraycopy(r, 0, result, 0, r.length);
            System.arraycopy(s, 0, result, RS_LEN, s.length);
            return result;
        }

        /**
         * BC的SM3withSM2验签需要的rs是asn1格式的，这个方法将直接拼接r||s的字节数组转化成asn1格式
         * @param sign in plain byte array
         * @return rs result in asn1 format
         */
        private static byte[] rsPlainByteArrayToAsn1(byte[] sign){
            if(sign.length != RS_LEN * 2) throw new RuntimeException("err rs. ");
            BigInteger r = new BigInteger(1, Arrays.copyOfRange(sign, 0, RS_LEN));
            BigInteger s = new BigInteger(1, Arrays.copyOfRange(sign, RS_LEN, RS_LEN * 2));
            ASN1EncodableVector v = new ASN1EncodableVector();
            v.add(new ASN1Integer(r));
            v.add(new ASN1Integer(s));
            try {
                return new DERSequence(v).getEncoded("DER");
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        private static byte[] bigIntToFixexLengthBytes(BigInteger rOrS){
            // for sm2p256v1, n is 00fffffffeffffffffffffffffffffffff7203df6b21c6052b53bbf40939d54123,
            // r and s are the result of mod n, so they should be less than n and have length<=32
            byte[] rs = rOrS.toByteArray();
            if(rs.length == RS_LEN) return rs;
            else if(rs.length == RS_LEN + 1 && rs[0] == 0) return Arrays.copyOfRange(rs, 1, RS_LEN + 1);
            else if(rs.length < RS_LEN) {
                byte[] result = new byte[RS_LEN];
                Arrays.fill(result, (byte)0);
                System.arraycopy(rs, 0, result, RS_LEN - rs.length, rs.length);
                return result;
            } else {
                throw new RuntimeException("err rs: " + Hex.toHexString(rs));
            }
        }

        public static BCECPrivateKey getPrivatekeyFromD(BigInteger d){
            ECPrivateKeySpec ecPrivateKeySpec = new ECPrivateKeySpec(d, ecParameterSpec);
            return new BCECPrivateKey("EC", ecPrivateKeySpec, BouncyCastleProvider.CONFIGURATION);
        }

        public static BCECPublicKey getPublickeyFromXY(BigInteger x, BigInteger y){
            ECPublicKeySpec ecPublicKeySpec = new ECPublicKeySpec(x9ECParameters.getCurve().createPoint(x, y), ecParameterSpec);
            return new BCECPublicKey("EC", ecPublicKeySpec, BouncyCastleProvider.CONFIGURATION);
        }
    }
}
