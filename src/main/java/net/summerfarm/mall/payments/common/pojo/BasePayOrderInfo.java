package net.summerfarm.mall.payments.common.pojo;

import lombok.Data;
import net.summerfarm.mall.payments.common.enums.BasePayTypeEnum;
//import net.summerfarm.mall.payments.common.pojo.boc.BocPayInfo;
import net.summerfarm.mall.payments.common.pojo.cmb.CmbPayConfig;
import net.summerfarm.mall.payments.common.pojo.din.DinPayConfig;
import net.summerfarm.mall.payments.common.pojo.fireface.FireFacePayConfig;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-06-28
 * @description 支付订单信息
 */
@Data
public class BasePayOrderInfo {
    /**
     * 原订单编号
     */
    private String orderNo;
    /**
     * 支付订单编号（预售场景下订单编号和支付编号不一致）
     */
    private String payOrderNo;
    /**
     * 支付类型 {@link BasePayTypeEnum}
     */
    private Integer payType;
    /**
     * 支付类型
     */
    private String payTypeStr;
    /**
     * 支付类型原始参数
     */
    private String originPayType;
    /**
     * 是否为小程序支付
     */
    private Boolean isMpPay;
    /**
     * 支付总金额
     */
    private BigDecimal payTotalPrice;
    /**
     * 支付账号companyAccountId
     */
    private Integer companyAccountId;
    /**
     * 中银支付信息
     */
//    private BocPayInfo bocPayInfo;
    /**
     * 微信支付信息
     */
    private WeixPayInfo weixPayInfo;
    /**
     * 招行支付信息
     */
    private CmbPayConfig cmbPayConfig;

    /**
     * 火脸支付信息
     */
    private FireFacePayConfig fireFacePayConfig;


    /**
     * 智付支付信息
     */
    private DinPayConfig dinPayConfig;

    /**
     * 下单时间
     */
    private LocalDateTime orderTime;

    /**
     * 是否为pop订单
     */
    private boolean popFlag;

    /**
     * 用于获取用户session_key的code
     */
    private String jsCode;

    /**
     * 支付业务类型
     * @see net.summerfarm.mall.payments.common.enums.PaymentBizTypeEnum
     */
    private Integer paymentBizType;
    public boolean getPopFlag() {
        return popFlag;
    }
}
