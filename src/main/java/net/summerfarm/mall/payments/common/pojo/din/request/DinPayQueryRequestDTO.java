package net.summerfarm.mall.payments.common.pojo.din.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-02-12
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DinPayQueryRequestDTO {

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 下单返回的上游订单号与”请求订单号”二选一
     */
    private String channelNumber;
}
