package net.summerfarm.mall.payments.common.utils;

import org.apache.commons.lang3.RandomStringUtils;

public class RandomStringGenerator {

    private static final String ALLCHAR = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final int LENGTH = 16;

    public static String generateRandomString() {
        return RandomStringUtils.random(LENGTH, ALLCHAR.toCharArray());
    }

    public static String generateRandomString(int length) {
        return RandomStringUtils.random(length, ALLCHAR.toCharArray());
    }

    public static void main(String[] args) {
        System.out.println("Generated Random String: " + generateRandomString());
    }
}