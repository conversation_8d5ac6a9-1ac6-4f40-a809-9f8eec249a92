package net.summerfarm.mall.payments.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @auther george
 */
public class FireFacePaymentEnum {
    
    @Getter
    @AllArgsConstructor
    public enum domain {
        
        PRO("https://open.lianok.com/open/v1/api/biz/do"),
        ;

        private String domain;
    }

    @Getter
    @AllArgsConstructor
    public enum env {

            PRO(0),
            SANDBOX(1),
            ;

            private final Integer env;
    }

    @Getter
    @AllArgsConstructor
    public enum responseCode {
        SUCCESS("0"),

        PAY_NOTIFY_SUCCESS("200"),

        PAY_NOTIFY_PROCESS_SUCCESS("SUCCESS"),

        REFUND_NOTIFY_PROCESS_SUCCESS("SUCCESS"),

        B2B_NOT_AUTHORIZED("80007"),
        ;

        private final String code;
    }

    @Getter
    @AllArgsConstructor
    public enum orderStatus {
        PENDING_PAYMENT(0, "待支付"),
        IN_PROGRESS(1, "支付中"),
        SUCCESS(2, "支付成功"),
        CLOSED(3, "交易关闭"),
        PARTIAL_REFUND(4, "部分退款"),
        FULL_REFUND(5, "全部退款");

        private final Integer status;
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum retailStoreStatus {
        AUTHORIZED("1", "已完成认证");

        private final String status;
        private final String desc;
    }
}
