package net.summerfarm.mall.payments.common.pojo.din.notify;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-02-11
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DinPayNotifyDTO {

    /**
     * 客户端类型
     * ALIPAY：支付宝
     * WXPAY：微信
     * UNIONPAY：银联
     */
    private String paymentType;

    /**
     * 支付类型
     * SWIPE: 条码(被扫)
     * SCAN: 二维码(主扫)
     * PUBLIC: 公众号
     * APPLET: 小程序
     * WAP: H5支付
     */
    private String paymentMethods;

    /**
     * 商户编号
     * 支付系统分配的商户号
     */
    private String merchantId;

    /**
     * 请求订单号
     * 商户请求订单号，要求50字符以内，同一商户号下订单号唯一
     */
    private String orderNo;

    /**
     * 交易金额
     * 交易金额，单位：元。最小金额为0.01
     */
    private BigDecimal payAmount;

    /**
     * 币种类型
     * CNY: 人民币
     */
    private String currency;

    /**
     * 订单状态
     * INIT：已接收
     * DOING：处理中
     * SUCCESS：成功
     * FAIL：失败
     * CLOSE：关闭
     * CANCEL: 撤销
     */
    private String orderStatus;

    /**
     * 退款状态
     * PRE_REFUND: 转入退款
     * PART_REFUND: 部分退款
     * ALL_REFUND: 全额退款
     * FAIL_REFUND: 退款失败
     * NOT_YET: 尚未退款
     */
    private String refundStatus;

    /**
     * 订单备注
     * 交易下单时上送值原样返回
     */
    private String orderDesc;

    /**
     * 上游订单号
     * 交易订单号
     */
    private String channelNumber;

    /**
     * 上游交易单号
     * 成功时有返回
     */
    private String outTransactionOrderId;

    /**
     * 上游子商户号(U/A/T)
     */
    private String subMerchantNo;

    /**
     * 商户公众号sub_appid
     */
    private String appid;

    /**
     * 用户openid
     * 微信openid
     */
    private String openid;

    /**
     * 微信子商户subopenid 或 支付宝子商户用户buyer_id
     */
    private String subopenid;

    /**
     * 用户付款银行
     * 成功时有返回，具体见附件5.4付款银行目录
     */
    private String bankType;

    /**
     * 支付卡类型
     * DEBIT(借记卡)
     * CREDIT(贷记卡)
     * UNKNOWN(未知)
     * CFT(钱包零钱)
     */
    private String onlineCardType;

    /**
     * 上游返回: 现金支付金额
     * 订单总金额 - 现金券金额 = 现金支付金额
     */
    private BigDecimal cashFee;

    /**
     * 上游返回: 现金券金额
     */
    private BigDecimal couponFee;

    /**
     * 订单入账面额
     * 此订单入账面额(不扣手续费)
     */
    private BigDecimal orderCreditAmount;

    /**
     * 实际支付金额
     * 用户实际支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 渠道结算金额
     * 渠道结算金额，成功时返回
     */
    private BigDecimal channelSettlementAmount;

    /**
     * 交易手续费
     * 该笔交易产生的手续费，成功时返回
     */
    private BigDecimal orderFee;

    /**
     * 商户实际入账发生额
     * 该笔交易成功后收单商户实际入账发生额，成功时返回
     */
    private BigDecimal merchantCreditAmount;

    /**
     * 渠道支付宝费率活动标识
     * 交易成功时有返回，详见5.5
     */
    private String chargeFlag;

    /**
     * 商户费率
     * 商户交易的费率，条码支付成功时返回
     */
    private BigDecimal merchantFee;

    /**
     * 平台商补贴的手续费
     * 单位: 元，条码支付成功时返回
     */
    private BigDecimal feeAccountAmt;

    /**
     * 实收手续费
     * 交易成功时返回
     */
    private BigDecimal receiverFee;

    /**
     * 后收手续费
     * 交易成功时返回
     */
    private BigDecimal offlineFee;

    /**
     * 订单完成时间
     * 格式: yyyy-MM-dd HH:mm:ss
     */
    private String orderPayDate;

    /**
     * 通知时间
     * 精确到通知时间的毫秒数
     */
    private String timestamp;

    /**
     * 营销参数规则
     * JSON格式字符串，DES加密传输，详见3.6营销参数规则说明
     */
    private String marketingRules;

    /**
     * 分账规则及状态
     * 响应分账结果规则以及对应状态，详见3.7分帐规则说明
     */
    private String splitRules;

    /**
     * 微信交易类型
     * JSAPI、NATIVE、MICROPAY、APP、MWEB、FACEPAY
     */
    private String wxTradeType;

    /**
     * 银联返回的付款方附加数据
     * Base64编码的
     */
    private String upAddData;

    /**
     * 银联二维码返回的保留数据
     * Base64编码的
     */
    private String resvData;

    /**
     * 交易资金渠道和优惠信息-支付宝
     * 支付宝返回的资金渠道
     */
    private String fundBillList;

    /**
     * 优惠信息详情
     * 上游渠道返回的优惠详情
     */
    private String promotionDetail;

    /**
     * 支付宝优惠信息详情
     * 支付宝优惠券信息列表
     */
    private String voucherDetailList;

    /**
     * 管控类型
     * CONTROL：管控
     * UNCONTROL：非管控
     */
    private String controlType;

    /**
     * 管控状态
     * FREEZE：冻结
     * UNFREEZE：解冻
     */
    private String controlStatus;
}
