package net.summerfarm.mall.payments.common.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.codec.Base64Encoder;
import net.xianmu.common.exception.ProviderException;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.*;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Enumeration;
import java.util.Objects;

/**
 * 证书工具类
 */
public abstract class CertUtils {

    static Provider provider = null;

    static {
        provider = new BouncyCastleProvider();
        Security.addProvider(provider);
    }

    private CertUtils() {
    }

    public static PublicKey getPublicKey(String certFilePath){
        try {
            return getX509Certificate(certFilePath).getPublicKey();
        } catch (Exception e) {
            throw new ProviderException("获取公钥失败", e);
        }
    }

    /**
     * 获取证书
     *
     * @param certFilePath
     * @return
     * @throws CertificateException
     * @throws NoSuchProviderException
     * @throws IOException
     */
    public static X509Certificate getX509Certificate(String certFilePath) throws CertificateException,
            NoSuchProviderException, IOException {
        CertificateFactory cf = CertificateFactory.getInstance("X.509", provider);
        byte[] readAllBytes = Files.readAllBytes(Paths.get(certFilePath));
        String fileContent = new String(readAllBytes);
        if ("-----BEGIN CERTIFICATE-----".indexOf(fileContent) < 0) {
            fileContent = "-----BEGIN CERTIFICATE-----\n" + fileContent +
                    "\n-----END CERTIFICATE-----";
        }
        InputStream is = new ByteArrayInputStream(fileContent.getBytes());
        return (X509Certificate) cf.generateCertificate(is);
    }

    public static PrivateKey getPrivateKeyStr(String sm2FilePath, String sm2FilePwd) throws IOException {
        Objects.requireNonNull(sm2FilePath, "sm2FilePath required");
        Objects.requireNonNull(sm2FilePwd, "sm2FilePwd required");

        byte[] data = Files.readAllBytes(Paths.get(sm2FilePath));
        try {
            PKCS12_SM2 P12 = new PKCS12_SM2(data);
            return P12.getPrivateKey(sm2FilePwd);
        } catch (Exception e) {
            throw new IllegalArgumentException(e.getMessage(), e);
        }
    }


    public static PrivateKey getPrivateKey(String pfxPath, String pfxPassword) {
        try {
            if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
                Security.addProvider(new BouncyCastleProvider());
            }
            KeyStore ks = KeyStore.getInstance("PKCS12", BouncyCastleProvider.PROVIDER_NAME);
            FileInputStream fis = new FileInputStream(pfxPath);
            // If the keystore password is empty(""), then we have to set
            // to null, otherwise it won't work!!!
            char[] nPassword = null;
            if ((pfxPassword == null) || pfxPassword.trim().equals("")) {
                nPassword = null;
            } else {
                nPassword = pfxPassword.toCharArray();
            }
            System.out.println("ks:" + ks.getType() + "  provider:" + ks.getProvider());
            ks.load(fis, nPassword);
            fis.close();
            Enumeration<String> enumas = ks.aliases();
            String keyAlias = null;
            if (enumas.hasMoreElements())// we are readin just one certificate.
            {
                keyAlias = enumas.nextElement();
            }
            PrivateKey prikey = (PrivateKey) ks.getKey(keyAlias, nPassword);
            return prikey;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getPrivateKeyBase64(String pfxPath, String pfxPassword) {
        PrivateKey privateKey = getPrivateKey(pfxPath, pfxPassword);
        return Base64Encoder.encode(privateKey.getEncoded());
    }

    public static String getPublicKeyBase64(String certFilePath) throws CertificateException, IOException, NoSuchProviderException {
        PublicKey publicKey = getPublicKey(certFilePath);
        return Base64Encoder.encode(publicKey.getEncoded());
    }

    public static PublicKey getPublicKeyByBase64(String base64PublicKey) {
        Security.addProvider(new BouncyCastleProvider());
        try {
            // 解码 Base64 公钥
            byte[] keyBytes = Base64.decode(base64PublicKey);

            // 使用 BouncyCastle 提供的 SM2 KeyFactory
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("EC", "BC");
            return keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ProviderException("Failed to convert Base64 string to PublicKey.");
        }
    }

    public static PrivateKey getPrivateKeyByBase64(String base64PrivateKey) {
        try {
            // 解码 Base64 私钥
            byte[] keyBytes = Base64.decode(base64PrivateKey);

            // 使用 BouncyCastle 提供的 SM2 KeyFactory
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("EC", "BC");
            return keyFactory.generatePrivate(keySpec);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ProviderException("Failed to convert Base64 string to PrivateKey.");
        }
    }
}