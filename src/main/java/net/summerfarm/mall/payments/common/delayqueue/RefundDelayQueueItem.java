package net.summerfarm.mall.payments.common.delayqueue;

import lombok.Data;
import net.summerfarm.common.delayqueue.DelayQueueItem;
import net.summerfarm.mall.common.delayqueue.DelayQueueId;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-08-12
 * @description
 */
@Data
public class RefundDelayQueueItem extends DelayQueueItem {
    /**
     * 支付订单号
     */
    private String refundNo;

    public RefundDelayQueueItem() {
    }

    public RefundDelayQueueItem(String refundNo, Long delayTime){
        super(refundNo, LocalDateTime.now(), delayTime);
        this.refundNo = refundNo;
    }
}
