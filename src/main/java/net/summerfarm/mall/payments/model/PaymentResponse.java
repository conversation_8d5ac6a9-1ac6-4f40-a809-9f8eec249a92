package net.summerfarm.mall.payments.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 支付响应对象
 * @author: <PERSON>
 * @date: 2024-12-09
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentResponse {

    /**
     * 支付响应码
     */
    @JSONField(name = "returncode")
    private String returnCode;

    /**
     * 异常信息
     */
    @JSONField(name = "errmsg")
    private String errMsg;

    /**
     * 支付渠道 例如：微信原生支付、招行间连微信支付等
     * @see net.summerfarm.payment.routing.common.enums.PaymentChannelTypeEnums
     */
    private String channelType;

    /**
     * 响应结果
     */
    private PaymentResponseData data;

}
