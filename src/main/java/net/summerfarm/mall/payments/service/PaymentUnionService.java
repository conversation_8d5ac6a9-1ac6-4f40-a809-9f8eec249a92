package net.summerfarm.mall.payments.service;

import net.summerfarm.mall.payments.model.PaymentRequest;
import net.summerfarm.mall.payments.model.PaymentResponse;
import net.summerfarm.payment.routing.model.vo.PaymentUsableChannelVO;
import net.xianmu.common.result.CommonResult;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-12-10
 **/
public interface PaymentUnionService {

    /**
     * 查询可用支付渠道
     * @return
     */
    CommonResult<PaymentUsableChannelVO> queryUsablePaymentChannels();

    /**
     * 统一支付接口
     * @param request
     * @return
     */
    CommonResult<PaymentResponse> unionPay(PaymentRequest request);

    Integer getDefaultPaymentMethod();
}
