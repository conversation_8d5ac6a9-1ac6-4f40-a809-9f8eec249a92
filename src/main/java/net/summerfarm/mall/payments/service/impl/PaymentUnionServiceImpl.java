package net.summerfarm.mall.payments.service.impl;

import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.input.payment.PaymentInput;
import net.summerfarm.mall.model.input.payment.PaymentOrderInput;
import net.summerfarm.mall.model.vo.payment.PayResultVO;
import net.summerfarm.mall.payments.common.config.PaymentConfig;
import net.summerfarm.mall.payments.common.enums.PaymentBizTypeEnum;
import net.summerfarm.mall.payments.model.PaymentRequest;
import net.summerfarm.mall.payments.model.PaymentResponse;
import net.summerfarm.mall.payments.model.PaymentResponseData;
import net.summerfarm.mall.payments.request.PaymentHandler;
import net.summerfarm.mall.payments.service.PaymentUnionService;
import net.summerfarm.mall.service.MerchantSubAccountService;
import net.summerfarm.mall.service.OrderRelationService;
import net.summerfarm.mall.service.OrderSellingEntityService;
import net.summerfarm.payment.routing.common.enums.*;
import net.summerfarm.payment.routing.model.dto.PaymentRoutingDTO;
import net.summerfarm.payment.routing.model.dto.PaymentRoutingQueryDTO;
import net.summerfarm.payment.routing.model.dto.PaymentUsableMethodDTO;
import net.summerfarm.payment.routing.model.vo.PaymentUsableChannelVO;
import net.summerfarm.payment.routing.service.PaymentRuleRoutingService;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static org.junit.Assert.fail;

import java.util.*;


/**
 * @description:
 * @author: George
 * @date: 2024-12-10
 **/
@Slf4j
@Service
public class PaymentUnionServiceImpl implements PaymentUnionService {

    @Resource
    private PaymentRuleRoutingService paymentRuleRoutingService;
    @Resource
    private PaymentHandler paymentHandler;
    @Resource
    private PaymentConfig paymentConfig;
    @Resource
    private MerchantSubAccountService merchantSubAccountService;
    @Resource
    private OrderSellingEntityService orderSellingEntityService;
    @Resource
    private OrderRelationService orderRelationService;

    @Override
    public CommonResult<PaymentResponse> unionPay(PaymentRequest request) {
        // 校验前端参数
        validatePaymentRequest(request);
        // 统一处理参数
        unionParamHandler(request);
        // 根据业务类型选择支付方式
        PaymentBizTypeEnum bizTypeEnum = PaymentBizTypeEnum.getByCode(request.getBizType());
        switch (Objects.requireNonNull(bizTypeEnum)) {
            case ORDER_PAY:
                return orderPay(request);
            case MASTER_ORDER_PAY:
                return masterOrderPay(request);
            default:
                throw new ParamsException("业务类型不合法");
        }
    }

    private void unionParamHandler(PaymentRequest request) {
        // 账期客户直接设置支付方式为账期
        if (RequestHolder.isMajorDirect()) {
            request.setPaymentMethod(PaymentMethodEnums.BILL.getMethod());
        }
    }

    private CommonResult<PaymentResponse> orderPay(PaymentRequest request) {
        PaymentRoutingDTO routingInfo = fetchRoutingInfo(request);
        if (routingInfo == null || routingInfo.getChannelCode() == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "暂无可用的支付方式");
        }
        String channelType = PaymentChannelTypeEnums.getChannelTypeByMethodAndCode(request.getPaymentMethod(), routingInfo.getChannelCode(), RequestHolder.isMiniProgramLogin());
        String platform = determinePlatform(routingInfo.getChannelCode(), request.getPaymentMethod());

        PaymentOrderInput input = buildPaymentOrderInput(request, routingInfo, platform);
        String res = paymentHandler.pay(input);
        return buildPaymentResponse(res, channelType);
    }

    private CommonResult<PaymentResponse> masterOrderPay(PaymentRequest request) {
        PaymentRoutingDTO routingInfo = fetchRoutingInfo(request);
        if (routingInfo == null || routingInfo.getChannelCode() == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "暂无可用的支付方式");
        }
        String platform = determinePlatform(routingInfo.getChannelCode(), request.getPaymentMethod());

        PaymentInput input = buildPaymentInput(request, routingInfo, platform);
        CommonResult<PayResultVO> payResult = paymentHandler.payV2(input);
        return buildPaymentV2Response(payResult, request, routingInfo);
    }

    private PaymentOrderInput buildPaymentOrderInput(PaymentRequest request, PaymentRoutingDTO routingInfo, String platform) {
        return PaymentOrderInput.builder()
                .orderNo(request.getBizOrderNo())
                .payChannel(routingInfo.getChannelCode())
                .payPlatform(platform)
                .companyAccountId(routingInfo.getCompanyAccountId())
                .routeVersionFlag(true)
                .jsCode(request.getJsCode())
                .build();
    }

    private PaymentInput buildPaymentInput(PaymentRequest request, PaymentRoutingDTO routingInfo, String platform) {
        return PaymentInput.builder()
                .masterOrderNo(request.getBizOrderNo())
                .payChannel(routingInfo.getChannelCode())
                .payPlatform(platform)
                .companyAccountId(routingInfo.getCompanyAccountId())
                .routeVersionFlag(true)
                .jsCode(request.getJsCode())
                .build();
    }

    private PaymentRoutingDTO fetchRoutingInfo(PaymentRequest request) {
        Integer paymentMethod = request.getPaymentMethod();
        // 本地支付方式直接构造路由信息
        if (PaymentMethodEnums.isNativePaymentMethods(paymentMethod)) {
            return PaymentRoutingDTO.builder().channelCode(paymentMethod).build();
        }

        // 临时的支付路由配置
        PaymentRoutingDTO tempRoutingConfig = fetchTempRoutingInfo(paymentMethod);
        if (tempRoutingConfig != null) {
            return tempRoutingConfig;
        }

        PaymentRoutingQueryDTO queryDTO = buildRoutingQueryDTO(request);
        log.info("支付路由查询参数:{}", queryDTO);
        PaymentRoutingDTO routingInfo = paymentRuleRoutingService.getRoutingInfo(queryDTO);
        log.info("支付路由信息:{}", routingInfo);
        log.info("稽核订单:{}, 销售主体名称:{}, 支付销售主体名称:{}", request.getBizOrderNo(), queryDTO.getSellingEntityName(), routingInfo.getSellingEntityName());
        return routingInfo;
    }

    private PaymentRoutingDTO fetchTempRoutingInfo(Integer paymentMethod) {
        try {
            // 非小程序、非微信支付方式直接返回
            if (!RequestHolder.isMiniProgramLogin()) {
                return null;
            }
            if (!Objects.equals(paymentMethod, PaymentMethodEnums.WECHAT.getMethod())) {
                return null;
            }
            // 临时的支付路由配置
            String tempRoutingConfig = paymentConfig.getTempRoutingConfig();
            if (StringUtils.isBlank(tempRoutingConfig)) {
                return null;
            }

            JSONObject tempRoutingConfigJson = JSONObject.parseObject(tempRoutingConfig);
            Boolean useTempRouting = tempRoutingConfigJson.getBoolean("useTempRouting");
            if (useTempRouting != null && useTempRouting) {
                Integer areaNo = RequestHolder.getMerchantAreaNo();
                // 杭州、绍兴等走区域配置
                String routingContent = tempRoutingConfigJson.getString(String.valueOf(areaNo));
                if (StringUtils.isBlank(routingContent)) {
                    // 根据区域找不到 则走默认配置（火脸）
                    routingContent = tempRoutingConfigJson.getString("default");
                }
                return JSONObject.parseObject(routingContent, PaymentRoutingDTO.class);
            }
        } catch (Exception e) {
            log.error("获取临时支付路由配置异常", e);
        }
        return null;
    }

    private PaymentRoutingQueryDTO buildRoutingQueryDTO(PaymentRequest request) {
        Integer paymentMethod = request.getPaymentMethod();
        Integer areaNo = RequestHolder.getMerchantAreaNo();
        boolean miniAppFlag = RequestHolder.isMiniProgramLogin();
        String openId = merchantSubAccountService.getOpenId();
        // 获取销售主体名称
        String sellingEntityName = getSellingEntityName(request);
        return PaymentRoutingQueryDTO.builder()
                .tenantId(TenantIdEnums.SUMMER_FARM.getTenantId())
                .businessLine(PaymentBusinessLineEnums.SUMMERFARM.getCode())
                .platform(miniAppFlag ? PaymentDictionaryEnums.Platform.MINI_APP.getName() : PaymentDictionaryEnums.Platform.H5.getName())
                .paymentMethod(PaymentMethodEnums.getCodeByMethod(paymentMethod))
                .routeKey(PaymentRuleRoutingKeyEnums.AREA_NO.getKey())
                .routeValue(String.valueOf(areaNo))
                .openId(openId)
                .sellingEntityName(sellingEntityName)
                .mId(RequestHolder.getMId())
                .b2bSwitch(paymentConfig.getB2bSwitch())
                .build();
    }

    /**
     * 获取订单的销售主体名称
     * @param request
     * @return
     */
    private String getSellingEntityName(PaymentRequest request) {
        PaymentBizTypeEnum bizTypeEnum = PaymentBizTypeEnum.getByCode(request.getBizType());
        switch (Objects.requireNonNull(bizTypeEnum)) {
            case ORDER_PAY:
                List<String> sellingEntityNames = orderSellingEntityService.querySellingEntityByNos(Collections.singletonList(request.getBizOrderNo()));
                return CollectionUtils.isEmpty(sellingEntityNames) ? null : sellingEntityNames.get(0);
            case MASTER_ORDER_PAY:
                List<Orders> orders = orderRelationService.selectOrdersByMasterOrderNo(request.getBizOrderNo());
                return CollectionUtils.isEmpty(orders) ? null : orders.get(0).getSellingEntityName();
            default:
                throw new ParamsException("业务类型不合法");
        }
    }

    /**
     * 该标识用于招行区分支付方式
     * @param channelCode
     * @param paymentMethod
     * @return
     */
    private String determinePlatform(Integer channelCode, Integer paymentMethod) {
        if (Objects.equals(channelCode, PaymentChannelProviderEnums.CMB.getCode())) {
            return Objects.equals(paymentMethod, PaymentMethodEnums.WECHAT.getMethod()) ? "WEIX" : "ZFBA";
        }
        return null;
    }

    private CommonResult<PaymentResponse> buildPaymentResponse(String payResponse, String channelType) {
        if (StringUtils.isEmpty(payResponse)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "支付失败");
        }
        JSONObject responseJson = JSONObject.parseObject(payResponse);
        String returnCode = responseJson.getString("returncode");
        String errMsg = responseJson.getString("errmsg");
        PaymentResponseData data = responseJson.toJavaObject(PaymentResponseData.class);
        String packageStr = Optional.ofNullable(responseJson.getString("package")).orElse(responseJson.getString("packageStr"));
        data.setPackageStr(packageStr);
        CommonResult<PaymentResponse> result = CommonResult.ok(
                PaymentResponse.builder()
                        .returnCode(returnCode)
                        .errMsg(errMsg)
                        .channelType(channelType)
                        .data(data)
                        .build()
        );
        log.info("支付v1响应结果:{}", JSONObject.toJSONString(result));
        return result;
    }

    private CommonResult<PaymentResponse> buildPaymentV2Response(CommonResult<PayResultVO> payResult, PaymentRequest request, PaymentRoutingDTO routingInfo) {
        if (payResult == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "支付失败");
        }
        if (!ResultStatusEnum.OK.getStatus().equals(payResult.getStatus())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, payResult.getMsg());
        }
        PayResultVO resultData = payResult.getData();
        PaymentResponseData paymentData = PaymentResponseData.builder()
                .appId(resultData.getAppId())
                .nonceStr(resultData.getNonceStr())
                .packageStr(resultData.getPackageStr())
                .paySign(resultData.getPaySign())
                .signType(resultData.getSignType())
                .timeStamp(resultData.getTimeStamp())
                .paymentNo(resultData.getPaymentNo())
                .signature(resultData.getSignature())
                .qrCode(resultData.getQrCode())
                .txnTime(resultData.getTxnTime())
                .prepayFail(resultData.isPrepayFail())
                .signData(resultData.getSignData())
                .build();
        CommonResult<PaymentResponse> result = CommonResult.ok(
                PaymentResponse.builder()
                        .returnCode(resultData.getReturncode())
                        .errMsg(resultData.getErrmsg())
                        .channelType(PaymentChannelTypeEnums.getChannelTypeByMethodAndCode(request.getPaymentMethod(), routingInfo.getChannelCode(), RequestHolder.isMiniProgramLogin()))
                        .data(paymentData)
                        .build()
        );
        log.info("支付v2响应结果:{}", JSONObject.toJSONString(result));
        return result;
    }

    private void validatePaymentRequest(PaymentRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("支付请求参数不能为空");
        }
        if (Objects.isNull(request.getBizType())) {
            throw new ParamsException("业务类型不能为空");
        }
        if (PaymentBizTypeEnum.getByCode(request.getBizType()) == null) {
            throw new ParamsException("业务类型不合法");
        }
        if (StringUtils.isBlank(request.getPaymentMethod())) {
            throw new ParamsException("支付方式不能为空");
        }
        if (PaymentMethodEnums.getCodeByMethod(request.getPaymentMethod()) == null) {
            throw new ParamsException("支付方式不合法");
        }
        if (StringUtils.isBlank(request.getBizOrderNo())) {
            throw new ParamsException("业务单号不能为空");
        }
    }

    /**
     * 查询可用的支付渠道
     */
    @Override
    public CommonResult<PaymentUsableChannelVO> queryUsablePaymentChannels() {
        if (RequestHolder.getMerchantAreaNo() == null) {
            return CommonResult.fail(ResultStatusEnum.UNAUTHORIZED);
        }

        // 构建查询 DTO
        PaymentRoutingQueryDTO routingQueryDTO = buildRoutingQueryDTO();
        PaymentUsableMethodDTO paymentUsableMethodDTO = paymentRuleRoutingService.queryUsableMethods(routingQueryDTO);

        // 构建返回结果
        PaymentUsableChannelVO paymentUsableChannelVO = buildPaymentUsableChannelVO(paymentUsableMethodDTO);

        // 灰度路由处理
        boolean routeVersionFlag = determineRouteVersionFlag(paymentConfig.getRouteGrayScale(),
                String.valueOf(RequestHolder.getMerchantAreaNo()));
        paymentUsableChannelVO.setRouteVersionFlag(routeVersionFlag);

        return CommonResult.ok(paymentUsableChannelVO);
    }

    private PaymentRoutingQueryDTO buildRoutingQueryDTO() {
        String areaNo = String.valueOf(RequestHolder.getMerchantAreaNo());
        boolean miniAppFlag = RequestHolder.isMiniProgramLogin();

        return PaymentRoutingQueryDTO.builder()
                .tenantId(TenantIdEnums.SUMMER_FARM.getTenantId())
                .businessLine(PaymentBusinessLineEnums.SUMMERFARM.getCode())
                .platform(miniAppFlag ? PaymentDictionaryEnums.Platform.MINI_APP.getName()
                        : PaymentDictionaryEnums.Platform.H5.getName())
                .routeKey(PaymentRuleRoutingKeyEnums.AREA_NO.getKey())
                .routeValue(areaNo)
                .build();
    }

    private boolean determineRouteVersionFlag(String routeGrayScale, String areaNo) {
        // 没有灰度配置的情况
        if (StringUtils.isBlank(routeGrayScale)) {
            return false;
        }

        Map<String, String> routingMap = JSONObject.parseObject(routeGrayScale, Map.class);
        if (CollectionUtil.isEmpty(routingMap)) {
            return false;
        }
        // 检查是否有全局路由
        if (routingMap.containsKey("all")) {
            String allRouting = routingMap.get("all");
            return "1".equals(allRouting);
        }

        // 没命中区域的情况
        if(!routingMap.containsKey(areaNo)) {
            return false;
        }
    
        // 没命中mid的情况
        String mIds = routingMap.get(areaNo);
        if (StringUtils.isBlank(mIds)) {
            return false;
        }

        // 检查当前商户是否在灰度路由列表中
        String[] mIdList = mIds.split(",");
        String mId = String.valueOf(RequestHolder.getMId());
        if (Arrays.asList(mIdList).contains(mId)) {
            return true;
        }
        return false;
    }

    private PaymentUsableChannelVO buildPaymentUsableChannelVO(PaymentUsableMethodDTO paymentUsableMethodDTO) {
        return PaymentUsableChannelVO.builder()
                .wechatPayFlag(paymentUsableMethodDTO.getWechatPayFlag())
                .alipayFlag(paymentUsableMethodDTO.getAlipayFlag())
                .paymentCodeFlag(paymentUsableMethodDTO.getPaymentCodeFlag())
                .build();
    }

    @Override
    public Integer getDefaultPaymentMethod() {
        // 获取当前商户的区域编号
        Integer areaNo = RequestHolder.getMerchantAreaNo();

        // 从配置中获取区域与支付方式的映射配置
        String paymentMethodConfig = paymentConfig.getDefaultPaymentMethodConfigByArea();

        // 如果配置为空，返回默认支付方式（微信）
        if (StringUtils.isBlank(paymentMethodConfig)) {
            return PaymentMethodEnums.WECHAT.getMethod();
        }

        // 解析配置为Map
        Map<Integer, Integer> paymentMethodMap = parsePaymentMethodConfig(paymentMethodConfig);

        // 如果解析结果为空，返回默认支付方式（微信）
        if (CollectionUtil.isEmpty(paymentMethodMap)) {
            return PaymentMethodEnums.WECHAT.getMethod();
        }

        // 根据区域编号获取支付方式，如果不存在则返回默认支付方式
        return paymentMethodMap.getOrDefault(areaNo, PaymentMethodEnums.WECHAT.getMethod());
    }

    /**
     * 解析支付方式配置
     *
     * @param config 支付方式配置字符串
     * @return 区域与支付方式的映射Map
     */
    private Map<Integer, Integer> parsePaymentMethodConfig(String config) {
        try {
            return JSONObject.parseObject(config, new TypeReference<Map<Integer, Integer>>() {});
        } catch (Exception e) {
            // 日志记录解析失败
            log.error("Failed to parse payment method config: {}", config, e);
            return Collections.emptyMap();
        }
    }
}
