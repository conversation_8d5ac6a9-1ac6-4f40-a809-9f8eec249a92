package net.summerfarm.mall.payments.notify;

import net.summerfarm.mall.payments.common.pojo.din.notify.DinNotifyDTO;
import net.summerfarm.mall.payments.common.pojo.din.notify.DinPayNotifyDTO;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-02-11
 **/
public interface DinNotifyService {
    /**
     * 支付回调
     * @param dinNotifyDTO
     * @return
     */
    String payNotify(DinNotifyDTO dinNotifyDTO);

    /**
     * 支付成功操作
     * @param data
     */
    void onSuccess(DinPayNotifyDTO data);

    /**
     * 退款回调
     * @param dinNotifyDTO
     * @return
     */
    String refundNotify(DinNotifyDTO dinNotifyDTO);
}
