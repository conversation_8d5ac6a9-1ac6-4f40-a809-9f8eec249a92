package net.summerfarm.mall.payments.notify;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-06-28
 * @description
 */
public interface CmbNotifyService {
    /**
     * 招行支付回调（按文档描述，只有成功的订单会回调）
     * @param notifyMap 回调参数
     * @param isNotify 是否是回调
     * @return 处理结果
     */
    Map<String, String> cmbPayNotify(Map<String, String> notifyMap, boolean isNotify);

    /**
     * 招行支付回调（按文档描述，只有成功的订单会回调）
     * @param notifyMap 回调参数
     * @param isNotify 是否是回调
     * @return 处理结果
     */
    Map<String, String> cmbPayNotifyV2(Map<String, String> notifyMap, boolean isNotify);

    /**
     * 招行退款回调
     * @param notifyMap 回调参数
     * @param isNotify 是否是回调
     * @return 处理结果
     */
    Map<String, String> cmbRefundNotify(Map<String, String> notifyMap, boolean isNotify);
}
