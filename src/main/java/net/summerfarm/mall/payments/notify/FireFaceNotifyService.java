package net.summerfarm.mall.payments.notify;

import com.lianok.docking.notification.OrderNotification;
import net.summerfarm.mall.payments.common.pojo.fireface.FireFaceNotifyDTO;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-01-13
 **/
public interface FireFaceNotifyService {

    /**
     * 支付回调
     * @param notifyDTO
     * @return
     */
    String payNotify(Integer companyAccountId, FireFaceNotifyDTO notifyDTO);


    /**
     * 退款回调
     * @param notifyDTO
     * @return
     */
    String fireFaceRefundNotify(Integer companyAccountId, FireFaceNotifyDTO notifyDTO);

    /**
     * 支付成功
     * @param orderNotification
     */
    void onSuccess(OrderNotification orderNotification);
}
