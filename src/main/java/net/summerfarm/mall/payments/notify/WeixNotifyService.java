package net.summerfarm.mall.payments.notify;

import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-06-28
 * @description
 */
public interface WeixNotifyService {
    /**
     * 微信支付回调处理
     * @return 回调
     * @throws IOException
     */
    String weixPayNotify() throws IOException;

    /**
     * 主子单微信支付回调处理
     * @return 回调
     * @throws IOException 异常
     */
    String weixPayNotifyV2() throws IOException;

    /**
     * 微信退款回调处理
     * @return 回调
     * @throws IOException
     */
    String weixRefundNotify() throws Exception;

    String handleNotify(Map<String, String> resultMap);

    /**
     * 主子单微信支付回调处理
     * @param resultMap 回调参数
     * @return 回调
     */
    String handleNotifyV2(Map<String, String> resultMap);
}
