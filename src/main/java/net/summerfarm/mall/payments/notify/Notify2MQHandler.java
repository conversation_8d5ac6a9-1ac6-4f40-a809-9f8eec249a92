package net.summerfarm.mall.payments.notify;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.mq.PayNotifySuccessData;
import net.summerfarm.mall.common.mq.PayNotifySuccessDataV2;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.model.domain.MasterOrder;
import net.summerfarm.mall.model.domain.MasterPayment;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.domain.Payment;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2025-02-12
 **/
@Service
public class Notify2MQHandler {

    @Resource
    private MqProducer mqProducer;

    public void notifyOrderPaySuccess(Orders orders, Payment payment) {
        // 通知订单支付成功
        PayNotifySuccessData payNotifySuccessData = new PayNotifySuccessData();
        payNotifySuccessData.setOrder(orders);
        payNotifySuccessData.setPayment(payment);
        MQData mqData = new MQData();
        mqData.setType(MType.PAYMENT_NOTIFY_SUCCESS.name());
        mqData.setData(JSONObject.toJSONString(payNotifySuccessData));
        mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST,null,JSONObject.toJSONString(mqData), orders.getOrderNo());
    }

    public void notifyMasterOrderPaySuccess(MasterOrder masterOrder, MasterPayment masterPayment) {
        // 通知主订单支付成功
        PayNotifySuccessDataV2 payNotifySuccessData = new PayNotifySuccessDataV2();
        payNotifySuccessData.setMasterPayment(masterPayment);
        payNotifySuccessData.setMasterOrder(masterOrder);
        MQData mqData = new MQData();
        mqData.setType(MType.MASTER_PAYMENT_NOTIFY_SUCCESS.name());
        mqData.setData(JSONObject.toJSONString(payNotifySuccessData));
        mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST, null, JSONObject.toJSONString(mqData), masterOrder.getMasterOrderNo());
    }
}
