package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.CustomizationRequest;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 定制需求表 Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Repository
public interface CustomizationRequestMapper {
    
    /**
     * 根据主键删除
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     * @param record 记录
     * @return 影响行数
     */
    int insert(CustomizationRequest record);

    /**
     * 选择性插入记录
     * @param record 记录
     * @return 影响行数
     */
    int insertSelective(CustomizationRequest record);

    /**
     * 根据主键查询
     * @param id 主键
     * @return 记录
     */
    CustomizationRequest selectByPrimaryKey(Long id);

    /**
     * 选择性更新记录
     * @param record 记录
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(CustomizationRequest record);

    /**
     * 根据主键更新记录
     * @param record 记录
     * @return 影响行数
     */
    int updateByPrimaryKey(CustomizationRequest record);

    /**
     * 根据客户ID查询定制需求列表
     * @param mId 客户ID
     * @return 定制需求列表
     */
    List<CustomizationRequest> selectByMerchantId(Long mId);

    /**
     * 根据订单号查询定制需求
     * @param orderNo 订单号
     * @return 定制需求
     */
    CustomizationRequest selectByOrderNo(String orderNo);

    /**
     * 根据SKU查询定制需求列表
     * @param sku SKU
     * @return 定制需求列表
     */
    List<CustomizationRequest> selectBySku(String sku);

    /**
     * 根据状态查询定制需求列表
     * @param status 状态
     * @return 定制需求列表
     */
    List<CustomizationRequest> selectByStatus(Integer status);

    /**
     * 根据客户ID和状态查询定制需求列表
     * @param mId 客户ID
     * @param status 状态
     * @return 定制需求列表
     */
    List<CustomizationRequest> selectByMerchantIdAndStatus(Long mId, Integer status);
}
