package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.AreaSku;
import net.summerfarm.mall.model.dto.product.ExpiredEarlyWarningDTO;
import net.summerfarm.mall.model.vo.AreaSkuVO;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Repository
public interface AreaSkuMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AreaSku record);

    int insertSelective(AreaSku record);

    AreaSku selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AreaSku record);

    int updateByPrimaryKey(AreaSku record);

    List<AreaSku> selectByStoreAndSku(@Param("storeNo") Integer storeNo, @Param("sku") String sku);

    /**
     * 更新排序
     * @param sku sku
     * @param areaNo 城市编号
     * @param pdPriority 排序
     * @return
     */
    int updatePdPriority(@Param("sku") String sku, @Param("areaNo") Integer areaNo, @Param("pdPriority") Integer pdPriority);

    /**
     * 查询
     * @param areaNo areaNo
     * @param sku sku
     * @return
     */
    AreaSku selectByAreaNoAndSku(@Param("areaNo") Integer areaNo, @Param("sku") String sku);

    int updateOnSale(AreaSku record);

    /**
     * 查询自动上下架sku数据--仅限本仓的
     * @param storeNo
     * @param sku
     * @param onSale
     * @param openSale
     * @param closeSale
     * @return
     */

    List<AreaSku> selectSelfStoreAutoSale(@Param("storeNo") Integer storeNo, @Param("sku") String sku,
          @Param("onSale") Boolean onSale, @Param("openSale") Integer openSale, @Param("closeSale") Integer closeSale);

    //清空自动上架
    int clearOpenSale(@Param("sku") String sku, @Param("areaNo") Integer areaNo);

    //清空自动下架
    int clearCloseSale(@Param("sku") String sku, @Param("areaNo") Integer areaNo);

    /**
     * 更新保质期
     *
     * @param storeNo     仓库编号
     * @param areaNo     城市编号
     * @param sku         sku
     * @param qualityDate 保质期
     * @return 影响行数
     */
    int updateQualityDate(@Param("storeNo") Integer storeNo, @Param("areaNo") Integer areaNo,@Param("sku") String sku,@Param("qualityDate") String qualityDate);


    /**
    *  新版 查询自动上下架sku数据
    */
    List<AreaSku> selectAutoSaleNew(@Param("storeNo") Integer storeNo, @Param("sku") String sku, @Param("onSale") Boolean onSale,
                                 @Param("openSale") Integer openSale, @Param("closeSale") Integer closeSale);

    AreaSkuVO selectVONew(@Param("sku") String sku,@Param("areaNo") Integer areaNo,@Param("mId") Long mId, @Param("accountId") Long accountId, @Param("suitId") Integer suitId);


    /**
     * 查询商品预警天数
     * @param extType 商品类型
     * @param sku     商品编号
     * @param areaNo  城市编号
     * @return
     */
    @Deprecated
    ExpiredEarlyWarningDTO selectNearExpiredSku(Integer extType, String sku, Integer areaNo);

    /**
     * 查询某一个运营区域某些sku的信息
     * @param areaNo 运营区域编号
     * @param skus   sku集合
     * @return sku信息
     */
    List<ProductInfoVO> selectByAreaNoAndSkus(@Param("areaNo") Integer areaNo,
                                              @Param("skus") Collection<String> skus,
                                              @Param("adminId") Integer adminId,
                                              @Param("direct") Integer direct);

    /**
     * 查询城市上架的代仓sku
     * @param areaNo 城市编号
     * @param adminId 大客户adminId
     * @return
     */
    List<String> selectMskuList(@Param("areaNo") Integer areaNo, @Param("adminId") Integer adminId);

    /**
     * 查询area sku价格
     * @param areaNo
     * @param skus
     * @return
     */
    List<AreaSku> selectAreaSkuByAreaNoAndSkus(@Param("areaNo") Integer areaNo, @Param("skus") Collection<String> skus);

    /**
     * 查询生效且上架的sku
     * @param areaNo
     * @param sku
     * @return
     */
    AreaSkuVO selectValidAndOnSale(@Param("areaNo") Integer areaNo, @Param("sku") String sku);

    /**
     * 查询对应城市代仓sku
     * @param adminId 大客户id
     * @param areaNo 城市编号
     * @return sku
     */
    Set<String> selectAgentSku(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo);

    /**
     * 查询当前区域内上架中的所有sku信息--单店
     * @param pdId
     * @return
     */
    List<AreaSku> getAreaSkuInfoByPdId(@Param("pdId") Long pdId);
}