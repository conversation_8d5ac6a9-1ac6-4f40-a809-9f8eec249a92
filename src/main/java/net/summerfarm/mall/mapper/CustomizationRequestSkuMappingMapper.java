package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.CustomizationRequestSkuMapping;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 定制需求sku关联表 Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Repository
public interface CustomizationRequestSkuMappingMapper {
    
    /**
     * 根据主键删除
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     * @param record 记录
     * @return 影响行数
     */
    int insert(CustomizationRequestSkuMapping record);

    /**
     * 选择性插入记录
     * @param record 记录
     * @return 影响行数
     */
    int insertSelective(CustomizationRequestSkuMapping record);

    /**
     * 根据主键查询
     * @param id 主键
     * @return 记录
     */
    CustomizationRequestSkuMapping selectByPrimaryKey(Long id);

    /**
     * 选择性更新记录
     * @param record 记录
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(CustomizationRequestSkuMapping record);

    /**
     * 根据主键更新记录
     * @param record 记录
     * @return 影响行数
     */
    int updateByPrimaryKey(CustomizationRequestSkuMapping record);

    /**
     * 根据定制需求ID查询SKU映射列表
     * @param customizationRequestId 定制需求ID
     * @return SKU映射列表
     */
    List<CustomizationRequestSkuMapping> selectByCustomizationRequestId(Long customizationRequestId);

    /**
     * 根据SKU查询映射记录
     * @param sku SKU
     * @return 映射记录
     */
    CustomizationRequestSkuMapping selectBySku(String sku);

    /**
     * 根据模版SKU查询映射列表
     * @param sourceSku 模版SKU
     * @return 映射列表
     */
    List<CustomizationRequestSkuMapping> selectBySourceSku(String sourceSku);

    /**
     * 根据定制需求ID删除所有映射记录
     * @param customizationRequestId 定制需求ID
     * @return 影响行数
     */
    int deleteByCustomizationRequestId(Long customizationRequestId);

    /**
     * 批量插入SKU映射记录
     * @param list SKU映射列表
     * @return 影响行数
     */
    int insertBatch(List<CustomizationRequestSkuMapping> list);
}
