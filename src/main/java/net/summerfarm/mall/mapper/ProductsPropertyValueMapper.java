package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.ProductsPropertyValue;
import net.summerfarm.mall.model.vo.ProductsPropertyValueVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ProductsPropertyValueMapper {
    List<ProductsPropertyValueVO> selectValue(@Param("pdId") Long pdId, @Param("sku") String sku);

    List<ProductsPropertyValueVO> selectByPdIds(@Param("pdIds") List<Long> pdIds);

    List<Map<String, Object>> selectProductsPropertyValueNameByPdIdsAndProductsPropertyName(@Param("pdIds") Set<Long> pdIds, @Param("name") String name);

    List<Map<String, Object>>  selectProductsPropertyValueNameBySkusAndProductsPropertyName(@Param("skus") Set<String> skus, @Param("name") String name);

    List<ProductsPropertyValue> selectByPdId(Long pdId);
}
