package net.summerfarm.mall.order.place;

import cn.hutool.core.collection.CollectionUtil;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.enums.CouponTypeEnum;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;
import net.summerfarm.mall.mapper.MerchantCouponMapper;
import net.summerfarm.mall.model.domain.OrderPreferential;
import net.summerfarm.mall.model.dto.order.PlaceOrderCalcDTO;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.order.AbstractPlaceOrderHandler;
import org.springframework.stereotype.Service;

/**
 * 精准送优惠券处理
 *
 * @author: <EMAIL>
 * @create: 2023/10/12
 */
@Slf4j
@Service
public class AccurateCouponNewHandler extends AbstractPlaceOrderHandler {

    @Resource
    private MerchantCouponMapper merchantCouponMapper;

    @Override
    public boolean handlePlaceOrder(PlaceOrderCalcDTO orderCalcDTO, OrderResultVO resultVO) {
        BigDecimal precisionDeliveryFee = resultVO.getPrecisionDeliveryFee();
        PlaceOrderVO placeOrderVO = orderCalcDTO.getPlaceOrderVO();
        if (precisionDeliveryFee == null) {
            return false;
        }
        if (CollectionUtil.isEmpty(placeOrderVO.getSubOrderResultMap()) || StringUtils.isBlank(
                placeOrderVO.getTimeFrameName())) {
            return false;
        }
        //获取精准送可用优惠券
        List<MerchantCouponVO> frameCouponList = merchantCouponMapper.selectUsableCoupon(
                CouponTypeEnum.TIME_FARME.getType(), placeOrderVO.getMId(),
                Global.TIME_FRAME_DELIVERY_FEE);
        if (CollectionUtil.isEmpty(frameCouponList)) {
            return false;
        }
        resultVO.setUsableAccurateCoupon(frameCouponList);

        //如果有多张运费券且用户还未选择(下单时不能自动选最优，否则会导致用户不选券也会自动使用券)，选中最优的
        MerchantCouponVO selectedCoupon = null;
        Map<Integer, Integer> usedCouponIds = placeOrderVO.getUsedCouponIds();
        if (CollectionUtil.isEmpty(usedCouponIds)
                || usedCouponIds.get(CouponTypeEnum.TIME_FARME.getType()) == null) {
            if (Objects.equals(placeOrderVO.getIsTakePrice(), 0)) {
                frameCouponList.sort(Comparator.comparing(MerchantCouponVO::getMoney).reversed());
                selectedCoupon = frameCouponList.get(0);
            }
        } else {
            Integer frameCouponId = usedCouponIds.get(CouponTypeEnum.TIME_FARME.getType());
            selectedCoupon = frameCouponList.stream()
                    .filter(x -> Objects.equals(frameCouponId, x.getId())).findFirst()
                    .orElse(null);
        }
        if (selectedCoupon == null) {
            return false;
        }

        Map<LocalDate, OrderResultVO> subOrderResultMap = placeOrderVO.getSubOrderResultMap();
        BigDecimal couponAmount = selectedCoupon.getMoney();
        //子订单和主订单 实付金额都需减掉优惠
        resultVO.setActualTotalPrice(resultVO.getActualTotalPrice().subtract(couponAmount));
        resultVO.addUsedMerchantCouponId(selectedCoupon.getId());
        //按子订单处理
        for (Entry<LocalDate, OrderResultVO> entry : subOrderResultMap.entrySet()) {
            OrderResultVO subOrderResult = entry.getValue();
            //订单包含代销不入仓的商品则不支持精准送
            if (Objects.equals(subOrderResult.getHasSaleNotInStore(), 1)) {
                continue;
            }
            subOrderResult.setActualTotalPrice(subOrderResult.getActualTotalPrice().subtract(couponAmount));
            OrderPreferential orderPreferential = new OrderPreferential(
                    OrderPreferentialTypeEnum.ACCURATE_COUPON, couponAmount);
            orderPreferential.setActivityName(selectedCoupon.getName());
            orderPreferential.setRelatedId(selectedCoupon.getId() != null ? selectedCoupon.getId().longValue() : selectedCoupon.getCouponId());
            subOrderResult.addOrderPreferential(orderPreferential);
            resultVO.addOrderPreferential(orderPreferential);
        }
        return true;
    }
}
