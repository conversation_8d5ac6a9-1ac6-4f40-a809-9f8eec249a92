package net.summerfarm.mall.order.place;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.MarketRuleTypeEnum;
import net.summerfarm.mall.common.util.MoneyUtil;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;
import net.summerfarm.mall.enums.PlaceOrderPriceEnum;
import net.summerfarm.mall.enums.ProductTypeEnum;
import net.summerfarm.mall.mapper.MarketRuleDetailMapper;
import net.summerfarm.mall.mapper.MarketRuleMapper;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.dto.order.PlaceOrderCalcDTO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.model.vo.price.SkuPreferentialVO;
import net.summerfarm.mall.order.AbstractPlaceOrderHandler;
import net.summerfarm.mall.service.MarketRuleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 满减计算逻辑
 */
@Service
@Slf4j
public class FullReduceHandler extends AbstractPlaceOrderHandler {
    @Resource
    private MarketRuleMapper marketRuleMapper;
    @Resource
    private MarketRuleDetailMapper marketRuleDetailMapper;
    @Resource
    private MarketRuleService marketRuleService;

    @Override
    public boolean handlePlaceOrder(PlaceOrderCalcDTO orderCalcDTO, OrderResultVO resultVO) {
        PlaceOrderVO placeOrderVO = orderCalcDTO.getPlaceOrderVO();

        //预估到手价和预下单、下单分开处理
        boolean isCache = placeOrderVO.getTakePriceFlag();

        //查询生效的满减规则
        List<MarketRule> validMarketRuleList;
        Integer areaNo = Optional.ofNullable(placeOrderVO.getArea()).map(Area::getAreaNo).orElse(null);
        if (isCache) {
            validMarketRuleList = marketRuleService.selectValidRuleByCache(areaNo);
            validMarketRuleList = validMarketRuleList.stream().filter(marketRule -> marketRule.getType()
                    .equals(MarketRuleTypeEnum.FULL_REDUCE.ordinal())).collect(Collectors.toList());
        } else {
            validMarketRuleList = marketRuleMapper.selectValidRule(MarketRuleTypeEnum.FULL_REDUCE.ordinal(), areaNo);
        }
        if (CollectionUtil.isEmpty(validMarketRuleList)) {
            log.info("用户所在的区域:{}满减活动为空！", areaNo);
            return false;
        }

        //计算sku
        Set<String> skuSet = new HashSet<>();
        for (OrderItemCalcDTO dto : orderCalcDTO.getItemCalcDTOList()) {
            //过滤换购商品
            if (Objects.equals(dto.getProductType(), ProductTypeEnum.EXCHANGE.getCode())) {
                continue;
            }
            skuSet.add(dto.getSku());
        }
        boolean usedFlag = false;

        //批量查询满减可以sku范围
        List<Integer> ruleIds = validMarketRuleList.stream().map(MarketRule::getId).collect(Collectors.toList());
        Map<Integer, Set<String>> usableSkuMap;
        if (isCache) {
            List<MarketRuleDetail> marketRuleDetails = marketRuleService.getAllEfficientRuleDetailSkuByCache(placeOrderVO.getArea().getAreaNo());
            usableSkuMap = marketRuleDetails.stream().filter(marketRuleDetail -> ruleIds.contains(marketRuleDetail.getRuleId()) &&
                            marketRuleDetail.getType().equals(MarketRuleTypeEnum.FULL_REDUCE.ordinal())).
                    collect(Collectors.groupingBy(MarketRuleDetail::getRuleId,
                            Collectors.mapping(MarketRuleDetail::getSku, Collectors.toSet())));
        } else {
            List<MarketRuleDetail> marketRuleDetails = marketRuleDetailMapper.selectRuleUsableSkusByRuleIds(ruleIds);
            usableSkuMap = marketRuleDetails.stream().collect(Collectors.groupingBy(MarketRuleDetail::getRuleId,
                    Collectors.mapping(MarketRuleDetail::getSku, Collectors.toSet())));
        }

        for (MarketRule rule : validMarketRuleList) {
            JSONObject ruleDetail = JSON.parseObject(rule.getRuleDetail());
            List<JSONObject> levelList = JSON.parseArray(ruleDetail.getString("value"), JSONObject.class);
            levelList.sort(Comparator.comparing(o -> o.getInteger("ruleLevel")));

            //使用sku过滤
            // 一个运营区域只会有一个MarketRule,暂时不考虑批量查询
            if (CollectionUtil.isEmpty(usableSkuMap) || !usableSkuMap.containsKey(rule.getId())) {
                continue;
            }
            Set<String> usableSkuSet = usableSkuMap.get(rule.getId());

            int usableAmount = 0;
            BigDecimal totalUsablePrice = BigDecimal.ZERO;
            List<OrderItemCalcDTO> usableList = new ArrayList<>();
            for (OrderItemCalcDTO dto : orderCalcDTO.getItemCalcDTOList()) {
                //过滤换购商品
                if (Objects.equals(dto.getProductType(), ProductTypeEnum.EXCHANGE.getCode())) {
                    continue;
                }
                if (usableSkuSet.contains(dto.getSku())) {
                    usableList.add(dto);
                    usableAmount += dto.getAmount();
                    totalUsablePrice = totalUsablePrice.add(dto.getActualTotalPrice());
                }
            }
            if (CollectionUtil.isEmpty(usableList)) {
                continue;
            }

            //满减类型：1、阶梯满减 2、每满元减 3、每满件减
            Integer type = ruleDetail.getInteger("type");
            //不满足门槛、不计算
            if (Objects.equals(3, type)) {
                Integer firstLevel = levelList.get(0).getInteger("ruleLevel");
                if (usableAmount < firstLevel) {
                    continue;
                }
            } else {
                BigDecimal firstLevel = levelList.get(0).getBigDecimal("ruleLevel");
                if (totalUsablePrice.compareTo(firstLevel) < 0) {
                    continue;
                }
            }

            //计算优惠金额
            BigDecimal totalDiscount = BigDecimal.ZERO;
            JSONObject validLevel = levelList.get(0);
            int recordCount = 1;
            if (Objects.equals(1, type)) {
                //最高阶梯
                for (JSONObject jsonObject : levelList) {
                    BigDecimal ruleLevel = jsonObject.getBigDecimal("ruleLevel");
                    if (totalUsablePrice.compareTo(ruleLevel) < 0) {
                        break;
                    }
                    validLevel = jsonObject;
                }
                totalDiscount = validLevel.getBigDecimal("value");
            } else if (Objects.equals(2, type)) {
                BigDecimal ruleLevel = validLevel.getBigDecimal("ruleLevel");
                BigDecimal ruleVale = validLevel.getBigDecimal("value");
                BigDecimal divide = totalUsablePrice.divide(ruleLevel, 0, RoundingMode.FLOOR);
                totalDiscount = ruleVale.multiply(divide);
                recordCount = divide.intValue();
            } else if (Objects.equals(3, type)) {
                Integer ruleLevel = validLevel.getInteger("ruleLevel");
                BigDecimal ruleVale = validLevel.getBigDecimal("value");
                recordCount = usableAmount / ruleLevel;
                totalDiscount = ruleVale.multiply(BigDecimal.valueOf(usableAmount / ruleLevel));
            }

            //分摊优惠金额,换购商品不参与满减,所以不分摊
            Map<String, BigDecimal> downMap = new HashMap<>(16);
            usableList.forEach(el -> downMap.put(el.getSuitId() + ":" + el.getParentSku() + ":" + el.getSku(), el.getActualTotalPrice()));
            Map<String, BigDecimal> discountMap = MoneyUtil.moneyDownUtil(downMap, totalDiscount);
            if (CollectionUtil.isEmpty(discountMap)) {
                continue;
            }
            for (OrderItemCalcDTO dto : orderCalcDTO.getItemCalcDTOList()) {
                //过滤换购商品
                if (Objects.equals(dto.getProductType(), ProductTypeEnum.EXCHANGE.getCode())) {
                    continue;
                }
                String key = dto.getSuitId() + ":" + dto.getParentSku() + ":" + dto.getSku();
                if (discountMap.get(key) != null) {
                    dto.decreaseActualTotalPrice(discountMap.get(key));
                    //添加订单项优惠明细
                    dto.addItemPreferential(() -> {
                        OrderItemPreferential orderPreferential = new OrderItemPreferential(OrderPreferentialTypeEnum.FULL_REDUCE, discountMap.get(key));
                        orderPreferential.setRelatedId(rule.getId().longValue());
                        orderPreferential.setActivityName(rule.getName());
                        orderPreferential.setCreateTime(LocalDateTime.now());
                        return orderPreferential;
                    });

                    if (placeOrderVO.getTakePriceFlag()){
                        //到手价明细返回
                        JSONObject finalValidLevel = validLevel;
                        dto.addSkuPreferential(() -> {
                            SkuPreferentialVO skuPreferentialVO = new SkuPreferentialVO(OrderPreferentialTypeEnum.FULL_REDUCE, discountMap.get(key));
                            skuPreferentialVO.setRuleLevel(finalValidLevel.getBigDecimal("ruleLevel"));
                            skuPreferentialVO.setValue(finalValidLevel.getBigDecimal("value"));
                            skuPreferentialVO.setRefinementType(type);
                            return skuPreferentialVO;
                        });
                    }
                }
            }

            //记录优惠信息
            Integer value = validLevel.getInteger("value");
            Integer level = validLevel.getInteger("ruleLevel");
            for (int i = 0; i < recordCount; i++) {
                resultVO.addMarketRuleHistory(() ->
                        new MarketRuleHistory(JSON.toJSONString(rule), null, rule.getId(), value, level, MarketRuleTypeEnum.FULL_REDUCE.ordinal(), null));
            }

            resultVO.addOrderPreferential(new OrderPreferential(OrderPreferentialTypeEnum.FULL_REDUCE, totalDiscount));

            usedFlag = true;
        }

        return usedFlag;
    }
}
