package net.summerfarm.mall.order.place;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.enums.SkuTypeEnum;
import net.summerfarm.enums.TrolleyProductTypeEnum;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;
import net.summerfarm.mall.enums.PlaceOrderPriceEnum;
import net.summerfarm.mall.enums.ProductTypeEnum;
import net.summerfarm.mall.mapper.DiscountCardAvailableMapper;
import net.summerfarm.mall.mapper.DiscountCardMapper;
import net.summerfarm.mall.mapper.DiscountCardToMerchantMapper;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.dto.order.PlaceOrderCalcDTO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.model.vo.price.SkuPreferentialVO;
import net.summerfarm.mall.order.AbstractPlaceOrderHandler;
import net.summerfarm.mall.service.DiscountCardService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 优惠卡计算逻辑
 */
@Service
@Slf4j
public class DiscountCardHandler extends AbstractPlaceOrderHandler {
    @Resource
    private DiscountCardMapper discountCardMapper;
    @Resource
    private DiscountCardToMerchantMapper discountCardToMerchantMapper;
    @Resource
    private DiscountCardAvailableMapper discountCardAvailableMapper;

    @Resource
    private DiscountCardService discountCardService;

    @Override
    public boolean handlePlaceOrder(PlaceOrderCalcDTO orderCalcDTO, OrderResultVO resultVO) {
        PlaceOrderVO orderVO = orderCalcDTO.getPlaceOrderVO();

        //预估到手价和预下单、下单分开处理
        boolean isCache = orderVO.getTakePriceFlag();

        //查询可用优惠卡
        List<DiscountCardToMerchant> cardList;
        if (isCache) {
            cardList = discountCardService.getMerchantCardByCache(orderVO.getMId());
        } else {
            cardList = discountCardToMerchantMapper.selectCard(orderVO.getMId());
        }
        if (CollectionUtil.isEmpty(cardList)) {
            return false;
        }

        Set<String> skuSet = new HashSet<>();
        List<OrderItemCalcDTO> dtoList = new ArrayList<>();
        for (OrderItemCalcDTO calcDTO : orderCalcDTO.getItemCalcDTOList()) {
            //过滤赠品
            if (Objects.equals(TrolleyProductTypeEnum.GIFT.ordinal(), calcDTO.getProductType())) {
                continue;
            }
            //过滤换购商品
            if (Objects.equals(calcDTO.getProductType(), ProductTypeEnum.EXCHANGE.getCode())) {
                continue;
            }
            //过滤代仓
            if (Objects.equals(calcDTO.getSkuType(), SkuTypeEnum.AGENT.ordinal())) {
                continue;
            }

            skuSet.add(calcDTO.getSku());
            dtoList.add(calcDTO);
        }
        if (CollectionUtil.isEmpty(skuSet)) {
            return false;
        }

        //查询用户所有优惠卡
        List<Integer> cardIdList = cardList.stream().map(DiscountCardToMerchant::getDiscountCardId).collect(Collectors.toList());
        List<DiscountCard> discountCards;
        if (isCache) {
            discountCards = discountCardService.selectAllDiscountCard();
            discountCards = discountCards.stream().filter(el -> cardIdList.contains(el.getId())).collect(Collectors.toList());
        } else {
            discountCards = discountCardMapper.listByIds(cardIdList);
        }
        if (CollectionUtil.isEmpty(discountCards)) {
            return false;
        }

        Map<Integer, DiscountCard> cardMap = discountCards.stream().collect(Collectors.toMap(DiscountCard::getId, el -> el));

        //使用优惠卡
        List<DiscountCardUseRecord> useRecords = new ArrayList<>();

        //查询优惠卡可以sku信息
        Set<String> cardSkuSet;
        Map<Integer, Set<String>> listMap;
        if (isCache) {
            List<DiscountCardAvailable> discountCardAvailableList = discountCardService.getAllDiscountCardAvailableByCache();
            listMap = discountCardAvailableList.stream().filter(el -> cardIdList.contains(el.getDiscountCardId()))
                    .collect(Collectors.groupingBy(DiscountCardAvailable::getDiscountCardId,
                            Collectors.mapping(DiscountCardAvailable::getSku, Collectors.toSet())));
        } else {
            List<DiscountCardAvailable> discountCardAvailables = discountCardAvailableMapper.listByCardIdsAndSkus(cardIdList, skuSet);
            listMap = discountCardAvailables.stream().collect(Collectors.groupingBy(DiscountCardAvailable::getDiscountCardId,
                    Collectors.mapping(DiscountCardAvailable::getSku, Collectors.toSet())));
        }

        for (DiscountCardToMerchant toMerchant : cardList) {
            DiscountCard discountCard = cardMap.get(toMerchant.getDiscountCardId());
            //校验城市
            if (!StringUtils.isEmpty(discountCard.getAreaNos())) {
                String[] areaNos = discountCard.getAreaNos().split(",");
                List<String> resultList = new ArrayList<>(Arrays.asList(areaNos));
                if (!resultList.contains(String.valueOf(orderVO.getArea().getAreaNo()))) {
                    log.info("用户:{}使用优惠卡:{}，城市:{}不符合", orderVO.getMId(), discountCard.getId(), orderVO.getArea().getAreaNo());
                    continue;
                }
            }

            //校验sku
            if (CollectionUtil.isEmpty(listMap) || !listMap.containsKey(toMerchant.getDiscountCardId())) {
                log.info("用户:{}使用优惠卡:{}，sku:{}不符合", orderVO.getMId(), discountCard.getId(), JSON.toJSONString(skuSet));
                continue;
            }
            cardSkuSet = listMap.get(toMerchant.getDiscountCardId());

            //使用优惠卡
            int leftTimes = toMerchant.getTotalTimes() - toMerchant.getUsedTimes();
            for (OrderItemCalcDTO dto : dtoList) {
                if (cardSkuSet.contains(dto.getSku())) {
                    int useTimes = Math.min(leftTimes, dto.getAmount());

                    //扣减次数
                    leftTimes -= useTimes;

                    //处理订单项金额
                    BigDecimal discountTotal = discountCard.getDiscount().multiply(BigDecimal.valueOf(useTimes));
                    if (dto.getActualTotalPrice().compareTo(discountTotal) < 0) {
                        log.info("用户:{}使用优惠卡:{}，金额小于优惠金额discountTotal:{}，全部金额actualTotalPrice:{}", orderVO.getMId(),
                                discountCard.getId(), discountTotal, dto.getActualTotalPrice());
                        continue;
                    }

                    dto.decreaseActualTotalPrice(discountTotal);
                    OrderPreferential preferential = new OrderPreferential(
                            OrderPreferentialTypeEnum.DISCOUNT_CARD, discountTotal);
                    preferential.setActivityName(discountCard.getName());
                    preferential.setRelatedId(toMerchant.getId().longValue());

                    //记录优惠
                    resultVO.addOrderPreferential(preferential);

                    //添加订单项优惠明细
                    dto.addItemPreferential(() -> {
                        OrderItemPreferential orderPreferential = new OrderItemPreferential(OrderPreferentialTypeEnum.DISCOUNT_CARD, discountTotal);
                        orderPreferential.setRelatedId(toMerchant.getId().longValue());
                        orderPreferential.setActivityName(discountCard.getName());
                        orderPreferential.setCreateTime(LocalDateTime.now());
                        return orderPreferential;
                    });

                    if (orderVO.getTakePriceFlag()){
                        //到手价明细返回
                        dto.addSkuPreferential(() -> {
                            SkuPreferentialVO skuPreferentialVO = new SkuPreferentialVO(OrderPreferentialTypeEnum.DISCOUNT_CARD, discountTotal);
                            //优惠卡这里固定是1
                            skuPreferentialVO.setRuleLevel(BigDecimal.ONE);
                            skuPreferentialVO.setValue(discountCard.getDiscount());
                            return skuPreferentialVO;
                        });
                    }

                    //生成使用记录
                    DiscountCardUseRecord record = new DiscountCardUseRecord();
                    record.setCardName(discountCard.getName());
                    record.setCreateTime(LocalDateTime.now());
                    record.setDiscountCardMerchantId(toMerchant.getId());
                    record.setSuitId(dto.getSuitId());
                    record.setSku(dto.getSku());
                    record.setUseTimes(useTimes);
                    record.setTotalDiscount(discountCard.getDiscount().multiply(BigDecimal.valueOf(useTimes)));
                    useRecords.add(record);
                }

                //次数用完，不再继续计算
                if (leftTimes <= 0) {
                    break;
                }
            }
        }
        //添加优惠卡使用记录
        resultVO.setDiscountCardUseRecords(useRecords);

        return !CollectionUtil.isEmpty(useRecords);
    }
}
