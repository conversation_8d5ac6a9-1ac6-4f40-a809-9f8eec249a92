package net.summerfarm.mall.order.place;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.MarketRuleTypeEnum;
import net.summerfarm.mall.enums.MarketRuleHistorySendStatusEnum;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;
import net.summerfarm.mall.enums.ProductTypeEnum;
import net.summerfarm.mall.mapper.MarketRuleDetailMapper;
import net.summerfarm.mall.mapper.MarketRuleMapper;
import net.summerfarm.mall.model.domain.MarketRule;
import net.summerfarm.mall.model.domain.MarketRuleHistory;
import net.summerfarm.mall.model.domain.OrderItemPreferential;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.dto.order.PlaceOrderCalcDTO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.order.AbstractPlaceOrderHandler;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @Description 满返计算逻辑
 */
@Slf4j
@Service
public class FullReturnHandler extends AbstractPlaceOrderHandler {
    @Resource
    private MarketRuleMapper marketRuleMapper;
    @Resource
    private MarketRuleDetailMapper marketRuleDetailMapper;

    @Override
    public boolean handlePlaceOrder(PlaceOrderCalcDTO orderCalcDTO, OrderResultVO resultVO) {
        PlaceOrderVO placeOrderVO = orderCalcDTO.getPlaceOrderVO();

        //查询生效的满返规则
        List<MarketRule> validMarketRuleList = marketRuleMapper.selectValidRule(MarketRuleTypeEnum.FULL_RETURN.ordinal(), placeOrderVO.getArea().getAreaNo());
        if (CollectionUtil.isEmpty(validMarketRuleList)) {
            log.info("满返营销-未查询到满返营销,areaNo:{}", placeOrderVO.getArea().getAreaNo());
            return false;
        }

        //计算sku
        Set<String> skuSet = new HashSet<>();
        for (OrderItemCalcDTO dto : orderCalcDTO.getItemCalcDTOList()) {
            //过滤换购商品
            if (Objects.equals(dto.getProductType(), ProductTypeEnum.EXCHANGE.getCode())) {
                continue;
            }
            skuSet.add(dto.getSku());
        }

        log.info("满返营销-用户营销sku:{}", JSON.toJSONString(skuSet));
        boolean usedFlag = false;
        for (MarketRule rule : validMarketRuleList) {
            JSONObject ruleDetail = JSON.parseObject(rule.getRuleDetail());
            List<JSONObject> levelList = JSON.parseArray(ruleDetail.getString("value"), JSONObject.class);
            levelList.sort(Comparator.comparing(o -> o.getInteger("ruleLevel")));

            //使用sku过滤
            Set<String> usableSkuSet = marketRuleDetailMapper.selectRuleSku(rule.getId(), skuSet);
            log.info("满返营销-符合条件的sku:{}, 当前规则id:{}", JSON.toJSONString(skuSet), rule.getId());
            if (CollectionUtil.isEmpty(usableSkuSet)) {
                continue;
            }
            BigDecimal totalUsablePrice = BigDecimal.ZERO;
            List<OrderItemCalcDTO> usableList = new ArrayList<>();
            for (OrderItemCalcDTO dto : orderCalcDTO.getItemCalcDTOList()) {
                //过滤换购商品
                if (Objects.equals(dto.getProductType(), ProductTypeEnum.EXCHANGE.getCode())) {
                    continue;
                }
                if (usableSkuSet.contains(dto.getSku())) {
                    usableList.add(dto);
                    totalUsablePrice = totalUsablePrice.add(dto.getActualTotalPrice());
                }
            }
            if (CollectionUtil.isEmpty(usableList)) {
                continue;
            }

            //不满足门槛、不计算
            BigDecimal firstLevel = levelList.get(0).getBigDecimal("ruleLevel");
            if (totalUsablePrice.compareTo(firstLevel) < 0) {
                continue;
            }

            //满返类型：1、阶梯满返 2、每满返
            Integer type = ruleDetail.getInteger("type");

            //计算使用阶梯
            JSONObject validLevel = levelList.get(0);
            int recordCount = 0;
            if (Objects.equals(1, type)) {
                //最高阶梯
                for (JSONObject jsonObject : levelList) {
                    BigDecimal ruleLevel = jsonObject.getBigDecimal("ruleLevel");
                    if (totalUsablePrice.compareTo(ruleLevel) < 0) {
                        break;
                    }
                    validLevel = jsonObject;
                }
                recordCount = 1;
            } else {
                recordCount = totalUsablePrice.divide(firstLevel, 0, RoundingMode.FLOOR).intValue();
            }

            //记录满返信息
            Integer value = validLevel.getInteger("value");
            Integer level = validLevel.getInteger("ruleLevel");
            for (int i = 0; i < recordCount; i++) {
                resultVO.addMarketRuleHistory(() ->
                        new MarketRuleHistory(JSON.toJSONString(rule), null, rule.getId(), value, level,
                                MarketRuleTypeEnum.FULL_RETURN.ordinal(), MarketRuleHistorySendStatusEnum.TO_BE_ISSUED.getCode()));
            }
            usedFlag = true;

            //记录满返优惠明细
            usableList.stream().forEach(dto -> {
                //添加订单项优惠明细
                dto.addItemPreferential(() -> {
                    OrderItemPreferential orderPreferential = new OrderItemPreferential(OrderPreferentialTypeEnum.FULL_RETURN, BigDecimal.ZERO);
                    //此处记录优惠券ID
                    orderPreferential.setRelatedId(value.longValue());
                    orderPreferential.setActivityName(rule.getName());
                    orderPreferential.setDiscountsDetailSnapshot(JSON.toJSONString(rule));
                    orderPreferential.setCreateTime(LocalDateTime.now());
                    return orderPreferential;
                });
            });
        }

        return usedFlag;
    }
}
