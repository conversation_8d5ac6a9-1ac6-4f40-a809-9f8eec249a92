package net.summerfarm.mall.order.place;

import cn.hutool.core.collection.CollectionUtil;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map.Entry;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.model.dto.order.PlaceOrderCalcDTO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.order.AbstractPlaceOrderHandler;
import net.summerfarm.mall.service.TrolleyService;
import org.springframework.stereotype.Service;

/**
 * 超时加单处理
 * @author: <EMAIL>
 * @create: 2023/10/12
 */
@Slf4j
@Service
public class OutTimeNewHandler extends AbstractPlaceOrderHandler {

    @Resource
    private TrolleyService trolleyService;

    @Override
    public boolean handlePlaceOrder(PlaceOrderCalcDTO orderCalcDTO, OrderResultVO resultVO) {
        PlaceOrderVO placeOrderVO = orderCalcDTO.getPlaceOrderVO();
        if (CollectionUtil.isEmpty(placeOrderVO.getSubOrderResultMap()) || !Objects.equals(
                placeOrderVO.getOutTimes(), 1)) {
            return false;
        }

        boolean flag = false;
        //按子订单处理
        for (Entry<LocalDate, OrderResultVO> entry : placeOrderVO.getSubOrderResultMap()
                .entrySet()) {
            OrderResultVO subOrderResult = entry.getValue();
            //订单包含代销不入仓的商品则不支持精准送
            if (Objects.equals(subOrderResult.getHasSaleNotInStore(), 1)) {
                continue;
            }
            subOrderResult.setOutTimes(1);
            int left = trolleyService.getOutTimes(placeOrderVO.getMId());
            if (left <= 0) {
                resultVO.setOutTimesFee(Global.OUT_TIMES_FEE);
                //超时加单固定5元
                subOrderResult.setOutTimesFee(Global.OUT_TIMES_FEE);
                subOrderResult.setActualTotalPrice(subOrderResult.getActualTotalPrice().add(Global.OUT_TIMES_FEE));
                subOrderResult.setOriginTotalPrice(subOrderResult.getOriginTotalPrice().add(Global.OUT_TIMES_FEE));

                resultVO.setOutTimesFee(Global.OUT_TIMES_FEE);
                resultVO.setActualTotalPrice(resultVO.getActualTotalPrice().add(Global.OUT_TIMES_FEE));
                resultVO.setOriginTotalPrice(resultVO.getOriginTotalPrice().add(Global.OUT_TIMES_FEE));
                flag = true;
            }
            if (!flag) {
                subOrderResult.setOutTimesFee(BigDecimal.ZERO);
                resultVO.setOutTimesFee(BigDecimal.ZERO);
            }
        }

        return flag;
    }
}
