package net.summerfarm.mall.order.place;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.enums.CommonStatus;
import net.summerfarm.mall.enums.DeliveryEveryDayEnum;
import net.summerfarm.mall.enums.ProductCategoryTypeEnum;
import net.summerfarm.mall.mapper.DeliveryPlanMapper;
import net.summerfarm.mall.model.dto.delivery.ConditionsDTO;
import net.summerfarm.mall.model.dto.delivery.DistributionRulesDTO;
import net.summerfarm.mall.model.dto.delivery.DistributionRulesQueryDTO;
import net.summerfarm.mall.model.dto.delivery.RulesDTO;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.dto.order.PlaceOrderCalcDTO;
import net.summerfarm.mall.model.vo.neworder.DeliveryFreeRuleVO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.order.AbstractPlaceOrderHandler;
import net.summerfarm.mall.service.DistributionRulesService;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @create: 2023/10/12
 */
@Slf4j
@Service
public class DeliveryFeeNewHandler extends AbstractPlaceOrderHandler {

    @Resource
    private DistributionRulesService rulesService;

    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;

    @Override
    public boolean handlePlaceOrder(PlaceOrderCalcDTO orderCalcDTO, OrderResultVO resultVO) {
        PlaceOrderVO placeOrderVO = orderCalcDTO.getPlaceOrderVO();
        if (CollectionUtil.isEmpty(placeOrderVO.getSubOrderResultMap())) {
            return false;
        }

        if (placeOrderVO.getDeliveryRulesType() != null && placeOrderVO.getDeliveryRulesType().equals(CommonStatus.YES.getCode())) {
            log.info("获取新版运费规则！orderCalcDTO：{}", JSON.toJSONString(orderCalcDTO));
            return false;
        }

        //获取运费规则
        DistributionRulesQueryDTO queryDTO = new DistributionRulesQueryDTO();
        queryDTO.setAdminId(Optional.ofNullable(placeOrderVO.getAdminId()).map(Integer::longValue)
                .orElse(null));
        queryDTO.setAreaNo(placeOrderVO.getArea().getAreaNo());
        queryDTO.setContactId(placeOrderVO.getContactId());
        DistributionRulesDTO rulesServiceInfo = rulesService.getInfo(queryDTO);
        if (rulesServiceInfo == null || CollectionUtil.isEmpty(rulesServiceInfo.getRulesDTOS())) {
            log.error("运费规则异常！");
            return false;
        }
        BigDecimal deliveryFee = BigDecimal.ZERO;
        BigDecimal ruleDeliveryFee = BigDecimal.ZERO;
        Map<Integer, RulesDTO> ruleMap = rulesServiceInfo.getRulesDTOS().stream()
                .collect(Collectors.toMap(RulesDTO::getAgeing, Function.identity(), (a, b) -> a));

        Map<LocalDate, OrderResultVO> subOrderResultMap = placeOrderVO.getSubOrderResultMap();
        //按子订单处理
        for (Entry<LocalDate, OrderResultVO> entry : subOrderResultMap.entrySet()) {
            OrderResultVO subOrderResultVO = entry.getValue();
            Integer isEveryDayFlag = subOrderResultVO.getIsEveryDayFlag();
            List<OrderItemCalcDTO> itemList = subOrderResultVO.getItemList();
            if (CollectionUtil.isEmpty(itemList)) {
                continue;
            }
            RulesDTO rulesDTO;
            //T+1 或者 T+N 运费规则
            //1、判断日配或者非日配 假如非日配获取T+N配送规则
            //2、假如为日配 则根据商品属性来判断 商品经销：T+1 商品代销不入仓：T+N
            if (Objects.equals(isEveryDayFlag, DeliveryEveryDayEnum.DAYCARE.getCode())) {
                rulesDTO =
                        Objects.equals(subOrderResultVO.getHasSaleNotInStore(), 1) ? ruleMap.get(1)
                                : ruleMap.get(0);
            } else if (Objects.equals(isEveryDayFlag, DeliveryEveryDayEnum.NON_DAYCARE.getCode())){
                rulesDTO = ruleMap.get(1);
            } else {
                throw new BizException("获取履约时效有误，请重试！");
            }
            if (rulesDTO == null) {
                continue;
            }
            subOrderResultVO.setSatisfyFreeDelivery(0);
            calDeliveryFee(placeOrderVO, subOrderResultVO, rulesDTO);
            BigDecimal subDeliveryFee = subOrderResultVO.getDeliveryFee();
            subOrderResultVO.setOriginTotalPrice(subOrderResultVO.getOriginTotalPrice()
                    .add(subOrderResultVO.getOriginalDeliveryFee()));
            subOrderResultVO.setActualTotalPrice(
                    subOrderResultVO.getActualTotalPrice().add(subDeliveryFee));
            deliveryFee = deliveryFee.add(subDeliveryFee);
            //运费规则总计运费
            ruleDeliveryFee = ruleDeliveryFee.add(subOrderResultVO.getRuleDeliveryFee());
        }
        resultVO.setRuleDeliveryFee(ruleDeliveryFee);
        resultVO.setDeliveryFee(deliveryFee);
        resultVO.setOriginalDeliveryFee(deliveryFee);
        resultVO.setOriginTotalPrice(resultVO.getOriginTotalPrice().add(deliveryFee));
        resultVO.setActualTotalPrice(resultVO.getActualTotalPrice().add(deliveryFee));
        return true;
    }

    /**
     * 计算运费
     *
     * @param placeOrderVO
     * @param resultVO
     */
    public void calDeliveryFee(PlaceOrderVO placeOrderVO, OrderResultVO resultVO,
            RulesDTO rulesDTO) {
        LocalDate deliveryDate = resultVO.getDeliveryDate();
        BigDecimal deliveryFee = rulesDTO.getDeliveryFee();
        //已无免邮日，无需再判断
        //配送日已有配送订单
        int delivery = deliveryPlanMapper.countByDeliveryTime(placeOrderVO.getMId(),
                placeOrderVO.getContactId(), deliveryDate);
        if (delivery > 0) {
            resultVO.setSatisfyFreeDelivery(1);
            resultVO.setRuleDeliveryFee(deliveryFee);
            resultVO.setOriginalDeliveryFee(BigDecimal.ZERO);
            resultVO.setDeliveryFee(BigDecimal.ZERO);
            return;
        }

        List<ConditionsDTO> deliveryRules = rulesDTO.getConditionsDTOS();
        resultVO.setRuleDeliveryFee(deliveryFee);
        resultVO.setOriginalDeliveryFee(deliveryFee);
        resultVO.setDeliveryFee(deliveryFee);
        //配置了运费，没有配置运费规则，则不用计算免运
        if (deliveryFee != null && CollectionUtil.isEmpty(deliveryRules)) {
            return;
        }

        //配置了运费规则
        calDeliveryFeeByRules(resultVO, rulesDTO);
    }

    /**
     * 计算运费-售后外面判断了是否有配送，这里不需要判断是否有配送
     *
     * @param placeOrderVO
     * @param resultVO
     */
    public void calDeliveryFeeForAfterSale(PlaceOrderVO placeOrderVO, OrderResultVO resultVO,
                               RulesDTO rulesDTO) {
        BigDecimal deliveryFee = rulesDTO.getDeliveryFee();
        List<ConditionsDTO> deliveryRules = rulesDTO.getConditionsDTOS();
        resultVO.setRuleDeliveryFee(deliveryFee);
        resultVO.setOriginalDeliveryFee(deliveryFee);
        resultVO.setDeliveryFee(deliveryFee);
        //配置了运费，没有配置运费规则，则不用计算免运
        if (deliveryFee != null && CollectionUtil.isEmpty(deliveryRules)) {
            return;
        }

        //配置了运费规则
        calDeliveryFeeByRules(resultVO, rulesDTO);
    }

    /**
     * 按规则计算免运以及免运差额
     *
     * @param resultVO
     * @param rulesDTO
     */
    private void calDeliveryFeeByRules(OrderResultVO resultVO, RulesDTO rulesDTO) {
        BigDecimal totalPrice = BigDecimal.ZERO;
        BigDecimal dairyPrice = BigDecimal.ZERO;
        BigDecimal nonDairyPrice = BigDecimal.ZERO;

        List<OrderItemCalcDTO> itemList = resultVO.getItemList();
        int dairyNum = 0;
        int nonDairyNum = 0;
        int totalNum = itemList.stream().mapToInt(OrderItemCalcDTO::getAmount).sum();

        for (OrderItemCalcDTO dto : itemList) {
            totalPrice = totalPrice.add(dto.getCalcPartDeliveryFee());
            //乳制品、非乳制品
            if (Objects.equals(ProductCategoryTypeEnum.DAIRY.getCode(), dto.getCategoryType())) {
                dairyNum += dto.getAmount();
                dairyPrice = dairyPrice.add(dto.getCalcPartDeliveryFee());
            } else {
                nonDairyNum += dto.getAmount();
                nonDairyPrice = nonDairyPrice.add(dto.getCalcPartDeliveryFee());
            }
        }

        List<DeliveryFreeRuleVO> freeDeliveryRules = Lists.newArrayList();
        for (ConditionsDTO rule : rulesDTO.getConditionsDTOS()) {
            DeliveryFreeRuleVO ruleVO = new DeliveryFreeRuleVO();
            ruleVO.setProductType(rule.getProductType());
            ruleVO.setSillType(rule.getSillType());
            freeDeliveryRules.add(ruleVO);
            //免运门槛为件数
            if (Objects.equals(rule.getSillType(), 2)) {
                ruleVO.setNumber(rule.getNumber());
                int num = rule.getProductType() == 1 ? totalNum
                        : (rule.getProductType() == 2 ? dairyNum : nonDairyNum);
                //达到免运门槛
                if (num >= rule.getNumber()) {
                    resultVO.setSatisfyFreeDelivery(1);
                    resultVO.setOriginalDeliveryFee(BigDecimal.ZERO);
                    resultVO.setDeliveryFee(BigDecimal.ZERO);
                } else {
                    ruleVO.setLackNumber(rule.getNumber() - num);
                }
                continue;
            }

            //免运门槛为金额
            if (Objects.equals(rule.getSillType(), 1)) {
                ruleVO.setAmount(rule.getAmount());
                BigDecimal amount = rule.getProductType() == 1 ? totalPrice
                        : (rule.getProductType() == 2 ? dairyPrice : nonDairyPrice);
                //达到免运门槛
                if (amount.compareTo(rule.getAmount()) >= 0) {
                    resultVO.setSatisfyFreeDelivery(1);
                    resultVO.setOriginalDeliveryFee(BigDecimal.ZERO);
                    resultVO.setDeliveryFee(BigDecimal.ZERO);
                } else {
                    ruleVO.setLackAmount(rule.getAmount().subtract(amount));
                }
            }
        }

        List<Integer> sortList;
        //处理下规则排序
        if (totalNum == dairyNum) {
            //全部为乳制品
            sortList = Lists.newArrayList(2, 1, 3);
        } else if (totalNum == nonDairyNum) {
            //全部为非乳制品
            sortList = Lists.newArrayList(3, 1, 2);
        } else {
            //乳制品、非乳制品都有
            sortList = Lists.newArrayList(1, 3, 2);
        }
        List<DeliveryFreeRuleVO> sortedRules = freeDeliveryRules.stream()
                .sorted(Comparator.comparing(DeliveryFreeRuleVO::getProductType,
                        Comparator.comparing(sortList::indexOf))).collect(
                        Collectors.toList());
        resultVO.setFreeDeliveryRule(sortedRules);
    }
}
