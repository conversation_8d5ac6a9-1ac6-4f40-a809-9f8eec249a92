package net.summerfarm.mall.order.place;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.CouponTypeEnum;
import net.summerfarm.mall.common.util.MoneyUtil;
import net.summerfarm.mall.enums.CouponEffectiveNumEnum;
import net.summerfarm.mall.enums.CouponSkuScopeEnum;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;
import net.summerfarm.mall.enums.PlaceOrderPriceEnum;
import net.summerfarm.mall.enums.ProductTypeEnum;
import net.summerfarm.mall.mapper.MerchantCouponMapper;
import net.summerfarm.mall.model.domain.OrderItemPreferential;
import net.summerfarm.mall.model.domain.OrderPreferential;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.dto.order.PlaceOrderCalcDTO;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.model.vo.price.SkuPreferentialVO;
import net.summerfarm.mall.order.AbstractPlaceOrderHandler;
import net.summerfarm.mall.service.ConfigService;
import net.summerfarm.mall.service.MerchantCouponService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 红包计算逻辑
 */
@Service
@Slf4j
public class RedPacketHandler extends AbstractPlaceOrderHandler {
    @Resource
    private MerchantCouponMapper merchantCouponMapper;
    @Resource
    private ConfigService configService;
    @Resource
    private MerchantCouponService merchantCouponService;

    @Override
    public boolean handlePlaceOrder(PlaceOrderCalcDTO orderCalcDTO, OrderResultVO resultVO) {
        PlaceOrderVO placeOrderVO = orderCalcDTO.getPlaceOrderVO();
        List<OrderItemCalcDTO> calcDTOList = new ArrayList<>();

        //预估到手价和预下单、下单分开处理
        boolean isCache = placeOrderVO.getTakePriceFlag();

        List<String> limitPriceSku = configService.getValuesWithCache("AOL_LIMIT_PRICE_SKU", 1800L);

        //计算订单总金额，简单过滤可用红包
        BigDecimal total = BigDecimal.ZERO;
        for (OrderItemCalcDTO dto : orderCalcDTO.getItemCalcDTOList()) {
            //过滤换购商品
            if (Objects.equals(dto.getProductType(), ProductTypeEnum.EXCHANGE.getCode())) {
                continue;
            }
            //代仓商品金额不计算红包
            if (Objects.equals(dto.getSkuType(), 1)){
                continue;
            }
            //安佳控价品不计算红包
            if (!CollectionUtils.isEmpty(limitPriceSku) && limitPriceSku.contains(dto.getSku())){
                continue;
            }

            total = total.add(dto.getActualTotalPrice());
            calcDTOList.add(dto);
        }

        if (CollectionUtils.isEmpty(calcDTOList)){
            return false;
        }

        List<MerchantCouponVO> couponVOList;
        if (isCache) {
            //获取用户所有可以红包，再根据金额进行过滤出可以的红包
            List<MerchantCouponVO> usableCouponByCache = merchantCouponService.getUsableCouponByCache(placeOrderVO.getMId());
            BigDecimal finalTotal = total;
            couponVOList = usableCouponByCache.stream().filter(merchantCouponVO -> merchantCouponVO.getAgioType().equals(CouponTypeEnum.RED_PACKET.getType()) &&
                            merchantCouponVO.getMoney().compareTo(finalTotal) <= 0 && merchantCouponVO.getThreshold().compareTo(finalTotal) <= 0)
                    .sorted(Comparator.comparing(MerchantCouponVO::getMoney).reversed()).collect(Collectors.toList());
        } else {
            couponVOList = merchantCouponMapper.selectUsableCoupon(CouponTypeEnum.RED_PACKET.getType(), placeOrderVO.getMId(), total);
        }

        //加载用户红包
        if (placeOrderVO.getIsTakePrice().equals(PlaceOrderPriceEnum.ISTAKEPRICE.getCode())) {
            if (!CollectionUtils.isEmpty(couponVOList)){
                List<MerchantCouponVO> sortedList = couponVOList.stream()
                        .sorted(Comparator.comparing(MerchantCouponVO::getMoney).reversed())
                        .collect(Collectors.toList());
                placeOrderVO.setMerchantCouponId(new HashSet<>(Collections.singletonList(sortedList.get(0).getId())));
            }
        }

        //将红包全部置为已经领取，后面校验下单金额会用到
        couponVOList.stream().forEach(e -> {
            e.setEffectiveNum(CouponEffectiveNumEnum.RECEIVE.getCode());
            e.setSkuScope(CouponSkuScopeEnum.ALL.getCode());
        });

        if (!CollectionUtil.isEmpty(couponVOList)) {
            //可用红包
            resultVO.setUsableRedPackCoupon(couponVOList);

            log.info("红包责任链--获取到用户可以红包信息:{}", JSON.toJSONString(couponVOList));

            //使用红包
            if (CollectionUtil.isEmpty(placeOrderVO.getMerchantCouponId())) {
                return false;
            }
            for (MerchantCouponVO couponVO : couponVOList) {
                if (placeOrderVO.getMerchantCouponId().contains(couponVO.getId())) {
                    resultVO.addUsedMerchantCouponId(couponVO.getId());

                    Map<String, BigDecimal> itemMap = new HashMap<>(8);
                    calcDTOList.stream()
                            .forEach(el -> itemMap.put(el.getSuitId() + ":" + el.getParentSku() + ":" + el.getSku(), el.getActualTotalPrice()));
                    Map<String, BigDecimal> downMap = MoneyUtil.moneyDownUtil(itemMap, couponVO.getMoney());
                    if (CollectionUtil.isEmpty(downMap)) {
                        continue;
                    }
                    for (OrderItemCalcDTO dto : calcDTOList) {
                        //过滤换购商品
                        if (Objects.equals(dto.getProductType(), ProductTypeEnum.EXCHANGE.getCode())) {
                            continue;
                        }
                        String key = dto.getSuitId() + ":" + dto.getParentSku() + ":" + dto.getSku();
                        if (downMap.get(key) != null) {
                            dto.decreaseActualTotalPrice(downMap.get(key));
                            dto.decreaseCalcPartDeliveryFee(downMap.get(key));

                            //添加订单项优惠明细
                            dto.addItemPreferential(() -> {
                                OrderItemPreferential orderPreferential = new OrderItemPreferential(OrderPreferentialTypeEnum.RED_PACKET, downMap.get(key));
                                orderPreferential.setRelatedId(couponVO.getId().longValue());
                                orderPreferential.setActivityName(couponVO.getName());
                                orderPreferential.setCreateTime(LocalDateTime.now());
                                return orderPreferential;
                            });

                            if (placeOrderVO.getTakePriceFlag()){
                                //到手价明细返回
                                dto.addSkuPreferential(() -> {
                                    SkuPreferentialVO skuPreferentialVO = new SkuPreferentialVO(OrderPreferentialTypeEnum.RED_PACKET, downMap.get(key));
                                    skuPreferentialVO.setValue(downMap.get(key));
                                    return skuPreferentialVO;
                                });
                            }
                        }
                    }
                    OrderPreferential orderPreferential = new OrderPreferential(
                            OrderPreferentialTypeEnum.RED_PACKET, couponVO.getMoney());
                    orderPreferential.setRelatedId(couponVO.getId() != null ? couponVO.getId().longValue() : couponVO.getCouponId());
                    orderPreferential.setActivityName(couponVO.getName());
                    resultVO.addOrderPreferential(orderPreferential);
                }
            }

            return true;
        }

        return false;
    }
}
