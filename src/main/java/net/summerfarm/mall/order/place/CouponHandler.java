package net.summerfarm.mall.order.place;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

import java.util.*;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.CouponTypeEnum;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.common.config.DynamicConfig;
import net.summerfarm.mall.common.util.MoneyUtil;
import net.summerfarm.mall.common.util.RedisCacheUtil;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.mapper.CouponBlackAndWhiteMapper;
import net.summerfarm.mall.mapper.MerchantCouponMapper;
import net.summerfarm.mall.model.bo.coupon.ReceiveIdCountBO;
import net.summerfarm.mall.model.domain.Coupon;
import net.summerfarm.mall.model.domain.CouponBlackAndWhite;
import net.summerfarm.mall.model.domain.MerchantCoupon;
import net.summerfarm.mall.model.domain.OrderItemPreferential;
import net.summerfarm.mall.model.domain.OrderPreferential;
import net.summerfarm.mall.model.dto.market.coupon.SkuCouponDTO;
import net.summerfarm.mall.model.dto.market.coupon.SkuCouponReqDTO;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.dto.order.PlaceOrderCalcDTO;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import net.summerfarm.mall.model.vo.neworder.FloorPriceFailVO;
import net.summerfarm.mall.model.vo.neworder.SubOrderFailVO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.model.vo.price.SkuPreferentialVO;
import net.summerfarm.mall.order.AbstractPlaceOrderHandler;
import net.summerfarm.mall.order.converter.MerchantCouponConverter;
import net.summerfarm.mall.service.ConfigService;
import net.summerfarm.mall.service.CouponSenderService;
import net.summerfarm.mall.service.CouponService;
import net.summerfarm.mall.service.MerchantCouponService;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.DoubleStream;

/**
 * <AUTHOR>
 * @Description 优惠券计算逻辑
 */
@Service
@Slf4j
public class CouponHandler extends AbstractPlaceOrderHandler {
    @Resource
    private MerchantCouponMapper merchantCouponMapper;
    @Resource
    private MerchantCouponService merchantCouponService;
    @Resource
    private CouponSenderService couponSenderService;
    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Resource
    private CouponBlackAndWhiteMapper couponBlackAndWhiteMapper;
    @Resource
    private ConfigService configService;
    @Resource
    private CouponService couponService;
    @Resource
    private DynamicConfig dynamicConfig;

    /** 释放卡劵-可以给安佳类产品使用 */
    public static final String releaseCard = "RELEASE_CARD";

    /** 屏蔽sku */
    public static final String aolLimitPriceSku = "AOL_LIMIT_PRICE_SKU";
    @Override
    public boolean handlePlaceOrder(PlaceOrderCalcDTO orderCalcDTO, OrderResultVO resultVO) {
        PlaceOrderVO placeOrderVO = orderCalcDTO.getPlaceOrderVO ();
        //下单 与 到手价进行区分， 为了不影响原逻辑，copy了一份相同的方法。
            if (Integer.valueOf (PlaceOrderPriceEnum.ISTAKEPRICE.getCode ()).equals (placeOrderVO.getIsTakePrice ())) {
            log.info ("handlePlaceOrder - 预下单，到手价，走缓存查黑白名单");
            return handlePlaceOrderNew (orderCalcDTO,resultVO);
        }else{
            log.info ("handlePlaceOrder - 下单，到手价，走mysql查黑白名单");
            return handlePlaceOrderOld (orderCalcDTO,resultVO);
        }
    }
    private boolean handlePlaceOrderNew(PlaceOrderCalcDTO orderCalcDTO, OrderResultVO resultVO) {
        PlaceOrderVO placeOrderVO = orderCalcDTO.getPlaceOrderVO ();
        List<OrderItemCalcDTO> calcDTOList = orderCalcDTO.getItemCalcDTOList ();

        //计算订单总金额，简单过滤可用优惠券
        BigDecimal total = BigDecimal.ZERO;

        //是否存在低价品
        boolean isFloorPriceSku = false;
        Set<String> floorPriceSkus = new HashSet<>();
        Map<String, BigDecimal> itemActualTotalPriceMap = new HashMap<>(calcDTOList.size());
        for (OrderItemCalcDTO dto : calcDTOList) {
            //计算未使用优惠券的实付单价
            BigDecimal unCouponSinglePrice = dto.getActualTotalPrice ().divide (BigDecimal.valueOf (dto.getAmount ()), 2, RoundingMode.FLOOR);
            dto.setUnCouponSinglePrice (unCouponSinglePrice);
            itemActualTotalPriceMap.put(dto.getSku(), dto.getActualTotalPrice());

            //过滤换购商品
            if (Objects.equals (dto.getProductType (), ProductTypeEnum.EXCHANGE.getCode ())) {
                log.info("换购商品金额不计算优惠券");
                continue;
            }
            //代仓商品金额不计算红包
            if (Objects.equals (dto.getSkuType (), 1)) {
                log.info("代仓商品金额不计算优惠券");
                continue;
            }

            //是否存在低价品
            if (dto.getFloorPrice() != null && dto.getFloorPrice().compareTo(BigDecimal.ZERO) > 0) {
                isFloorPriceSku = true;
                floorPriceSkus.add(dto.getSku());
            }
            total = total.add (dto.getActualTotalPrice ());
        }

        List<MerchantCouponVO> couponVOList = new ArrayList<> (16);
        List<MerchantCouponVO> voucherCoupons;

        //到手价优惠券缓存处理，支撑首页大量查询接口
        Set<Integer> hideCouponGrouping = null;
        List<String> releaseCardList;
        if (placeOrderVO.getTakePriceFlag ()) {
            //couponVOList = selectUsableCouponWithCache (CouponTypeEnum.NORMAL.getType (), placeOrderVO.getMId (), total);
            //voucherCoupons = selectUsableCouponWithCache (CouponEnum.CouponTypeEnum.VOUCHER.getCode (), placeOrderVO.getMId (), total);

            //获取当前用户所有卡包优惠券--本地缓存
            BigDecimal finalTotal = total;
            List<MerchantCouponVO> usableCouponByCache = merchantCouponService.getUsableCouponByCache(placeOrderVO.getMId());
            couponVOList = usableCouponByCache.stream().filter(merchantCouponVO -> merchantCouponVO.getAgioType().equals(CouponTypeEnum.NORMAL.getType()) &&
                            merchantCouponVO.getMoney().compareTo(finalTotal) <= 0 && merchantCouponVO.getThreshold().compareTo(finalTotal) <= 0 )
                    .sorted(Comparator.comparing(MerchantCouponVO::getMoney).reversed()).collect(Collectors.toList());

            voucherCoupons = usableCouponByCache.stream().filter(merchantCouponVO -> merchantCouponVO.getAgioType().equals(CouponEnum.CouponTypeEnum.VOUCHER.getCode()) &&
                            merchantCouponVO.getMoney().compareTo(finalTotal) <= 0 && merchantCouponVO.getThreshold().compareTo(finalTotal) <= 0 )
                    .sorted(Comparator.comparing(MerchantCouponVO::getMoney).reversed()).collect(Collectors.toList());

            //到手价查询需要剔除的优惠券类型
            List<String> values = configService.getValues(Global.HIDE_COUPON_GROUP);
            hideCouponGrouping = values.stream().map(Integer::valueOf).collect(Collectors.toSet());

            //优惠券释放：售后补偿券、销售囤货券、销售现货券、销售品类券这四类分组的优惠券支持安佳可用
           releaseCardList = configService.getValuesByLocalCache (releaseCard);
        } else {
            couponVOList = merchantCouponMapper.selectUsableCoupon (CouponTypeEnum.NORMAL.getType (), placeOrderVO.getMId (), total);
            voucherCoupons = merchantCouponMapper.selectUsableCoupon (CouponEnum.CouponTypeEnum.VOUCHER.getCode (), placeOrderVO.getMId (), total);

            //优惠券释放：售后补偿券、销售囤货券、销售现货券、销售品类券这四类分组的优惠券支持安佳可用
            releaseCardList = configService.getValues (releaseCard);
        }

        //处理商品兑换券
        if (!CollectionUtil.isEmpty (voucherCoupons)) {
            if (!CollectionUtil.isEmpty (couponVOList)) {
                couponVOList.addAll (voucherCoupons);
            } else {
                couponVOList = voucherCoupons;
            }
        }

        if (placeOrderVO.getIsTakePrice ().equals (PlaceOrderPriceEnum.ISTAKEPRICE.getCode ())) {
            couponVOList = this.getMerchantCoupon (calcDTOList, couponVOList, orderCalcDTO, total, placeOrderVO);
        } else {
            //将红包全部置为已经领取，后面校验下单金额会用到
            couponVOList.stream ().forEach (e -> {
                e.setEffectiveNum (CouponEffectiveNumEnum.RECEIVE.getCode ());
            });
        }

        if (CollectionUtil.isEmpty (couponVOList)) {
            return false;
        }
        //查询sku屏蔽名单
        List<String> blackSkus = configService.getValuesWithCache (aolLimitPriceSku, 30 * 60L);

        List<Integer> releaseCards = releaseCardList.stream ().map (Integer::valueOf).collect (Collectors.toList ());

        //处理券使用范围，按照金额排序处理
        Map<String, List<OrderItemCalcDTO>> couponMap = new HashMap<> ();
        for (MerchantCouponVO couponVO : couponVOList) {
            Integer couponId = couponVO.getCouponId ();
            Map<Integer, Set<String>> couponBlackAndWhiteMap = couponService.getCouponBlackAndWhiteMap (couponId);
            Set<String> black = couponBlackAndWhiteMap.getOrDefault (BlackAndWhiteTypeEnum.BLACK.getCode (), Collections.emptySet ());
            Set<String> white = couponBlackAndWhiteMap.getOrDefault (BlackAndWhiteTypeEnum.WHITE.getCode (), Collections.emptySet ());
            couponVO.setSkuScope (couponService.getSkuScope (couponVO.getSku (), couponVO.getCategoryId (), black, white));

            List<OrderItemCalcDTO> dtos = merchantCouponService.usableOrderItem (calcDTOList, couponVO, OrderTypeEnum.NORMAL,
                    black, white, blackSkus, releaseCards);
            couponMap.put (couponVO.getCouponId () + ":" + couponVO.getId (), dtos);
        }
        couponVOList = couponVOList.stream ().sorted (Comparator.comparing (MerchantCouponVO::getMoney).reversed ()).collect (Collectors.toList ());
        ;

        boolean usedCoupon = false;

        //如果优惠券的金额相等，则按照优惠券的id进行排序，且领券中心的未领取的优惠券（id为null）排在后面
        List<MerchantCouponVO> usableNormalCoupon = couponVOList.stream ()
                .filter (x -> CollectionUtil.isNotEmpty (
                        couponMap.get (x.getCouponId () + ":" + x.getId ())))
                .sorted (Comparator.comparing (MerchantCouponVO::getMoney).reversed ()
                        .thenComparing (MerchantCoupon::getId, Comparator.nullsLast (Integer::compareTo)))
                .collect (Collectors.toList ());

        MerchantCouponVO usedCouponVO = new MerchantCouponVO ();
        log.info("优惠券责任链--到手价或预下单获取到用户可以优惠券:{}", JSON.toJSONString(usableNormalCoupon));

        Set<Integer> filterCouponIds = new HashSet<> ();
        Integer maxLimitFrequency = null;
        if (isFloorPriceSku) {
            maxLimitFrequency = dynamicConfig.getCouponMaxLimitFrequency();
        }
        for (int i = 0; i < usableNormalCoupon.size(); i++) {
            MerchantCouponVO couponVO = usableNormalCoupon.get(i);
            List<OrderItemCalcDTO> dtoList = couponMap.get (couponVO.getCouponId() + ":" + couponVO.getId ());
            if (CollectionUtil.isEmpty (dtoList)) {
                continue;
            }

            //预估到手价屏蔽售后补偿券 需求名称：商城-预估到手价剔除售后补偿券 by[薄荷]
            if (placeOrderVO.getTakePriceFlag () && !CollectionUtils.isEmpty(hideCouponGrouping)
                    && hideCouponGrouping.contains(couponVO.getGrouping())) {
                log.info("优惠券：" + couponVO.getCouponId () + " ，普通商品预估到手价剔除售后补偿券");
                continue;
            }

            if (isFloorPriceSku) {
                if (i >= maxLimitFrequency) {
                    log.info("当前优惠券已经超过最大可执行次数，maxLimitFrequency:{}", maxLimitFrequency);
                    break;
                }

                Map<String, BigDecimal> itemMap = new HashMap<>(8);

                //优惠券释放：售后补偿券、销售囤货券、销售现货券、销售品类券这四类分组的优惠券支持安佳可用
                if (CollectionUtils.isEmpty(releaseCards) || !releaseCards.contains(couponVO.getGrouping())) {

                    //过滤安佳类产品不可用
                    dtoList = dtoList.stream().filter(el -> CollectionUtils.isEmpty(blackSkus) || !blackSkus.contains(el.getSku()))
                            .collect(Collectors.toList());
                }

                dtoList.stream()
                        .filter(el -> !Objects.equals(el.getSkuType(), 1))
                        .forEach(el -> itemMap.put(el.getSuitId() + ":" + el.getParentSku() + ":" + el.getSku(), itemActualTotalPriceMap.get(el.getSku())));
                Map<String, BigDecimal> downMap = MoneyUtil.moneyDownUtil(itemMap, couponVO.getMoney());
                if (CollectionUtil.isEmpty(downMap)) {
                    log.error("优惠券优惠金额计算失败！couponId:{}", couponVO.getCouponId());
                    continue;
                }

                //获取低价品信息
                List<OrderItemCalcDTO> floorPriceList = dtoList.stream().filter(e -> e.getFloorPrice() != null &&
                        e.getFloorPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());

                //校验当前均摊后的价格是否低于低价 售后补偿券不参与低价校验
                Set<String> floorPriceKeys = new HashSet<>();
                if (!CollectionUtils.isEmpty(floorPriceList) && !couponVO.getGrouping().equals(CouponEnum.Group.AFTER_SALE.getCode())) {
                    Map<String, BigDecimal> finalDownMap = downMap;
                    floorPriceList.forEach(el -> {
                        String key = el.getSuitId() + ":" + el.getParentSku() + ":" + el.getSku();
                        if (!finalDownMap.containsKey(key)) {
                            return;
                        }
                        BigDecimal totalDiscount = finalDownMap.get(key);
                        if (totalDiscount == null || totalDiscount.compareTo(BigDecimal.ZERO) <= 0) {
                            return;
                        }
                        BigDecimal totalPrice = itemActualTotalPriceMap.get(el.getSku());
                        BigDecimal individualDiscount = totalPrice.subtract(totalDiscount).divide(new BigDecimal(el.getAmount()), 2, RoundingMode.FLOOR);
                        if (individualDiscount.compareTo(el.getFloorPrice()) < 0) {
                            log.info("优惠券:{} 均摊后价格低于低价，将剔除当前sku:{}, individualDiscount:{}, floorPrice:{}", couponVO.getCouponId(), el.getSku(), individualDiscount, el.getFloorPrice());
                            floorPriceKeys.add(el.getSku());
                        }
                    });
                }

                if (!CollectionUtils.isEmpty(floorPriceKeys)) {
                    Map<String, BigDecimal> itemDtoMap = new HashMap<>(dtoList.size());

                    //假如存在破底价品则 需要把所有核心品去掉
                    dtoList.stream().filter(el -> !Objects.equals(el.getSkuType(), 1) && !floorPriceSkus.contains(el.getSku()))
                            .forEach(el -> itemDtoMap.put(el.getSuitId() + ":" + el.getParentSku() + ":" + el.getSku(), itemActualTotalPriceMap.get(el.getSku())));
                    BigDecimal totalSum = itemDtoMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                    downMap = MoneyUtil.moneyDownUtil(itemDtoMap, couponVO.getMoney());
                    if (CollectionUtil.isEmpty(downMap) || totalSum.compareTo(couponVO.getThreshold()) < 0) {
                        log.info("过滤后低价品，重新计算商品均摊失败，优惠券不生效！couponId:{}, floorPriceKeys:{}, totalSum:{}", couponVO.getCouponId(), JSON.toJSONString(floorPriceKeys), totalSum);
                        filterCouponIds.add(couponVO.getCouponId());
                        continue;
                    }
                }
                if (usedCoupon) {
                    log.info("已经有最优的优惠券, couponId:{}", couponVO.getCouponId());
                    continue;
                }

                usedCoupon = true;
                if (couponVO.getEffectiveNum().equals(CouponEffectiveNumEnum.RECEIVE.getCode())) {
                    resultVO.addUsedMerchantCouponId(couponVO.getId());
                } else {
                    resultVO.addUsedMerchantCouponId(couponVO.getCouponId());
                }
                usedCouponVO = couponVO;
                orderCalcDTO.getPlaceOrderVO().setEffectiveNum(couponVO.getEffectiveNum());
                orderCalcDTO.getPlaceOrderVO().setCouponId(couponVO.getCouponId());
                if (Objects.nonNull(couponVO.getCouponSenderId())) {
                    orderCalcDTO.getPlaceOrderVO().setCouponSenderId(couponVO.getCouponSenderId());
                }
                for (OrderItemCalcDTO dto : calcDTOList) {
                    //过滤换购商品
                    if (Objects.equals(dto.getProductType(), ProductTypeEnum.EXCHANGE.getCode())) {
                        continue;
                    }
                    String key = dto.getSuitId() + ":" + dto.getParentSku() + ":" + dto.getSku();
                    if (downMap.get(key) != null) {
                        BigDecimal discount = downMap.get(key);
                        dto.decreaseActualTotalPrice(discount);
                        //售后补偿券不参与运费计算
                        if (!couponVO.getGrouping().equals(CouponEnum.Group.AFTER_SALE.getCode())) {
                            dto.decreaseCalcPartDeliveryFee(discount);
                        } else {
                            log.info("售后补偿券不参与运费实付价计算！");
                        }
                        //添加订单项优惠明细
                        dto.addItemPreferential(() -> {
                            OrderItemPreferential orderPreferential = new OrderItemPreferential(OrderPreferentialTypeEnum.COUPON, discount);
                            orderPreferential.setRelatedId(couponVO.getCouponId().longValue());
                            orderPreferential.setCreateTime(LocalDateTime.now());
                            return orderPreferential;
                        });
                        if (placeOrderVO.getTakePriceFlag()) {
                            //到手价明细返回
                            if (Objects.equals(CouponEnum.CouponTypeEnum.VOUCHER.getCode(), couponVO.getAgioType())) {
                                //兑换券特殊处理，money为抵扣金额字段
                                dto.addSkuPreferential(() -> {
                                    SkuPreferentialVO skuPreferentialVO = new SkuPreferentialVO(18, discount);
                                    skuPreferentialVO.setRuleLevel(couponVO.getThreshold());
                                    skuPreferentialVO.setValue(couponVO.getActualPayMoney());
                                    return skuPreferentialVO;
                                });
                            } else {
                                dto.addSkuPreferential(() -> {
                                    SkuPreferentialVO skuPreferentialVO = new SkuPreferentialVO(OrderPreferentialTypeEnum.COUPON, discount);
                                    skuPreferentialVO.setRuleLevel(couponVO.getThreshold());
                                    return skuPreferentialVO;
                                });
                            }
                        }
                    }
                }
                if (placeOrderVO.getTakePriceFlag() && usedCoupon) {
                    log.info("到手价不进行核心品优惠券信息过滤！");
                    break;
                }
            } else {
                Map<String, BigDecimal> itemMap = new HashMap<>(8);

                //优惠券释放：售后补偿券、销售囤货券、销售现货券、销售品类券这四类分组的优惠券支持安佳可用
                if (CollectionUtils.isEmpty(releaseCards) || !releaseCards.contains(couponVO.getGrouping())) {

                    //过滤安佳类产品不可用
                    dtoList = dtoList.stream().filter(el -> CollectionUtils.isEmpty(blackSkus) || !blackSkus.contains(el.getSku()))
                            .collect(Collectors.toList());
                }

                dtoList.stream().filter(el -> !Objects.equals(el.getSkuType(), 1))
                        .forEach(el -> itemMap.put(el.getSuitId() + ":" + el.getParentSku() + ":" + el.getSku(), el.getActualTotalPrice()));
                Map<String, BigDecimal> downMap = MoneyUtil.moneyDownUtil(itemMap, couponVO.getMoney());
                if (CollectionUtil.isEmpty(downMap)) {
                    log.error("优惠券优惠金额计算失败！couponId:{}", couponVO.getCouponId());
                    continue;
                }

                if (couponVO.getEffectiveNum().equals(CouponEffectiveNumEnum.RECEIVE.getCode())) {
                    resultVO.addUsedMerchantCouponId(couponVO.getId());
                } else {
                    resultVO.addUsedMerchantCouponId(couponVO.getCouponId());
                }
                usedCouponVO = couponVO;
                orderCalcDTO.getPlaceOrderVO().setEffectiveNum(couponVO.getEffectiveNum());
                orderCalcDTO.getPlaceOrderVO().setCouponId(couponVO.getCouponId());
                if (Objects.nonNull(couponVO.getCouponSenderId())) {
                    orderCalcDTO.getPlaceOrderVO().setCouponSenderId(couponVO.getCouponSenderId());
                }
                usedCoupon = true;
                for (OrderItemCalcDTO dto : calcDTOList) {
                    //过滤换购商品
                    if (Objects.equals(dto.getProductType(), ProductTypeEnum.EXCHANGE.getCode())) {
                        continue;
                    }
                    String key = dto.getSuitId() + ":" + dto.getParentSku() + ":" + dto.getSku();
                    if (downMap.get(key) != null) {
                        BigDecimal discount = downMap.get(key);
                        dto.decreaseActualTotalPrice(discount);
                        //售后补偿券不参与运费计算
                        if (!couponVO.getGrouping().equals(CouponEnum.Group.AFTER_SALE.getCode())) {
                            dto.decreaseCalcPartDeliveryFee(discount);
                        } else {
                            log.info("售后补偿券不参与运费实付价计算！");
                        }
                        //添加订单项优惠明细
                        dto.addItemPreferential(() -> {
                            OrderItemPreferential orderPreferential = new OrderItemPreferential(OrderPreferentialTypeEnum.COUPON, discount);
                            orderPreferential.setRelatedId(couponVO.getCouponId().longValue());
                            orderPreferential.setCreateTime(LocalDateTime.now());
                            return orderPreferential;
                        });
                        if (placeOrderVO.getTakePriceFlag()) {
                            //到手价明细返回
                            if (Objects.equals(CouponEnum.CouponTypeEnum.VOUCHER.getCode(), couponVO.getAgioType())) {
                                //兑换券特殊处理，money为抵扣金额字段
                                dto.addSkuPreferential(() -> {
                                    SkuPreferentialVO skuPreferentialVO = new SkuPreferentialVO(18, discount);
                                    skuPreferentialVO.setRuleLevel(couponVO.getThreshold());
                                    skuPreferentialVO.setValue(couponVO.getActualPayMoney());
                                    return skuPreferentialVO;
                                });
                            } else {
                                dto.addSkuPreferential(() -> {
                                    SkuPreferentialVO skuPreferentialVO = new SkuPreferentialVO(OrderPreferentialTypeEnum.COUPON, discount);
                                    skuPreferentialVO.setRuleLevel(couponVO.getThreshold());
                                    return skuPreferentialVO;
                                });
                            }
                        }
                    }
                }
                if (placeOrderVO.getIsTakePrice().equals(PlaceOrderPriceEnum.ISTAKEPRICE.getCode()) && usedCoupon) {
                    break;
                }
            }
        }
        //预下单、下单需要过滤掉领券中心的券（如果最大额的是未领取的券需要保留，否则都不取领券中心的券）
        if ((placeOrderVO.getIsTakePrice ().equals (PlaceOrderPriceEnum.NOTAKEPRICE.getCode ()) || !placeOrderVO.getTakePriceFlag ()) && CollectionUtil.isNotEmpty (usableNormalCoupon)) {
            //这张券可能是领券中心的券，也可能是个人中心的券
            MerchantCouponVO maxAmountCoupon = usableNormalCoupon.get (0);
            //最多只保留领券中心一张待领取的券(只有最大额是未领取的券)
            usableNormalCoupon = usableNormalCoupon.stream ()
                    .filter (x -> x.getId () != null || Objects.equals (maxAmountCoupon.getCouponSenderId (), x.getCouponSenderId ()))
                    .collect (Collectors.toList ());
        }

        //过滤掉不可用的优惠券
        if (!CollectionUtils.isEmpty(filterCouponIds)) {
            usableNormalCoupon = usableNormalCoupon.stream().filter(x -> !filterCouponIds.contains(x.getCouponId())).collect(Collectors.toList());
        }
        resultVO.addUsableNormalCoupon (usableNormalCoupon);
        if (usedCoupon) {
            log.info("用户可用优惠券信息-usedCouponVO:{}", JSON.toJSONString(usedCouponVO));
            OrderPreferential orderPreferential = new OrderPreferential (
                    OrderPreferentialTypeEnum.COUPON, usedCouponVO.getMoney ());
            orderPreferential.setActivityName (usedCouponVO.getName ());
            orderPreferential.setRelatedId (usedCouponVO.getId () != null ? usedCouponVO.getId ().longValue () : usedCouponVO.getCouponId ());
            resultVO.addOrderPreferential (orderPreferential);
        }
        return usedCoupon;
    }
    public boolean handlePlaceOrderOld(PlaceOrderCalcDTO orderCalcDTO, OrderResultVO resultVO) {
        PlaceOrderVO placeOrderVO = orderCalcDTO.getPlaceOrderVO();
        List<OrderItemCalcDTO> calcDTOList = orderCalcDTO.getItemCalcDTOList();

        //计算订单总金额，简单过滤可用优惠券
        BigDecimal total = BigDecimal.ZERO;
        for (OrderItemCalcDTO dto : calcDTOList) {
            //计算未使用优惠券的实付单价
            BigDecimal unCouponSinglePrice = dto.getActualTotalPrice().divide(BigDecimal.valueOf(dto.getAmount()), 2, RoundingMode.FLOOR);
            dto.setUnCouponSinglePrice(unCouponSinglePrice);

            //过滤换购商品
            if (Objects.equals(dto.getProductType(), ProductTypeEnum.EXCHANGE.getCode())) {
                log.info("换购商品金额不计算优惠券");
                continue;
            }
            //代仓商品金额不计算红包
            if (Objects.equals(dto.getSkuType(), 1)){
                log.info("代仓商品金额不计算优惠券");
                continue;
            }
            total = total.add(dto.getActualTotalPrice());
        }

        List<MerchantCouponVO> couponVOList = new ArrayList<>(16);
        List<MerchantCouponVO> voucherCoupons;

        //到手价优惠券缓存处理，支撑首页大量查询接口
        if (placeOrderVO.getTakePriceFlag()) {
            /*couponVOList = selectUsableCouponWithCache(CouponTypeEnum.NORMAL.getType(), placeOrderVO.getMId(), total);
            voucherCoupons = selectUsableCouponWithCache(CouponEnum.CouponTypeEnum.VOUCHER.getCode(), placeOrderVO.getMId(), total);*/
            //获取当前用户所有卡包优惠券--本地缓存
            BigDecimal finalTotal = total;
            List<MerchantCouponVO> usableCouponByCache = merchantCouponService.getUsableCouponByCache(placeOrderVO.getMId());
            couponVOList = usableCouponByCache.stream().filter(merchantCouponVO -> merchantCouponVO.getAgioType().equals(CouponTypeEnum.NORMAL.getType()) &&
                            merchantCouponVO.getMoney().compareTo(finalTotal) <= 0 && merchantCouponVO.getThreshold().compareTo(finalTotal) <= 0 )
                    .sorted(Comparator.comparing(MerchantCouponVO::getMoney).reversed()).collect(Collectors.toList());

            voucherCoupons = usableCouponByCache.stream().filter(merchantCouponVO -> merchantCouponVO.getAgioType().equals(CouponEnum.CouponTypeEnum.VOUCHER.getCode()) &&
                            merchantCouponVO.getMoney().compareTo(finalTotal) <= 0 && merchantCouponVO.getThreshold().compareTo(finalTotal) <= 0 )
                    .sorted(Comparator.comparing(MerchantCouponVO::getMoney).reversed()).collect(Collectors.toList());
        } else {
            couponVOList = merchantCouponMapper.selectUsableCoupon(CouponTypeEnum.NORMAL.getType(), placeOrderVO.getMId(), total);
            voucherCoupons = merchantCouponMapper.selectUsableCoupon(CouponEnum.CouponTypeEnum.VOUCHER.getCode(), placeOrderVO.getMId(), total);
        }

        //处理商品兑换券
        if (!CollectionUtil.isEmpty(voucherCoupons)) {
            if (!CollectionUtil.isEmpty(couponVOList)) {
                couponVOList.addAll(voucherCoupons);
            } else {
                couponVOList = voucherCoupons;
            }
        }

        if (placeOrderVO.getIsTakePrice().equals(PlaceOrderPriceEnum.ISTAKEPRICE.getCode())) {
            couponVOList = this.getMerchantCoupon(calcDTOList, couponVOList, orderCalcDTO, total, placeOrderVO);
        } else {
            //将红包全部置为已经领取，后面校验下单金额会用到
            couponVOList.stream().forEach(e -> {
                e.setEffectiveNum(CouponEffectiveNumEnum.RECEIVE.getCode());
            });
        }

        if (CollectionUtil.isEmpty(couponVOList)) {
            return false;
        }

        //设置商品的使用范围
        //查询黑白名单数据
        List<Long> couponIdList = couponVOList.stream().map(e -> e.getCouponId().longValue()).collect(Collectors.toList());
        CouponBlackAndWhite couponBlackAndWhite = new CouponBlackAndWhite();
        //couponBlackAndWhite.setType(BlackAndWhiteTypeEnum.BLACK.getCode());
        couponBlackAndWhite.setCouponIds(couponIdList);
        List<CouponBlackAndWhite> blackAndWhiteList = couponBlackAndWhiteMapper.getAllByEntity(couponBlackAndWhite);
        Map<Long, List<CouponBlackAndWhite>> blackMap = null;
        Map<Long, List<CouponBlackAndWhite>> whiteMap = null;
        if (!CollectionUtils.isEmpty(blackAndWhiteList)) {
            Map<Integer, List<CouponBlackAndWhite>> collectCouponBlackAndWhite = blackAndWhiteList.stream().collect(Collectors.groupingBy(CouponBlackAndWhite::getType));
            if (!CollectionUtils.isEmpty(collectCouponBlackAndWhite.get(BlackAndWhiteTypeEnum.BLACK.getCode()))) {
                blackMap = collectCouponBlackAndWhite.get(BlackAndWhiteTypeEnum.BLACK.getCode()).stream().collect(Collectors.groupingBy(CouponBlackAndWhite::getCouponId));
            }
            if (!CollectionUtils.isEmpty(collectCouponBlackAndWhite.get(BlackAndWhiteTypeEnum.WHITE.getCode()))) {
                whiteMap = collectCouponBlackAndWhite.get(BlackAndWhiteTypeEnum.WHITE.getCode()).stream().collect(Collectors.groupingBy(CouponBlackAndWhite::getCouponId));
            }
        }

        Map<Long, List<CouponBlackAndWhite>> finalBlackAndWhiteListMap = blackMap;
        Map<Long, List<CouponBlackAndWhite>> finalWhiteMap = whiteMap;
        couponVOList.forEach(merchantCouponVO -> {
            //商品范围：根据后台选择的商品范围
            if ((StringUtils.isNotBlank(merchantCouponVO.getSku()) && !CollectionUtils.isEmpty(JSON.parseObject(merchantCouponVO.getSku(),
                    new TypeReference<Map<String, String>>() {}))) || (!CollectionUtils.isEmpty(finalWhiteMap) &&
                    !CollectionUtils.isEmpty(finalWhiteMap.get(merchantCouponVO.getCouponId().longValue())))) {
                //指定商品
                merchantCouponVO.setSkuScope(CouponSkuScopeEnum.PARTIAL_SKU.getCode());
            } else if (StringUtils.isNotBlank(merchantCouponVO.getCategoryId()) && !CollectionUtils.isEmpty(JSON.parseObject(merchantCouponVO.getCategoryId(), new TypeReference<Map<String, String>>() {}))) {
                //指定类目 区分有无黑名单
                merchantCouponVO.setSkuScope(CouponSkuScopeEnum.PARTIAL_CATEGORY.getCode());
                if (!CollectionUtil.isEmpty(finalBlackAndWhiteListMap) && !CollectionUtils.isEmpty(finalBlackAndWhiteListMap.get(merchantCouponVO.getCouponId().longValue()))) {
                    merchantCouponVO.setSkuScope(CouponSkuScopeEnum.PARTIAL_CATEGORY_EXCEPTIONALITY.getCode());
                }
            } else {
                //全部商品 区分有无黑名单
                merchantCouponVO.setSkuScope(CouponSkuScopeEnum.ALL.getCode());
                if (!CollectionUtil.isEmpty(finalBlackAndWhiteListMap) && !CollectionUtils.isEmpty(finalBlackAndWhiteListMap.get(merchantCouponVO.getCouponId().longValue()))) {
                    merchantCouponVO.setSkuScope(CouponSkuScopeEnum.ALL_EXCEPTIONALITY.getCode());
                }
            }
        });

        if (CollectionUtil.isEmpty(couponVOList)) {
            return false;
        }

        //查询sku屏蔽名单
        List<String> blackSkus = configService.getValuesWithCache(aolLimitPriceSku, 30 * 60L);

        //优惠券释放：售后补偿券、销售囤货券、销售现货券、销售品类券这四类分组的优惠券支持安佳可用
        List<String> releaseCardList = configService.getValues(releaseCard);
        List<Integer> releaseCards = releaseCardList.stream().map(Integer::valueOf).collect(Collectors.toList());

        //处理券使用范围，按照金额排序处理
        Map<String, List<OrderItemCalcDTO>> couponMap = new HashMap<>();
        for (MerchantCouponVO couponVO : couponVOList) {
            Set<String> blackSkuList = null;
            Set<String> whiteSkuList = null;
            if (!CollectionUtil.isEmpty(blackMap) && !CollectionUtils.isEmpty(blackMap.get(couponVO.getCouponId().longValue()))) {
                List<CouponBlackAndWhite> couponBlackAndWhites = blackMap.get(couponVO.getCouponId().longValue());
                blackSkuList = couponBlackAndWhites.stream().map(CouponBlackAndWhite::getSku).collect(Collectors.toSet ());
            }
            if (!CollectionUtils.isEmpty(whiteMap) && !CollectionUtils.isEmpty(whiteMap.get(couponVO.getCouponId().longValue()))) {
                List<CouponBlackAndWhite> couponBlackAndWhites = whiteMap.get(couponVO.getCouponId().longValue());
                whiteSkuList = couponBlackAndWhites.stream().map(CouponBlackAndWhite::getSku).collect(Collectors.toSet());
            }
            List<OrderItemCalcDTO> dtos = merchantCouponService.usableOrderItem(calcDTOList, couponVO, OrderTypeEnum.NORMAL,
                    blackSkuList, whiteSkuList, blackSkus, releaseCards);
            couponMap.put(couponVO.getCouponId()+":"+couponVO.getId(), dtos);
        }
        couponVOList = couponVOList.stream().sorted(Comparator.comparing(MerchantCouponVO::getMoney).reversed()).collect(Collectors.toList());;

        boolean usedCoupon = false;

        //如果优惠券的金额相等，则按照优惠券的id进行排序，且领券中心的未领取的优惠券（id为null）排在后面
        List<MerchantCouponVO> usableNormalCoupon = couponVOList.stream()
                .filter(x -> CollectionUtil.isNotEmpty(
                        couponMap.get(x.getCouponId() + ":" + x.getId())))
                .sorted(Comparator.comparing(MerchantCouponVO::getMoney).reversed()
                        .thenComparing(MerchantCoupon::getId, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());

        MerchantCouponVO usedCouponVO = new MerchantCouponVO();
        log.info("优惠券责任链--下单获取到用户可以优惠券:{}", JSON.toJSONString(usableNormalCoupon));
        for (MerchantCouponVO couponVO : usableNormalCoupon) {
            List<OrderItemCalcDTO> dtoList = couponMap.get(couponVO.getCouponId() + ":" + couponVO.getId());
            if (CollectionUtil.isEmpty(dtoList)) {
                continue;
            }

            //选择的券处理金额
            if (CollectionUtil.isEmpty(placeOrderVO.getMerchantCouponId())) {
                continue;
            }
            if (placeOrderVO.getMerchantCouponId().contains(couponVO.getId())) {
                Map<String, BigDecimal> itemMap = new HashMap<>(8);

                //优惠券释放：售后补偿券、销售囤货券、销售现货券、销售品类券这四类分组的优惠券支持安佳可用
                if (CollectionUtils.isEmpty(releaseCards) || !releaseCards.contains(couponVO.getGrouping())) {

                    //过滤安佳类产品不可用
                    dtoList = dtoList.stream().filter(el -> CollectionUtils.isEmpty(blackSkus) || !blackSkus.contains(el.getSku()))
                            .collect(Collectors.toList());
                }

                dtoList.stream()
                        .filter(el -> !Objects.equals(el.getSkuType(), 1))
                        .forEach(el -> itemMap.put(el.getSuitId() + ":" + el.getParentSku() + ":" + el.getSku(), el.getActualTotalPrice()));

                Map<String, BigDecimal> downMap = MoneyUtil.moneyDownUtil(itemMap, couponVO.getMoney());
                if (CollectionUtil.isEmpty(downMap)) {
                    continue;
                }

                //售后补偿券不参与计算
                if (!couponVO.getGrouping().equals(CouponEnum.Group.AFTER_SALE.getCode())) {
                    //获取低价品信息
                    List<OrderItemCalcDTO> floorPriceList = dtoList.stream().filter(e -> e.getFloorPrice() != null &&
                            e.getFloorPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());

                    //校验当前均摊后的价格是否低于低价
                    Set<String> floorPriceKeys = new HashSet<>();
                    Set<String> floorPriceSkus = new HashSet<>();
                    for (OrderItemCalcDTO el : floorPriceList) {
                        floorPriceSkus.add(el.getSku());
                        String key = el.getSuitId() + ":" + el.getParentSku() + ":" + el.getSku();
                        if (!downMap.containsKey(key)) {
                            continue;
                        }
                        BigDecimal totalDiscount = downMap.get(key);
                        if (totalDiscount == null || totalDiscount.compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }
                        BigDecimal actualTotalPrice = el.getActualTotalPrice().subtract(totalDiscount);
                        BigDecimal individualDiscount = actualTotalPrice.divide(new BigDecimal(el.getAmount()), 2, RoundingMode.FLOOR);
                        if (individualDiscount.compareTo(el.getFloorPrice()) >= 0) {
                            continue;
                        }

                        log.info("优惠券均摊后价格低于低价，sku:{}, individualDiscount:{}, floorPrice:{}", el.getSku(), individualDiscount, el.getFloorPrice());
                        floorPriceKeys.add(el.getSku());
                    }

                    //过滤破价低价品后重新计算商品均摊 售后补偿券不参与计算
                    if (!CollectionUtils.isEmpty(floorPriceKeys)) {
                        Map<String, BigDecimal> itemDtoMap = new HashMap<>(dtoList.size());

                        //添加所有核心品
                        dtoList = dtoList.stream().filter(e -> !floorPriceSkus.contains(e.getSku())).collect(Collectors.toList());
                        dtoList.stream().filter(el -> !Objects.equals(el.getSkuType(), 1))
                                .forEach(el -> itemDtoMap.put(el.getSuitId() + ":" + el.getParentSku() + ":" + el.getSku(), el.getActualTotalPrice()));

                        BigDecimal totalSum = itemDtoMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                        downMap = MoneyUtil.moneyDownUtil(itemDtoMap, couponVO.getMoney());
                        if (CollectionUtil.isEmpty(downMap) || totalSum.compareTo(couponVO.getThreshold()) < 0) {
                            log.info("过滤后低价品，重新计算商品均摊失败，优惠券不生效！couponId:{}, floorPriceKeys:{}, , totalSum:{}", couponVO.getCouponId(), JSON.toJSONString(floorPriceKeys), totalSum);
                            floorPriceList.forEach(el ->
                                    resultVO.addFloorPriceFailVO(() -> {
                                        FloorPriceFailVO floorPriceFailVO = new FloorPriceFailVO();
                                        floorPriceFailVO.setFloorPrice(el.getFloorPrice());
                                        floorPriceFailVO.setSku(el.getSku());
                                        floorPriceFailVO.setWeight(el.getWeight());
                                        floorPriceFailVO.setPdName(el.getPdName());
                                        floorPriceFailVO.setPicturePath(el.getPicturePath());
                                        floorPriceFailVO.setCouponMoney(couponVO.getMoney());
                                        return floorPriceFailVO;
                                    })
                            );
                            break;
                        }
                    }
                }

                usedCoupon = true;
                resultVO.addUsedMerchantCouponId(couponVO.getId());
                usedCouponVO = couponVO;
                for (OrderItemCalcDTO dto : calcDTOList) {
                    //过滤换购商品
                    if (Objects.equals(dto.getProductType(), ProductTypeEnum.EXCHANGE.getCode())) {
                        continue;
                    }
                    String key = dto.getSuitId() + ":" + dto.getParentSku() + ":" + dto.getSku();
                    if (downMap.get(key) != null) {
                        dto.setUseCoupon(1);
                        BigDecimal discount = downMap.get(key);
                        dto.decreaseActualTotalPrice(discount);

                        //售后补偿券不参与运费计算
                        if (!couponVO.getGrouping().equals(CouponEnum.Group.AFTER_SALE.getCode())) {
                            dto.decreaseCalcPartDeliveryFee(discount);
                        } else {
                            log.info("售后补偿券不参与运费实付价计算！");
                        }
                        //添加订单项优惠明细
                        dto.addItemPreferential(() -> {
                            OrderItemPreferential orderPreferential = new OrderItemPreferential(OrderPreferentialTypeEnum.COUPON, discount);
                            orderPreferential.setRelatedId(couponVO.getId().longValue());
                            orderPreferential.setActivityName(couponVO.getName());
                            orderPreferential.setCreateTime(LocalDateTime.now());
                            return orderPreferential;
                        });
                    }
                }
            }
        }
        //预下单、下单需要过滤掉领券中心的券（如果最大额的是未领取的券需要保留，否则都不取领券中心的券）
        if ((placeOrderVO.getIsTakePrice().equals(PlaceOrderPriceEnum.NOTAKEPRICE.getCode())  || !placeOrderVO.getTakePriceFlag()) && CollectionUtil.isNotEmpty(usableNormalCoupon)) {
            //这张券可能是领券中心的券，也可能是个人中心的券
            MerchantCouponVO maxAmountCoupon = usableNormalCoupon.get(0);
            //最多只保留领券中心一张待领取的券(只有最大额是未领取的券)
            usableNormalCoupon = usableNormalCoupon.stream()
                    .filter(x -> x.getId()!= null || Objects.equals(maxAmountCoupon.getCouponSenderId(), x.getCouponSenderId()))
                    .collect(Collectors.toList());
        }
        resultVO.addUsableNormalCoupon(usableNormalCoupon);
        if (usedCoupon) {
            log.info("用户可用优惠券信息-usedCouponVO:{}", JSON.toJSONString(usedCouponVO));
            OrderPreferential orderPreferential = new OrderPreferential(
                    OrderPreferentialTypeEnum.COUPON, usedCouponVO.getMoney());
            orderPreferential.setActivityName(usedCouponVO.getName());
            orderPreferential.setRelatedId(usedCouponVO.getId() != null ? usedCouponVO.getId().longValue() : usedCouponVO.getCouponId());
            resultVO.addOrderPreferential(orderPreferential);
        }
        return usedCoupon;
    }

    private List<MerchantCouponVO> getMerchantCoupon(List<OrderItemCalcDTO> calcDTOList, List<MerchantCouponVO> couponVOList, PlaceOrderCalcDTO orderCalcDTO, BigDecimal total, PlaceOrderVO placeOrderVO){
        Long mId = placeOrderVO.getMId();

        //根据条件是否走全局方法缓存
        CommonResult<List<SkuCouponDTO>> listCommonResult;
        if (placeOrderVO.getTakePriceFlag()) {//NOSONAR
            List<Coupon> coupons = couponSenderService.listUsableCouponByCache(mId);
            listCommonResult = couponSenderService.listAllValidCouponFromCacheV2 (calcDTOList, coupons);
        } else {
            SkuCouponReqDTO skuCouponReqDTO = new SkuCouponReqDTO();
            List<String> skuList =  calcDTOList.stream().map(OrderItemCalcDTO::getSku).collect(Collectors.toList());
            skuCouponReqDTO.setSkus(skuList);
            listCommonResult = couponSenderService.listAllValidCouponFromCache (skuCouponReqDTO);
        }
        List<MerchantCouponVO> resultCouponList = new ArrayList<>();
        //可领取的优惠券
        List<SkuCouponDTO> skuCouponList =  listCommonResult.getData();
        if (!CollectionUtils.isEmpty(skuCouponList)){
            List<Coupon> distinctSortedCoupons = skuCouponList.stream()
                    .flatMap(skuCoupon -> skuCoupon.getCouponList().stream())
                    .filter(e -> e.getEffectiveNum() == CouponEffectiveNumEnum.UNCLAIMED.getCode())
                    .collect(Collectors.toList());

            //查询手动领取的情况下用户领取卡劵的次数
            if (!CollectionUtil.isEmpty(distinctSortedCoupons)) {
                List<Integer> couponIdList = distinctSortedCoupons.stream().map(Coupon::getId).collect(Collectors.toList());

                Map<Integer, ReceiveIdCountBO> countBOMap;
                List<ReceiveIdCountBO> receiveIdCountBos;
                if (placeOrderVO.getTakePriceFlag()) {//NOSONAR
                    countBOMap = new HashMap<>(couponIdList.size());
                    receiveIdCountBos = merchantCouponService.getUserReceiveCountByCache(mId, CouponReceiveTypeEnum.RECEIVE.getCode());
                    Map<Integer, ReceiveIdCountBO> receiveIdCountBOMap = receiveIdCountBos.stream().collect(Collectors.toMap(x -> x.getCouponId(),
                            Function.identity(), (p1, p2) -> p2));
                    couponIdList.forEach(couponId -> {
                        if (!CollectionUtils.isEmpty(receiveIdCountBOMap) && receiveIdCountBOMap.containsKey(couponId)) {
                            countBOMap.put(couponId, receiveIdCountBOMap.get(couponId));
                        }
                    });
                } else {
                    receiveIdCountBos = merchantCouponMapper.getUserReceiveCount(couponIdList, mId, CouponReceiveTypeEnum.RECEIVE.getCode());
                    countBOMap = receiveIdCountBos.stream().collect(Collectors.toMap(x -> x.getCouponId(), Function.identity(), (p1, p2) -> p2));
                }

                Iterator<Coupon> couponIterator = distinctSortedCoupons.iterator();
                while (couponIterator.hasNext()) {
                    Coupon coupon = couponIterator.next();
                    //查询手动领取的情况下用户领取卡劵的次数
                    //1、限制领取次数的情况下判断次数是否用完
                    if (coupon.getQuantityClaimed() > 0) {
                        ReceiveIdCountBO receiveIdCountBO = countBOMap.get(coupon.getId());
                        if (Objects.nonNull(receiveIdCountBO) && receiveIdCountBO.getNum() >= coupon.getQuantityClaimed()) {
                            couponIterator.remove();
                            continue;
                        }
                    }

                    //2、限制张数情况下判断余量是否充足
                    if (coupon.getGrantLimit() > 0 && coupon.getGrantAmount() <= 0) {
                        couponIterator.remove();
                        continue;
                    }
                }
                resultCouponList = distinctSortedCoupons.stream().map(MerchantCouponConverter::converterMerchantCoupon).collect(Collectors.toList());
            }
        }
        //用户可用
        if (!CollectionUtils.isEmpty(couponVOList)){
            couponVOList.stream().forEach(e -> {
                e.setEffectiveNum(CouponEffectiveNumEnum.RECEIVE.getCode());
            });
            resultCouponList.addAll(couponVOList);
        }
        resultCouponList = resultCouponList.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(
                        t -> t.getCouponId() + ";" + t.getId()))),
                ArrayList::new));
        return resultCouponList;
    }

    private List<MerchantCouponVO> selectUsableCouponWithCache(Integer type, Long mId, BigDecimal total) {
        String key = Global.CACHE_M_COUPON + mId + ":" + type;
        List<MerchantCouponVO> cacheList;
        cacheList = redisCacheUtil.getDataWithCacheList(key, 5, MerchantCouponVO.class, () -> merchantCouponMapper.selectUsableCouponWithoutThreshold(type, mId));
        if (CollectionUtil.isEmpty(cacheList)) {
            return null;
        }
        return cacheList
                .stream()
                .filter(el -> total.compareTo(el.getThreshold()) >= 0 && total.compareTo(el.getMoney()) >= 0)
                .sorted(Comparator.comparing(MerchantCouponVO::getMoney).reversed())
                .collect(Collectors.toList());
    }
}
