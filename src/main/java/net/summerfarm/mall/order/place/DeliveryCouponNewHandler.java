package net.summerfarm.mall.order.place;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.CouponTypeEnum;
import net.summerfarm.mall.common.util.MoneyUtil;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;
import net.summerfarm.mall.mapper.MerchantCouponMapper;
import net.summerfarm.mall.model.domain.OrderPreferential;
import net.summerfarm.mall.model.dto.order.PlaceOrderCalcDTO;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.order.AbstractPlaceOrderHandler;
import org.springframework.stereotype.Service;

/**
 * 运费券处理
 *
 * @author: <EMAIL>
 * @create: 2023/10/12
 */
@Service
@Slf4j
public class DeliveryCouponNewHandler extends AbstractPlaceOrderHandler {

    @Resource
    private MerchantCouponMapper merchantCouponMapper;

    @Override
    public boolean handlePlaceOrder(PlaceOrderCalcDTO orderCalcDTO, OrderResultVO resultVO) {
        //无优惠的优惠券
        BigDecimal deliveryFee = resultVO.getOriginalDeliveryFee();
        if (deliveryFee.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        PlaceOrderVO placeOrderVO = orderCalcDTO.getPlaceOrderVO();
        //可用的运费券
        List<MerchantCouponVO> deliveryCouponList = merchantCouponMapper.selectUsableCoupon(
                CouponTypeEnum.DELIVERY.getType(), placeOrderVO.getMId(), deliveryFee);
        if (CollectionUtil.isEmpty(deliveryCouponList)) {
            return false;
        }
        resultVO.addUsableNormalCoupon(deliveryCouponList);

        //如果有多张运费券且用户还未选择(下单时不能自动选最优，否则会导致用户不选券也会自动使用券)，选中最优的
        MerchantCouponVO selectedCoupon = null;
        Map<Integer, Integer> usedCouponIds = placeOrderVO.getUsedCouponIds();
        if (CollectionUtil.isEmpty(usedCouponIds)
                || usedCouponIds.get(CouponTypeEnum.DELIVERY.getType()) == null) {
            if (Objects.equals(placeOrderVO.getIsTakePrice(), 0)) {
                deliveryCouponList.sort(Comparator.comparing(MerchantCouponVO::getMoney).reversed());
                selectedCoupon = deliveryCouponList.get(0);
            }
        } else {
            Integer deliveryCouponId = usedCouponIds.get(CouponTypeEnum.DELIVERY.getType());
            selectedCoupon = deliveryCouponList.stream()
                    .filter(x -> Objects.equals(deliveryCouponId, x.getId())).findFirst()
                    .orElse(null);
        }
        if (selectedCoupon == null) {
            return false;
        }
        resultVO.setUsedDeliveryCouponId(selectedCoupon.getId());
        resultVO.addUsedMerchantCouponId(selectedCoupon.getId());

        Map<String, BigDecimal> deliveryFeeMap = Maps.newHashMap();
        Map<LocalDate, OrderResultVO> subOrderResultMap = placeOrderVO.getSubOrderResultMap();
        for (Entry<LocalDate, OrderResultVO> entry : subOrderResultMap.entrySet()) {
            OrderResultVO subOrderResult = entry.getValue();
            //子订单运费
            BigDecimal subDeliveryFee = subOrderResult.getOriginalDeliveryFee();
            if (subDeliveryFee.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            deliveryFeeMap.put(entry.getKey().toString(), subDeliveryFee);
        }
        if (CollectionUtil.isEmpty(deliveryFeeMap)) {
            return false;
        }
        BigDecimal couponDiscountFee = selectedCoupon.getMoney();
        resultVO.setDeliveryFee(deliveryFee.subtract(couponDiscountFee));
        resultVO.setActualTotalPrice(resultVO.getActualTotalPrice().subtract(couponDiscountFee));
        OrderPreferential orderPreferential = new OrderPreferential(
                OrderPreferentialTypeEnum.DELIVERY_COUPON, couponDiscountFee);
        orderPreferential.setRelatedId(selectedCoupon.getId() != null ? selectedCoupon.getId().longValue() : selectedCoupon.getCouponId());
        orderPreferential.setActivityName(selectedCoupon.getName());
        //主订单优惠信息
        resultVO.addOrderPreferential(orderPreferential);
        //按子订单的运费比例均摊运费，多余的金额随机加到其中一个子订单上
        Map<String, BigDecimal> downMap = MoneyUtil.moneyDownUtil(deliveryFeeMap, couponDiscountFee);
        if (CollectionUtil.isEmpty(downMap)) {
            log.info("运费券均摊失败");
            return false;
        }
        for (Entry<String, BigDecimal> entry : downMap.entrySet()) {
            LocalDate localDate = LocalDate.parse(entry.getKey());
            OrderResultVO subOrderResultVO = subOrderResultMap.get(localDate);
            subOrderResultVO.setDeliveryFee(subOrderResultVO.getDeliveryFee().subtract(entry.getValue()));
            subOrderResultVO.setActualTotalPrice(subOrderResultVO.getActualTotalPrice().subtract(entry.getValue()));
            OrderPreferential subOrderPreferential = new OrderPreferential(
                    OrderPreferentialTypeEnum.DELIVERY_COUPON, entry.getValue());
            subOrderPreferential.setRelatedId(selectedCoupon.getId() != null ? selectedCoupon.getId().longValue() : selectedCoupon.getCouponId());
            subOrderPreferential.setActivityName(selectedCoupon.getName());
            //子订单运费优惠信息
            subOrderResultVO.addOrderPreferential(subOrderPreferential);
        }
        return true;
    }
}
