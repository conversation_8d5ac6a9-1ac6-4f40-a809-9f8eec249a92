package net.summerfarm.mall.order.place;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.model.dto.order.PlaceOrderCalcDTO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.order.AbstractPlaceOrderHandler;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Map.Entry;
import java.util.Objects;

/**
 * 精准送处理逻辑
 *
 * @author: <EMAIL>
 * @create: 2023/10/12
 */
@Slf4j
@Service
public class AccurateDeliveryNewHandler extends AbstractPlaceOrderHandler {

    @Override
    public boolean handlePlaceOrder(PlaceOrderCalcDTO orderCalcDTO, OrderResultVO resultVO) {
        PlaceOrderVO placeOrderVO = orderCalcDTO.getPlaceOrderVO();
        if (CollectionUtil.isEmpty(placeOrderVO.getSubOrderResultMap()) || StringUtils.isBlank(
                placeOrderVO.getTimeFrameName())) {
            return false;
        }
        String timeFrame = placeOrderVO.getTimeFrameName();

        boolean flag = false;
        //按子订单处理
        for (Entry<LocalDate, OrderResultVO> entry : placeOrderVO.getSubOrderResultMap()
                .entrySet()) {
            OrderResultVO subOrderResult = entry.getValue();
            //订单包含代销不入仓的商品则不支持精准送
            if (Objects.equals(subOrderResult.getHasSaleNotInStore(), 1)) {
                continue;
            }
            //精准送固定30元,记录选择精准送时间，用于配送计划保存
            subOrderResult.setUsedTimeFrame(timeFrame);
            subOrderResult.setPrecisionDeliveryFee(Global.TIME_FRAME_DELIVERY_FEE);
            subOrderResult.setOriginTotalPrice(subOrderResult.getOriginTotalPrice().add(Global.TIME_FRAME_DELIVERY_FEE));
            subOrderResult.setActualTotalPrice(subOrderResult.getActualTotalPrice().add(Global.TIME_FRAME_DELIVERY_FEE));
            resultVO.setUsedTimeFrame(timeFrame);
            resultVO.setPrecisionDeliveryFee(Global.TIME_FRAME_DELIVERY_FEE);
            resultVO.setOriginTotalPrice(resultVO.getOriginTotalPrice().add(Global.TIME_FRAME_DELIVERY_FEE));
            resultVO.setActualTotalPrice(resultVO.getActualTotalPrice().add(Global.TIME_FRAME_DELIVERY_FEE));
            flag = true;
        }

        return flag;
    }
}
