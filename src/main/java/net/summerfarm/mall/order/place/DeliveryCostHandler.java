package net.summerfarm.mall.order.place;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.CommonStatus;
import net.summerfarm.mall.enums.DeliveryEveryDayEnum;
import net.summerfarm.mall.mapper.DeliveryPlanMapper;
import net.summerfarm.mall.model.dto.delivery.FreeDeliveryDifferenceDTO;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.dto.order.PlaceOrderCalcDTO;
import net.summerfarm.mall.model.vo.neworder.SubOrderFailVO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.order.AbstractPlaceOrderHandler;
import net.summerfarm.mall.service.facade.DistributionRulesFacade;
import net.summerfarm.mall.service.facade.dto.DeliveryFeeReq;
import net.summerfarm.mall.service.facade.dto.DeliveryFeeRes;
import net.summerfarm.mall.service.facade.dto.DeliveryFeeSkuInfoRes;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2024/9/10 11:22
 * @PackageName:net.summerfarm.mall.order.place
 * @ClassName: DeliveryCostHandler
 * @Description: 计算包裹运费--配送规则支持起送价及阶梯运费
 * @Version 1.0
 */

@Service
@Slf4j
public class DeliveryCostHandler  extends AbstractPlaceOrderHandler {

    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;

    @Resource
    private DistributionRulesFacade distributionRulesFacade;

    @Override
    public boolean handlePlaceOrder(PlaceOrderCalcDTO orderCalcDTO, OrderResultVO resultVO) {
        PlaceOrderVO placeOrderVO = orderCalcDTO.getPlaceOrderVO();
        if (CollectionUtil.isEmpty(placeOrderVO.getSubOrderResultMap())) {
            return false;
        }

        if (placeOrderVO.getDeliveryRulesType() == null || placeOrderVO.getDeliveryRulesType().equals(CommonStatus.NO.getCode())) {
            log.info("获取老版运费规则！orderCalcDTO：{}", JSON.toJSONString(orderCalcDTO));
            return false;
        }

        //根据包裹获取配送费
        BigDecimal deliveryFee = BigDecimal.ZERO;
        BigDecimal ruleDeliveryFee = BigDecimal.ZERO;
        DeliveryFeeReq deliveryFeeReq = new DeliveryFeeReq();
        deliveryFeeReq.setAreaNo(placeOrderVO.getArea().getAreaNo());
        deliveryFeeReq.setContactId(placeOrderVO.getContactId().intValue());
        deliveryFeeReq.setProvince(placeOrderVO.getContact().getProvince());
        deliveryFeeReq.setCity(placeOrderVO.getContact().getCity());
        deliveryFeeReq.setArea(placeOrderVO.getContact().getArea());
        deliveryFeeReq.setAddress(placeOrderVO.getContact().getAddress());
        deliveryFeeReq.setXmAdminId(Optional.ofNullable(placeOrderVO.getAdminId()).orElse(null));
        Map<LocalDate, OrderResultVO> subOrderResultMap = placeOrderVO.getSubOrderResultMap();
        for (Map.Entry<LocalDate, OrderResultVO> entry : subOrderResultMap.entrySet()) {
            OrderResultVO subOrderResultVO = entry.getValue();
            Integer isEveryDayFlag = subOrderResultVO.getIsEveryDayFlag();
            List<OrderItemCalcDTO> itemList = subOrderResultVO.getItemList();
            if (CollectionUtil.isEmpty(itemList)) {
                continue;
            }
            subOrderResultVO.setSatisfyFreeDelivery(CommonStatus.NO.getCode());
            DeliveryFeeRes deliveryFeeRes = getDeliveryFeeRes(deliveryFeeReq, entry, isEveryDayFlag, itemList);
            BigDecimal subDeliveryFee = deliveryFeeRes.getDeliveryFee();
            subOrderResultVO.setRuleDeliveryFee(subDeliveryFee);
            subOrderResultVO.setOriginalDeliveryFee(subDeliveryFee);
            subOrderResultVO.setDeliveryFee(subDeliveryFee);
            subOrderResultVO.setHitRuleDetailSnapshot(deliveryFeeRes.getHitRuleDetailSnapshot());
            subOrderResultVO.setStartPrice(deliveryFeeRes.getStartPrice());
            subOrderResultVO.setIsSatisfyDeliveryThreshold(deliveryFeeRes.getIsSatisfyDeliveryThreshold());
            subOrderResultVO.setDeliveryThresholdMinDifference(deliveryFeeRes.getDeliveryThresholdMinDifference());
            subOrderResultVO.setFreeDeliveryDifferenceList(deliveryFeeRes.getFreeDeliveryDifferenceList());
            subOrderResultVO.setMatchDeliveryFeeRule(deliveryFeeRes.getMatchDeliveryFeeRuleResp());
            ruleDeliveryFee = ruleDeliveryFee.add(subDeliveryFee);
            if (!CollectionUtils.isEmpty(deliveryFeeRes.getFreeDeliveryDifferenceList())) {
                FreeDeliveryDifferenceDTO freeDelivery = deliveryFeeRes.getFreeDeliveryDifferenceList().stream()
                        .filter(f -> f.getDeliveryFee().compareTo(BigDecimal.ZERO) <= 0)
                        .sorted(Comparator.comparing(FreeDeliveryDifferenceDTO::getCurrentDifference)).findFirst().orElse(null);
                subOrderResultVO.setFreeDelivery(freeDelivery);
            }

            //没有匹配到规则默认免邮
            if (!deliveryFeeRes.getIsMatchRule()) {
                log.info("当前包裹未匹配到运费规则，默认免邮-subOrderResultVO:{}", JSON.toJSONString(subOrderResultVO));
                subOrderResultVO.setSatisfyFreeDelivery(CommonStatus.YES.getCode());
                subOrderResultVO.setOriginalDeliveryFee(BigDecimal.ZERO);
                subOrderResultVO.setDeliveryFee(BigDecimal.ZERO);
                subOrderResultVO.setOriginalDeliveryFee(BigDecimal.ZERO);
                continue;
            }

            //校验是否已经有履约配送计划 有配送计划的情况下 不收配送费且不校验起步价信息
            int delivery = deliveryPlanMapper.countByDeliveryTime(placeOrderVO.getMId(),
                    placeOrderVO.getContactId(), entry.getKey());
            if (delivery > 0) {
                log.info("当前包裹已经存在履约配送计划，故不收配送费！subOrderResultVO:{}", JSON.toJSONString(subOrderResultVO));
                subOrderResultVO.setSatisfyFreeDelivery(CommonStatus.YES.getCode());
                subOrderResultVO.setOriginalDeliveryFee(BigDecimal.ZERO);
                subOrderResultVO.setDeliveryFee(BigDecimal.ZERO);
                subOrderResultVO.setIsSatisfyDeliveryThreshold(Boolean.TRUE);
                subOrderResultVO.setDeliveryThresholdMinDifference(BigDecimal.ZERO);
                continue;
            }

            //已满足起步价且运费为0
            if (deliveryFeeRes.getIsSatisfyDeliveryThreshold() && subDeliveryFee.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("当前包裹已满足免邮-subOrderResultVO:{}", JSON.toJSONString(subOrderResultVO));
                subOrderResultVO.setSatisfyFreeDelivery(CommonStatus.YES.getCode());
                subOrderResultVO.setOriginalDeliveryFee(BigDecimal.ZERO);
                subOrderResultVO.setDeliveryFee(BigDecimal.ZERO);
                continue;
            }

            //下单、代下单会校验并报错提示 -- 商城正常下单理论上基本不存在这种情况
            if (!deliveryFeeRes.getIsSatisfyDeliveryThreshold() && deliveryFeeRes.getDeliveryThresholdMinDifference().compareTo(BigDecimal.ZERO) > 0) {
                log.error("当前包裹未达到起送门槛-subOrderResultVO:{}", JSON.toJSONString(subOrderResultVO));
                if (placeOrderVO.getHelpOrder().equals(CommonStatus.YES.getCode())) {
                    throw new BizException("未达起送金额（满" + deliveryFeeRes.getStartPrice() + "元起送，还差" +
                            deliveryFeeRes.getDeliveryThresholdMinDifference() + "元），请继续加购！");
                }
                resultVO.addSubOrderFailVO(() -> {
                    SubOrderFailVO subOrderFailVO = new SubOrderFailVO();
                    subOrderFailVO.setDeliveryDate(entry.getKey());
                    subOrderFailVO.setStartPrice(deliveryFeeRes.getStartPrice());
                    subOrderFailVO.setDeliveryThresholdMinDifference(deliveryFeeRes.getDeliveryThresholdMinDifference());
                    return subOrderFailVO;
                });
                continue;
            }
            deliveryFee = deliveryFee.add(subDeliveryFee);
            subOrderResultVO.setOriginTotalPrice(subOrderResultVO.getOriginTotalPrice().add(subOrderResultVO.getOriginalDeliveryFee()));
            subOrderResultVO.setActualTotalPrice(subOrderResultVO.getActualTotalPrice().add(subDeliveryFee));
        }
        resultVO.setRuleDeliveryFee(ruleDeliveryFee);
        resultVO.setDeliveryFee(deliveryFee);
        resultVO.setOriginalDeliveryFee(deliveryFee);
        resultVO.setOriginTotalPrice(resultVO.getOriginTotalPrice().add(deliveryFee));
        resultVO.setActualTotalPrice(resultVO.getActualTotalPrice().add(deliveryFee));
        return true;
    }

    private DeliveryFeeRes getDeliveryFeeRes(DeliveryFeeReq deliveryFeeReq, Map.Entry<LocalDate, OrderResultVO> entry, Integer isEveryDayFlag, List<OrderItemCalcDTO> itemList) {
        //获取配送费及对应规则
        deliveryFeeReq.setDeliveryDate(entry.getKey());
        deliveryFeeReq.setIsEveryDayFlag(isEveryDayFlag.equals(DeliveryEveryDayEnum.DAYCARE.getCode())
                ? Boolean.TRUE : Boolean.FALSE);
        List<DeliveryFeeSkuInfoRes> deliveryFeeSkuInfoResList = new ArrayList<>();
        itemList.forEach(item -> {
            DeliveryFeeSkuInfoRes deliveryFeeSkuInfoRes = new DeliveryFeeSkuInfoRes();
            deliveryFeeSkuInfoRes.setSku(item.getSku());
            deliveryFeeSkuInfoRes.setSkuAmount(item.getAmount());
            deliveryFeeSkuInfoRes.setSkuSubType(item.getSubType());
            deliveryFeeSkuInfoRes.setCategoryType(item.getCategoryType());
            deliveryFeeSkuInfoRes.setSkuPrice(item.getPrice());
            deliveryFeeSkuInfoRes.setTotalPrice(item.getCalcPartDeliveryFee());
            deliveryFeeSkuInfoResList.add(deliveryFeeSkuInfoRes);
        });
        deliveryFeeReq.setDeliveryFeeSkuInfoRes(deliveryFeeSkuInfoResList);
        return distributionRulesFacade.queryDeliveryFee(deliveryFeeReq);
    }
}
