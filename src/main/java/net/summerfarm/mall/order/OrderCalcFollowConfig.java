package net.summerfarm.mall.order;

import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 订单计算流程配置
 */
@Configuration
public class OrderCalcFollowConfig {
    @Resource
    private AbstractOrderItemHandler giftHandler;
    @Resource
    private AbstractOrderItemHandler agentHandler;
    @Resource
    private AbstractOrderItemHandler prepayHandler;
    @Resource
    private AbstractOrderItemHandler activityHandler;
    @Resource
    private AbstractOrderItemHandler expandActivityHandler;
    @Resource
    private AbstractOrderItemHandler exchangeBuyHandler;
    @Resource
    private AbstractPlaceOrderHandler discountCardHandler;
    @Resource
    private AbstractPlaceOrderHandler fullReduceHandler;
    @Resource
    private AbstractPlaceOrderHandler couponHandler;
    @Resource
    private AbstractPlaceOrderHandler redPacketHandler;
    @Resource
    private AbstractPlaceOrderHandler fullReturnHandler;
    @Resource
    private AbstractPlaceOrderHandler deliveryFeeNewHandler;
    @Resource
    private AbstractPlaceOrderHandler accurateDeliveryNewHandler;
    @Resource
    private AbstractPlaceOrderHandler outTimeNewHandler;
    @Resource
    private AbstractPlaceOrderHandler accurateCouponNewHandler;
    @Resource
    private AbstractPlaceOrderHandler deliveryCouponNewHandler;
    @Resource
    private AbstractPlaceOrderHandler deliveryCostHandler;

    public static List<AbstractOrderItemHandler> PERIOD_ITEM_HANDLER = new LinkedList<>();
    public static List<AbstractPlaceOrderHandler> PERIOD_ORDER_NEW_HANDLER = new LinkedList<>();
    public static List<AbstractOrderItemHandler> CASH_ITEM_HANDLER = new LinkedList<>();
    public static List<AbstractPlaceOrderHandler> CASH_MASTER_ORDER_HANDLER = new LinkedList<>();
    public static List<AbstractPlaceOrderHandler> CASH_SUB_ORDER_HANDLER = new LinkedList<>();
    public static List<AbstractOrderItemHandler> NORMAL_ITEM_HANDLER = new LinkedList<>();

    /**
     * 到手价 订单项Handler链
     */
    public static List<AbstractOrderItemHandler> TAKE_ACTUAL_PRICE_ITEM_HANDLER = new LinkedList<>();
    /**
     * 到手价 订单Handler链
     */
    public static List<AbstractPlaceOrderHandler> TAKE_ACTUAL_PRICE_ORDER_HANDLER = new LinkedList<>();

    /**
     * 主订单责任链-新
     */
    public static List<AbstractPlaceOrderHandler> NORMAL_MASTER_ORDER_HANDLER = new LinkedList<>();

    /**
     * 子订单责任链-新
     */
    public static List<AbstractPlaceOrderHandler> NORMAL_SUB_ORDER_HANDLER = new LinkedList<>();


    @PostConstruct
    public void init() {
        //账期大客户订单项处理：赠品-代仓-预付
        PERIOD_ITEM_HANDLER.add(giftHandler);
        PERIOD_ITEM_HANDLER.add(agentHandler);
        PERIOD_ITEM_HANDLER.add(prepayHandler);

        //账期大客户订单处理：精准送-运费
//        PERIOD_ORDER_HANDLER.add(accurateDeliveryHandler);
//        PERIOD_ORDER_HANDLER.add(deliveryFeeHandler);

        //账期大客户订单处理-新：精准送-运费
        PERIOD_ORDER_NEW_HANDLER.add(accurateDeliveryNewHandler);
        PERIOD_ORDER_NEW_HANDLER.add(deliveryFeeNewHandler);
        PERIOD_ORDER_NEW_HANDLER.add(deliveryCostHandler);

        //现结大客户订单项处理：赠品-代仓-预付
        CASH_ITEM_HANDLER.add(giftHandler);
        CASH_ITEM_HANDLER.add(agentHandler);
        CASH_ITEM_HANDLER.add(prepayHandler);

        //现结大客户订单处理：优惠券-精准送-精准送优惠券-红包-运费-运费券
//        CASH_ORDER_HANDLER.add(couponHandler);
//        CASH_ORDER_HANDLER.add(accurateDeliveryHandler);
//        CASH_ORDER_HANDLER.add(accurateCouponHandler);
//        CASH_ORDER_HANDLER.add(redPacketHandler);
//        CASH_ORDER_HANDLER.add(deliveryFeeHandler);
//        CASH_ORDER_HANDLER.add(deliveryCouponHandler);

        //现结大客户主订单处理-新：优惠券-红包-精准送-运费-精准送优惠券-运费券
        CASH_MASTER_ORDER_HANDLER.add(couponHandler);
        CASH_MASTER_ORDER_HANDLER.add(redPacketHandler);

        //现结大客户子订单处理-新：优惠券-红包-精准送-运费-精准送优惠券-运费券
        CASH_SUB_ORDER_HANDLER.add(accurateDeliveryNewHandler);
        CASH_SUB_ORDER_HANDLER.add(deliveryFeeNewHandler);
        CASH_SUB_ORDER_HANDLER.add(deliveryCostHandler);
        CASH_SUB_ORDER_HANDLER.add(accurateCouponNewHandler);
        CASH_SUB_ORDER_HANDLER.add(deliveryCouponNewHandler);

        //单店订单项处理：换购品-拓展购买-赠品-特价-阶梯价&搭配购
        NORMAL_ITEM_HANDLER.add(exchangeBuyHandler);
        NORMAL_ITEM_HANDLER.add(expandActivityHandler);
        NORMAL_ITEM_HANDLER.add(giftHandler);
        NORMAL_ITEM_HANDLER.add(activityHandler);

        //单店订单处理：优惠卡-满减-优惠券-满返-精准送-精准送优惠券-超时加单-红包-运费-运费券
//        NORMAL_ORDER_HANDLER.add(discountCardHandler);
//        NORMAL_ORDER_HANDLER.add(fullReduceHandler);
//        NORMAL_ORDER_HANDLER.add(couponHandler);
//        NORMAL_ORDER_HANDLER.add(fullReturnHandler);
//        NORMAL_ORDER_HANDLER.add(accurateDeliveryHandler);
//        NORMAL_ORDER_HANDLER.add(accurateCouponHandler);
//        NORMAL_ORDER_HANDLER.add(outTimeHandler);
//        NORMAL_ORDER_HANDLER.add(redPacketHandler);
//        NORMAL_ORDER_HANDLER.add(deliveryFeeHandler);
//        NORMAL_ORDER_HANDLER.add(deliveryCouponHandler);

        // 到手价 订单项Handler链 活动-阶梯价&搭配购
        TAKE_ACTUAL_PRICE_ITEM_HANDLER.add(activityHandler);

        // 到手价 订单Handler链 优惠卡-满减-优惠券-红包
        TAKE_ACTUAL_PRICE_ORDER_HANDLER.add(discountCardHandler);
        TAKE_ACTUAL_PRICE_ORDER_HANDLER.add(fullReduceHandler);
        TAKE_ACTUAL_PRICE_ORDER_HANDLER.add(couponHandler);
        TAKE_ACTUAL_PRICE_ORDER_HANDLER.add(redPacketHandler);

        //单店主订单处理-新：优惠卡-满减-优惠券-满返-红包
        NORMAL_MASTER_ORDER_HANDLER.add(discountCardHandler);
        NORMAL_MASTER_ORDER_HANDLER.add(fullReduceHandler);
        NORMAL_MASTER_ORDER_HANDLER.add(couponHandler);
        NORMAL_MASTER_ORDER_HANDLER.add(fullReturnHandler);
        NORMAL_MASTER_ORDER_HANDLER.add(redPacketHandler);

        //子订单处理-新：精准送-精准送优惠券-超时加单-运费-运费券
        NORMAL_SUB_ORDER_HANDLER.add(accurateDeliveryNewHandler);
        NORMAL_SUB_ORDER_HANDLER.add(outTimeNewHandler);
        NORMAL_SUB_ORDER_HANDLER.add(deliveryFeeNewHandler);
        NORMAL_SUB_ORDER_HANDLER.add(deliveryCostHandler);
        NORMAL_SUB_ORDER_HANDLER.add(accurateCouponNewHandler);
        NORMAL_SUB_ORDER_HANDLER.add(deliveryCouponNewHandler);

    }
}
