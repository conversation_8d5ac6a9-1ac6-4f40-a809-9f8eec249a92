package net.summerfarm.mall.order;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;

/**
 * <AUTHOR>
 * @Description 下单订单项处理抽象类
 */
public abstract class AbstractOrderItemHandler {

    /**
     * 处理优惠
     * @param itemCalcVO 订单项
     * @param placeOrderVO 下单信息
     * @param resultVO 优惠项使用信息
     * @return 处理结果
     */
    public abstract boolean handleOrderItem(OrderItemCalcDTO itemCalcVO, PlaceOrderVO placeOrderVO, OrderResultVO resultVO);
}
