package net.summerfarm.mall.order.converter;

import net.summerfarm.mall.model.domain.Coupon;
import net.summerfarm.mall.model.vo.MerchantCouponVO;

/**
 * <AUTHOR>
 */
public class MerchantCouponConverter {

    public static MerchantCouponVO converterMerchantCoupon(Coupon coupon) {
        MerchantCouponVO merchantCouponVO = new MerchantCouponVO();
        merchantCouponVO.setCouponId(coupon.getId());
        merchantCouponVO.setGrouping(coupon.getGrouping());
        merchantCouponVO.setName(coupon.getName());
        merchantCouponVO.setCode(coupon.getCode());
        merchantCouponVO.setMoney(coupon.getMoney());
        merchantCouponVO.setThreshold(coupon.getThreshold());
        merchantCouponVO.setNewHand(coupon.getNewHand());
        merchantCouponVO.setReamrk(coupon.getReamrk());
        merchantCouponVO.setAgioType(coupon.getAgioType());
        merchantCouponVO.setCategoryId(coupon.getCategoryId());
        merchantCouponVO.setSku(coupon.getSku());
        merchantCouponVO.setEffectiveNum(coupon.getEffectiveNum());
        merchantCouponVO.setActivityScope(coupon.getActivityScope());
        merchantCouponVO.setVaildDate(coupon.getVaildDate());
        merchantCouponVO.setAddTime(coupon.getAddTime());
        merchantCouponVO.setId(coupon.getMerchantCouponId());
        merchantCouponVO.setSkuScope(coupon.getSkuScope());
        merchantCouponVO.setCouponSenderId(coupon.getCouponSenderId());
        return merchantCouponVO;

    }

}
