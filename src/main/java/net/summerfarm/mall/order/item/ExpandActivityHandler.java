package net.summerfarm.mall.order.item;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.ExpandActivityStatusEnum;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;
import net.summerfarm.mall.mapper.ExpandActivityMapper;
import net.summerfarm.mall.model.bo.price.PriceInfoBO;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.order.AbstractOrderItemHandler;
import net.summerfarm.mall.service.ExpandActivityService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExpandActivityHandler extends AbstractOrderItemHandler {
    @Resource
    private ExpandActivityMapper expandActivityMapper;
    @Resource
    private ExpandActivityService expandActivityService;

    @Override
    public boolean handleOrderItem(OrderItemCalcDTO itemCalcVO, PlaceOrderVO placeOrderVO, OrderResultVO resultVO) {
        if (null != placeOrderVO.getExpandActivityId()){
            ExpandActivity expandActivity = expandActivityMapper.selectByPrimaryKey(placeOrderVO.getExpandActivityId());
            if (!ObjectUtils.isEmpty(expandActivity) && expandActivity.getStatus().equals(ExpandActivityStatusEnum.ENABLE.getId())){
                // 获取活动价信息
                PriceInfoBO activityPriceInfo = expandActivityService.handleExpandActivityPriceBySku(itemCalcVO, expandActivity);
                if (ObjectUtils.isEmpty(activityPriceInfo)){
                    return true;
                }
                BigDecimal activityPrice = activityPriceInfo.getPrice();

                //添加拓展活动优惠用户购买数量记录->用于限购数量
                if (null != expandActivity.getPurchaseLimit()){
                    itemCalcVO.setExpandActivityQuantity(new ExpandActivityQuantity(RequestHolder.getMId(),itemCalcVO.getSku(),expandActivity.getId(),itemCalcVO.getAmount()));
                }

                if(activityPrice.compareTo(itemCalcVO.getOriginalPrice()) < 0) {
                    BigDecimal subtract = itemCalcVO.getActualTotalOriginalPrice().subtract(activityPrice.multiply(BigDecimal.valueOf(itemCalcVO.getAmount())));
                    itemCalcVO.decreaseActualTotalPrice(subtract);
                    itemCalcVO.decreaseCalcPartDeliveryFee(subtract);

                    //添加订单优惠
                    resultVO.addOrderPreferential(() -> {
                        OrderPreferential orderPreferential = new OrderPreferential(OrderPreferentialTypeEnum.EXPAND_ACTIVITY, subtract);
                        orderPreferential.setActivityName(expandActivity.getName());
                        orderPreferential.setRelatedId(expandActivity.getId());
                        return orderPreferential;
                    });

                    //添加订单项优惠明细
                    itemCalcVO.addItemPreferential(() -> {
                        OrderItemPreferential orderPreferential = new OrderItemPreferential(OrderPreferentialTypeEnum.EXPAND_ACTIVITY, subtract);
                        orderPreferential.setRelatedId(expandActivity.getId());
                        orderPreferential.setCreateTime(LocalDateTime.now());
                        return orderPreferential;
                    });
                }
            }
            return true;
        }
        return false;
    }

}
