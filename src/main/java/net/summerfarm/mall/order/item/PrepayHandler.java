package net.summerfarm.mall.order.item;

import cn.hutool.core.collection.CollectionUtil;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;
import net.summerfarm.mall.mapper.AreaSkuMapper;
import net.summerfarm.mall.mapper.PrepayInventoryMapper;
import net.summerfarm.mall.model.domain.AreaSku;
import net.summerfarm.mall.model.domain.OrderItemPreferential;
import net.summerfarm.mall.model.domain.OrderPreferential;
import net.summerfarm.mall.model.domain.PrepayInventory;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.order.AbstractOrderItemHandler;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 预付商品计算逻辑
 */
@Service
public class PrepayHandler extends AbstractOrderItemHandler {
    @Override
    public boolean handleOrderItem(OrderItemCalcDTO itemCalcVO, PlaceOrderVO placeOrderVO, OrderResultVO resultVO) {
        if (itemCalcVO.getPrepayUsableQuantity() == null) {
            return false;
        }

        int useQuantity = Math.min(itemCalcVO.getPrepayUsableQuantity(), itemCalcVO.getAmount());
        BigDecimal discountPrice = itemCalcVO.getPrepaySetPrice().multiply(BigDecimal.valueOf(useQuantity));
        itemCalcVO.decreaseActualTotalPrice(discountPrice);
        itemCalcVO.setIsPrePay(true);
        //记录使用次数
        resultVO.addPrepayRecord(itemCalcVO.getSku(), useQuantity);
        //添加订单项优惠明细
        itemCalcVO.addItemPreferential(() -> {
            OrderItemPreferential orderPreferential = new OrderItemPreferential(OrderPreferentialTypeEnum.PREPAY, discountPrice);
            orderPreferential.setCreateTime(LocalDateTime.now());
            return orderPreferential;
        });

        //累计预付总优惠
        List<OrderPreferential> preferentialList = resultVO.getPreferentialList();
        if (CollectionUtil.isNotEmpty(preferentialList)) {
            for (OrderPreferential preferential : preferentialList) {
                if (Objects.equals(preferential.getType(), OrderPreferentialTypeEnum.PREPAY.ordinal())) {
                    preferential.setAmount(preferential.getAmount().add(discountPrice));
                }
            }
        } else {
            resultVO.addOrderPreferential(() -> {
                OrderPreferential orderPreferential = new OrderPreferential(OrderPreferentialTypeEnum.PREPAY, discountPrice);
                return orderPreferential;
            });
        }
        return true;
    }
}
