package net.summerfarm.mall.order.item;

import java.math.BigDecimal;
import net.summerfarm.enums.TrolleyProductTypeEnum;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;
import net.summerfarm.mall.model.domain.OrderItemPreferential;
import net.summerfarm.mall.model.domain.OrderPreferential;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.order.AbstractOrderItemHandler;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 赠品计算逻辑
 */
@Service
public class GiftHandler extends AbstractOrderItemHandler {

    @Override
    public boolean handleOrderItem(OrderItemCalcDTO itemCalcVO, PlaceOrderVO placeOrderVO, OrderResultVO resultVO) {
        if (Objects.equals(TrolleyProductTypeEnum.GIFT.ordinal(), itemCalcVO.getProductType())) {
            BigDecimal actualTotalPrice = itemCalcVO.getActualTotalPrice();
            itemCalcVO.decreaseActualTotalPrice(actualTotalPrice);
            itemCalcVO.decreaseCalcPartDeliveryFee(itemCalcVO.getCalcPartDeliveryFee());

            //添加订单项优惠明细
            itemCalcVO.addItemPreferential(() -> {
                OrderItemPreferential orderPreferential = new OrderItemPreferential(OrderPreferentialTypeEnum.GIFT,
                        actualTotalPrice);
                orderPreferential.setCreateTime(LocalDateTime.now());
                return orderPreferential;
            });

            resultVO.addOrderPreferential(() -> {
                OrderPreferential orderPreferential = new OrderPreferential(OrderPreferentialTypeEnum.GIFT,
                        actualTotalPrice);
                return orderPreferential;
            });
            return true;
        }

        return false;
    }
}
