package net.summerfarm.mall.order.item;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;
import net.summerfarm.mall.mapper.ActivitySkuPurchaseQuantityMapper;
import net.summerfarm.mall.model.domain.ActivitySkuPurchaseQuantity;
import net.summerfarm.mall.model.domain.OrderItemPreferential;
import net.summerfarm.mall.model.domain.OrderPreferential;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDetailDTO;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.model.vo.price.SkuPreferentialVO;
import net.summerfarm.mall.order.AbstractOrderItemHandler;
import net.summerfarm.mall.service.ActivitySkuService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 活动计算逻辑
 */
@Service
@Slf4j
public class ActivityHandler extends AbstractOrderItemHandler {
    @Resource
    private ActivitySkuPurchaseQuantityMapper activitySkuPurchaseQuantityMapper;

    @Resource
    private ActivitySkuService activitySkuService;

    @Override
    public boolean handleOrderItem(OrderItemCalcDTO itemCalcVO, PlaceOrderVO placeOrderVO, OrderResultVO resultVO) {
        Map<String, ActivitySkuDetailDTO> activitySkuDetailMap = placeOrderVO.getActivitySkuDetailMap();
        ActivitySkuDetailDTO activitySkuVO = activitySkuDetailMap.get(itemCalcVO.getSku());

        //预估到手价（且不需要均摊）和下单、预下单分开处理
        if (placeOrderVO.getTakePriceFlag()) {//NOSONAR
            log.info("活动预下单、下单责任链");
            return handleOrderItemIsTakePrice(activitySkuVO, itemCalcVO, placeOrderVO, resultVO);
        } else {
            log.info("活动预下单、下单责任链");
            return handleOrderItemNoTakePrice(activitySkuVO, itemCalcVO, placeOrderVO, resultVO);
        }
    }

    private boolean handleOrderItemNoTakePrice(ActivitySkuDetailDTO activitySkuVO, OrderItemCalcDTO itemCalcVO, PlaceOrderVO placeOrderVO, OrderResultVO resultVO) {
        if (activitySkuVO != null && activitySkuVO.getActivityPrice() != null) {
            // 获取活动价信息
            BigDecimal activityPrice = activitySkuVO.getActivityPrice();
            BigDecimal actualTotalPrice = BigDecimal.ZERO;
            BigDecimal subtract = BigDecimal.ZERO;
            //优惠数量
            Integer discountNum = 0;
            Integer quantityPurchased = 0;
            if(activityPrice.compareTo(itemCalcVO.getOriginalPrice()) <= 0){
                if (activitySkuVO.getLimitQuantity() > 0) {
                    if (activitySkuVO.getPurchasedQuantity() != null) {
                        log.info("从上游活动信息中获取用户已经购买活动数量，quantityPurchased：{}", activitySkuVO.getPurchasedQuantity());
                        quantityPurchased = activitySkuVO.getPurchasedQuantity();
                    } else {
                        // 用户限购不为空且活动库存>0
                        List<ActivitySkuPurchaseQuantity> purchaseQuantities = null;
                        //判断限购类型
                        Integer accountLimit = activitySkuVO.getAccountLimit();
                        if (accountLimit == 1) {
                            //件数限购
                            purchaseQuantities = activitySkuPurchaseQuantityMapper.selectPurchaseQuantity(RequestHolder.getMId(),activitySkuVO.getBasicInfoId(),activitySkuVO.getSku());
                        } else {
                            //日限购
                            purchaseQuantities = activitySkuPurchaseQuantityMapper.selectPurchaseQuantityToday(RequestHolder.getMId(),activitySkuVO.getBasicInfoId(),activitySkuVO.getSku());
                        }
                        if (CollectionUtil.isNotEmpty(purchaseQuantities)) {
                            quantityPurchased = purchaseQuantities.stream().mapToInt(ActivitySkuPurchaseQuantity::getPurchaseQuantity).sum();
                        }
                    }
                }

                if (null != activitySkuVO.getActualQuantity()){
                    if (activitySkuVO.getActualQuantity() == 0){
                        // 活动库存为0，不走优惠
                        return true;
                    }else {
                        // 活动库存>0 计算优惠数量
                        if (activitySkuVO.getLimitQuantity() > 0){
                            Integer quantity = quantityPurchased;
                            if (activitySkuVO.getLimitQuantity() <= quantity){
                                // 用户购买数量达到限购数量，不走优惠
                                log.info("用户购买数量达到限购数量，不走优惠。限购数量：{}，已购买数量quantity：{}", activitySkuVO.getLimitQuantity(), quantity);
                                return true;
                            }else {
                                // 用户购买数量没有达到限购数量，走优惠
                                if (itemCalcVO.getAmount() <= activitySkuVO.getActualQuantity()){
                                    // 活动库存大于等于购买数量
                                    discountNum = quantity + itemCalcVO.getAmount() <= activitySkuVO.getLimitQuantity()?itemCalcVO.getAmount():activitySkuVO.getLimitQuantity()-quantity;
                                }else {
                                    // 活动库存小于购买数量
                                    discountNum = quantity + activitySkuVO.getActualQuantity() <= activitySkuVO.getLimitQuantity()?activitySkuVO.getActualQuantity():activitySkuVO.getLimitQuantity()-quantity;
                                }
                                actualTotalPrice = activityPrice.multiply(BigDecimal.valueOf(discountNum));
                                subtract = itemCalcVO.getActualTotalOriginalPrice().subtract(discountNum.intValue()==itemCalcVO.getAmount()?actualTotalPrice:itemCalcVO.getOriginalPrice().multiply(BigDecimal.valueOf(itemCalcVO.getAmount()-discountNum)).add(actualTotalPrice));
                            }
                        }else {
                            //不设置用户限购且活动库存>0计算优惠数量
                            discountNum = activitySkuVO.getActualQuantity()>=itemCalcVO.getAmount()?itemCalcVO.getAmount():activitySkuVO.getActualQuantity();
                            actualTotalPrice = activityPrice.multiply(BigDecimal.valueOf(discountNum));
                            subtract = itemCalcVO.getActualTotalOriginalPrice().subtract(discountNum.intValue()==itemCalcVO.getAmount()?actualTotalPrice:itemCalcVO.getOriginalPrice().multiply(BigDecimal.valueOf(itemCalcVO.getAmount()-discountNum)).add(actualTotalPrice));
                        }
                    }
                }else {
                    if (activitySkuVO.getLimitQuantity() > 0){
                        Integer quantity = quantityPurchased;
                        if (activitySkuVO.getLimitQuantity() <= quantity){
                            // 用户购买数量达到限购数量，不走优惠
                            log.info("用户购买数量达到限购数量，不走优惠。限购数量：{}，已购买数量quantity：{}", activitySkuVO.getLimitQuantity(), quantity);
                            return true;
                        }else {
                            // 用户购买数量没有达到限购数量，走优惠
                            discountNum = quantity + itemCalcVO.getAmount() <= activitySkuVO.getLimitQuantity()?itemCalcVO.getAmount():activitySkuVO.getLimitQuantity()-quantity;
                            actualTotalPrice = activityPrice.multiply(BigDecimal.valueOf(discountNum));
                            subtract = itemCalcVO.getActualTotalOriginalPrice().subtract(discountNum.intValue()==itemCalcVO.getAmount()?actualTotalPrice:itemCalcVO.getOriginalPrice().multiply(BigDecimal.valueOf(itemCalcVO.getAmount()-discountNum)).add(actualTotalPrice));
                        }
                    }else {
                        //活动库存和用户限购都没有设置
                         discountNum = itemCalcVO.getAmount();
                         actualTotalPrice = activityPrice.multiply(BigDecimal.valueOf(discountNum));
                         subtract = itemCalcVO.getActualTotalOriginalPrice().subtract(discountNum.intValue()==itemCalcVO.getAmount()?actualTotalPrice:itemCalcVO.getOriginalPrice().multiply(BigDecimal.valueOf(itemCalcVO.getAmount()-discountNum)).add(actualTotalPrice));
                    }
                }
                log.info("用户：{}下单活动优惠明细，sku：{} 活动ID：{}，优惠数量：{}，购买数量：{}，活动价格：{}，平台：{}",
                        RequestHolder.getMId(),itemCalcVO.getSku(),activitySkuVO.getBasicInfoId(),discountNum,itemCalcVO.getAmount(),activityPrice, activitySkuVO.getPlatform());

                itemCalcVO.decreaseActualTotalPrice(subtract);
                itemCalcVO.decreaseCalcPartDeliveryFee(subtract);

                BigDecimal orderFinalSubtract = subtract;
                OrderPreferentialTypeEnum orderPreferentialTypeEnum = OrderPreferentialTypeEnum.getActivityPreference(
                        activitySkuVO.getPlatform());
                resultVO.addOrderPreferential(() -> {
                    OrderPreferential orderPreferential = new OrderPreferential(orderPreferentialTypeEnum, orderFinalSubtract);
                    orderPreferential.setRelatedId(activitySkuVO.getBasicInfoId());
                    orderPreferential.setActivityName(activitySkuVO.getActivityName());
                    return orderPreferential;
                });

                //添加订单项优惠明细
                BigDecimal itemFinalSubtract = subtract;
                itemCalcVO.addItemPreferential(() -> {
                    OrderItemPreferential orderPreferential = new OrderItemPreferential(orderPreferentialTypeEnum, itemFinalSubtract);
                    orderPreferential.setRelatedId(activitySkuVO.getBasicInfoId().longValue());
                    orderPreferential.setCreateTime(LocalDateTime.now());
                    return orderPreferential;
                });

                if (placeOrderVO.getTakePriceFlag()){
                    //到手价明细返回
                    if (itemFinalSubtract.compareTo(BigDecimal.ZERO) != 0){
                        itemCalcVO.addSkuPreferential(() -> {
                            SkuPreferentialVO skuPreferentialVO = new SkuPreferentialVO(orderPreferentialTypeEnum, itemFinalSubtract);
                            skuPreferentialVO.setValue(activityPrice);
                            return skuPreferentialVO;
                        });
                    }

                }

                //添加活动优惠用户购买数量记录->用于活动库存和限购数量
                Integer buyQuantity = 0;
                Integer remainQuantity = 0;
                if (activitySkuVO.getLimitQuantity() > 0){
                    buyQuantity = quantityPurchased;
                    if (null != activitySkuVO.getActualQuantity()){
                        remainQuantity = activitySkuVO.getActualQuantity()<=(activitySkuVO.getLimitQuantity()-buyQuantity)?activitySkuVO.getActualQuantity():activitySkuVO.getLimitQuantity()-buyQuantity;
                    }else {
                        remainQuantity = activitySkuVO.getLimitQuantity()-buyQuantity;
                    }
                }

                itemCalcVO.setActivitySkuPurchaseQuantity(new ActivitySkuPurchaseQuantity(RequestHolder.getMId(),itemCalcVO.getSku(),activitySkuVO.getBasicInfoId(),discountNum,activitySkuVO.getLimitQuantity() > 0?remainQuantity:null));

                return true;
            }
        }
        return false;
    }

    private boolean handleOrderItemIsTakePrice(ActivitySkuDetailDTO activitySkuVO, OrderItemCalcDTO itemCalcVO, PlaceOrderVO placeOrderVO, OrderResultVO resultVO) {
        if (activitySkuVO != null && itemCalcVO.getActivityPrice() != null) {
            //获取sku上组装的活动价格
            BigDecimal activityPrice = itemCalcVO.getActivityPrice();

            BigDecimal actualTotalPrice = BigDecimal.ZERO;
            BigDecimal subtract = BigDecimal.ZERO;
            //优惠数量
            Integer discountNum = 0;

            //活动已经购买数量
            Integer quantityPurchased = 0;
            if (activityPrice.compareTo(itemCalcVO.getOriginalPrice()) <= 0) {

                //计算活动已经购买数量
                if (activitySkuVO.getLimitQuantity() > 0) {
                    if (activitySkuVO.getPurchasedQuantity() != null) {
                        log.info("从上游活动信息中获取用户已经购买活动数量，quantityPurchased：{}", activitySkuVO.getPurchasedQuantity());
                        quantityPurchased = activitySkuVO.getPurchasedQuantity();
                    } else {
                        // 用户限购不为空且活动库存>0
                        List<ActivitySkuPurchaseQuantity> purchaseQuantities = null;
                        //判断限购类型
                        Integer accountLimit = activitySkuVO.getAccountLimit();

                        List<ActivitySkuPurchaseQuantity> allPurchaseQuantities;
                        if (accountLimit == 1) {
                            //件数限购
                            allPurchaseQuantities = activitySkuService.selectPurchaseQuantityByCache(RequestHolder.getMId(), activitySkuVO.getBasicInfoId());
                        } else {
                            //日限购
                            allPurchaseQuantities = activitySkuService.selectPurchaseQuantityTodayByCache(RequestHolder.getMId(), activitySkuVO.getBasicInfoId());
                        }
                        Map<String, List<ActivitySkuPurchaseQuantity>> listMap = allPurchaseQuantities.stream().collect(Collectors.groupingBy(ActivitySkuPurchaseQuantity::getSku));
                        if (!CollectionUtils.isEmpty(listMap) && listMap.containsKey(itemCalcVO.getSku())) {
                            purchaseQuantities = listMap.get(itemCalcVO.getSku());
                        }

                        if (CollectionUtil.isNotEmpty(purchaseQuantities)) {
                            quantityPurchased = purchaseQuantities.stream().mapToInt(ActivitySkuPurchaseQuantity::getPurchaseQuantity).sum();
                        }
                    }
                }

                if (null != activitySkuVO.getActualQuantity()) {
                    if (activitySkuVO.getActualQuantity() == 0) {
                        // 活动库存为0，不走优惠
                        return true;
                    } else {
                        // 活动库存>0 计算优惠数量
                        if (activitySkuVO.getLimitQuantity() > 0) {
                            Integer quantity = quantityPurchased;
                            if (activitySkuVO.getLimitQuantity() <= quantity) {
                                // 用户购买数量达到限购数量，不走优惠
                                log.info("用户购买数量达到限购数量，不走优惠。限购数量：{}，已购买数量quantity：{}", activitySkuVO.getLimitQuantity(), quantity);
                                return true;
                            } else {
                                // 用户购买数量没有达到限购数量，走优惠
                                if (itemCalcVO.getAmount() <= activitySkuVO.getActualQuantity()) {
                                    // 活动库存大于等于购买数量
                                    discountNum = quantity + itemCalcVO.getAmount() <= activitySkuVO.getLimitQuantity() ? itemCalcVO.getAmount() : activitySkuVO.getLimitQuantity() - quantity;
                                } else {
                                    // 活动库存小于购买数量
                                    discountNum = quantity + activitySkuVO.getActualQuantity() <= activitySkuVO.getLimitQuantity() ? activitySkuVO.getActualQuantity() : activitySkuVO.getLimitQuantity() - quantity;
                                }
                                actualTotalPrice = activityPrice.multiply(BigDecimal.valueOf(discountNum));
                                subtract = itemCalcVO.getActualTotalOriginalPrice().subtract(discountNum.intValue() == itemCalcVO.getAmount() ? actualTotalPrice : itemCalcVO.getOriginalPrice().multiply(BigDecimal.valueOf(itemCalcVO.getAmount() - discountNum)).add(actualTotalPrice));
                            }
                        } else {
                            //不设置用户限购且活动库存>0计算优惠数量
                            discountNum = activitySkuVO.getActualQuantity() >= itemCalcVO.getAmount() ? itemCalcVO.getAmount() : activitySkuVO.getActualQuantity();
                            actualTotalPrice = activityPrice.multiply(BigDecimal.valueOf(discountNum));
                            subtract = itemCalcVO.getActualTotalOriginalPrice().subtract(discountNum.intValue() == itemCalcVO.getAmount() ? actualTotalPrice : itemCalcVO.getOriginalPrice().multiply(BigDecimal.valueOf(itemCalcVO.getAmount() - discountNum)).add(actualTotalPrice));
                        }
                    }
                } else {
                    if (activitySkuVO.getLimitQuantity() > 0) {
                        Integer quantity = quantityPurchased;
                        if (activitySkuVO.getLimitQuantity() <= quantity) {
                            // 用户购买数量达到限购数量，不走优惠
                            log.info("用户购买数量达到限购数量，不走优惠。限购数量：{}，已购买数量quantity：{}", activitySkuVO.getLimitQuantity(), quantity);
                            return true;
                        } else {
                            // 用户购买数量没有达到限购数量，走优惠
                            discountNum = quantity + itemCalcVO.getAmount() <= activitySkuVO.getLimitQuantity() ? itemCalcVO.getAmount() : activitySkuVO.getLimitQuantity() - quantity;
                            actualTotalPrice = activityPrice.multiply(BigDecimal.valueOf(discountNum));
                            subtract = itemCalcVO.getActualTotalOriginalPrice().subtract(discountNum.intValue() == itemCalcVO.getAmount() ? actualTotalPrice : itemCalcVO.getOriginalPrice().multiply(BigDecimal.valueOf(itemCalcVO.getAmount() - discountNum)).add(actualTotalPrice));
                        }
                    } else {
                        //活动库存和用户限购都没有设置
                        discountNum = itemCalcVO.getAmount();
                        actualTotalPrice = activityPrice.multiply(BigDecimal.valueOf(discountNum));
                        subtract = itemCalcVO.getActualTotalOriginalPrice().subtract(discountNum.intValue() == itemCalcVO.getAmount() ? actualTotalPrice : itemCalcVO.getOriginalPrice().multiply(BigDecimal.valueOf(itemCalcVO.getAmount() - discountNum)).add(actualTotalPrice));
                    }
                }
                log.info("用户：{}下单活动优惠明细，sku：{} 活动ID：{}，优惠数量：{}，购买数量：{}，活动价格：{}，平台：{}",
                        RequestHolder.getMId(), itemCalcVO.getSku(), activitySkuVO.getBasicInfoId(), discountNum, itemCalcVO.getAmount(), activityPrice, activitySkuVO.getPlatform());

                itemCalcVO.decreaseActualTotalPrice(subtract);
                itemCalcVO.decreaseCalcPartDeliveryFee(subtract);

                BigDecimal orderFinalSubtract = subtract;
                OrderPreferentialTypeEnum orderPreferentialTypeEnum = OrderPreferentialTypeEnum.getActivityPreference(
                        activitySkuVO.getPlatform());
                resultVO.addOrderPreferential(() -> {
                    OrderPreferential orderPreferential = new OrderPreferential(orderPreferentialTypeEnum, orderFinalSubtract);
                    orderPreferential.setRelatedId(activitySkuVO.getBasicInfoId());
                    orderPreferential.setActivityName(activitySkuVO.getActivityName());
                    return orderPreferential;
                });

                //添加订单项优惠明细
                BigDecimal itemFinalSubtract = subtract;
                itemCalcVO.addItemPreferential(() -> {
                    OrderItemPreferential orderPreferential = new OrderItemPreferential(orderPreferentialTypeEnum, itemFinalSubtract);
                    orderPreferential.setRelatedId(activitySkuVO.getBasicInfoId().longValue());
                    orderPreferential.setCreateTime(LocalDateTime.now());
                    return orderPreferential;
                });

                if (placeOrderVO.getTakePriceFlag()) {//NOSONAR
                    //到手价明细返回
                    if (itemFinalSubtract.compareTo(BigDecimal.ZERO) != 0) {
                        itemCalcVO.addSkuPreferential(() -> {
                            SkuPreferentialVO skuPreferentialVO = new SkuPreferentialVO(orderPreferentialTypeEnum, itemFinalSubtract);
                            skuPreferentialVO.setValue(activityPrice);
                            return skuPreferentialVO;
                        });
                    }

                }

                //添加活动优惠用户购买数量记录->用于活动库存和限购数量
                Integer buyQuantity = 0;
                Integer remainQuantity = 0;
                if (activitySkuVO.getLimitQuantity() > 0) {
                    buyQuantity = quantityPurchased;
                    if (null != activitySkuVO.getActualQuantity()) {
                        remainQuantity = activitySkuVO.getActualQuantity() <= (activitySkuVO.getLimitQuantity() - buyQuantity) ? activitySkuVO.getActualQuantity() : activitySkuVO.getLimitQuantity() - buyQuantity;
                    } else {
                        remainQuantity = activitySkuVO.getLimitQuantity() - buyQuantity;
                    }
                }
                itemCalcVO.setActivitySkuPurchaseQuantity(new ActivitySkuPurchaseQuantity(RequestHolder.getMId(), itemCalcVO.getSku(), activitySkuVO.getBasicInfoId(), discountNum, activitySkuVO.getLimitQuantity() > 0 ? remainQuantity : null));
                return true;
            }
        }
        return false;
    }
}
