package net.summerfarm.mall.order.item;

import java.math.BigDecimal;
import net.summerfarm.enums.SkuTypeEnum;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;
import net.summerfarm.mall.model.domain.OrderItemPreferential;
import net.summerfarm.mall.model.domain.OrderPreferential;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.order.AbstractOrderItemHandler;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 代仓计算逻辑
 */
@Service
public class AgentHandler extends AbstractOrderItemHandler {

    @Override
    public boolean handleOrderItem(OrderItemCalcDTO itemCalcVO, PlaceOrderVO placeOrderVO, OrderResultVO resultVO) {
        if (Objects.equals(itemCalcVO.getSkuType(), SkuTypeEnum.AGENT.ordinal())) {
            //下单时支付，实付价不处理
            if (itemCalcVO.getAgentPayMethod() != null && Objects.equals(itemCalcVO.getAgentPayMethod(), 1)) {
               return true;
            }
            BigDecimal actualTotalPrice = itemCalcVO.getActualTotalPrice();
            //代仓实付价为0，原价参与优惠门槛计算
            itemCalcVO.decreaseActualTotalPrice(actualTotalPrice);
            //添加订单项优惠明细
            itemCalcVO.addItemPreferential(() -> {
                OrderItemPreferential orderPreferential = new OrderItemPreferential(OrderPreferentialTypeEnum.AGENT,
                        actualTotalPrice);
                orderPreferential.setCreateTime(LocalDateTime.now());
                return orderPreferential;
            });

            resultVO.addOrderPreferential(() -> {
                OrderPreferential orderPreferential = new OrderPreferential(OrderPreferentialTypeEnum.AGENT,
                        actualTotalPrice);
                return orderPreferential;
            });
            return true;
        }

        return false;
    }
}
