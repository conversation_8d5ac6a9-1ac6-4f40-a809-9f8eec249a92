package net.summerfarm.mall.order.item;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.ExchangeBaseStatusEnum;
import net.summerfarm.mall.enums.ExchangeScopeStatusEnum;
import net.summerfarm.mall.enums.OrderPreferentialTypeEnum;
import net.summerfarm.mall.enums.ProductTypeEnum;
import net.summerfarm.mall.mapper.ExchangeBaseInfoMapper;
import net.summerfarm.mall.mapper.ExchangeItemConfigMapper;
import net.summerfarm.mall.mapper.ExchangeScopeConfigMapper;
import net.summerfarm.mall.mapper.offline.ExchangeItemMapper;
import net.summerfarm.mall.model.domain.ExchangeBaseInfo;
import net.summerfarm.mall.model.domain.ExchangeItem;
import net.summerfarm.mall.model.domain.ExchangeItemConfig;
import net.summerfarm.mall.model.domain.ExchangeScopeConfig;
import net.summerfarm.mall.model.domain.OrderItemPreferential;
import net.summerfarm.mall.model.domain.OrderPreferential;
import net.summerfarm.mall.model.dto.market.exchange.ExchangeItemDTO;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.order.AbstractOrderItemHandler;
import net.summerfarm.mall.service.helper.ExchangeBuyServiceHelper;
import org.springframework.stereotype.Service;

/**
 * 换购商品订单项处理
 *
 * @author: <EMAIL>
 * @create: 2022/9/18
 */
@Service
@Slf4j
public class ExchangeBuyHandler extends AbstractOrderItemHandler {

    @Resource
    private ExchangeScopeConfigMapper exchangeScopeConfigMapper;

    @Resource
    private ExchangeBaseInfoMapper exchangeBaseInfoMapper;

    @Resource
    private ExchangeItemConfigMapper exchangeItemConfigMapper;

    @Resource
    private ExchangeItemMapper exchangeItemMapper;

    @Resource
    private ExchangeBuyServiceHelper exchangeBuyServiceHelper;

    @Override
    public boolean handleOrderItem(OrderItemCalcDTO itemCalcVO, PlaceOrderVO placeOrderVO,
            OrderResultVO resultVO) {
        if (Objects.equals(itemCalcVO.getProductType(), ProductTypeEnum.EXCHANGE.getCode())) {
            Long scopeConfigId = placeOrderVO.getBizId();
            //校验活动是否生效
            ProductInfoVO productInfoVO = exchangeBuyServiceHelper.checkAndGetProductInfo(itemCalcVO, scopeConfigId, placeOrderVO.getTakePriceFlag());
            if (productInfoVO == null) {
                throw new DefaultServiceException(ResultConstant.ORDER_FAILED, "下单失败，存在失效商品");
            }
            BigDecimal actualTotalPrice = BigDecimal.valueOf(productInfoVO.getSalePrice())
                    .multiply(BigDecimal.valueOf(itemCalcVO.getAmount()));
            itemCalcVO.setActualTotalPrice(actualTotalPrice);
            itemCalcVO.setCalcPartDeliveryFee(actualTotalPrice);

            BigDecimal subtract = itemCalcVO.getActualTotalOriginalPrice()
                    .subtract(itemCalcVO.getActualTotalPrice());
            //添加订单项优惠明细
            itemCalcVO.addItemPreferential(() -> {
                OrderItemPreferential orderPreferential = new OrderItemPreferential(
                        OrderPreferentialTypeEnum.EXCHANGE_BUY, subtract);
                orderPreferential.setActivityName(productInfoVO.getExchangeBuyId());
                orderPreferential.setCreateTime(LocalDateTime.now());
                return orderPreferential;
            });

            resultVO.addOrderPreferential(() -> {
                OrderPreferential orderPreferential = new OrderPreferential(OrderPreferentialTypeEnum.EXCHANGE_BUY, subtract);
                orderPreferential.setActivityName(productInfoVO.getExchangeBuyId());
                return orderPreferential;
            });
            return true;
        }
        return false;
    }

    /**
     * 校验商品是否在换购活动内
     *
     * @param itemCalcVO
     * @param scopeConfigId
     * @return
     */
    public ProductInfoVO checkAndGetProductInfo(OrderItemCalcDTO itemCalcVO, Long scopeConfigId) {
        ExchangeScopeConfig scopeConfig = exchangeScopeConfigMapper.selectByPrimaryKey(
                scopeConfigId);
        if (!Objects.equals(scopeConfig.getStatus(), ExchangeScopeStatusEnum.OPEN.getCode())) {
            throw new DefaultServiceException(ResultConstant.ORDER_FAILED, "下单失败，换购活动失效");
        }
        Long baseInfoId = scopeConfig.getBaseInfoId();
        ExchangeBaseInfo exchangeBaseInfo = exchangeBaseInfoMapper.selectById(baseInfoId);
        if (exchangeBaseInfo == null) {
            throw new DefaultServiceException(ResultConstant.ORDER_FAILED, "下单失败，换购活动失效");
        }
        ProductInfoVO productInfoVO = null;
        //判断换购sku是配置的还是算法统计的
        String sku = itemCalcVO.getSku();
        ExchangeItemConfig itemConfig = exchangeItemConfigMapper.selectBySku(scopeConfigId, sku);
        if (itemConfig != null) {
            //计算换购价
            List<ProductInfoVO> list = exchangeBuyServiceHelper.buildProductInfos(
                    Lists.newArrayList(itemConfig), exchangeBaseInfo, true);
            if (CollectionUtil.isNotEmpty(list)) {
                productInfoVO = list.get(0);
            }
            return productInfoVO;
        }

        ExchangeItemDTO exchangeItemDTO = ExchangeItemDTO.builder().mId(RequestHolder.getMId())
                .sku(sku).build();
        List<ExchangeItem> items = exchangeItemMapper.selectByQuery(exchangeItemDTO);
        if (CollectionUtil.isNotEmpty(items)) {
            //计算换购价
            List<ProductInfoVO> list = exchangeBuyServiceHelper.buildProductInfos(items,
                    exchangeBaseInfo, true);
            if (CollectionUtil.isNotEmpty(list)) {
                productInfoVO = list.get(0);
            }
        }
        return productInfoVO;
    }
}
