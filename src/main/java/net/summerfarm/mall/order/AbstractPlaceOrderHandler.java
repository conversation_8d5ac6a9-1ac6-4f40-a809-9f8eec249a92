package net.summerfarm.mall.order;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.summerfarm.mall.model.dto.order.PlaceOrderCalcDTO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;

/**
 * <AUTHOR>
 * @Description 下单订单项处理抽象类
 */
public abstract class AbstractPlaceOrderHandler {
    /**
     * 处理优惠
     * @param orderCalcDTO 订单
     * @param resultVO 订单优惠信息
     * @return 处理结果
     */
    public abstract boolean handlePlaceOrder(PlaceOrderCalcDTO orderCalcDTO, OrderResultVO resultVO);
}
