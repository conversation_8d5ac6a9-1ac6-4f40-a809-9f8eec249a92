package net.summerfarm.mall.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.delayqueue.DelayQueueItem;
import net.summerfarm.mall.common.delayqueue.OrderCancelItem;
import net.summerfarm.mall.common.delayqueue.VirtualOrderCancelItem;
import net.summerfarm.mall.enums.StockChangeType;
import net.summerfarm.mall.model.domain.Contact;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.domain.QuantityChangeRecord;
import net.summerfarm.mall.model.dto.order.DeliveryPointDTO;
import net.summerfarm.mall.model.input.OrderBillExportReq;
import net.summerfarm.mall.model.vo.*;
import net.xianmu.common.result.CommonResult;
import org.jdom.JDOMException;

import java.io.IOException;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Package: net.summerfarm.trade.service
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/9/30
 */
public interface OrderService {
    /**
     * 用户确认收货
     * @param orderno
     * @return
     */
    String userConfirm(String orderno);

    void orderConfirm(DeliveryPlanVO deliveryPlanVO);

    /**
     * 得到用户所有购物订单信息
     * @param pageIndex
     * @param pageSize
     * @param orderStatus
     * @return
     */
    AjaxResult getOrders(int pageIndex, int pageSize, Short orderStatus);

    AjaxResult orderDetails(String orderNo);



    /**
     * 超时关闭订单--全品类需求上完之后 该接口弃用
     */
    @Deprecated
    void timeOutClose(OrderCancelItem orderCancelItem);

    /**
     * 查询订单详情
     * @param orderNo
     * @return
     */
    AjaxResult selectOrderDetail(String orderNo);

    /**
     * 取消订单
     * @param orderNo
     * @return
     */
    AjaxResult cancelOrder(String orderNo) throws IOException, JDOMException;

    /**
     * 新增参数 地址id 通过地址id处理冻结库存
     *
     *
    */
    void updateStock(String sku, Integer areaNo, Integer quantity, String name, StockChangeType changeType, String recordNo, Map<String, QuantityChangeRecord> recordMap, Boolean handleReserve ,Long contactId);

    void updateScore(String orderNo, Long mId);

    List<OrderCancelItem> unpaidOrders();

    /**
     * 获取普通订单详情
     * @param orderNo
     * @return
     */
    OrderVO normalOrderDetail(String orderNo);

    Orders createDiscountCardOrder(Integer discountCardId);

    /**
    * 查询订单数量
    */
    AjaxResult sumNoPayOrders();

    /**
     * 查看是否是茶百道门店下安佳淡奶油的订单
     * @param orderNo
     */
    Boolean checkPush(String orderNo);

    /**
     * 处理水果类销量问题
     * @param orderNo
     */
    void addFruitSales(String orderNo);

    /**
     * 处理水果类销量问题
     * @param orderItemId
     * @@param areaNo
     */
    void reduceFruitSales(Long orderItemId, Integer areaNo);


    /**
     * 查询未支付虚拟商品订单
     * @return 订单信息
     */
    Collection<? extends DelayQueueItem> unpaidVirtualOrders();

    /**
     * 关闭虚拟商品定点杆
     * @param delayQueueItem 订单信息
     */
    void virtualTimeOutClose(VirtualOrderCancelItem delayQueueItem);

    void handleFruitSales(String orderNo);

    /**
     * 查看当前订单是否为用户当天第一个订单
     * @param orderNo 订单号
     * @param mId   用户id
     * @return true为是，false为不是
     */
    Boolean checkFirstOrderByMid(String orderNo,Long mId);

    /**
     * 查看当前用户T+1是否有省心送配送计划
     * @param contacts contacts
     * @param localDate   localDate
     * @param mId   mId
     * @return true为是，false为不是
     */
    Boolean checkDeliveryTime(List<Contact> contacts, LocalDate localDate,Long mId);

    /**
     * 获得司机配送实时点位
     * @param deliveryPointDTO 订单参数
     * @return 返回点位结果
     */
    AjaxResult getDeliveryPoint(DeliveryPointDTO deliveryPointDTO);

    /**
     * @return 返回可查看点位订单信息
     */
    CommonResult<OrderDeliveryVO> getOrderDelivery();

    /**
     * @description: 根据商户编号获取未完成订单数量-待配送、待收货
     * @author: lzh
     * @date: 2023/4/21 18:02
     * @param: [mId]
     * @return: java.lang.Integer
     **/
    Integer getUnfilledOrderByMid(Long mId);

    /**
     * @description: 单店、现结门店
     * @author: lzh
     * @date: 2023/6/7 18:40
     * @param: [orderBillExportReq]
     * @return: java.lang.Boolean
     **/
    Boolean billExport(OrderBillExportReq orderBillExportReq);

    /**
     * @description: 单店账单导出并发送邮件
     * @author: lzh
     * @date: 2023/6/8 14:40
     * @param: [orderBillExportReq]
     * @return: void
     **/
    void orderBillExportAndSendMail(OrderBillExportReq orderBillExportReq);

    /**
     * 判断订单是否为代销不入库订单：t、代销不入库 f、非代销不入库
     * @param orderNo 订单号
     * @return true代销不入库，false非代销不入库
     */
    boolean isNotWarehouseIntoOrder(String orderNo);

    /**
     * 获取省心送规则 -- 默认走本地缓存1min
     * @param timingRuleId 省心送规则id
     * @return 省心送规则
     */
    TimingRuleVO getTimingRuleVOByCache(Integer timingRuleId);
}
