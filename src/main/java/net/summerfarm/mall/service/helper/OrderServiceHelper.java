package net.summerfarm.mall.service.helper;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.message.client.resp.MsgSendLogResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.UnsafeUtil;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.OrderSaleType;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.constant.Constants;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.facade.ofc.OfcLogisticsQueryFacade;
import net.summerfarm.mall.facade.ofc.dto.OrderFulfillmentInfo;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.bo.coupon.CouponSenderBO;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.tms.DistOrderDTO;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.cache.DeliveryEvaluationCache;
import net.summerfarm.mall.service.facade.TmsDistOrderDetailFacade;
import net.summerfarm.mall.service.facade.WncDeliveryFenceQueryFacade;
import net.summerfarm.mall.service.facade.dto.FenceCloseTimeReq;
import net.summerfarm.mall.service.strategy.coupon.CouponSenderContext;
import net.summerfarm.mall.task.AsyncTaskService;
import net.summerfarm.mall.wechat.templatemessage.ArrivalNoticeMsg;
import net.summerfarm.mall.wechat.templatemessage.TemplateMsgSender;
import net.summerfarm.mall.wechat.templatemessage.msgTemplate.MsgTemplateFactory;
import net.summerfarm.tms.client.enums.SourceEnum;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.mall.common.util.CommonToolUtil.collectExceptionStackMsg;

/**
 * OrderService辅助处理类
 *
 * @author: <EMAIL>
 * @create: 2022/6/27
 */
@Slf4j
@Component
public class OrderServiceHelper {

    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private MarketRuleHistoryMapper marketRuleHistoryMapper;
    @Resource
    private CouponMapper couponMapper;
    @Resource
    private TimingOrderMapper timingOrderMapper;
    @Resource
    private OrdersCouponMapper ordersCouponMapper;
    @Resource
    private OrderPreferentialMapper orderPreferentialMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private TemplateMsgSender templateMsgSender;
    @Resource
    private CouponSenderContext couponSenderContext;
    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;
    @Resource
    private AsyncTaskService asyncTaskService;
    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private ArrivalNoticeMapper arrivalNoticeMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private AreaStoreMallMapper areaStoreMallMapper;
    @Resource
    private DiscountCardUseRecordMapper discountCardUseRecordMapper;
    @Resource
    private DiscountCardToMerchantMapper discountCardToMerchantMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private FenceService fenceService;
    @Resource
    private DeliveryEvaluationCache deliveryEvaluationCache;
    @Resource
    private OrderItemPreferentialMapper orderItemPreferentialMapper;
    @Resource
    private TimingRuleMapper timingRuleMapper;
    @Resource
    private TmsDistOrderDetailFacade tmsDistOrderDetailFacade;
    @Resource
    DeliverPlanRemarkSnapshotService deliverPlanRemarkSnapshotService;
    @Resource
    private TimingOrderRefundWhiteListMapper timingOrderRefundWhiteListMapper;
    @Resource
    private OrderPreferentialService orderPreferentialService;
    @Resource
    private OrderItemPreferentialService orderItemPreferentialService;
    @Resource
    private OrderRelationMapper orderRelationMapper;
    @Resource
    private TimingOrderRefundTimeMapper timingOrderRefundTimeMapper;
    @Resource
    @Lazy
    private OrderNewService orderNewService;

    @Resource
    private WncDeliveryFenceQueryFacade wncDeliveryFenceQueryFacade;

    @Autowired
    private OfcLogisticsQueryFacade ofcLogisticsQueryFacade;

    /**
     * 首单发券
     *
     * @param mId       商户id
     * @param orderType 订单类型
     * @param orderNo   订单号
     */
    public void firstOrderSendCoupon(Long mId, Integer orderType, String orderNo) {
        if (!OrderTypeEnum.isAllowInviteFirstOrderType(orderType)) {
            log.info("当前订单{}类型{}不符合邀请首单赠券", orderNo, orderType);
            return;
        }
        Merchant merchant = merchantMapper.selectOneByMid(mId);
        int isNewHand = ordersMapper.countOrderByMid(mId);
        if (Global.BIG_MERCHANT.equals(merchant.getSize()) || isNewHand > 1) {
            return;
        }
        //发放邀请首单奖励优惠券
        if (!StringUtils.isBlank(merchant.getInviterChannelCode())) {
            Map<String, Object> selectKeys = new HashMap<>();
            selectKeys.put("channelCode", merchant.getInviterChannelCode());
            Merchant inviter = merchantMapper.selectOne(selectKeys);
            if (inviter == null) {
                log.info("未查询到当前订单{}商户{}的邀请人，不执行发券", orderNo, mId);
                return;
            }
            if (merchant.getmId().longValue() == inviter.getmId()) {
                log.info("邀请人和被邀请人商户号一致，不执行发券，商户:{}", inviter.getMcontact());
                return;
            }
            CouponSenderBO couponSenderBO = new CouponSenderBO();
            couponSenderBO.setSenderType(CouponSenderSetupSenderTypeEnum.RECOMMENDED_ORDER);
            couponSenderBO.setMId(inviter.getmId());
            log.info("订单{}为首单,开始自动给被邀请人发放优惠券", orderNo);
            couponSenderContext.sendCoupon(couponSenderBO);
        }
    }

    /**
     * 处理订单中用的商家优惠券关系
     * @param orderVO
     */
    public void handleOrdersCoupon(OrderVO orderVO) {
        Map select = new HashMap();
        select.put("orderNo", orderVO.getOrderNo());
        // 查询订单中用的商家优惠券关系
        List<MerchantCouponVO> couponVOS = ordersCouponMapper.select(select);
        if (!CollectionUtils.isEmpty(couponVOS)) {
            for (MerchantCouponVO couponVO : couponVOS) {
                if (couponVO == null) {
                    continue;
                }
                if (couponVO.getAgioType() == 1) {
                    orderVO.setCoupon(orderVO.getCoupon().add(couponVO.getMoney()));
                    orderVO.setCouponName(couponVO.getName());
                } else if (couponVO.getAgioType() == 4) {  //红包
                    orderVO.setRpCoupon(orderVO.getRpCoupon().add(couponVO.getMoney()));
                    orderVO.setRpCouponName(couponVO.getName());
                } else if (couponVO.getAgioType() == 5) { //兑换券
                    List<OrderItemPreferential> itemPreferentials = orderItemPreferentialMapper.queryByOrderNo(orderVO.getOrderNo());
                    for (OrderItemPreferential el : itemPreferentials) {
                        if (Objects.equals(couponVO.getId().longValue(), el.getRelatedId())) {
                            orderVO.setCoupon(orderVO.getRpCoupon().add(el.getAmount()));
                            break;
                        }
                    }
                } else {
                    orderVO.setDeliveryCoupon(orderVO.getDeliveryCoupon().add(couponVO.getMoney()));
                }
            }
        }
    }

    public void handleOrdersCouponV2(OrderVO orderVO, String masterOrderNo, List<OrderPreferential> orderPreferentialList) {
        // 查询订单中用的优惠券
        Map select = new HashMap();
        select.put("orderNo", masterOrderNo);
        List<MerchantCouponVO> couponList = ordersCouponMapper.select(select);

        //订单未使用优惠券
        if (CollectionUtils.isEmpty(couponList)) {
            return;
        }

        //查询订单使用的所有优惠券信息
        Map<Long, BigDecimal> preferentialMap = orderPreferentialList.stream()
                .filter(el -> OrderPreferentialTypeEnum.isCoupon(el.getType()))
                .collect(Collectors.toMap(OrderPreferential::getRelatedId, OrderPreferential::getAmount));

        // 1-商家优惠券 2-平台优惠券 3-运费券 4-红包 5-兑换券
        for (MerchantCouponVO couponVO : couponList) {
            if (couponVO == null) {
                continue;
            }

            //兑换券金额处理，需要在订单项优惠明细上计算优惠金额
            if (couponVO.getAgioType() != null && couponVO.getAgioType() == 5) {
                OrderItemPreferential condition = new OrderItemPreferential();
                condition.setOrderNo(orderVO.getOrderNo());
                condition.setType(OrderPreferentialTypeEnum.COUPON.ordinal());
                List<OrderItemPreferential> itemPreferentialList = orderItemPreferentialService.selectSelective(condition);
                for (OrderItemPreferential el : itemPreferentialList) {
                    if (Objects.equals(couponVO.getId().longValue(), el.getRelatedId())) {
                        orderVO.setCoupon(orderVO.getCoupon().add(el.getAmount()));
                        orderVO.setCouponName(couponVO.getName());
                        break;
                    }
                }
                return;
            }

            BigDecimal couponMoney = preferentialMap.get(couponVO.getId().longValue());
//            BigDecimal couponMoney = preferentialMap.getOrDefault(couponVO.getId().longValue(), BigDecimal.ZERO);
            if (couponMoney == null) {
                continue;
            }

            //其他类型券处理，直接在订单优惠明细上计算优惠金额
            if (couponVO.getAgioType() == 1) {
                orderVO.setCoupon(orderVO.getCoupon().add(couponMoney));
                orderVO.setCouponName(couponVO.getName());
            } else if (couponVO.getAgioType() == 4) {
                orderVO.setRpCoupon(orderVO.getRpCoupon().add(couponMoney));
                orderVO.setRpCouponName(couponVO.getName());
            } else if (couponVO.getAgioType() == 3) {
                orderVO.setPrecisionDeliveryCoupon(orderVO.getPrecisionDeliveryCoupon().add(couponMoney));
                orderVO.setPrecisionDeliveryCouponName(couponVO.getName());
            } else {
                orderVO.setDeliveryCouponName(couponVO.getName());
                orderVO.setDeliveryCoupon(orderVO.getDeliveryCoupon().add(couponMoney));
            }
        }
    }

    /**
     * 处理配送订单列表页配送计划
     *
     * @param orderVO
     * @param deliveryPlanVOS
     */
    public void handleDeliveryPlans(OrderVO orderVO, List<DeliveryPlanVO> deliveryPlanVOS) {
        DeliveryPlanVO deliveryPlanVO = deliveryPlanVOS.get(0);
        if (deliveryPlanVO.getInterceptFlag() == 1) {
            orderVO.setDeliveryStatus(DeliveryStatusEnum.INTERCEPT.getStatus());
        } else {
            boolean isTimingOrder = OrderTypeEnum.TIMING.getId().equals(orderVO.getType());
            Integer source = isTimingOrder ? SourceEnum.XM_MALL_TIMING.getValue() : SourceEnum.XM_MALL.getValue();
            DistOrderDTO distOrderDTO = tmsDistOrderDetailFacade.queryDistOrderDetail(orderVO.getOrderNo(),
                    deliveryPlanVO.getContactId(), deliveryPlanVO.getDeliveryTime(), source);
            if (distOrderDTO != null && distOrderDTO.getStatus() != null) {
                orderVO.setDeliveryStatus(TmsDistOrderStatusEnum.getDistOrderStatus(distOrderDTO.getStatus()));
            } else {
                orderVO.setDeliveryStatus(DeliveryStatusEnum.TO_BE_WIRED.getStatus());
            }
            if (!OrderTypeEnum.TIMING.getId().equals(orderVO.getType())){
                orderVO.setDeliveryEvaluationStatus(
                        getDeliveryEvlStatus(
                                orderVO.getAreaNo(),
                                deliveryPlanVO.getStatus(),
                                deliveryPlanVO.getDeliveryEvaluationStatus(),
                                deliveryPlanVO.getDeliveryTime()));
                orderVO.setDeliveryPlanId(deliveryPlanVO.getId());
                orderVO.setDeliveryTime(deliveryPlanVO.getDeliveryTime());
            }
        }

        //校验截单时间--改成tms查询
        /*WarehouseLogisticsCenter wlc = warehouseLogisticsCenterMapper.selectByStoreNo(
                deliveryPlanVO.getOrderStoreNo());
        log.info("wlc:" + wlc.getCloseTime());
        Date orderTime = orderVO.getOrderTime();
        SimpleDateFormat hms = new SimpleDateFormat("HH:mm:ss");
        String closeTime = wlc.getCloseTime();
        String orderFormat = hms.format(orderTime);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat ds = new SimpleDateFormat("yyyy-MM-dd ");
        log.info("orderTime:" + ds.format(orderTime));
        try {
            Date orderHms = hms.parse(orderFormat);
            Date closeHms = hms.parse(closeTime);
            //如果超过截单时间，日期加一天
            if (closeHms.compareTo(orderHms) < 0) {
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(orderTime);
                //把日期往后增加一天,整数  往后推,负数往前移动
                calendar.add(Calendar.DATE, 1);
                orderTime = calendar.getTime();
            }
            Date startTime = df.parse(ds.format(orderTime) + wlc.getCloseTime());
            Date date = new Date();
            int i = date.compareTo(startTime);
            log.info(i + "");
            if (i < 0) {
                orderVO.setDeliveryStatus(DeliveryStatusEnum.NOT_YET.getStatus());
            }
        } catch (ParseException e) {
            log.info("日期转换失败", e);
        }*/
    }

    /**
     * 处理配送计划
     *
     * @param orderDeliveryVO
     * @param deliveryPlanVOS
     */
    public void handleOrderDeliveryPlans(OrderDeliveryVO orderDeliveryVO, List<DeliveryPlanVO> deliveryPlanVOS) {
        DeliveryPlanVO deliveryPlanVO = deliveryPlanVOS.get(0);
        Orders orders = ordersMapper.selectOne(deliveryPlanVO.getOrderNo());
        if (deliveryPlanVO.getInterceptFlag() == 1) {
            orderDeliveryVO.setDeliveryStatus(DeliveryStatusEnum.INTERCEPT.getStatus());
        } else {
            DistOrderDTO distOrderDTO = tmsDistOrderDetailFacade.queryDistOrderDetail(deliveryPlanVO.getOrderNo(),deliveryPlanVO.getContactId(),deliveryPlanVO.getDeliveryTime(), TmsSourceEnum.getDistOrderSource(orders.getType()));
            if (distOrderDTO != null && distOrderDTO.getStatus() != null) {
                orderDeliveryVO.setDeliveryStatus(TmsDistOrderStatusEnum.getDistOrderStatus(distOrderDTO.getStatus()));
            } else {
                orderDeliveryVO.setDeliveryStatus(DeliveryStatusEnum.TO_BE_WIRED.getStatus());
            }
        }

        //校验截单时间--改成tms查询
        /*WarehouseLogisticsCenter wlc = warehouseLogisticsCenterMapper.selectByStoreNo(
                deliveryPlanVO.getOrderStoreNo());
        log.info("wlc:" + wlc.getCloseTime());
        Date orderTime = orderDeliveryVO.getOrderTime();
        SimpleDateFormat hms = new SimpleDateFormat("HH:mm:ss");
        String closeTime = wlc.getCloseTime();
        String orderFormat = hms.format(orderTime);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat ds = new SimpleDateFormat("yyyy-MM-dd ");
        log.info("orderTime:" + ds.format(orderTime));
        try {
            Date orderHms = hms.parse(orderFormat);
            Date closeHms = hms.parse(closeTime);
            //如果超过截单时间，日期加一天
            if (closeHms.compareTo(orderHms) < 0) {
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(orderTime);
                //把日期往后增加一天,整数  往后推,负数往前移动
                calendar.add(Calendar.DATE, 1);
                orderTime = calendar.getTime();
            }
            Date startTime = df.parse(ds.format(orderTime) + wlc.getCloseTime());
            Date date = new Date();
            int i = date.compareTo(startTime);
            log.info(i + "");
            if (i < 0) {
                orderDeliveryVO.setDeliveryStatus(DeliveryStatusEnum.NOT_YET.getStatus());
            }
        } catch (ParseException e) {
            log.info("日期转换失败", e);
        }*/
    }

    /**
     * 判断是否在固定星期免配送费日期中
     *
     * @param freeDeliveryWeek
     * @return true-在免配送费周期中
     */
    public boolean isFreeDeliveryWeekDay(String freeDeliveryWeek, Long contactId) {
        if (StringUtils.isBlank(freeDeliveryWeek)) {
            return false;
        }
        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        if (Objects.isNull(contact)) {
            return false;
        }
        LocalTime closeTime = warehouseLogisticsService.selectCloseTime(contact.getStoreNo());
        List<String> freeDayList = Global.OMIT_EMPTY_SPLITTER.splitToList(freeDeliveryWeek);
        if (!CollectionUtils.isEmpty(freeDayList) && freeDayList.size() == Global.DAY_OF_WEEK_NUM) {
            return true;
        }
        DayOfWeek dayOfWeek = LocalDateTime.now().getDayOfWeek();
        if (!LocalTime.now().isBefore(closeTime)) {
            dayOfWeek = dayOfWeek.plus(1);
        }
        int freeDeliveryWeekDay = dayOfWeek.getValue();
        return CollectionUtils.contains(freeDayList.iterator(),
                String.valueOf(freeDeliveryWeekDay));
    }

    /**
     * 组装省心购订单详情
     *
     * @param orderNo
     * @return
     */
    public TimingOrderVO buildTimingOrderDetail(String orderNo) {
        TimingOrderVO timingOrderVO = timingOrderMapper.selectDetail(orderNo);
        Map select = new HashMap();
        select.put("orderNo", orderNo);
        List<MerchantCouponVO> couponVOS = ordersCouponMapper.select(select);
        if (!CollectionUtils.isEmpty(couponVOS)) {
            for (MerchantCouponVO couponVO : couponVOS) {
                if (couponVO == null) {
                    continue;
                }
                if (couponVO.getAgioType() == 1) {
                    timingOrderVO.setCouponName(couponVO.getName());
                    timingOrderVO.setCoupon(timingOrderVO.getCoupon().add(couponVO.getMoney()));
                } else if (couponVO.getAgioType() == 4) {
                    //红包
                    timingOrderVO.setRpCouponName(couponVO.getName());
                    timingOrderVO.setRpCoupon(timingOrderVO.getRpCoupon().add(couponVO.getMoney()));
                }
            }
        }

        List<OrderPreferential> orderPreferentials = orderPreferentialMapper.selectPreferential(orderNo);
        timingOrderVO.setPreferentials(orderPreferentials);
        List<OrderItemVO> orderItemVOS = orderItemMapper.selectOrderItemVO(orderNo);
        Integer afterQuantity = 0;
        timingOrderVO.setOrderItems(orderItemVOS);

        //获取订单快照信息
        orderNewService.getOrderItemInfo(orderItemVOS);

        //计算加格优惠
        BigDecimal skuPriceDiscount = BigDecimal.ZERO;
        for (OrderPreferential el : orderPreferentials) {
            if (OrderPreferentialTypeEnum.isSkuPriceDiscount(el.getType())){
                skuPriceDiscount = skuPriceDiscount.add(el.getAmount());
            }
        }
        timingOrderVO.setDiscount(skuPriceDiscount);

        if (!CollectionUtils.isEmpty(orderItemVOS)) {
            BigDecimal skuTotalOriginPrice = BigDecimal.ZERO;
            for (OrderItemVO orderItemVO : orderItemVOS) {
                skuTotalOriginPrice = skuTotalOriginPrice.add(orderItemVO.getOriginalPrice().multiply(BigDecimal.valueOf(orderItemVO.getAmount())));

                //获取到省心送id
                TimingRule timingRule = timingRuleMapper.selectTimingRuleBySku(
                        timingOrderVO.getAreaNo(), orderItemVO.getSku(), TimingRuleEnum.type.TIMING.ordinal());
                if (timingRule != null) {
                    orderItemVO.setTimingRuleId(timingRule.getId());
                }
            }
            timingOrderVO.setSkuTotalOriginPrice(skuTotalOriginPrice);
        }


        AfterSaleOrder afterSaleOrder = new AfterSaleOrder();
        afterSaleOrder.setOrderNo(orderNo);
        List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderService.selectAfterSaleOrderVO(afterSaleOrder);
        for (AfterSaleOrderVO afterSaleOrderVO : afterSaleOrderVOS) {
            if (afterSaleOrderVO.getDeliveryed() == 0 && Objects.equals(
                    afterSaleOrderVO.getStatus(), 2) && afterSaleOrderVO.getQuantity() != null) {
                afterQuantity = afterSaleOrderVO.getQuantity() + afterQuantity;
            }

        }
        timingOrderVO.setAfterQuantity(afterQuantity);

        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.timingOrderPlans(orderNo);
        Map<Integer, OrderFulfillmentInfo> timingOrderFulfillmentInfoMap = ofcLogisticsQueryFacade.queryTimingOrderLogisticsInfo(orderNo);
        for(DeliveryPlanVO deliveryPlanVO : deliveryPlanVOS) {
            DistOrderDTO distOrderDTO = tmsDistOrderDetailFacade.queryDistOrderDetail(orderNo,deliveryPlanVO.getContactId(),deliveryPlanVO.getDeliveryTime(), TmsSourceEnum.getDistOrderSource(OrderTypeEnum.TIMING.getId()));
            if (!ObjectUtils.isEmpty(distOrderDTO) && null != distOrderDTO.getStatus()){
                deliveryPlanVO.setPathStatus(TmsDistOrderStatusEnum.getDistOrderStatus(distOrderDTO.getStatus()));
            }else {
                deliveryPlanVO.setPathStatus(DeliveryStatusEnum.NOT_YET.getStatus());
            }
            deliveryPlanVO.setDeliveryEvaluationStatus(getDeliveryEvlStatus(
                    timingOrderVO.getAreaNo(),
                    deliveryPlanVO.getStatus(),
                    deliveryPlanVO.getDeliveryEvaluationStatus(),
                    deliveryPlanVO.getDeliveryTime()));
            OrderFulfillmentInfo orderFulfillmentInfo = timingOrderFulfillmentInfoMap.get(deliveryPlanVO.getId());
            if (null != orderFulfillmentInfo){
                deliveryPlanVO.setOrderFulfillmentType(orderFulfillmentInfo.getOrderFulfillmentType());
                deliveryPlanVO.setLogisticsInfoList(orderFulfillmentInfo.getLogisticsInfoList());
            }
        }
        timingOrderVO.setDeliveryPlen(deliveryPlanVOS);

        if (!CollectionUtils.isEmpty(deliveryPlanVOS)) {
            try {
                timingOrderVO.setDeliveredQuantity(deliveryPlanVOS.stream()
                        .filter(deliveryPlanVO -> !deliveryPlanVO.getDeliveryTime()
                                .isAfter(LocalDate.now())).mapToInt(dp -> dp.getQuantity()).sum());
            } catch (NoSuchElementException e) {
                //DO NOTHING
                timingOrderVO.setDeliveredQuantity(0);
            }
            timingOrderVO.setWaitDeliveryQuantity(
                    orderItemVOS.get(0).getAmount() - timingOrderVO.getDeliveredQuantity());
        } else {
            timingOrderVO.setDeliveredQuantity(0);
            timingOrderVO.setWaitDeliveryQuantity(orderItemVOS.get(0).getAmount());
        }

        TimingOrder timingOrder = timingOrderMapper.selectByOrderNo(orderNo);
        timingOrderVO.setDeliveryEndTime(timingOrder.getDeliveryEndTime());
        TimingOrderRefundTime timingOrderRefundTime =  timingOrderRefundTimeMapper.selectByOrderNo(orderNo);
        if (!ObjectUtils.isEmpty(timingOrderRefundTime)){
            timingOrderVO.setRefundTime(timingOrderRefundTime.getRefundTime());
        }

        deliverPlanRemarkSnapshotService.mergeDeliveryPlanVOSSnapshot(timingOrderVO.getDeliveryPlen());
        return timingOrderVO;

    }
    public Integer getDeliveryEvlStatus(Integer areaNo,Integer deliveryStatus,Integer deliveryEvaluationStatus,LocalDate deliveryTime) {
        //已评价
        if (DeliveryEvaluationStatusEnum.EVALUATION_FINISH.getStatus().equals(deliveryEvaluationStatus)){
            return DeliveryEvaluationStatusEnum.EVALUATION_FINISH.getStatus();
        }
        //判断配送状态
        if (!(Constants.DeliveryConstant.DELIVERY_FINISH.equals(deliveryStatus)
                || DeliveryStatusEnum.COMPLETE_DELIVERY.getStatus().equals(deliveryStatus)
                || DeliveryStatusEnum.TO_BE_WIRED.getStatus().equals(deliveryStatus))){
            return DeliveryEvaluationStatusEnum.EVALUATION_WITHOUT.getStatus();
        }
        //判断是否需要做区域限制
        if (deliveryEvaluationCache.getLimitAreaNoFlag(DeliveryEvaluationCache.DELIVERY_EL_LIMIT_AREA_FLAG)){
            List<Integer> limitAreaNoCacheList = deliveryEvaluationCache.getLimitAreaNoList(DeliveryEvaluationCache.DELIVERY_EL_LIMIT_AREA_LIST);
            if (!limitAreaNoCacheList.contains(areaNo)){
                return DeliveryEvaluationStatusEnum.EVALUATION_WITHOUT.getStatus();
            }
        }
        //判断日期区间
        if (!DateUtils.isEffectiveDate(deliveryTime.atStartOfDay(),
                deliveryTime.plusDays(deliveryEvaluationCache.getLimitAreaNoDays(DeliveryEvaluationCache.DELIVERY_EL_LIMIT_TIME)).atTime(Global.END_DAY_TIME),
                LocalDateTime.now())){
            return DeliveryEvaluationStatusEnum.EVALUATION_WITHOUT.getStatus();
        }
        return DeliveryEvaluationStatusEnum.EVALUATION_WAITING.getStatus();
    }

    /**
     * 处理订单中的预支付商品
     *
     * @param orderNo
     * @param orderItems
     */
    public void handlePrepayOrderItem(String orderNo, List<OrderItemVO> orderItems) {
        // 查询预支付商品配置
        List<PrepayInventoryRecord> prepayInventoryRecords = prepayInventoryRecordMapper.selectRecordListByOrderNo(
                orderNo);
        if (!CollectionUtils.isEmpty(prepayInventoryRecords)) {
            Map<String, List<PrepayInventoryRecord>> collect =
                    prepayInventoryRecords.stream()
                            .collect(Collectors.groupingBy(PrepayInventoryRecord::getSku));
            orderItems.forEach(orderItemVO -> {
                //实付是0
                if (!CollectionUtils.isEmpty(collect.get(orderItemVO.getSku()))) {
                    orderItemVO.setPrePayAmount(orderItemVO.getAmount());
                }
            });
        }
    }

    /**
     * 用户确认收货的后续操作
     *
     * @param orders
     * @param mId
     */
    public void afterUserConfirm(Orders orders, Long mId) {
        String orderNo = orders.getOrderNo();
        log.info("执行订单{}收货后操作", orderNo);
        //查询出用户信息
        Merchant merchant = merchantMapper.selectOneByMid(mId);
        // 查询是否有子订单，有子订单需要判断子订单是否都确认收货了，是否有过售后单
        List<OrderRelation> relations = orderRelationMapper.selectByOrderNoBatch(
                Lists.newArrayList(orderNo));
        boolean flag = true;
        if (CollectionUtil.isNotEmpty(relations)) {
            orderNo = relations.get(0).getMasterOrderNo();
            List<String> subOrderNos = orderRelationMapper.selectOrderNoByMasterOrderNo(orderNo);
            List<Orders> subOrderList = ordersMapper.listOrderByOrderNoList(subOrderNos);
            if (CollectionUtil.isNotEmpty(subOrderList)) {
                for (Orders subOrder : subOrderList) {
                    String subOrderNo = subOrder.getOrderNo();
                    if (Objects.equals(subOrderNo, orderNo)) {
                        continue;
                    }
                    //子订单还有未确认收货的
                    if (!Objects.equals(OrderStatusEnum.RECEIVED.getId(), Integer.valueOf(subOrder.getStatus()))) {
                        flag = false;
                        break;
                    }
                    AfterSaleOrder afterSaleOrder = new AfterSaleOrder();
                    afterSaleOrder.setOrderNo(subOrderNo);
                    List<AfterSaleOrderVO> afterSaleOrderList = afterSaleOrderService.selectAfterSaleOrderVO(
                            afterSaleOrder);
                    //子订单收货但是有过售后单的
                    if (CollectionUtil.isNotEmpty(afterSaleOrderList)) {
                        flag = false;
                        break;
                    }
                }
            }
        } else {
            AfterSaleOrder afterSaleOrder = new AfterSaleOrder();
            afterSaleOrder.setOrderNo(orderNo);
            List<AfterSaleOrderVO> afterSaleOrderList = afterSaleOrderService.selectAfterSaleOrderVO(
                    afterSaleOrder);
            if (CollectionUtil.isNotEmpty(afterSaleOrderList)) {
                flag = false;
            }
        }

        //存在售后 不再满返
        if (!(Global.BIG_MERCHANT.equals(merchant.getSize())) && flag) {
            //这个里面处理的是saleprice
            List<MarketRuleHistory> marketRuleHistoryList = marketRuleHistoryMapper.select(orderNo,
                    MarketRuleHistoryTypeEnum.FULL_RETURN.getCode());
            log.info("执行订单收货后发放满返券操作,marketRuleHistoryList:{}", JSON.toJSONString(marketRuleHistoryList));
            if (!CollectionUtils.isEmpty(marketRuleHistoryList)) {
                for (MarketRuleHistory marketRuleHistory : marketRuleHistoryList) {
                    String detail = marketRuleHistory.getDetail();
                    if (StringUtils.isBlank(detail)) {
                        log.warn("执行订单收货后发放满返券操作失败，满返规则为空！marketRuleHistory:{}", JSON.toJSONString(marketRuleHistory));
                        continue;
                    }

                    //校验状态防止重复发放
                    if (marketRuleHistory.getSendStatus() != null && !Objects.equals(marketRuleHistory.getSendStatus(),
                            MarketRuleHistorySendStatusEnum.TO_BE_ISSUED.getCode())) {
                        log.warn("当前满返发放状态不为待发放状态！marketRuleHistory:{}", JSON.toJSONString(marketRuleHistory));
                        continue;
                    }

                    //判断是否确认收货后才返券（历史数据中couponRule为空！）
                    MarketRule marketRule = JSON.parseObject(detail, MarketRule.class);
                    if (marketRule.getCouponRule() == null || Objects.equals(marketRule.getCouponRule(), MarketRuleCouponRuleEnum.CONFIRM_RECEIPT.getCode())) {
                        //发券
                        sendCoupon(marketRuleHistory, orderNo, merchant, marketRule);
                    }
                }
            }
        }
    }

    /**
     * 发送优惠券
     *  @param marketRuleHistory
     * @param orderNo
     * @param merchant
     * @param marketRule
     */
    public void sendCoupon(MarketRuleHistory marketRuleHistory,
                           String orderNo, Merchant merchant, MarketRule marketRule) {
        log.info("开始发放满返券, marketRuleHistory:{}, orderNo:{}, merchant:{}, marketRule:{}", JSON.toJSONString(marketRuleHistory),
                 orderNo, JSON.toJSONString(merchant), JSON.toJSONString(marketRule));
        Integer couponId = marketRuleHistory.getValue();
        Coupon coupon = couponMapper.selectByPrimaryKey(couponId);
        if (coupon == null) {
            log.error("发送满返优惠劵失败，不存在的优惠券{}", couponId);
            return;
        }

        //领取卡劵
        CouponSenderBO couponSenderBO = new CouponSenderBO();
        couponSenderBO.setCoupon(coupon);
        couponSenderBO.setMId(merchant.getmId());
        couponSenderBO.setReceiveType(CouponReceiveTypeEnum.FULL_RETURN.getCode());
        couponSenderBO.setRelatedId(marketRuleHistory.getId().longValue());
        Boolean aBoolean = couponSenderContext.sendActivityCoupon(couponSenderBO);

        if (!aBoolean) {
            log.error("发送满返优惠劵失败，余量不足或已达到领取上线,couponSenderBO:{}", JSON.toJSONString(couponSenderBO));
            return;
        }

        //更新状态
        marketRuleHistoryMapper.updateSendStatus(marketRuleHistory.getId(), MarketRuleHistorySendStatusEnum.ISSUED.getCode());

        //优惠券类型
        int couponType = coupon.getType();
        LocalDateTime validDate = null;
        if (couponType == 0) { //0指固定时间间隔到期
            int vaildTime = coupon.getVaildTime();
            validDate = LocalDateTime.of(LocalDate.now().plusDays(vaildTime),
                    LocalTime.of(23, 59, 59));
        } else if (couponType == 1) { //1固定时间点到期
            validDate = coupon.getVaildDate();
        }

        //发送优惠券消息
        try {
            String openId = merchant.getOpenid();
            //根据type值，获取对应消息
            String msgJson = MsgTemplateFactory.createRuleMsg(openId, orderNo, coupon.getMoney(),
                    DateUtils.localDateTime2Date(validDate));
            log.info(msgJson);
            MsgSendLogResp sendResult = templateMsgSender.sendTemplateMsg(msgJson);
            if (!ObjectUtils.isEmpty(sendResult) && sendResult.getSendStatus() == 0) {
                log.info("发送优惠券消息成功");
            }
        } catch (Exception e) {
            log.warn("发送优惠券消息失败", JSON.toJSONString(e));
        }
    }

    /**
     * 订单确认前校验
     *
     * @param orderNo
     * @param orders
     * @return
     */
    public boolean checkBeforeConfirm(String orderNo, Orders orders) {
        if (orders == null) {
            log.warn("订单:{}不存在,无法确认收货", orderNo);
            return true;
        }
        //只有在非待收货状态下才能收货
        if (orders.getStatus() != OrderStatusEnum.DELIVERING.getId()) {
            log.warn("订单:{},当前订单状态为:{},非待收货状态,无法确认收货", orderNo, orders.getStatus());
            return true;
        }
        //查询订单金额
        double totalPay = orders.getTotalPrice().doubleValue();
        if (totalPay < 0) {
            log.warn("订单:{}总金额异常,无法确认收货", orderNo);
            return true;
        }
        return false;
    }

    /**
     * 判断是否支持超时加单
     *
     * @param paymentTime
     * @param area
     * @param contact
     * @param orders
     * @return
     */
    public Boolean supportOverTimeAddOrder(LocalDateTime paymentTime, Area area, Contact contact, Orders orders) {
        LocalTime now = LocalTime.now();
        //南京不支持
        Integer supportAddOrder = area.getSupportAddOrder();
        //不支持加单
        if (Objects.equals(supportAddOrder, 1)) {
            return false;
        }
        /*Integer storeNo = fenceService.selectStoreNoByAreaNo(area.getAreaNo());
        LocalTime closeTime = warehouseLogisticsService.selectCloseTime(storeNo);*/
        FenceCloseTimeReq req = new FenceCloseTimeReq();
        req.setSource(DistOrderSourceEnum.getSourceEnumByOrderType(orders.getType()));
        req.setContactId(contact.getContactId());
        req.setArea(contact.getArea());
        req.setCity(contact.getCity());
        LocalTime closeTime = wncDeliveryFenceQueryFacade.queryCloseTime(req);
        LocalTime saleOutTime = closeTime.plusMinutes(30);
        if (paymentTime.toLocalTime().isAfter(closeTime) &&
                paymentTime.toLocalTime().isBefore(saleOutTime) &&
                now.isAfter(closeTime) && now.isBefore(saleOutTime)) {
            return true;
        }
        return false;
    }

    /**
     * 在线(虚拟)库存变更之后逻辑。。。。。。
     *
     * @param areaStore      库存使用仓
     * @param onlineChange   库存变更量
     * @param saledAreaStore 库存售卖的仓，可能和库存使用仓不一致
     */
    public void afterOnlineQuantityChange(AreaStore areaStore, Integer onlineChange,
            AreaStore saledAreaStore) {
        String sku = areaStore.getSku();
        Integer storeNo = areaStore.getAreaNo();
        //发送通知 8点到晚上10点发送消息
        if (LocalTime.now().isBefore(LocalTime.of(22, 00)) && LocalTime.now()
                .isAfter(LocalTime.of(8, 00)) && areaStore.getOnlineQuantity() + onlineChange > 0) {

            List<ArrivalNotice> arrivalNoticeList = new ArrayList<>();
            //查看是否有其他仓托管给此仓，若有则需要一并发送消息
            List<WarehouseInventoryMapping> mappings = warehouseInventoryService.selectAll(storeNo,
                    sku);
            if (!CollectionUtils.isEmpty(mappings)) {
                ArrivalNotice selectNotice = new ArrivalNotice();
                selectNotice.setSku(sku);
                for (WarehouseInventoryMapping mapping : mappings) {
                    selectNotice.setStoreNo(mapping.getWarehouseNo());
                    List<ArrivalNotice> tempArrivalNotices = arrivalNoticeMapper.selectBySku(
                            selectNotice);
                    if (!CollectionUtils.isEmpty(tempArrivalNotices)) {
                        arrivalNoticeList.addAll(tempArrivalNotices);
                    }
                }
            }
            // 开始发送
            if (!CollectionUtils.isEmpty(arrivalNoticeList)) {
                for (ArrivalNotice arrivalNotice : arrivalNoticeList) {
                    //更改状态
                    if (arrivalNoticeMapper.updateById(arrivalNotice.getId()) == 1) {
                        String msg = ArrivalNoticeMsg.templateMessage(arrivalNotice);
                        templateMsgSender.sendTemplateMsg(msg);
                    }
                }
            }
        }

        if (onlineChange > 0) {
            timingOrderLock(sku, storeNo);
        }

        Inventory inventory = inventoryMapper.selectBySku(sku);
        Area queryArea = areaMapper.selectByAreaNo(storeNo);

        try {
            //小于最小起售量 发送钉钉审批
            if (queryArea != null && Objects.equals(queryArea.getType(), 0)
                    && areaStore.getSafeQuantity() > 0) {
                int onlineQuantity = areaStore.getOnlineQuantity() + onlineChange;
                if (onlineQuantity <= inventory.getBaseSaleQuantity()
                        || onlineQuantity <= inventory.getBaseSaleUnit()) {
                    asyncTaskService.dingtalkProcess(sku, storeNo);
                }
            }
        } catch (Exception e) {
            log.info("钉钉审批发送失败");
        }
    }

    /**
     * 省心送加冻结库存(明日配送且未加冻结的订单)
     */
    private void timingOrderLock(String sku, Integer storeNo) {
        LocalDate deliveryDate = LocalDate.now().plusDays(1);
        if (LocalTime.now().isAfter(LocalTime.of(22, 00))) {
            deliveryDate = LocalDate.now().plusDays(2);
        }
        List<DeliveryPlan> deliveryPlans = deliveryPlanMapper.getUnLockOrder(deliveryDate, sku,
                storeNo);
        if (!CollectionUtils.isEmpty(deliveryPlans)) {
            asyncTaskService.timingOrderLock(sku, storeNo);
        }
    }

    /**
     * 获取库存数量
     *
     * @param sku
     * @param contactId
     * @return
     */
    public Integer queryStoreQuantity(String sku, Long contactId) {
        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        Integer storeNo = contact.getStoreNo();
        AreaStore select = areaStoreMallMapper.selectByStoreNoAndSkuNew(storeNo, sku);
        Integer onlineQuantity = select.getOnlineQuantity();
        Integer reserveMaxQuantity = select.getReserveMaxQuantity();
        Integer reserveMinQuantity = select.getReserveMinQuantity();
        Integer reserveUseQuantity = select.getReserveUseQuantity();
        if (reserveMaxQuantity != null) {
            Integer reserveQuantity = reserveMaxQuantity - reserveMinQuantity;
            onlineQuantity =
                    reserveUseQuantity > reserveQuantity ? onlineQuantity - reserveMinQuantity
                            : onlineQuantity - (reserveMaxQuantity - reserveUseQuantity);
        }
        return onlineQuantity;
    }

    /**
     * 更新在线库存
     *
     * @param sku
     * @param storeNo
     * @param quantity
     * @param name
     * @param changeType
     * @param recordNo
     * @param recordMap
     * @return
     */
    public synchronized AreaStore updateOnlineStock(String sku, Integer storeNo, Integer quantity,
            String name, StockChangeType changeType, String recordNo,
            Map<String, QuantityChangeRecord> recordMap) {
        log.info("配送仓为{}", storeNo);
        WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(storeNo,
                sku);
        AreaStore areaStore = areaStoreMallMapper.selectByStoreNoAndSku(mapping.getWarehouseNo(), sku);
        //如果是销售库存变动类型并且使用中心仓
        Integer warehouseNo = areaStore.getAreaNo();
        try {
            int onlineChange = quantity, changeChange = 0;
            // 还库存时需要先还虚拟变值，然后再加虚拟
            if (quantity > 0 && areaStore.getChange() > 0) {
                changeChange =
                        areaStore.getChange() >= quantity ? -quantity : -areaStore.getChange();
                onlineChange =
                        areaStore.getChange() >= quantity ? 0 : quantity - areaStore.getChange();
            }
            areaStoreMallMapper.updateOnlineQuantityAndChange(onlineChange, changeChange, sku,
                    warehouseNo);
            // 库存变动记录
            quantityChangeRecord(recordMap, warehouseNo, sku, name, changeType.getTypeName(),
                    recordNo, areaStore.getOnlineQuantity(),
                    QuantityChangeRecord.oldOnlineQuantityOffset);
            quantityChangeRecord(recordMap, warehouseNo, sku, name, changeType.getTypeName(),
                    recordNo, areaStore.getChange(), QuantityChangeRecord.oldChangeOffset);
            // 库存变动相关的逻辑
            afterOnlineQuantityChange(areaStore, onlineChange, areaStore);
        } catch (DataIntegrityViolationException e) {
            throw new BizException(0,"库存不足");
        } catch (UncategorizedSQLException e) {
            log.error(collectExceptionStackMsg(e));
            throw new DefaultServiceException(1, "系统错误,请重新提交.");
        }
        return areaStore;
    }

    /**
     * 如果是销售变动类型的则需要销售冻结连总冻结一起变
     *
     * @param storeNo             配送仓 编号
     * @param quantity
     * @param areaStore
     * @param stockChangeTypeEnum
     */
    public void updateLockQuantity(Integer storeNo, Integer quantity, AreaStore areaStore,
            StockChangeType stockChangeTypeEnum, String name, String recordNo,
            Map<String, QuantityChangeRecord> recordMap, Boolean handleReserve) {

        String sku = areaStore.getSku();
        Integer reserveQuantity = 0;
        Integer warehouseNo = areaStore.getAreaNo();
        AreaStore queryAreaStore = areaStoreMallMapper.selectByStoreNoAndSkuNew(storeNo, sku);
        // 销售变动类型，更新库存变动仓,
        if (stockChangeTypeEnum instanceof SaleStockChangeTypeEnum) {
            //开始扣减预留库存信息,大客户触发 支持预留
            if (queryAreaStore.getSupportReserved() == NumberUtils.INTEGER_ONE.intValue()
                    && queryAreaStore != null && queryAreaStore.getReserveMaxQuantity() > 0
                    && handleReserve) {
                reserveQuantity = quantity;
            }
            //售后当前预留库存使用数量< 售后数量
            if (reserveQuantity > 0) {
                if (queryAreaStore.getReserveUseQuantity() - quantity < 0) {
                    reserveQuantity = queryAreaStore.getReserveUseQuantity();
                }
            }
            log.info("客户类型{},{}", handleReserve, reserveQuantity);
            areaStoreMallMapper.updateSaleLockStock(-quantity, sku, warehouseNo, -reserveQuantity);
         } else {
            areaStoreMallMapper.updateLockStock(-quantity, sku, warehouseNo);
        }
        if (queryAreaStore.getReserveMaxQuantity() > 0 && reserveQuantity != 0) {
            quantityChangeRecord(recordMap, warehouseNo, sku, stockChangeTypeEnum.getTypeName(),
                    name, recordNo, queryAreaStore.getReserveUseQuantity(),
                    QuantityChangeRecord.oldReserveUseQuantityOffset);
        }

        quantityChangeRecord(recordMap, warehouseNo, sku, stockChangeTypeEnum.getTypeName(), name,
                recordNo, areaStore.getLockQuantity(), QuantityChangeRecord.oldLockQuantityOffset);
    }

    private void quantityChangeRecord(Map<String, QuantityChangeRecord> recordMap, Integer areaNo,
            String sku, String recorder, String typeName, String recordNo, Integer oldQuantity,
            long offset) {
        QuantityChangeRecord record = recordMap.get(sku + ":" + areaNo);
        if (record == null) {
            record = new QuantityChangeRecord(areaNo, sku, recorder, typeName, recordNo);
        }
        UnsafeUtil.unsafe.compareAndSwapObject(record, offset, null, oldQuantity);
        recordMap.put(sku + ":" + areaNo, record);
    }

    /**
     * 取消订单前校验并获取商家信息
     *
     * @param orderNo
     * @param merchant
     * @return
     */
    public void checkBeforeCancelOrder(String orderNo, Merchant merchant) {
        if (StringUtils.isBlank(orderNo)) {
            throw new DefaultServiceException(1, ResultConstant.RECORD_NOT_EXIST);
        }
        Orders order = ordersMapper.selectByOrderNo(orderNo);
        if (order == null || !Objects.equals(merchant.getmId(), order.getmId())) {
            throw new DefaultServiceException(1, ResultConstant.RECORD_NOT_EXIST);
        }
        //大客户直营店可以取消订单
        if (Global.BIG_MERCHANT.equals(merchant.getSize()) && 1 == merchant.getDirect()) {
            if (order.getStatus() != 3 || LocalTime.now().isAfter(LocalTime.of(21, 30))) {
                throw new BizException(1, "订单不能被取消，请联系相关人员");
            }
            //其他客户可以待支付状态取消
        } else if (order.getStatus() != 1) {
            throw new BizException(ResultConstant.ORDER_STATUS_ERROR);
        }
    }

    /**
     * 黄金卡处理;更改黄金卡使用次数,使用记录
     * @param orderNo
     */
    public void handleDiscountCard(String orderNo) {
        List<DiscountCardUseRecord> useRecords = discountCardUseRecordMapper.selectByOrderNo(
                orderNo);
        if(!CollectionUtils.isEmpty(useRecords)){
            Map<Integer, List<DiscountCardUseRecord>> collect = useRecords.stream().collect(Collectors.groupingBy(DiscountCardUseRecord::getDiscountCardMerchantId));
            collect.forEach((cardId,records) ->{
                Integer discountCardMerchantId = records.get(0).getDiscountCardMerchantId();
                DiscountCardToMerchant discountCardToMerchant = discountCardToMerchantMapper.selectByPrimaryKey(discountCardMerchantId);
                int sum = records.stream().mapToInt(DiscountCardUseRecord::getUseTimes).sum();
                DiscountCardToMerchant update = new DiscountCardToMerchant();
                update.setId(discountCardMerchantId);
                update.setUsedTimes(discountCardToMerchant.getUsedTimes() - sum);
                update.setUpdateTime(LocalDateTime.now());
                discountCardToMerchantMapper.updateByPrimaryKeySelective(update);
            });
        }
    }

    /**
     * 创建订单
     * @param merchantSubject
     * @param discountCard
     * @param orderNo
     * @return
     */
    public Orders createOrder(MerchantSubject merchantSubject, DiscountCard discountCard, String orderNo) {
        Long mId = merchantSubject.getMerchantId();
        //创建订单
        Long accountId = merchantSubject.getAccount().getAccountId();
        Orders order = new Orders(orderNo, mId, accountId, new Date(), OrderTypeEnum.VIRTUAL_GOODS.getId(), null);
        order.setDeliveryFee(BigDecimal.ZERO);
        order.setStatus((short) OrderStatusEnum.NO_PAYMENT.getId());
        order.setAreaNo(merchantSubject.getArea().getAreaNo());
        order.setDirect(merchantSubject.getDirect());
        order.setSkuShow(merchantSubject.getSkuShow());
//        order.setmSize(merchantSubject.getSize());
        String mSize = "";
        if(Objects.equals(merchantSubject.getSize(),"大客户")){
            Admin admin = adminMapper.selectAdminInfoByadminId(merchantSubject.getAdminId());
            if(Objects.equals(admin.getAdminType(),0)){
                mSize = "大客户";
            }else if(Objects.equals(admin.getAdminType(),1)){
                mSize= "普通客户";
            }else if(Objects.equals(admin.getAdminType(),2)){
                mSize="批发客户";
            }else{
                mSize= "普通客户";
            }
        }else{
            mSize = merchantSubject.getSize();
        }
        order.setmSize(mSize);
        order.setTotalPrice(discountCard.getPrice());
        order.setOriginPrice(discountCard.getPrice());
        order.setDiscountCardId(discountCard.getId());
        order.setOrderSaleType(OrderSaleType.NORMAL_OR_CARD.ordinal());

        //下单的门店为所属大客户的时候，则在orders表内记录此时下单时归属的大客户amdinId
        Merchant merchant = merchantMapper.selectOneByMid(mId);
        if(Objects.nonNull(merchant) && Objects.nonNull(merchant.getAdminId())){
            order.setAdminId(merchant.getAdminId());
        }
        ordersMapper.insertSelective(order);
        return order;
    }
}

