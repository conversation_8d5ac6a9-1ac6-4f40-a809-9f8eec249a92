package net.summerfarm.mall.service.helper;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.ExchangeScopeStatusEnum;
import net.summerfarm.mall.enums.ProductTypeEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.service.AreaService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * OrderCalcService辅助处理类
 *
 * @author: <EMAIL>
 * @create: 2022/6/27
 */
@Slf4j
@Component
public class OrderCalcServiceHelper {

    @Resource
    private MerchantMapper merchantMapper;

    @Resource
    private AreaMapper areaMapper;

    @Resource
    private AreaService areaService;

    @Resource
    private ContactMapper contactMapper;

    @Resource
    private ExchangeBaseInfoMapper exchangeBaseInfoMapper;

    @Resource
    private ExchangeScopeConfigMapper exchangeScopeConfigMapper;

    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 补充下单流程中必要的参数
     *
     * @param placeOrderVO 下单数据
     */
    public void initPlaceOrder(PlaceOrderVO placeOrderVO) {
        //默认数据处理
        if (placeOrderVO.getOutTimes() == null) {
            placeOrderVO.setOutTimes(0);
        }
        if (placeOrderVO.getHelpOrder() == null) {
            placeOrderVO.setHelpOrder(0);
        }

        Integer userAreaNo = 1001;
        //用户数据：用户自己下单从登录信息获取，否则代下单时，查询数据库。
        if (Objects.equals(placeOrderVO.getHelpOrder(), 0)) {
            placeOrderVO.setMId(RequestHolder.getMId());
            placeOrderVO.setAccountId(RequestHolder.getAccountId());
            placeOrderVO.setMname(RequestHolder.getName());
            placeOrderVO.setMajorMerchant(RequestHolder.isMajor());
            placeOrderVO.setAdminId(RequestHolder.getAdminId());
            placeOrderVO.setDirect(RequestHolder.getDirect());
            placeOrderVO.setSkuShow(RequestHolder.getSkuShow());
            placeOrderVO.setOpenId(RequestHolder.getOpenId());
            placeOrderVO.setServer(RequestHolder.getMerchantSubject().getServer());
            userAreaNo = RequestHolder.getMerchantAreaNo();
            // 过渡业务线字段还没有到用户缓存的阶段
            Integer businessLine = RequestHolder.getBusinessLine();
            if(businessLine == null) {
                Merchant merchant = merchantMapper.selectByMid(RequestHolder.getMId());
                businessLine = merchant == null ? null : merchant.getBusinessLine();
            }
            placeOrderVO.setBusinessLine(businessLine);

        } else {
            // 代下单时，查询数据库。
            // 同时，用Merchant的Area来查询缓存
            log.info("本次请求走的是代下单逻辑:{}, RequestHolder.mId:", placeOrderVO.getMId(), RequestHolder.getMId());
            Merchant merchant = merchantMapper.selectOneByMid(placeOrderVO.getMId());
            placeOrderVO.setMajorMerchant(Objects.equals(merchant.getSize(), Global.BIG_MERCHANT));
            placeOrderVO.setMname(merchant.getMname());
            placeOrderVO.setAdminId(merchant.getAdminId());
            placeOrderVO.setDirect(merchant.getDirect());
            placeOrderVO.setSkuShow(merchant.getSkuShow());
            placeOrderVO.setServer(merchant.getServer());
            placeOrderVO.setBusinessLine(merchant.getBusinessLine());
            //由于是代下单，userAreaNo 应该从merchant取；
            userAreaNo=merchant.getAreaNo();
        }

        placeOrderVO.setSize(placeOrderVO.getMajorMerchant() ? Global.BIG_MERCHANT : Global.COMMON_MERCHANT);


        //查询用户运营服务区
        //优先从缓存查询用户运营服务区
        Area area = areaService.selectAreaWithCache(userAreaNo);
        placeOrderVO.setArea(area);

        //配送地址信息
        Contact contact;
        if (null != placeOrderVO.getContactId()) {
            contact = contactMapper.selectByPrimaryKey(placeOrderVO.getContactId());
        } else {
            contact = contactMapper.selectIsDefaultByMid(placeOrderVO.getMId());
        }
        placeOrderVO.setContact(contact);

        log.info("当前下单用户信息：placeOrderVO：{}", JSON.toJSONString(placeOrderVO));
    }

    /**
     * 根据联系人ID获取联系人信息
     */
    public Contact getContactById(Long contactId) {
        if (contactId == null) {
            return null;
        }
        return contactMapper.selectByPrimaryKey(contactId);
    }

    /**
     * 根据商户ID获取默认联系人信息
     */
    public Contact getDefaultContactByMid(Long mId) {
        if (mId == null) {
            return null;
        }
        return contactMapper.selectIsDefaultByMid(mId);
    }

    /**
     * 换购活动商品校验
     * @param itemCalcDTOList
     * @param bizId
     */
    public void checkExchangeBuyCondition(List<OrderItemCalcDTO> itemCalcDTOList, Long bizId) {
        List<OrderItemCalcDTO> exchangeItemSkus = itemCalcDTOList.stream()
                .filter(x -> Objects.equals(x.getProductType(),
                        ProductTypeEnum.EXCHANGE.getCode())).collect(Collectors.toList());
        if (bizId == null || CollectionUtil.isEmpty(exchangeItemSkus)) {
            return;
        }
        ExchangeScopeConfig scopeConfig = exchangeScopeConfigMapper.selectByPrimaryKey(bizId);
        if (scopeConfig == null || !Objects.equals(scopeConfig.getStatus(), ExchangeScopeStatusEnum.OPEN.getCode())) {
            throw new DefaultServiceException(ResultConstant.DEFAULT_FAILED, "换购活动失效");
        }
        ExchangeBaseInfo exchangeBaseInfo = exchangeBaseInfoMapper.selectById(scopeConfig.getBaseInfoId());
        if (exchangeBaseInfo == null) {
            throw new DefaultServiceException(ResultConstant.DEFAULT_FAILED, "换购活动失效");
        }
        Integer triggerNum = exchangeBaseInfo.getTriggerNum();
        int notExchangeItemNum = itemCalcDTOList.size() - exchangeItemSkus.size();
        //只有下单中包含了换购商品才需要校验
        if (notExchangeItemNum < triggerNum) {
            log.warn("本次下单换购条件不满足,bizId:{},换购触发条件:{},实际当前值:{}", bizId, triggerNum, notExchangeItemNum);
            throw new DefaultServiceException(ResultConstant.ORDER_FAILED, "不满足换购条件，请重新下单");
        }
    }

}
