package net.summerfarm.mall.service.search.function;

import net.summerfarm.mall.facade.engine.model.EsMarketItemInfoDTO;

import java.math.BigDecimal;
import java.util.function.Function;

/**
 * 用于获取SKU销量的函数式接口
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@FunctionalInterface
public interface SkuSalesVolumeFunction extends Function<EsMarketItemInfoDTO, BigDecimal> {

    /**
     * 获取SKU销量
     *
     * @param sku SKU信息
     * @return SKU销量数值（GMV）
     */
    @Override
    BigDecimal apply(EsMarketItemInfoDTO sku);
}
