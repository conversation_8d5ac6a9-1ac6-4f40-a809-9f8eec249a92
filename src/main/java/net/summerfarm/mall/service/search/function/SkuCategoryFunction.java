package net.summerfarm.mall.service.search.function;

import net.summerfarm.mall.facade.engine.model.EsMarketItemInfoDTO;

import java.util.function.Function;

/**
 * 用于获取SKU类目的函数式接口
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@FunctionalInterface
public interface SkuCategoryFunction extends Function<EsMarketItemInfoDTO, Long> {
    
    /**
     * 获取SKU类目标识
     * 
     * @param sku SKU信息
     * @return 类目标识
     */
    @Override
    Long apply(EsMarketItemInfoDTO sku);
}
