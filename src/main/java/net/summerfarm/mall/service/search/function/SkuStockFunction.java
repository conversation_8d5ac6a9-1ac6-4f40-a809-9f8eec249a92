package net.summerfarm.mall.service.search.function;

import net.summerfarm.mall.facade.engine.model.EsMarketItemInfoDTO;

import java.util.function.Function;

/**
 * 用于判断SKU是否有库存的函数式接口
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@FunctionalInterface
public interface SkuStockFunction extends Function<EsMarketItemInfoDTO, Boolean> {
    
    /**
     * 判断SKU是否有库存
     * 
     * @param sku SKU信息
     * @return true表示有库存，false表示无库存
     */
    @Override
    Boolean apply(EsMarketItemInfoDTO sku);
}
