package net.summerfarm.mall.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.common.util.PageInfoHelper;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.SpringContextUtil;
import net.summerfarm.mall.mapper.CategoryMapper;
import net.summerfarm.mall.mapper.CouponMapper;
import net.summerfarm.mall.mapper.InventoryMapper;
import net.summerfarm.mall.mapper.OrderItemMapper;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.model.converter.MerchantJobConverter;
import net.summerfarm.mall.model.domain.Coupon;
import net.summerfarm.mall.model.domain.OrderItem;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.dto.CategoryDto;
import net.summerfarm.mall.model.input.MerchantJobQueryInput;
import net.summerfarm.mall.model.input.MerchantJobUpdateInput;
import net.summerfarm.mall.model.vo.InventoryVO;
import net.summerfarm.mall.model.vo.MerchantJobVO;
import net.summerfarm.mall.model.vo.SkuJobVO;
import net.summerfarm.mall.service.impl.CategoryServiceImpl;
import net.xianmu.common.cache.InMemoryCache;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.jobsdk.enums.CrmJobEnum;
import net.xianmu.jobsdk.enums.CrmJobMerchantDetailEnum;
import net.xianmu.jobsdk.mapper.CrmJobMerchantDetailMapper;
import net.xianmu.jobsdk.mapper.CrmJobRewardRecordMapper;
import net.xianmu.jobsdk.model.dto.CrmJobMerchantDetailDto;
import net.xianmu.jobsdk.model.po.CrmJobMerchantDetail;
import net.xianmu.jobsdk.model.po.CrmJobRewardRecord;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/12/18 16:17
 */

@Slf4j
@Service
public class MerchantJobService {

    @Resource
    private CrmJobMerchantDetailMapper crmJobMerchantDetailMapper;
    @Resource
    private CategoryServiceImpl categoryService;
    @Resource
    private CategoryMapper categoryMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private CouponMapper couponMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private CrmJobRewardRecordMapper crmJobRewardRecordMapper;


    public PageInfo<MerchantJobVO> getPage(MerchantJobQueryInput input) {
        PageHelper.startPage(input.getPageIndex(), input.getPageSize());
        List<CrmJobMerchantDetailDto> list = crmJobMerchantDetailMapper.getPageByMid(RequestHolder.getMId());
        return wrapJobPage(list);
    }


    public MerchantJobVO getDetail(MerchantJobQueryInput input) {
        CrmJobMerchantDetailDto dto = crmJobMerchantDetailMapper.getDetailById(input.getId());
        return null == dto ? null : wrapJobList(Collections.singletonList(dto)).get(0);
    }


    private PageInfo<MerchantJobVO> wrapJobPage(List<CrmJobMerchantDetailDto> list) {
        if (CollectionUtil.isEmpty(list)) {
            return PageInfoHelper.createPageInfoV2(new ArrayList<>());
        }

        PageInfo<MerchantJobVO> pageInfo = PageInfoHelper.toPageResp(PageInfoHelper.createPageInfoV2(list), MerchantJobConverter::convertDtoToVo);
        List<MerchantJobVO> merchantJobVOS = pageInfo.getList();
        wrapJobCategoryDetailByCache(merchantJobVOS);
        wrapJobProductDetail(merchantJobVOS);
        wrapJobRewardDetail(merchantJobVOS);
        return pageInfo;
    }


    private List<MerchantJobVO> wrapJobList(List<CrmJobMerchantDetailDto> list) {
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<MerchantJobVO> merchantJobVOS = MerchantJobConverter.toMerchantJobVoList(list);
        wrapJobCategoryDetailByCache(merchantJobVOS);
        wrapJobProductDetail(merchantJobVOS);
        wrapJobRewardDetail(merchantJobVOS);
        return merchantJobVOS;
    }


    private void wrapJobCategoryDetailByCache(List<MerchantJobVO> jobList) {
        // 全部类目
        jobList.forEach(vo -> {
            if (CollectionUtil.isNotEmpty(vo.getCategoryIdList()) && vo.getCategoryIdList().contains(-1)) {
                vo.setSupportAllCategory(true);
            }
        });

        // 指定类目
        List<Integer> categoryIdList = jobList.stream().filter(vo -> CollectionUtil.isNotEmpty(vo.getCategoryIdList())).filter(vo -> !vo.getCategoryIdList().contains(-1))
                .flatMap(vo -> vo.getCategoryIdList().stream())
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(categoryIdList)) {
            return;
        }

        // 将类目铺平到3级，用于获取前台类目映射
        jobList.stream().filter(vo -> CollectionUtil.isNotEmpty(vo.getCategoryIdList())).filter(vo -> !vo.getCategoryIdList().contains(-1)).forEach(vo ->vo.setLevel3CategoryIdList(categoryService.getSubLevelCategoryIdsByCache(vo.getCategoryIdList())));

        // 查询对应的前台类目
        List<Integer> allSubLevelCategoryIds = jobList.stream().filter(vo -> CollectionUtil.isNotEmpty(vo.getLevel3CategoryIdList()))
                .flatMap(vo -> vo.getLevel3CategoryIdList().stream())
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(allSubLevelCategoryIds)) {
            return;
        }

        List<CategoryDto> categoryDtoList = categoryMapper.getCategoryByIds(allSubLevelCategoryIds, RequestHolder.getMerchantAreaNo());
        Map<Integer, List<CategoryDto>> categoryDtoMap = categoryDtoList.stream()
                .collect(Collectors.groupingBy(CategoryDto::getId));
        jobList.forEach(vo -> {
            List<Integer> list = vo.getLevel3CategoryIdList();
            if (CollectionUtil.isNotEmpty(list)) {
                List<CategoryDto> categoryDtos = new ArrayList<>();
                list.forEach(id -> {
                    List<CategoryDto> dtos = categoryDtoMap.get(id);
                    if (CollectionUtil.isNotEmpty(dtos)) {
                        categoryDtos.addAll(dtos);
                    }
                });
                vo.setCategoryList(categoryDtos);
            }
        });
    }


    private void wrapJobProductDetail(List<MerchantJobVO> jobList) {
        // 获取任务命中的sku列表
        List<String> skuList = jobList.stream().filter(vo -> CollectionUtil.isNotEmpty(vo.getSkuList()))
                .flatMap(vo -> vo.getSkuList().stream())
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(skuList)) {
            return;
        }

        List<InventoryVO> inventoryVOList = inventoryMapper.selectByPrimaryKeyList(null, skuList);
        Map<String, InventoryVO> inventoryVOMap = inventoryVOList.stream().collect(Collectors.toMap(InventoryVO::getSku, Function.identity()));
        jobList.forEach(vo -> {
            List<String> list = vo.getSkuList();
            if (CollectionUtil.isNotEmpty(list)) {
                List<InventoryVO> inventoryVOs = list.stream()
                        .map(inventoryVOMap::get) // 根据 ID 获取分类信息
                        .filter(Objects::nonNull) // 过滤掉不存在的分类
                        .collect(Collectors.toList()); // 收集为列表
                vo.setInventoryList(inventoryVOs);
            }
        });
    }

    private void wrapJobRewardDetail(List<MerchantJobVO> jobList) {
        Set<Integer> couponIdList = new HashSet<>();
        jobList.forEach(dto -> couponIdList.add(Integer.valueOf(dto.getRewardValue())));
        if (CollectionUtil.isEmpty(couponIdList)) {
            log.info("优惠信息不存在！");
            return;
        }
        List<Coupon> couponEntities = couponMapper.selectByIdIn(new ArrayList<>(couponIdList));
        if (CollectionUtil.isEmpty(couponEntities)) {
            log.info("优惠信息不存在！couponIdList:{}", JSON.toJSONString(couponIdList));
            return;
        }
        Map<Integer, Coupon> collect = couponEntities.stream().collect(Collectors.toMap(Coupon::getId, Function.identity()));
        jobList.forEach(dto -> {
            Integer couponId = Integer.valueOf(dto.getRewardValue());
            Coupon coupon = collect.get(couponId);
            if(coupon != null) {
                dto.setCoupon(coupon);
                dto.setRewardAmount(coupon.getMoney());
            }
        });
    }


    public PageInfo<MerchantJobVO> getHistoryPage(MerchantJobQueryInput input) {
        PageHelper.startPage(input.getPageIndex(), input.getPageSize());
        List<CrmJobMerchantDetailDto> historyPage = crmJobMerchantDetailMapper.getHistoryPageByMid(RequestHolder.getMId());
        return wrapJobPage(historyPage);
    }


    public List<SkuJobVO> queryTaskListBySkus(MerchantJobQueryInput input) {
        List<String> skus = input.getSkus();
        if (CollectionUtil.isEmpty(skus)) {
            return Collections.emptyList();
        }

        // 获取当前用户的下单任务
        List<MerchantJobVO> voList = this.getJobProductExpansionJobListByMid(RequestHolder.getMId());
        return this.wrapSkuResultList(voList, skus);
    }

    public List<SkuJobVO> queryCacheTaskListBySkus(MerchantJobQueryInput input) {
        List<String> skus = input.getSkus();
        if (CollectionUtil.isEmpty(skus)) {
            return Collections.emptyList();
        }

        // 基于缓存获取当前用户的下单任务
        MerchantJobService bean = SpringContextUtil.getBean("merchantJobService", MerchantJobService.class);
        List<MerchantJobVO> voList = bean.getJobProductExpansionJobListCacheByMid(RequestHolder.getMId());
        return this.wrapSkuResultList(voList, skus);
    }


    @InMemoryCache(expiryTimeInSeconds = 2 * 60)
    public List<MerchantJobVO> getJobProductExpansionJobListCacheByMid(Long mid) {
        return getJobProductExpansionJobListByMid(mid);
    }

    public List<MerchantJobVO> getJobProductExpansionJobListByMid(Long mid) {
        List<CrmJobMerchantDetailDto> page = crmJobMerchantDetailMapper.getPageByMid(mid);
        page = page.stream().filter(dto -> CrmJobEnum.Type.PRODUCT_ORDER.getCode().equals(dto.getType())).collect(Collectors.toList());
        return wrapJobList(page);
    }


    private List<SkuJobVO> wrapSkuResultList(List<MerchantJobVO> voList, List<String> skus){
        // 按 sku 分类任务
        Map<String, List<MerchantJobVO>> skuTaskMap = voList.stream()
                .flatMap(vo -> vo.getSkuList().stream().map(sku -> new AbstractMap.SimpleEntry<>(sku, vo)))
                .collect(Collectors.groupingBy(Map.Entry::getKey, Collectors.mapping(Map.Entry::getValue, Collectors.toList())));

        // 遍历每个 sku, 构建 SkuJobVO
        List<SkuJobVO> resultList = skus.stream()
                .map(sku -> {
                    SkuJobVO skuJobVO = new SkuJobVO();
                    skuJobVO.setSku(sku);

                    // 获取该 sku 对应的任务列表
                    List<MerchantJobVO> taskList = skuTaskMap.getOrDefault(sku, Collections.emptyList());
                    List<MerchantJobVO> claimedList = new ArrayList<>();
                    List<MerchantJobVO> waitingToClaimList = new ArrayList<>();

                    // 根据任务的 claimingStatus 分类任务
                    taskList.forEach(vo -> {
                        if (CrmJobMerchantDetailEnum.TaskClaimingStatus.CLAIMED.getCode().equals(vo.getClaimingStatus())) {
                            claimedList.add(vo);
                        } else {
                            waitingToClaimList.add(vo);
                        }
                    });

                    claimedList.sort(Comparator.comparing(MerchantJobVO::getRewardAmount).reversed());
                    waitingToClaimList.sort(Comparator.comparing(MerchantJobVO::getRewardAmount).reversed());
                    skuJobVO.setClaimedList(claimedList);
                    skuJobVO.setWaitingToClaimList(waitingToClaimList);
                    return skuJobVO;
                })
                .filter(vo -> CollectionUtil.isNotEmpty(vo.getClaimedList()) || CollectionUtil.isNotEmpty(vo.getWaitingToClaimList()))
                .collect(Collectors.toList());

        return resultList;
    }


    public List<MerchantJobVO> queryTaskListMayBeAchieved(MerchantJobQueryInput input) {
        String orderNo = input.getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            throw new ParamsException("请求参数异常");
        }

        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        List<CrmJobMerchantDetailDto> claimedJobList = crmJobMerchantDetailMapper.getClaimedPageByMidAndPayTime(orders.getmId(), orders.getOrderTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime());
        List<MerchantJobVO> merchantJobVOS = MerchantJobConverter.toMerchantJobVoList(claimedJobList);

        if (CollectionUtil.isEmpty(merchantJobVOS)) {
            log.info("该门店{}暂无生效中、并且已领取的任务", RequestHolder.getMId());
            return Collections.emptyList();
        }

        // 判断当前订单能达成的任务
        List<OrderItem> orderItemEntities = orderItemMapper.selectOrderItem(orderNo);
        List<MerchantJobVO> resultLis = merchantJobVOS.stream()
                .filter(vo -> this.enableToast(vo, orderNo))
                .filter(vo -> this.checkJobIsAchievedAfterPaymentSucceeded(vo, orders, orderItemEntities)).collect(Collectors.toList());
        return resultLis;
    }

    private boolean enableToast(MerchantJobVO vo, String orderNo){
        if(CrmJobMerchantDetailEnum.TaskCompletionStatus.UNCOMPLETED.getCode().equals(vo.getAchievedStatus())){
            return true;
        }
        List<String> orderNoList = vo.getOrderNoList();
        if(CollectionUtil.isNotEmpty(orderNoList) && orderNoList.size() == 1) {
            return orderNoList.contains(orderNo);
        }
        log.info("任务已经被其他订单完成了，无需提示。MerchantJobVO：{}, orderNo:{}", JSON.toJSONString(vo), orderNo);
        return false;
    }

    /**
     * 支付完成后判断任务是否达成
     *
     * @param jobDto
     * @param ordersEntity
     * @param orderItemEntities
     */
    private boolean checkJobIsAchievedAfterPaymentSucceeded(MerchantJobVO vo, Orders ordersEntity, List<OrderItem> orderItemEntities) {
        // 1. 任务类型校验
        if (!checkOrderTypeRule(vo, ordersEntity)) {
            log.info("该任务不支持此类订单！OrdersEntity：{}, job:{}", JSON.toJSONString(ordersEntity), JSON.toJSONString(vo));
            return false;
        }

        // 2. 判断具体的任务规则
        boolean isAchieved = false;
        if (CrmJobEnum.Type.PRODUCT_ORDER.getCode().equals(vo.getType())) {
            isAchieved = this.checkProductExpansionJobRule(orderItemEntities, vo, ordersEntity);
        } else if (CrmJobEnum.Type.CATEGORY_ORDER.getCode().equals(vo.getType())) {
            isAchieved = this.checkOrderJobRule(orderItemEntities, vo, ordersEntity);
        }
        log.info("订单是否能完成：{}，订单号：{}，任务：{}", isAchieved, ordersEntity.getOrderNo(), JSON.toJSONString(vo));
        return isAchieved;
    }


    /**
     * 判断任务所支持的订单类型
     *
     * @param jobDto
     * @param ordersEntity
     * @return
     */
    private boolean checkOrderTypeRule(MerchantJobVO vo, Orders ordersEntity) {
        List<Integer> orderTypeList = vo.getOrderTypeList();
        if (CollectionUtil.isEmpty(orderTypeList)) {
            log.info("任务支持的订单类型为空！");
            return false;
        }

        return orderTypeList.contains(ordersEntity.getType());
    }


    /**
     * 校验下单任务，这里计算的是商品原价
     *
     * @param orderItemEntities
     * @param jobDto
     * @param ordersEntity
     * @return
     */
    private boolean checkOrderJobRule(List<OrderItem> orderItemEntities, MerchantJobVO jobDto, Orders ordersEntity) {
        List<Integer> sourceCategoryIdList = jobDto.getCategoryIdList();
        BigDecimal countPrice = BigDecimal.ZERO;
            if (CollectionUtil.isEmpty(sourceCategoryIdList) || sourceCategoryIdList.contains(-1)) {
                // 全部商品
                countPrice = orderItemEntities.stream()
                        .map(item -> item.getOriginalPrice().multiply(BigDecimal.valueOf(item.getAmount())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                // 指定类目
                // 解析获得所有的三级类目
                List<Integer> categoryIdList = categoryService.getSublevelCategoryIds(sourceCategoryIdList);
                countPrice = orderItemEntities.stream()
                        .filter(item -> categoryIdList.contains(item.getCategoryId()))
                        .map(item -> item.getOriginalPrice().multiply(BigDecimal.valueOf(item.getAmount())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
        return checkJobCompletionRule(jobDto.getCompletionType(), jobDto.getCompletionValue(), countPrice.toString());
    }

    /**
     * 校验拓品任务
     *
     * @param orderItemEntities
     * @param jobDto
     * @param ordersEntity
     * @return
     */
    private boolean checkProductExpansionJobRule(List<OrderItem> orderItemEntities, MerchantJobVO jobDto, Orders ordersEntity) {
        List<String> skuList = jobDto.getSkuList();
        for (OrderItem orderItemEntity : orderItemEntities) {
            // 比较每一个满足任务配置的订单项
            if (skuList.contains(orderItemEntity.getSku())) {
                Integer amount = orderItemEntity.getAmount();
                if(checkJobCompletionRule(jobDto.getCompletionType(), jobDto.getCompletionValue(), String.valueOf(amount))) {
                    return true;
                }
            }
        }
        return false;
    }


    private boolean checkJobCompletionRule(Integer completionType, String completionValue, String sourceData) {
        log.info("开始规则比对：规则类型:{}, 规则的阈值:{}, 订单数据：{}", completionType, completionValue, sourceData);
        // 商品件数
        if (CrmJobEnum.CompletionCriteriaType.SINGLE_ORDER_PRODUCT_QUANTITY.getCode().equals(completionType)) {
            return Integer.parseInt(sourceData) >= Integer.parseInt(completionValue);
        } else if (CrmJobEnum.CompletionCriteriaType.SINGLE_ORDER_AMOUNT.getCode().equals(completionType)) {
            // 下单金额
            BigDecimal orderAmount = new BigDecimal(sourceData);
            BigDecimal targetAmount = new BigDecimal(completionValue);
            return orderAmount.compareTo(targetAmount) >= 0;
        }
        return false;
    }


    public BigDecimal queryMerchantJobReward(Long mid) {
        List<CrmJobRewardRecord> crmJobRewardRecords = crmJobRewardRecordMapper.selectByMId(mid);
        return CollectionUtil.isEmpty(crmJobRewardRecords) ? BigDecimal.ZERO : crmJobRewardRecords.stream()
                .map(CrmJobRewardRecord::getRewardAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    /**
     * 查询当前商户最近一条即将过期的任务
     * @param mid
     * @return
     */
    public MerchantJobVO queryMerchantJobBeAboutToExpire(Long mid) {
        List<CrmJobMerchantDetailDto> list = crmJobMerchantDetailMapper.getPageByMid(mid);
        if(CollectionUtil.isEmpty(list)){
            return null;
        }
        List<MerchantJobVO> merchantJobVOS = MerchantJobConverter.toMerchantJobVoList(list);
        List<MerchantJobVO> sortedList = merchantJobVOS.stream()
                .sorted(Comparator.comparingLong(job ->
                        Math.abs(LocalDateTime.now().until(job.getEndTime(), java.time.temporal.ChronoUnit.MILLIS))
                ))
                .collect(Collectors.toList());
        return sortedList.get(0);
    }


}
