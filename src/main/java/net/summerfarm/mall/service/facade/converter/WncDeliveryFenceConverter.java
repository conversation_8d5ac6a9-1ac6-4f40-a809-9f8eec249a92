package net.summerfarm.mall.service.facade.converter;

import net.summerfarm.mall.enums.FenceStatusEnum;
import net.summerfarm.mall.model.domain.Contact;
import net.summerfarm.mall.service.facade.dto.*;
import net.summerfarm.wnc.client.req.AddressQueryReq;
import net.summerfarm.wnc.client.req.fence.ContactAddressQueryReq;
import net.summerfarm.wnc.client.resp.AreaQueryResp;
import net.summerfarm.wnc.client.resp.DeliveryFenceResp;
import net.summerfarm.wnc.client.resp.fence.ContactAddressBelongFenceResp;
import net.summerfarm.wnc.client.resp.preciseDelivery.PreciseDeliveryConfigResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description
 * @date 2024/1/4 13:46:32
 */
public class WncDeliveryFenceConverter {


    public static List<ContactAddressQueryReq> contactBelongFenceReqToBatchQueryReq(ContactBelongFenceReq contactBelongFenceReq) {
        List<Contact> contacts = contactBelongFenceReq.getContacts();
        List<ContactAddressQueryReq> contactAddressQueryReqList = new ArrayList<>(contacts.size());
        contacts.stream().forEach(e -> {
            ContactAddressQueryReq contactAddressQueryReq = new ContactAddressQueryReq();
            AddressQueryReq addressQueryReq = new AddressQueryReq();

            addressQueryReq.setAddress(e.getAddress());
            addressQueryReq.setArea(e.getArea());
            addressQueryReq.setCity(e.getCity());
            addressQueryReq.setProvince(e.getProvince());

            contactAddressQueryReq.setContactId(e.getContactId());
            contactAddressQueryReq.setAddressReq(addressQueryReq);
            contactAddressQueryReqList.add(contactAddressQueryReq);
        });
        return contactAddressQueryReqList;
    }

    public static void contactAddressBelongFenceRespToContactBelongFenceRes(String fenceChannelType, Map<Long, ContactBelongFenceRes> map, List<ContactAddressBelongFenceResp> data) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }

        data.stream().forEach(e -> {
            ContactBelongFenceRes contactBelongFenceRes = new ContactBelongFenceRes();
            contactBelongFenceRes.setContactId(e.getContactId());
            if (Objects.isNull(e.getDeliveryFenceResp())) {
                contactBelongFenceRes.setStatus(FenceStatusEnum.PAUSE.getCode());
            } else {
                DeliveryFenceResp deliveryFenceResp = e.getDeliveryFenceResp();
                contactBelongFenceRes.setFenceName(deliveryFenceResp.getFenceName());
                List<String> fenceChannelList = null;

                //查询围栏渠道
                if (StringUtils.isNotBlank(e.getDeliveryFenceResp().getOrderChannelType())) {
                    String[] fenceChannels = e.getDeliveryFenceResp().getOrderChannelType().split(",");
                    fenceChannelList = Arrays.asList(fenceChannels);
                }

                //设置围栏状态 假如围栏状态是开启 判断当前客户是否再围栏开启的渠道内
                if (Objects.equals(deliveryFenceResp.getStatus(), FenceStatusEnum.OPEN.getCode())) {
                    contactBelongFenceRes.setStatus(FenceStatusEnum.OPEN.getCode());
                    if (!CollectionUtils.isEmpty(fenceChannelList) && !fenceChannelList.contains(fenceChannelType)) {
                        contactBelongFenceRes.setStatus(FenceStatusEnum.PAUSE.getCode());
                    }
                } else {
                    contactBelongFenceRes.setStatus(FenceStatusEnum.PAUSE.getCode());
                }
            }
            map.put(e.getContactId(), contactBelongFenceRes);
        });
    }

    public static PreciseDeliveryRes converterPreciseDeliveryRes(PreciseDeliveryConfigResp data) {
        if (Objects.isNull(data)) {
            return null;
        }

        PreciseDeliveryRes preciseDeliveryRes = new PreciseDeliveryRes();
        preciseDeliveryRes.setArea(data.getArea());
        preciseDeliveryRes.setCity(data.getCity());
        if (!CollectionUtils.isEmpty(data.getTimeList())) {
            List<TimeFramesDTO> timeFramesDTOS = new ArrayList<>(data.getTimeList().size());
            data.getTimeList().stream().forEach(e -> {
                TimeFramesDTO timeFramesDTO = new TimeFramesDTO();
                timeFramesDTO.setBeginTime(e.getBeginTime());
                timeFramesDTO.setEndTime(e.getEndTime());
                timeFramesDTOS.add(timeFramesDTO);
            });
            preciseDeliveryRes.setFramesDTOList(timeFramesDTOS);
        }
        return preciseDeliveryRes;
    }

    public static AreaQueryRes converterAreaQueryRes(AreaQueryResp data) {
        if (Objects.isNull(data)) {
            return null;
        }

        AreaQueryRes areaQueryRes = new AreaQueryRes();
        areaQueryRes.setId(data.getId());
        areaQueryRes.setAreaNo(data.getAreaNo());
        areaQueryRes.setAreaName(data.getAreaName());
        return areaQueryRes;
    }
}
