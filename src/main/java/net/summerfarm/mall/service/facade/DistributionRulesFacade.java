package net.summerfarm.mall.service.facade;

import net.summerfarm.mall.model.dto.delivery.DeliveryFeeRuleInfoDTO;
import net.summerfarm.mall.model.dto.delivery.DistributionRulesDTO;
import net.summerfarm.mall.service.facade.dto.DeliveryFeeReq;
import net.summerfarm.mall.service.facade.dto.DeliveryFeeRes;
import net.summerfarm.mall.service.facade.dto.DeliveryFeeRuleReq;
import net.summerfarm.mall.service.facade.dto.DeliveryFeeSkuInfoRes;
import net.xianmu.marketing.center.client.freight.req.DistributionRulesDetailReq;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description
 * @date 2023/10/9 17:41:32
 */
public interface DistributionRulesFacade {

    /**
     * @description: 获取免运规则
     * @author: lzh
     * @date: 2023/10/9 17:48
     * @param: [distributionRulesDetailReq]
     * @return: net.summerfarm.mall.model.dto.delivery.DistributionRulesDTO
     **/
    DistributionRulesDTO getDetail(DistributionRulesDetailReq distributionRulesDetailReq);

    /**
    * @description 获取配送费规则
    * @params []
    * @return java.util.List<net.summerfarm.mall.model.dto.delivery.DeliveryFeeRuleInfoDTO>
    * <AUTHOR>
    * @date  2024/9/9 16:07
    */
    List<DeliveryFeeRuleInfoDTO> queryDeliveryFeeRule(DeliveryFeeRuleReq deliveryFeeRuleReq);

    /**
    * @description 获取配送单运费
    * @params [deliveryFeeReq]
    * @return net.summerfarm.mall.service.facade.dto.DeliveryFeeSkuInfoRes
    * <AUTHOR>
    * @date  2024/9/9 16:23
    */
    DeliveryFeeRes queryDeliveryFee(DeliveryFeeReq deliveryFeeReq);
}
