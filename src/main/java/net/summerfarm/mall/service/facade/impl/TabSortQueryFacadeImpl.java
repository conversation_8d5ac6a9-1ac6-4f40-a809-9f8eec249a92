package net.summerfarm.mall.service.facade.impl;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.service.facade.TabSortQueryFacade;
import net.summerfarm.mall.service.facade.converter.TabSortConverter;
import net.summerfarm.mall.service.facade.dto.CombinationSortDTO;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.client.tabsort.provider.TabSortQueryProvider;
import net.xianmu.marketing.center.client.tabsort.req.TabSortReq;
import net.xianmu.marketing.center.client.tabsort.resp.SortCombinationTabResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @author: <EMAIL>
 * @create: 2024/1/17
 */
@Slf4j
@Component
public class TabSortQueryFacadeImpl implements TabSortQueryFacade {

    @DubboReference
    private TabSortQueryProvider tabSortQueryProvider;

    public List<CombinationSortDTO> listAllTabSort(Integer areaNo, Long mId) {
        List<CombinationSortDTO> list = Lists.newArrayList();
        if (areaNo == null || mId == null) {
            log.warn("获取商品排序tab失败,参数异常,areaNo:{},mId:{}", areaNo, mId);
            return list;
        }
        TabSortReq sortReq = new TabSortReq();
        sortReq.setAreaNo(areaNo);
        sortReq.setMId(mId);
        try {
            DubboResponse<List<SortCombinationTabResp>> response = tabSortQueryProvider.listAllTab(sortReq);
            if (response == null && !response.isSuccess()) {
                log.warn("未获取到商品排序tab,areaNo:{},mId:{}", areaNo, mId);
                return list;
            }
            list = TabSortConverter.toCombinationSortDTOList(response.getData());
        } catch (Exception e) {
            log.error("获取商品排序tab失败,areaNo:{},mId:{},cause:{}", areaNo, mId, Throwables.getStackTraceAsString(e));
        }
        return list;
    }

    public List<String> listTabSkuByTabId(Integer tabId) {
        List<String> skuList = Lists.newArrayList();
        try {
            DubboResponse<List<String>> response = tabSortQueryProvider.listSkusByTabId(tabId.longValue());
            if (response == null && !response.isSuccess()) {
                log.warn("未获取到商品排序tab的sku,tabId:{}", tabId);
                return skuList;
            }
            skuList = response.getData();
        } catch (Exception e) {
            log.error("获取到商品排序tab的sku失败,tabId:{}", tabId, Throwables.getStackTraceAsString(e));
        }
        return skuList;
    }

}
