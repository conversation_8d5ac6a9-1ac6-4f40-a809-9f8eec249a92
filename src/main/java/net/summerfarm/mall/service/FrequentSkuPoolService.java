package net.summerfarm.mall.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.mall.enums.FrequentSkuPoolEnums;
import net.summerfarm.mall.model.input.frequentSkuPool.FrequentSkuPoolPageInput;
import net.summerfarm.mall.model.input.frequentSkuPool.MerchantFrequentlyConfigUpdateInput;
import net.summerfarm.mall.model.vo.FrontCategoryVO;
import net.summerfarm.mall.model.vo.MerchantFrequentlyConfigVO;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.model.vo.frequentSkuPool.BusinessFormatListVO;
import net.summerfarm.mall.model.vo.frequentSkuPool.FrequentSkuInShoppingCartVO;
import net.summerfarm.mall.model.vo.frequentSkuPool.FrequentSkuPoolVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/21 10:48
 * @PackageName:net.summerfarm.mall.service
 * @ClassName: FrequentSkuPoolService
 * @Description: TODO
 * @Version 1.0
 */
public interface FrequentSkuPoolService {

    /**
    * @description 常购清单消息提醒查询
    * @params []
    * @return net.summerfarm.mall.model.vo.MerchantFrequentlyConfigVO
    * <AUTHOR>
    * @date  2025/5/21 10:50
    */
    MerchantFrequentlyConfigVO queryMerchantFrequentlyConfig();

    /**
    * @description 常购清单消息提醒修改
    * @params [input]
    * @return java.lang.Boolean
    * <AUTHOR>
    * @date  2025/5/21 13:33
    */
    Boolean updateMerchantFrequentlyConfig(MerchantFrequentlyConfigUpdateInput input);

    /**
    * @description 常购清单列表
    * @params [input]
    * @return com.github.pagehelper.PageInfo<net.summerfarm.mall.model.vo.frequentSkuPool.FrequentSkuPoolVO>
    * <AUTHOR>
    * @date  2025/5/21 14:05
    */
    PageInfo<FrequentSkuPoolVO> pageFrequentSkuPool(FrequentSkuPoolPageInput input);

    /**
    * @description 常购清单弹窗推荐信息
    * @params []
    * @return java.util.List<net.summerfarm.mall.model.vo.frequentSkuPool.FrequentSkuPoolVO>
    * <AUTHOR>
    * @date  2025/5/21 14:06
    */
    List<FrequentSkuPoolVO> recommendFrequentSkuPool();

    /**
    * @description 常购清单列表类目信息
    * @params []
    * @return java.util.List<net.summerfarm.mall.model.vo.FrontCategoryVO>
    * <AUTHOR>
    * @date  2025/5/21 14:07
    */
    List<FrontCategoryVO> frequentSkuPoolCategory();

    List<BusinessFormatListVO> businessRankingList();

    void updateTopSku(Long mId, String sku, FrequentSkuPoolEnums.Top top);

    void deleteData(Long mId, String sku);

    void addData(Long mId, List<String> skuCodeList, String masterOrderNo, Integer source);

    Map<String, Boolean> checkSkuInFrequentSkuPool(Long mId, List<String> skuCodeList);

    boolean isInExperiment();

    FrequentSkuInShoppingCartVO querySkuListInShoppingCart();

    void cancelRecommendData(Long mId, List<String> skuList);
}
