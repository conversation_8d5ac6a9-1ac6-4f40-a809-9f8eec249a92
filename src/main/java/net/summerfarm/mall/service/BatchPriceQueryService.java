package net.summerfarm.mall.service;

import net.summerfarm.mall.model.dto.price.BatchTakePriceRequest;
import net.summerfarm.mall.model.dto.price.BatchTakePriceResponse;

/**
 * 批量价格查询服务接口
 * 
 * <AUTHOR>
 */
public interface BatchPriceQueryService {

    /**
     * 批量查询SKU到手价
     * 
     * @param request 查询请求
     * @return 查询结果
     */
    BatchTakePriceResponse batchQueryTakePrice(BatchTakePriceRequest request);

    /**
     * 验证请求签名
     * 
     * @param request 请求参数
     * @return 验证结果
     */
    boolean verifySignature(BatchTakePriceRequest request);
}
