package net.summerfarm.mall.service.frequentSkuPool;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.common.util.PageInfoHelper;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.constant.Constants;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.mapper.offline.SkuSalesRankingListMapper;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.domain.frequentSkuPool.MerchantFrequentlyBuyingSku;
import net.summerfarm.mall.model.domain.frequentSkuPool.MerchantFrequentlyBuyingSkuNotificationConfig;
import net.summerfarm.mall.model.domain.frequentSkuPool.MerchantSkuOrderData;
import net.summerfarm.mall.model.domain.offline.SkuSalesRankingList;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDTO;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDetailDTO;
import net.summerfarm.mall.model.input.frequentSkuPool.FrequentSkuPoolPageInput;
import net.summerfarm.mall.model.input.frequentSkuPool.MerchantFrequentlyConfigUpdateInput;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.model.vo.frequentSkuPool.BusinessFormatListVO;
import net.summerfarm.mall.model.vo.frequentSkuPool.FrequentSkuInShoppingCartVO;
import net.summerfarm.mall.model.vo.frequentSkuPool.FrequentSkuPoolVO;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryReq;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryRes;
import net.xianmu.common.cache.InMemoryCache;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 常购清单相关接口
 * <AUTHOR>
 * @Date 2025/5/21 10:48
 * @PackageName:net.summerfarm.mall.service.impl
 * @ClassName: FrequentSkuPoolServiceImpl
 * @Description: TODO
 * @Version 1.0
 */
@Service
@Slf4j
public class FrequentSkuPoolServiceImpl implements FrequentSkuPoolService {

    @Resource
    private MerchantFrequentlyBuyingSkuMapper merchantFrequentlyBuyingSkuMapper;

    @Resource
    private MerchantFrequentlyBuyingSkuNotificationConfigMapper merchantFrequentlyBuyingSkuNotificationConfigMapper;

    @Resource
    private MerchantSkuOrderDataMapper merchantSkuOrderDataMapper;

    @Resource
    private FrontCategoryMapper frontCategoryMapper;

    @Resource
    private ProductService productService;

    @Resource
    private InventoryService inventoryService;

    @Resource
    private AreaSkuService areaSkuService;

    @Resource
    private WmsAreaStoreFacade wmsAreaStoreFacade;

    @Resource
    private ContactService contactService;

    @Resource
    private FrequentSkuPoolAddHandler frequentSkuPoolAddHandler;

    @Resource
    private OrderItemMapper orderItemMapper;

    @Resource
    private SkuSalesRankingListMapper skuSalesRankingListMapper;

    @Resource
    private MerchantMapper merchantMapper;

    @Resource
    private ActivityService activityService;

    @Resource
    private SalePriceTransService salePriceTransService;

    @Resource
    private TimingRuleService timingRuleService;

    @Resource
    private ArrivalNoticeService arrivalNoticeService;

    @Resource
    private OrderRelationMapper orderRelationMapper;

    @Resource
    private ShoppingCartMapper shoppingCartMapper;

    /**
     * 采购助手AB实验ID
     */
    private static final String FREQUENT_SKU_AB_EXPERIMENT_ID = "frequent_sku_pool202506";

    /**
     * 采购助手AB实验分组
     */
    private static final Set<String> FREQUENT_SKU_AB_EXPERIMENT_VARIANT = new HashSet<>(Arrays.asList("V3", "V4"));

    @Override
    public MerchantFrequentlyConfigVO queryMerchantFrequentlyConfig() {
        Long mId = RequestHolder.getMId();
        if (mId == null){
            throw new BizException("请先登录");
        }
        MerchantFrequentlyBuyingSkuNotificationConfig config = merchantFrequentlyBuyingSkuNotificationConfigMapper.selectByMid(mId);
        MerchantFrequentlyConfigVO merchantFrequentlyConfigVO = new MerchantFrequentlyConfigVO();
        if (config == null) {
            config = this.initNotificationConfig(mId);
        }
        merchantFrequentlyConfigVO.setGoodsArrived(config.getGoodsArrived());
        merchantFrequentlyConfigVO.setSpecialOffer(config.getSpecialOffer());
        return merchantFrequentlyConfigVO;
    }

    @Override
    public Boolean updateMerchantFrequentlyConfig(MerchantFrequentlyConfigUpdateInput input) {
        MerchantFrequentlyBuyingSkuNotificationConfig config = merchantFrequentlyBuyingSkuNotificationConfigMapper.selectByMid(RequestHolder.getMId());
        if (config == null) {
            return Boolean.FALSE;
        }
        if (input.getGoodsArrived() != null) {
            config.setGoodsArrived(input.getGoodsArrived());
        }
        if (input.getSpecialOffer() != null) {
            config.setSpecialOffer(input.getSpecialOffer());
        }

        //到货提醒关掉需要删除到货提醒数据
        if (Objects.equals(input.getGoodsArrived(), CommonStatus.NO.getCode())) {
            List<MerchantFrequentlyBuyingSku> merchantFrequentlyBuyingSkus = merchantFrequentlyBuyingSkuMapper.getAllByMIdAndStatus(RequestHolder.getMId(), FrequentSkuPoolEnums.Status.JOINED_LIST.getCode());
            Set<String> skus = merchantFrequentlyBuyingSkus.stream().map(MerchantFrequentlyBuyingSku::getSku).collect(Collectors.toSet());
            arrivalNoticeService.batchDelete(skus);
        }
        int i = merchantFrequentlyBuyingSkuNotificationConfigMapper.updateByPrimaryKeySelective(config);
        return i > 0;
    }

    @Override
    public PageInfo<FrequentSkuPoolVO> pageFrequentSkuPool(FrequentSkuPoolPageInput input) {
        // 大客户不返回
        if (RequestHolder.isMajor()) {
            return PageInfo.emptyPageInfo();
        }
        Long mId = RequestHolder.getMId();
        if (mId == null){
            throw new BizException("请先登录");
        }
        if (input.getPageIndex() == null || input.getPageIndex() < 1 || input.getPageSize() == null || input.getPageSize() > 100){
            throw new BizException("查询分页参数错误");
        }

        String abExpVariant = RequestHolder.getAbExpVariant(FREQUENT_SKU_AB_EXPERIMENT_ID);
        if (StringUtils.isBlank(abExpVariant) || !FREQUENT_SKU_AB_EXPERIMENT_VARIANT.contains(abExpVariant)){
            log.info("【常购清单分页查询接口日志】未命中采购助手AB实验, mid:{}, variantId:{}", mId, abExpVariant);
            return PageInfo.emptyPageInfo();
        }

        // 第一次查询，初始化数据
        if (isFirstTimeQuery(mId)) {
            log.info("【常购清单分页查询接口日志】第一次查询，初始化数据, mid:{}", mId);
            long startTime = System.currentTimeMillis();
            // 1. 初始化通知配置
            this.initNotificationConfig(mId);

            // 2. 初始化系统推荐数据
            this.initRecommendData(mId);
            log.info("【常购清单分页查询接口日志】初始化数据结束, mid >>> {}, cost >>> {}", mId, System.currentTimeMillis() - startTime);
        }
        // 获取最新数据的同步日期
        String dayTag = this.getDayTag();

        // 获取后台类目id
        List<Integer> categoryIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(input.getFirstCategoryIdList())){
            categoryIdList = frontCategoryMapper.selectCategoryByParentIds(input.getFirstCategoryIdList(), RequestHolder.getMerchantAreaNo());
        }
        PageHelper.startPage(input.getPageIndex(), input.getPageSize());
        List<FrequentSkuPoolVO> frequentSkuPoolVOS = merchantFrequentlyBuyingSkuMapper.pageFrequentSkuPool(
                dayTag,
                mId,
                FrequentSkuPoolEnums.Status.JOINED_LIST.getCode(),
                categoryIdList.stream().distinct().collect(Collectors.toList())
        );
        if (CollectionUtil.isEmpty(frequentSkuPoolVOS)) {
            return PageInfo.emptyPageInfo();
        }

        //查询价格信息
        Set<String> skus = frequentSkuPoolVOS.stream().map(FrequentSkuPoolVO::getSku).collect(Collectors.toSet());
        Map<String, AreaSku> areaSkuMap = areaSkuService.areaSkuMapper(skus, RequestHolder.getMerchantAreaNo());
        if (CollectionUtil.isEmpty(areaSkuMap)) {
            log.warn("根据sku获取区域售卖信息失败, skus:{}", skus);
            return PageInfo.emptyPageInfo();
        }

        //查询sku的信息
        Map<String, Inventory> inventoryMap = inventoryService.selectInventoryWithCacheBatch(skus);
        if (CollectionUtil.isEmpty(inventoryMap)) {
            log.warn("根据sku获取商品信息失败, skus:{}", skus);
            return PageInfo.emptyPageInfo();
        }

        //查询商品信息
        Set<Long> pdIds = inventoryMap.values().stream().map(Inventory::getPdId).collect(Collectors.toSet());
        Map<Long, Products> productsMap = productService.selectProductsWithCachePipeline(pdIds);
        if (CollectionUtil.isEmpty(productsMap)) {
            log.warn("根据pdId获取商品信息失败, pdIds:{}", pdIds);
            return PageInfo.emptyPageInfo();
        }

        //查询库存信息
        Map<String, AreaStoreQueryRes> areaStoreMap = getAreaStoreMap(RequestHolder.getMId(), skus);

        List<TimingRuleVO> areaTimingRules = timingRuleService.getTimingInfoByAreaNoCache(RequestHolder.getMerchantAreaNo());
        Map<String, TimingRuleVO> timingRuleVOMap = areaTimingRules.stream().collect(Collectors.
                toMap(TimingRuleVO::getTimingSku, Function.identity(), (a, b) -> b));

        //组装返回值
        List<ActivitySkuDTO> activitySkuDTOS = new ArrayList<>();
        for (FrequentSkuPoolVO frequentSkuPoolVO : frequentSkuPoolVOS) {
            AreaSku areaSku = areaSkuMap.get(frequentSkuPoolVO.getSku());
            if (areaSku == null) {
                log.warn("根据sku获取区域售卖信息失败, sku:{}", frequentSkuPoolVO.getSku());
                continue;
            }

            Inventory inventory = inventoryMap.get(frequentSkuPoolVO.getSku());
            if (inventory == null) {
                log.warn("根据sku获取商品信息失败, sku:{}", frequentSkuPoolVO.getSku());
                continue;
            }

            Products products = productsMap.get(inventory.getPdId());
            if (products == null) {
                log.warn("根据pdId获取商品信息失败, pdId:{}", inventory.getPdId());
                continue;
            }

            //设置商品信息
            frequentSkuPoolVO.setPdName(products.getPdName());
            frequentSkuPoolVO.setSkuName(inventory.getSkuName());
            frequentSkuPoolVO.setWeight(inventory.getWeight());
            frequentSkuPoolVO.setBaseSaleUnit(inventory.getBaseSaleUnit());
            frequentSkuPoolVO.setBaseSaleQuantity(inventory.getBaseSaleQuantity());
            frequentSkuPoolVO.setMaturity(inventory.getMaturity());
            frequentSkuPoolVO.setCategoryId(products.getCategoryId());
            frequentSkuPoolVO.setPicturePath(Optional.ofNullable(inventory.getSkuPic()).orElse(products.getPicturePath()));
            frequentSkuPoolVO.setWeightNum(inventory.getWeightNum());
            frequentSkuPoolVO.setPdId(inventory.getPdId());
            frequentSkuPoolVO.setOnSale(areaSku.getOnSale());
            frequentSkuPoolVO.resetSkuNameAndSkuPic();

            //设置价格信息
            if (areaSku.getPrice() != null) {
                frequentSkuPoolVO.setOriginalPrice(areaSku.getPrice().toString());
                frequentSkuPoolVO.setSalePrice(areaSku.getPrice().doubleValue());
                frequentSkuPoolVO.setPrice(areaSku.getPrice().doubleValue());
            }

            //设置单斤价格信息
            AvgInfoVO avgInfo = inventoryService.getAvgInfo(frequentSkuPoolVO.getCategoryId(), frequentSkuPoolVO.getWeight());
            if (avgInfo != null) {
                frequentSkuPoolVO.setAvgNumerator(avgInfo.getAvgNumerator());
                frequentSkuPoolVO.setAvgUnit(avgInfo.getAvgUnit());
            }

            //设置库存信息
            if (!areaStoreMap.isEmpty() && Objects.nonNull(areaStoreMap.get(frequentSkuPoolVO.getSku()))) {
                AreaStoreQueryRes areaStoreQueryRes = areaStoreMap.get(frequentSkuPoolVO.getSku());

                //设置默认库存，防止库存泄露，库存从其他接口获取
                if (frequentSkuPoolVO.getBaseSaleQuantity() * frequentSkuPoolVO.getBaseSaleUnit() > areaStoreQueryRes.getOnlineQuantity()) {
                    frequentSkuPoolVO.setOnlineQuantity(Constants.AreaStoreConstant.defaultOnlineQuantity);
                    frequentSkuPoolVO.setQuantity(Constants.AreaStoreConstant.defaultOnlineQuantity);
                } else {
                    frequentSkuPoolVO.setOnlineQuantity(Constants.ONLINE_QUANTITY);
                    frequentSkuPoolVO.setQuantity(Constants.ONLINE_QUANTITY);
                }
            }

            if (frequentSkuPoolVO.getOnSale()) {
                ActivitySkuDTO activitySkuDTO = new ActivitySkuDTO();
                activitySkuDTO.setSku(frequentSkuPoolVO.getSku());
                activitySkuDTOS.add(activitySkuDTO);
            }
            if (!timingRuleVOMap.isEmpty() && timingRuleVOMap.containsKey(frequentSkuPoolVO.getSku())) {
                frequentSkuPoolVO.setTimingRuleSku(Boolean.TRUE);
            }
        }

        //查询活动信息
        Map<String, ActivitySkuDetailDTO> activitySkuDetailMap = activityService.listByActivitySku(
                activitySkuDTOS, RequestHolder.getMerchantAreaNo(), RequestHolder.getMId());
        for (FrequentSkuPoolVO frequentSkuPoolVO : frequentSkuPoolVOS) {
            salePriceTransService.handleSalePrice(activitySkuDetailMap, frequentSkuPoolVO);
        }

        return PageInfoHelper.createPageInfo(frequentSkuPoolVOS);
    }

    /**
     * 采购助手数据初始化
     * @param mId 商户id
     */
    private void initRecommendData(Long mId) {
        String abExpVariant = RequestHolder.getAbExpVariant(FREQUENT_SKU_AB_EXPERIMENT_ID);
        if (StringUtils.isBlank(abExpVariant) || !FREQUENT_SKU_AB_EXPERIMENT_VARIANT.contains(abExpVariant)){
            log.info("【用户数据初始化】未命中采购助手AB实验, mid:{}, variantId:{}", mId, abExpVariant);
            return;
        }
        log.info("【用户数据初始化】命中采购助手AB实验, mid:{}, abExpVariant:{}", mId, abExpVariant);
        String dayTag = this.getDayTag();
        List<MerchantSkuOrderData> merchantSkuOrderData = new ArrayList<>();
        if ("V3".equals(abExpVariant)){
            merchantSkuOrderData = merchantSkuOrderDataMapper.listByMIdAndSixtyDaysData(dayTag, mId);
        }
        if ("V4".equals(abExpVariant)){
            merchantSkuOrderData = merchantSkuOrderDataMapper.listByMIdAndTwoYearsData(dayTag, mId);
        }
        if (CollectionUtil.isEmpty(merchantSkuOrderData)){
            log.info("【用户数据初始化】未获取到用户订单数据, mid:{}, abExpVariant:{}", mId, abExpVariant);
            return;
        }
        // 查询上下架信息
        Set<String> skuCodeSet = merchantSkuOrderData.stream().map(MerchantSkuOrderData::getSku).collect(Collectors.toSet());
        Map<String, AreaSku> areaSkuMap = areaSkuService.areaSkuMapper(skuCodeSet, RequestHolder.getMerchantAreaNo());
        if (CollectionUtil.isEmpty(areaSkuMap)) {
            log.warn("根据sku获取区域售卖信息失败, skus:{}", skuCodeSet);
            return;
        }
        List<String> skuCodeList = new ArrayList<>();
        for (MerchantSkuOrderData merchantSkuOrder : merchantSkuOrderData) {
            String sku = merchantSkuOrder.getSku();
            AreaSku areaSku = areaSkuMap.get(sku);
            if (areaSku == null) {
                log.warn("根据sku获取区域售卖信息失败, sku:{}", sku);
                continue;
            }
            if (!areaSku.getOnSale()){//NOSONAR
                log.info("商品未上架, sku:{}", sku);
                continue;
            }
            skuCodeList.add(sku);
        }
        frequentSkuPoolAddHandler.addFrequentSkuPool(mId, skuCodeList, FrequentSkuPoolEnums.DataSource.SYSTEM_INIT.getCode());
    }

    @Override
    public List<FrequentSkuPoolVO> recommendFrequentSkuPool() {
        Long mId = RequestHolder.getMId();
        if (mId == null){
            throw new BizException("请先登录");
        }
        String abExpVariant = RequestHolder.getAbExpVariant(FREQUENT_SKU_AB_EXPERIMENT_ID);
        if (StringUtils.isBlank(abExpVariant) || !FREQUENT_SKU_AB_EXPERIMENT_VARIANT.contains(abExpVariant)){
            log.info("未命中采购助手AB实验, mid:{}, variantId:{}", mId, abExpVariant);
            return Collections.emptyList();
        }
        log.info("【查询推荐数据】命中采购助手AB实验, mid:{}, abExpVariant:{}", mId, abExpVariant);
        String dayTag = this.getDayTag();
        List<String> skuCodeList = new ArrayList<>();
        if ("V3".equals(abExpVariant)){
            skuCodeList = merchantSkuOrderDataMapper.listSixtyDaysRecommendDataByMId(dayTag, mId);
        }
        if ("V4".equals(abExpVariant)){
            skuCodeList = merchantSkuOrderDataMapper.listTwoYearsRecommendDataByMId(dayTag, mId);
        }
        log.info("【查询推荐数据结果】 mid:{}, abExpVariant:{}, skuCodeList:{}", mId, abExpVariant, JSON.toJSONString(skuCodeList));
        if (CollectionUtil.isEmpty(skuCodeList)){
            log.info("未获取到用户推荐数据, mid:{}, abExpVariant:{}", mId, abExpVariant);
            return Collections.emptyList();
        }
        //查询价格信息
        Map<String, AreaSku> areaSkuMap = areaSkuService.areaSkuMapper(new HashSet<>(skuCodeList), RequestHolder.getMerchantAreaNo());
        if (CollectionUtil.isEmpty(areaSkuMap)) {
            log.warn("根据sku获取区域售卖信息失败, skus:{}", skuCodeList);
            return Collections.emptyList();
        }

        //查询sku的信息
        Map<String, Inventory> inventoryMap = inventoryService.selectInventoryWithCacheBatch(new HashSet<>(skuCodeList));
        if (CollectionUtil.isEmpty(inventoryMap)) {
            log.warn("根据sku获取商品信息失败, skus:{}", skuCodeList);
            return Collections.emptyList();
        }

        //查询商品信息
        Set<Long> pdIds = inventoryMap.values().stream().map(Inventory::getPdId).collect(Collectors.toSet());
        Map<Long, Products> productsMap = productService.selectProductsWithCachePipeline(pdIds);
        if (CollectionUtil.isEmpty(productsMap)) {
            log.warn("根据pdId获取商品信息失败, pdIds:{}", pdIds);
            return Collections.emptyList();
        }

        List<FrequentSkuPoolVO> frequentSkuPoolVOS = new ArrayList<>();
        //组装返回值
        for (String skuCode : skuCodeList) {
            AreaSku areaSku = areaSkuMap.get(skuCode);
            if (areaSku == null) {
                log.info("根据sku获取区域售卖信息失败, sku:{}", skuCode);
                continue;
            }

            if (!areaSku.getOnSale()){
                log.info("商品未上架, sku:{}", skuCode);
                continue;
            }

            Inventory inventory = inventoryMap.get(skuCode);
            if (inventory == null) {
                log.info("根据sku获取商品信息失败, sku:{}", skuCode);
                continue;
            }

            Products products = productsMap.get(inventory.getPdId());
            if (products == null) {
                log.info("根据pdId获取商品信息失败, pdId:{}", inventory.getPdId());
                continue;
            }

            FrequentSkuPoolVO frequentSkuPoolVO = new FrequentSkuPoolVO();
            //设置商品信息
            frequentSkuPoolVO.setSku(skuCode);
            frequentSkuPoolVO.setPdName(products.getPdName());
            frequentSkuPoolVO.setSkuName(inventory.getSkuName());
            frequentSkuPoolVO.setWeight(inventory.getWeight());
            frequentSkuPoolVO.setBaseSaleUnit(inventory.getBaseSaleUnit());
            frequentSkuPoolVO.setBaseSaleQuantity(inventory.getBaseSaleQuantity());
            frequentSkuPoolVO.setMaturity(inventory.getMaturity());
            frequentSkuPoolVO.setCategoryId(products.getCategoryId());
            frequentSkuPoolVO.setPicturePath(Optional.ofNullable(inventory.getSkuPic()).orElse(products.getPicturePath()));
            frequentSkuPoolVO.setWeightNum(inventory.getWeightNum());
            frequentSkuPoolVO.setPdId(inventory.getPdId());
            frequentSkuPoolVO.setOnSale(areaSku.getOnSale());
            frequentSkuPoolVO.resetSkuNameAndSkuPic();
            frequentSkuPoolVOS.add(frequentSkuPoolVO);
        }
        return frequentSkuPoolVOS;
    }

    @Override
    public List<FrontCategoryVO> frequentSkuPoolCategory() {
        Long mId = RequestHolder.getMId();
        if (mId == null){
            throw new BizException("请先登录");
        }
        List<Integer> categoryIdList = merchantFrequentlyBuyingSkuMapper.listAllCategoryIdByMId(mId, FrequentSkuPoolEnums.Status.JOINED_LIST.getCode());
        if (CollectionUtil.isEmpty(categoryIdList)) {
            return Collections.emptyList();
        }
        List<FrontCategoryVO> frontCategoryVOS = frontCategoryMapper.selectByCategoryIds(categoryIdList, RequestHolder.getMerchantAreaNo());
        if (CollectionUtil.isEmpty(frontCategoryVOS)) {
            return Collections.emptyList();
        }
        return frontCategoryMapper.selectByIdList(frontCategoryVOS.stream().map(FrontCategoryVO::getParentId).distinct().collect(Collectors.toList()));
    }

    @Override
    public List<BusinessFormatListVO> businessRankingList() {
        Long mId = RequestHolder.getMId();
        if (mId == null){
            throw new BizException("请先登录");
        }
        // 只取这四种类型的TOP20
        Set<String> merchantTypeWhiteList = Sets.newHashSet(MerchantEnum.TypeEnum.COFFEE.getDescription(),
                MerchantEnum.TypeEnum.CAKE.getDescription(),
                MerchantEnum.TypeEnum.TEA.getDescription(),
                MerchantEnum.TypeEnum.OTHER.getDescription());
        Map<String, String> merchantTypeMapping = getMerchantTypeMapping();
        Map<String, List<ProductInfoVO>> tempResult = new HashMap<>(4);
        String maxDayTag = skuSalesRankingListMapper.selectMaxDayTag();
        if (StringUtils.isBlank(maxDayTag)) {
            log.warn("获取业态榜单MAX_DAY_TAG失败！");
            return Collections.emptyList();
        }
        List<SkuSalesRankingList> skuSalesRankingLists = skuSalesRankingListMapper.listByDayTag(maxDayTag);
        Set<String> skuCollectors = new HashSet<>();
        skuSalesRankingLists.forEach(skuSalesRankingList -> {
            List<String> skuCodeList = Arrays.asList(skuSalesRankingList.getSkuList().split(","));
            if (CollectionUtils.isEmpty(skuCodeList)) {
                return;
            }
            skuCollectors.addAll(skuCodeList);
        });
        if (CollectionUtils.isEmpty(skuCollectors)) {
            log.warn("获取业态榜单sku信息为空！");
            return Collections.emptyList();
        }
        Map<String, ProductInfoVO> productInfoVOMap = this.convertToProductInfoVOList(skuCollectors);
        for (SkuSalesRankingList skuSalesRankingList : skuSalesRankingLists) {
            String merchantType = skuSalesRankingList.getMerchantType();
            if (!merchantTypeWhiteList.contains(merchantType)) {
                continue;
            }

            List<String> skuCodeList = Arrays.asList(skuSalesRankingList.getSkuList().split(","));
            List<ProductInfoVO> productInfoVOS = new ArrayList<>(skuCodeList.size());
            skuCodeList.forEach(skuCode -> {
                if (!productInfoVOMap.containsKey(skuCode)) {
                    return;
                }
                productInfoVOS.add(productInfoVOMap.get(skuCode));
            });
            tempResult.put(merchantTypeMapping.get(merchantType), productInfoVOS);
        }
        Merchant merchant = merchantMapper.selectOneByMid(mId);
        if (merchant == null) {
            log.warn("根据mId获取门店信息失败, mId:{}", mId);
            return Collections.emptyList();
        }
        // 获取当前门店的榜单名称
        String currentMerchantRankingName = merchantTypeMapping.get(merchant.getType());

        // 创建一个新的数组来保持插入顺序
        List<BusinessFormatListVO> orderedResult = new ArrayList<>();

        // 如果存在当前商户类型的榜单，先将其放入结果集
        if (tempResult.containsKey(currentMerchantRankingName)) {
            orderedResult.add(new BusinessFormatListVO(currentMerchantRankingName, tempResult.get(currentMerchantRankingName)));
            tempResult.remove(currentMerchantRankingName);
        }

        // 再将其他类型的榜单放入结果集
        tempResult.forEach((name, productList) -> {
            orderedResult.add(new BusinessFormatListVO(name, productList));
        });

        return orderedResult;
    }

    private Map<String, ProductInfoVO> convertToProductInfoVOList(Set<String> skuCodeList) {
        Map<String, ProductInfoVO> productInfoVOMap = new HashMap<>(skuCodeList.size());

        //查询价格信息
        Map<String, AreaSku> areaSkuMap = areaSkuService.areaSkuMapper(skuCodeList, RequestHolder.getMerchantAreaNo());
        if (CollectionUtil.isEmpty(areaSkuMap)) {
            log.warn("根据sku获取区域售卖信息失败, skus:{}", skuCodeList);
            return Collections.emptyMap();
        }

        //查询sku的信息
        Map<String, Inventory> inventoryMap = inventoryService.selectInventoryWithCacheBatch(skuCodeList);
        if (CollectionUtil.isEmpty(inventoryMap)) {
            log.warn("根据sku获取商品信息失败, skus:{}", skuCodeList);
            return Collections.emptyMap();
        }

        //查询商品信息
        Set<Long> pdIds = inventoryMap.values().stream().map(Inventory::getPdId).collect(Collectors.toSet());
        Map<Long, Products> productsMap = productService.selectProductsWithCachePipeline(pdIds);
        if (CollectionUtil.isEmpty(productsMap)) {
            log.warn("根据pdId获取商品信息失败, pdIds:{}", pdIds);
            return Collections.emptyMap();
        }

        //查询库存信息
        Map<String, AreaStoreQueryRes> areaStoreMap = getAreaStoreMap(RequestHolder.getMId(), skuCodeList);

        //查询活动信息
        List<ActivitySkuDTO> activitySkuDTOS = skuCodeList.stream().map(s -> {
            ActivitySkuDTO activitySkuDTO = new ActivitySkuDTO();
            activitySkuDTO.setSku(s);
            return activitySkuDTO;
        }).collect(Collectors.toList());
        Map<String, ActivitySkuDetailDTO> activitySkuDetailMap = activityService.listByActivitySku(
                activitySkuDTOS, RequestHolder.getMerchantAreaNo(), RequestHolder.getMId());

        //组装返回值
        for (String skuCode : skuCodeList) {
            AreaSku areaSku = areaSkuMap.get(skuCode);
            if (areaSku == null) {
                log.info("根据sku获取区域售卖信息失败, sku:{}", skuCode);
                continue;
            }

            if (!areaSku.getOnSale()){
                log.info("商品未上架, sku:{}", skuCode);
                continue;
            }

            Inventory inventory = inventoryMap.get(skuCode);
            if (inventory == null) {
                log.info("根据sku获取商品信息失败, sku:{}", skuCode);
                continue;
            }

            Products products = productsMap.get(inventory.getPdId());
            if (products == null) {
                log.info("根据pdId获取商品信息失败, pdId:{}", inventory.getPdId());
                continue;
            }

            //设置商品信息
            ProductInfoVO productInfoVO = new ProductInfoVO();
            productInfoVO.setPdName(products.getPdName());
            productInfoVO.setSkuName(inventory.getSkuName());
            productInfoVO.setSku(skuCode);
            productInfoVO.setWeight(inventory.getWeight());
            productInfoVO.setBaseSaleUnit(inventory.getBaseSaleUnit());
            productInfoVO.setBaseSaleQuantity(inventory.getBaseSaleQuantity());
            productInfoVO.setMaturity(inventory.getMaturity());
            productInfoVO.setCategoryId(products.getCategoryId());
            productInfoVO.setPicturePath(Optional.ofNullable(inventory.getSkuPic()).orElse(products.getPicturePath()));
            productInfoVO.setWeightNum(inventory.getWeightNum());
            productInfoVO.setPdId(inventory.getPdId());
            if (areaSku.getPrice() != null) {
                productInfoVO.setOriginalPrice(areaSku.getPrice().toString());
                productInfoVO.setSalePrice(areaSku.getPrice().doubleValue());
            }
            productInfoVO.resetSkuNameAndSkuPic();


            //设置单斤价格信息
            AvgInfoVO avgInfo = inventoryService.getAvgInfo(productInfoVO.getCategoryId(), productInfoVO.getWeight());
            if (avgInfo != null) {
                productInfoVO.setAvgNumerator(avgInfo.getAvgNumerator ());
                productInfoVO.setAvgUnit(avgInfo.getAvgUnit ());
            }

            //设置库存信息
            if (!areaStoreMap.isEmpty() && Objects.nonNull(areaStoreMap.get(skuCode))) {
                AreaStoreQueryRes areaStoreQueryRes = areaStoreMap.get(skuCode);

                //设置默认库存，防止库存泄露，库存从其他接口获取
                if (productInfoVO.getBaseSaleQuantity() * productInfoVO.getBaseSaleUnit() > areaStoreQueryRes.getOnlineQuantity()) {
                    productInfoVO.setOnlineQuantity(Constants.AreaStoreConstant.defaultOnlineQuantity);
                    productInfoVO.setQuantity(Constants.AreaStoreConstant.defaultOnlineQuantity);
                }else {
                    productInfoVO.setOnlineQuantity(Constants.ONLINE_QUANTITY);
                    productInfoVO.setQuantity(Constants.ONLINE_QUANTITY);
                }
            }

            salePriceTransService.handleSalePrice(activitySkuDetailMap, productInfoVO);
            productInfoVOMap.put(skuCode, productInfoVO);
        }
        return productInfoVOMap;
    }

    /**
     * @return key: xianmudb里的merchant_type value: 业态榜单名称
     */
    private static Map<String, String> getMerchantTypeMapping(){
        Map<String, String> merchantTypeMapping = new HashMap<>();
        merchantTypeMapping.put("西餐", "其它热销");
        merchantTypeMapping.put("甜品冰淇淋", "其它热销");
        merchantTypeMapping.put("面包蛋糕", "烘焙热销");
        merchantTypeMapping.put("咖啡", "咖啡热销");
        merchantTypeMapping.put("水果/果切/榨汁店", "其它热销");
        merchantTypeMapping.put("茶饮", "茶饮热销");
        merchantTypeMapping.put("其他", "其它热销");
        return merchantTypeMapping;
    }

    @Override
    public void updateTopSku(Long mId, String sku, FrequentSkuPoolEnums.Top top) {
        merchantFrequentlyBuyingSkuMapper.updateTopByMIdAndSku(mId, sku, top.getCode());
    }

    @Override
    public void deleteData(Long mId, String sku) {
        if (mId == null){
            throw new BizException("请先登录");
        }
        if (StringUtils.isBlank(sku)) {
            throw new BizException("sku为空");
        }
        merchantFrequentlyBuyingSkuMapper.updateStatusByMIdAndSku(mId, sku,
                FrequentSkuPoolEnums.Status.DELETE.getCode(),
                FrequentSkuPoolEnums.Top.UN_TOP.getCode(),
                LocalDateTime.now());
    }

    @Override
    public void addData(Long mId, List<String> skuCodeList, String masterOrderNo, Integer source) {
        if (mId == null){
            throw new BizException("请先登录");
        }
        List<String> skuCodeListToInsertDb = new ArrayList<>();
        if (!CollectionUtil.isEmpty(skuCodeList)){
            skuCodeListToInsertDb.addAll(skuCodeList);
        }
        if (StringUtils.isNotBlank(masterOrderNo)){
            List<String> orderNos = orderRelationMapper.selectOrderNoByMasterOrderNo(masterOrderNo);
            if (!CollectionUtil.isEmpty(orderNos)){
                List<OrderItem> orderItemList = orderItemMapper.selectListByOrderNos(orderNos);
                if (!CollectionUtil.isEmpty(orderItemList)){
                    skuCodeListToInsertDb.addAll(orderItemList.stream().map(OrderItem::getSku).collect(Collectors.toList()));
                }
            }
        }
        frequentSkuPoolAddHandler.addFrequentSkuPool(mId, skuCodeListToInsertDb, source);
    }

    @Override
    public Map<String, Boolean> checkSkuInFrequentSkuPool(Long mId, List<String> skuCodeList) {
        try {
            if (mId == null){
                return Collections.emptyMap();
            }
            if (CollectionUtil.isEmpty(skuCodeList)){
                return Collections.emptyMap();
            }
            List<MerchantFrequentlyBuyingSku> merchantFrequentlyBuyingSkus = merchantFrequentlyBuyingSkuMapper.listAllByMIdAndSkuCodeList(mId, skuCodeList);
            Set<String> skuInFrequentSkuPool = merchantFrequentlyBuyingSkus.stream().filter(o -> FrequentSkuPoolEnums.Status.JOINED_LIST.getCode().equals(o.getStatus()))
                    .map(MerchantFrequentlyBuyingSku::getSku).collect(Collectors.toSet());
            Map<String, Boolean> result = new HashMap<>();
            for (String sku : skuCodeList) {
                result.put(sku, skuInFrequentSkuPool.contains(sku));
            }
            return result;
        } catch (Exception e) {
            log.error("查询sku是否在常购清单中，出现异常 error mId >>> {}, skuCodeList >>> {}", mId, skuCodeList, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public boolean isInExperiment() {
        String abExpVariant = RequestHolder.getAbExpVariant(FREQUENT_SKU_AB_EXPERIMENT_ID);
        if (StringUtils.isBlank(abExpVariant) || !FREQUENT_SKU_AB_EXPERIMENT_VARIANT.contains(abExpVariant)){
            return false;
        }
        return true;
    }

    @Override
    public FrequentSkuInShoppingCartVO querySkuListInShoppingCart() {
        Long mId = RequestHolder.getMId();
        if (mId == null){
            throw new BizException("请先登录");
        }
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        if (merchantSubject == null){
            log.info("未获取到用户信息，返回空数组");
            return FrequentSkuInShoppingCartVO.empty();
        }
        Integer merchantAreaNo = RequestHolder.getMerchantAreaNo();
        if (merchantAreaNo == null){
            log.info("未获取到用户区域信息，返回空数组");
            return FrequentSkuInShoppingCartVO.empty();
        }
        List<ShoppingCartVO> shoppingCartVOS = shoppingCartMapper.getShoppingCarts(merchantSubject.getMerchantId(), merchantSubject.getAccount().getAccountId(),
                merchantAreaNo, merchantSubject.getAdminId(), merchantSubject.getDirect());
        if (CollectionUtils.isEmpty(shoppingCartVOS)){
            return FrequentSkuInShoppingCartVO.empty();
        }
        // 过滤掉下架的商品
        shoppingCartVOS = shoppingCartVOS.stream().filter(e -> OnSaleStatusEnum.LIST.getCode().equals(e.getOnSale())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shoppingCartVOS)){
            return FrequentSkuInShoppingCartVO.empty();
        }
        if (shoppingCartVOS.size() > 1000){
            log.info("购物车商品数量超过1000个，返回空数组");
            return FrequentSkuInShoppingCartVO.empty();
        }
        // 购物车里sku的数量map
        Map<String, Integer> skuQuantityMap = shoppingCartVOS.stream().collect(Collectors.toMap(ShoppingCartVO::getSku, ShoppingCartVO::getQuantity, Integer::sum));

        // 查询购物车里商品是否在常购清单中
        List<MerchantFrequentlyBuyingSku> merchantFrequentlyBuyingSkus = merchantFrequentlyBuyingSkuMapper.listAllByMIdAndSkuCodeList(
                mId,
                shoppingCartVOS.stream().map(ShoppingCartVO::getSku).distinct().collect(Collectors.toList())
        );
        if (CollectionUtils.isEmpty(merchantFrequentlyBuyingSkus)){
            return FrequentSkuInShoppingCartVO.empty();
        }
        List<String> skuCodeList = merchantFrequentlyBuyingSkus.stream().filter(o -> FrequentSkuPoolEnums.Status.JOINED_LIST.getCode().equals(o.getStatus()))
                .map(MerchantFrequentlyBuyingSku::getSku).collect(Collectors.toList());
        Integer totalQuantity = 0;
        for (String skuCode : skuCodeList) {
            totalQuantity += skuQuantityMap.getOrDefault(skuCode, 0);
        }
        return new FrequentSkuInShoppingCartVO(skuCodeList, totalQuantity);
    }

    @Override
    @Transactional(rollbackOn =  Exception.class)
    public void cancelRecommendData(Long mId, List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)){
            throw new BizException("取消推荐数据失败，sku为空");
        }
        this.addData(mId, skuList, null, FrequentSkuPoolEnums.DataSource.RECOMMEND_POPUP_CANCEL.getCode());
        merchantFrequentlyBuyingSkuMapper.updateStatusByMIdAndSkuList(mId, skuList,
                FrequentSkuPoolEnums.Status.DELETE.getCode(), LocalDateTime.now().plusMonths(1L));
    }

    /**
     * @description 获取库存信息
     * @params [mId, skus]
     * @return java.util.Map<java.lang.String,net.summerfarm.mall.service.facade.dto.AreaStoreQueryRes>
     * <AUTHOR>
     * @date  2025/5/21 17:25
     */
    private Map<String, AreaStoreQueryRes> getAreaStoreMap(Long mId, Collection<String> skus) {
        if (mId == null || CollectionUtils.isEmpty(skus)) {
            log.error("mId:{}为空，或者skus为空，不可获取SKU库存:{}", mId, skus);
            return Collections.emptyMap();
        }
        // 查询库存
        Contact contact = contactService.getMerchantDefaultContactCache(mId);
        AreaStoreQueryReq queryReq = new AreaStoreQueryReq();
        queryReq.setContactId(contact != null ? contact.getContactId() : null);
        queryReq.setSkuCodeList(new ArrayList<>(skus));
        queryReq.setMId(mId);
        queryReq.setSource(DistOrderSourceEnum.getDistOrderSource(RequestHolder.getBusinessLine()));
        return wmsAreaStoreFacade.getInfo(queryReq);
    }

    /**
     * 初始化通知配置
     * 此方法用于创建并初始化一个商户频繁购买商品的通知配置对象，设置默认通知状态，
     * 并将其保存到数据库中
     *
     * @param mId 商户ID，用于关联通知配置与特定商户
     * @return 返回初始化后的通知配置对象
     */
    private MerchantFrequentlyBuyingSkuNotificationConfig initNotificationConfig(Long mId) {
        MerchantFrequentlyBuyingSkuNotificationConfig config = new MerchantFrequentlyBuyingSkuNotificationConfig();
        config.setMId(mId);
        config.setGoodsArrived(CommonStatus.YES.getCode());
        config.setSpecialOffer(CommonStatus.YES.getCode());
        merchantFrequentlyBuyingSkuNotificationConfigMapper.insert(config);
        return config;
    }

    /**
     * 判断是否是第一次查询
     * @param mId 商户ID
     * @return 如果是第一次查询，返回true；否则返回false
     */
    private boolean isFirstTimeQuery(Long mId){
        if (mId == null){
            throw new BizException("请先登录");
        }
        return merchantFrequentlyBuyingSkuNotificationConfigMapper.countByMId(mId) <= 0;
    }

    /**
     * 获取最新数据的同步日期
     * @return 最新数据的同步日期
     */
    @InMemoryCache(expiryTimeInSeconds = 120)
    private String getDayTag(){
        return merchantSkuOrderDataMapper.selectMaxDayTag();
    }
}
