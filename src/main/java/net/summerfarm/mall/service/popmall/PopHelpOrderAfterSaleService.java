package net.summerfarm.mall.service.popmall;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.after.template.AfterSaleOrderAuditHandler;
import net.summerfarm.mall.after.template.impl.AuditAfterSolution;
import net.summerfarm.mall.enums.AfterSaleDeliveryedEnum;
import net.summerfarm.mall.factory.AfterSaleSolutionOrderFactory;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.OrderItemVO;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * pop代下单 售后服务
 *
 * <AUTHOR>
 * @Date 2025/4/25 16:12
 * @Version 1.0
 */
@Slf4j
@Component
public class PopHelpOrderAfterSaleService {

    @Autowired
    private OrdersMapper ordersMapper;
    @Autowired
    private OrderItemMapper orderItemMapper;
    @Autowired
    private OrderItemExtraMapper orderItemExtraMapper;
    @Autowired
    private AfterSaleOrderMapper afterSaleOrderMapper;

    @Lazy
    @Autowired
    private AfterSaleOrderService afterSaleOrderService;

    @Autowired
    private AfterSaleSolutionOrderFactory afterSaleSolutionOrderFactory;

    @Autowired
    private InventoryMapper inventoryMapper;

    @Autowired
    private ProductsMapper productsMapper;

    /**
     * pop订单售后单自动进行代下单售后
     *
     * @param afterSaleOrderVO 售后单信息
     * @param orders           pop订单信息
     */
    public void createHelpAfterSaleOrderByPopOrder(AfterSaleOrderVO afterSaleOrderVO, Orders orders) {
        //region 1. 前置条件判断，是否触发 「pop售后单自动进行代下单售后」
        if (!OrderTypeEnum.POP.getId().equals(orders.getType())) {
            return;
        }
        if (!this.checkAfterSaleType(afterSaleOrderVO.getHandleType(), afterSaleOrderVO.getDeliveryed())) {
            return;
        }
        log.info("开始进行pop订单售后自动代下单售后服务  afterSaleOrderVO >>> {} \n orders >>> {}", JSON.toJSONString(afterSaleOrderVO), JSON.toJSONString(orders));
        if (null == afterSaleOrderVO.getSku()) {
            throw new BizException("售后商品不存在");
        }
        List<OrderItemVO> orderItemVOS = orderItemMapper.selectOrderItemSuitId(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getSku(), afterSaleOrderVO.getSuitId(), afterSaleOrderVO.getProductType());
        if (CollectionUtils.isEmpty(orderItemVOS)) {
            log.error("\n 当前售后的订单明细不存在 afterSaleOrderVO >>> {} \n", JSON.toJSONString(afterSaleOrderVO));
            return;
        }
        OrderItemVO currentPopOrderItem = orderItemVOS.get(0);
        List<OrderItemExtra> orderItemExtras = orderItemExtraMapper.selectByOrderItemIds(Collections.singletonList(currentPopOrderItem.getId()));
        if (CollectionUtils.isEmpty(orderItemExtras) || null == orderItemExtras.get(0).getHelpOrderItemId()) {
            log.info("当前pop售后的明细不存在对应的代下单记录，结束。 >>> {} ", JSON.toJSONString(orderItemExtras));
            return;
        }
        //endregion

        //region 2. 组装代下单售后单的请求参数
        List<OrderItem> helpOrderItemList = orderItemMapper.selectListByItemIds(Collections.singletonList(orderItemExtras.get(0).getHelpOrderItemId()));
        if (CollectionUtils.isEmpty(helpOrderItemList)) {
            log.error("\n 获取代下单订单明细失败 >>> {} \n", JSON.toJSONString(orderItemExtras));
            throw new BizException("售后失败");
        }
        OrderItem helpOrderItem = helpOrderItemList.get(0);
        Orders helpOrder = ordersMapper.selectByOrderNo(helpOrderItem.getOrderNo());
        if (null == helpOrder) {
            log.error("\n 获取代下单订单信息失败 >>> {} \n", JSON.toJSONString(orderItemExtras));
            throw new BizException("售后失败，订单信息异常，请联系客服处理。");
        }

        // 是否是已到货售后
        boolean isDeliveryAfterSale = AfterSaleDeliveryedEnum.BROKEN.getType().equals(afterSaleOrderVO.getDeliveryed());

        // 开始组装代下单的售后单参数啦
        AfterSaleOrderVO helpOrderAfterSaleOrderReq = new AfterSaleOrderVO();
        // 核心字段
        helpOrderAfterSaleOrderReq.setOrderNo(helpOrder.getOrderNo());
        helpOrderAfterSaleOrderReq.setSku(helpOrderItem.getSku());
        helpOrderAfterSaleOrderReq.setSuitId(helpOrderItem.getSuitId());
        helpOrderAfterSaleOrderReq.setProductType(helpOrderItem.getProductType());
        helpOrderAfterSaleOrderReq.setmId(helpOrder.getmId());

        // 源自pre接口的返回值字段 - 2025-06-25改为直接取货品表和商品表
        Inventory inventory = inventoryMapper.selectBySku(helpOrderItem.getSku());
        if (null == inventory) {
            log.info("代下单售后失败，sku信息异常 >>> {}", JSON.toJSONString(helpOrderItem));
            throw new BizException("售后失败，商品信息异常，请联系客服处理。");
        }
        Products products = productsMapper.selectByPrimaryKey(inventory.getPdId());
        if (null == products) {
            log.info("代下单售后失败，商品信息异常 >>> {}", JSON.toJSONString(inventory));
            throw new BizException("售后失败，商品信息异常，请联系客服处理。");
        }
        helpOrderAfterSaleOrderReq.setAfterSaleQuantity(inventory.getAfterSaleQuantity());
        helpOrderAfterSaleOrderReq.setAfterSaleTime(products.getAfterSaleTime());
        helpOrderAfterSaleOrderReq.setAfterSaleUnit(inventory.getAfterSaleUnit());
        helpOrderAfterSaleOrderReq.setCategoryId(products.getCategoryId());

        // 继承自源pop售后单的字段
        helpOrderAfterSaleOrderReq.setApplyRemark(afterSaleOrderVO.getApplyRemark());
        helpOrderAfterSaleOrderReq.setProofPic(afterSaleOrderVO.getProofPic());
        helpOrderAfterSaleOrderReq.setProofVideo(afterSaleOrderVO.getProofVideo());
        helpOrderAfterSaleOrderReq.setDeliveryed(afterSaleOrderVO.getDeliveryed());
        helpOrderAfterSaleOrderReq.setHandleType(this.getHelpOrderHandleType(afterSaleOrderVO.getHandleType()));
        helpOrderAfterSaleOrderReq.setIsManage(afterSaleOrderVO.getIsManage());
        helpOrderAfterSaleOrderReq.setType(afterSaleOrderVO.getType());
        helpOrderAfterSaleOrderReq.setRefundType(afterSaleOrderVO.getRefundType());
        helpOrderAfterSaleOrderReq.setSnapshot(afterSaleOrderVO.getSnapshot());

        // 需要计算的字段 （数量和金额）
        AjaxResult maxQuantityAjaxResult = afterSaleOrderService.getMaxQuantity(helpOrderAfterSaleOrderReq);
        log.info("代下单售后最大可售后数量 >>> {}", JSON.toJSONString(maxQuantityAjaxResult));
        if (!AjaxResult.isSuccess(maxQuantityAjaxResult)) {
            log.info("代下单售后失败，计算最大可售后数量失败 >>> {}", JSON.toJSONString(helpOrderAfterSaleOrderReq));
            throw new BizException("售后失败，" + maxQuantityAjaxResult.getMsg());
        }
        helpOrderAfterSaleOrderReq.setQuantity((Integer) maxQuantityAjaxResult.getData());

        AjaxResult afterSaleMaxMoneyAjaxResult = afterSaleOrderService.getAfterSaleMoney(helpOrderAfterSaleOrderReq);
        log.info("代下单售后最大可售后金额 >>> {}", JSON.toJSONString(afterSaleMaxMoneyAjaxResult));
        if (!AjaxResult.isSuccess(afterSaleMaxMoneyAjaxResult)) {
            log.info("代下单售后失败，计算最大可售后金额失败 >>> {}", JSON.toJSONString(afterSaleMaxMoneyAjaxResult));
            throw new BizException("售后失败，" + afterSaleMaxMoneyAjaxResult.getMsg());
        }
        helpOrderAfterSaleOrderReq.setHandleNum((BigDecimal) afterSaleMaxMoneyAjaxResult.getData());

        if (isDeliveryAfterSale){
            helpOrderAfterSaleOrderReq.setIsManage(true);

            helpOrderAfterSaleOrderReq.setQuantity(Math.min(afterSaleOrderVO.getQuantity(), (Integer) maxQuantityAjaxResult.getData()));
            log.info("【代下单售后可售后数量计算过程】最大数量 >>> {} 原pop售后数量 >>> {}, 最终数量>>> {}",
                    maxQuantityAjaxResult.getData(), afterSaleOrderVO.getQuantity(), helpOrderAfterSaleOrderReq.getQuantity());

            AjaxResult afterSaleMoneyAjaxResult = afterSaleOrderService.getAfterSaleMoney(helpOrderAfterSaleOrderReq);
            log.info("代下单售后可售后金额 >>> {}", JSON.toJSONString(afterSaleMoneyAjaxResult));
            if (!AjaxResult.isSuccess(afterSaleMoneyAjaxResult)) {
                log.info("代下单售后失败，计算可售后金额失败 >>> {}", JSON.toJSONString(afterSaleMoneyAjaxResult));
                throw new BizException("售后失败，" + afterSaleMoneyAjaxResult.getMsg());
            }
            BigDecimal afterSaleMaxMoney = (BigDecimal) afterSaleMaxMoneyAjaxResult.getData();
            BigDecimal afterSaleMoney = (BigDecimal) afterSaleMoneyAjaxResult.getData();
            helpOrderAfterSaleOrderReq.setHandleNum(afterSaleMaxMoney.min(afterSaleMoney));
            log.info("【代下单售后可售后金额计算过程】最大金额afterSaleMaxMoney >>> {} 当前数量金额afterSaleMoney >>> {}, 最终金额>>> {}",
                    afterSaleMaxMoney, afterSaleMoney, helpOrderAfterSaleOrderReq.getHandleNum());
        }
        //endregion

        //region 3. 保存代下单售后单
        AjaxResult saveAfterSaleResult;
        try {
            log.info("开始保存代下单售后单 >>> {}", JSON.toJSONString(helpOrderAfterSaleOrderReq));
            saveAfterSaleResult = afterSaleOrderService.newSave(helpOrderAfterSaleOrderReq);
        } catch (Exception e) {
            throw new BizException("售后失败，" + e.getMessage());
        }
        if (!AjaxResult.isSuccess(saveAfterSaleResult)) {
            log.info("POP售后成功，但保存代下单售后单失败 >>> {}", JSON.toJSONString(saveAfterSaleResult));
            throw new BizException("售后失败，" + saveAfterSaleResult.getMsg());
        }
        //endregion

        //region 4. 自动审批通过
        if (AfterSaleDeliveryedEnum.BROKEN.getType().equals(helpOrderAfterSaleOrderReq.getDeliveryed())
                && AfterSaleHandleType.ENTRY_BILL.getType().equals(helpOrderAfterSaleOrderReq.getHandleType())) {
            List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderMapper.selectAfterQuantityByOrderNo(helpOrderAfterSaleOrderReq.getOrderNo(), helpOrderAfterSaleOrderReq.getSku(), null, helpOrderAfterSaleOrderReq.getSuitId(), null, helpOrderAfterSaleOrderReq.getProductType());
            if (CollectionUtils.isEmpty(afterSaleOrderVOS)) {
                throw new BizException(("售后失败，代下单售后单不存在" + helpOrderAfterSaleOrderReq.getOrderNo()));
            }
            helpOrderAfterSaleOrderReq.setAfterSaleOrderNo(afterSaleOrderVOS.get(0).getAfterSaleOrderNo());
            helpOrderAfterSaleOrderReq.setStatus(AfterSaleOrderStatus.IN_HAND.getStatus());
            helpOrderAfterSaleOrderReq.setAuditer(!StringUtils.isEmpty(afterSaleOrderVO.getAuditer()) ? afterSaleOrderVO.getAuditer() : afterSaleOrderVO.getApplyer());
            AuditAfterSolution auditAfterSolution = afterSaleSolutionOrderFactory.getAuditSolution(helpOrderAfterSaleOrderReq, helpOrder);
            AfterSaleOrderAuditHandler afterSaleOrderAuditHandler = auditAfterSolution.getAfterSaleOrderAuditHandler();
            AjaxResult auditAjaxResult = afterSaleOrderAuditHandler.auditAfterSaleOrder(auditAfterSolution, helpOrderAfterSaleOrderReq, helpOrder);
            if (!AjaxResult.isSuccess(auditAjaxResult)) {
                throw new BizException("售后失败，" + auditAjaxResult.getMsg());
            }
        }
        //endregion

        log.info("pop代下单 售后服务 end");
    }

    /**
     * 获取代下单售后单的售后类型
     *
     * @param popAfterSaleHandleType pop售后类型
     * @return 代下单售后单的售后类型
     */
    private Integer getHelpOrderHandleType(Integer popAfterSaleHandleType) {
        Map<Integer, Integer> handleTypeMapping = new HashMap<>();
        // 映射退款类型到录入账单类型，表示代下单订单需要进行录入账单类型处理
        handleTypeMapping.put(AfterSaleHandleType.REFUND.getType(), AfterSaleHandleType.ENTRY_BILL.getType());

        // 映射录入账单类型类型到录入账单类型，表示代下单订单也需要进行录入账单类型处理
        handleTypeMapping.put(AfterSaleHandleType.ENTRY_BILL.getType(), AfterSaleHandleType.ENTRY_BILL.getType());

        // 映射退货退款类型到录入账单类型，表示在代下单订单中仅需要进行退款处理，不需要处理退货
        handleTypeMapping.put(AfterSaleHandleType.REFUND_GOODS.getType(), AfterSaleHandleType.ENTRY_BILL.getType());

        // 映射退款入账单类型到录入账单类型，表示在代下单订单中仅需要进行录入账单处理，不需要处理退货
        handleTypeMapping.put(AfterSaleHandleType.REFUND_ENTRY_BILL.getType(), AfterSaleHandleType.ENTRY_BILL.getType());
        Integer helpOrderHandleType = handleTypeMapping.get(popAfterSaleHandleType);
        if (helpOrderHandleType == null) {
            log.info("pop代下单 售后服务 映射失败 {} >>> {}", popAfterSaleHandleType, handleTypeMapping);
            throw new BizException("当前订单不支持该售后类型 >>> " + AfterSaleHandleType.getAfterSaleHandleType(popAfterSaleHandleType).getDescription());
        }
        return helpOrderHandleType;
    }

    /**
     * 检查当前售后类型是否需要进行处理对应的代下单
     *
     * @param popAfterSaleHandleType pop售后类型
     * @param deliveryed             已到货还是未到货
     * @return true 需要处理，false 不需要处理
     */
    private boolean checkAfterSaleType(Integer popAfterSaleHandleType, Integer deliveryed) {
        // 未到货退款
        if (AfterSaleDeliveryedEnum.NOT_NEED.getType().equals(deliveryed)
                && AfterSaleHandleType.REFUND.getType().equals(popAfterSaleHandleType)) {
            return true;
        }

        // 已到货退货退款
        if (AfterSaleDeliveryedEnum.BROKEN.getType().equals(deliveryed)
                && AfterSaleHandleType.REFUND_GOODS.getType().equals(popAfterSaleHandleType)) {
            return true;
        }

        // 已到货仅退款
        if (AfterSaleDeliveryedEnum.BROKEN.getType().equals(deliveryed)
                && AfterSaleHandleType.REFUND.getType().equals(popAfterSaleHandleType)){
            return true;
        }

        return false;
    }
}
