package net.summerfarm.mall.service.strategy.coupon;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum;
import net.summerfarm.enums.coupon.CouponStatus;
import net.summerfarm.mall.enums.CouponReceiveTypeEnum;
import net.summerfarm.mall.enums.CouponScopeTypeEnum;
import net.summerfarm.mall.enums.MerchantSizeEnum;
import net.summerfarm.mall.enums.market.ReceiveCouponErrorEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.bo.coupon.CouponSenderBO;
import net.summerfarm.mall.model.bo.coupon.ReceiveIdCountBO;
import net.summerfarm.mall.model.build.coupon.CouponReceiveLogBuild;
import net.summerfarm.mall.model.build.coupon.MerchantCouponBuilder;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.service.ConfigService;
import net.summerfarm.mall.service.CouponSenderSetupService;
import net.summerfarm.mall.service.MerchantPoolService;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.enums.coupon.ConfigCouponKeyEnum.DEFAULT_INVITE_COUPON;
import static net.summerfarm.enums.coupon.ConfigCouponKeyEnum.DEFAULT_NEW_REGISTRATION_COUPON;
import static net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum.*;
import static net.summerfarm.enums.coupon.CouponSenderSetupStatusEnum.IN_EFFECT;

/**
 * 卡券发放统一入口
 * 目前只针对三种事件进行卡券发放 1.用户领取 2.新人注册立即发放 3.推荐好友下单 4.活动领取（满返、抽奖） 5.售后返券
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021/11/25
 */
@Slf4j
@Component
public class CouponSenderContext {

    @Resource
    CouponMapper couponMapper;

    @Resource
    CouponSenderSetupMapper couponSenderSetupMapper;

    @Resource
    CouponSenderRelationMapper couponSenderRelationMapper;

    @Resource
    CouponReceiveLogMapper couponReceiveLogMapper;

    @Resource
    MerchantCouponMapper merchantCouponMapper;

    @Resource
    MerchantMapper merchantMapper;

    @Resource
    ConfigService configService;

    @Resource
    CouponSenderSetupService couponSenderSetupService;

    @Resource
    private CouponSenderRuleMapper couponSenderRuleMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private MerchantPoolService merchantPoolService;

    @Transactional(rollbackFor = Exception.class)
    public List<Coupon> sendCoupon(CouponSenderBO couponSenderBO){

        checkParams(couponSenderBO);

        List<Coupon> coupons;
        if(couponSenderBO.getSenderType() == USER_RECEIVE){
            coupons = userReceive(couponSenderBO);
        }else if(couponSenderBO.getSenderType() == REGISTRATION_SEND ||
                couponSenderBO.getSenderType() == RECOMMENDED_ORDER){
            coupons = autoSendCoupon(couponSenderBO);
        }else{
            throw new BizException("领取失败");
        }
        return coupons;
    }

    /**
     * 自动发券逻辑
     * @param couponSenderBO 发券参数
     * @return
     */
    private List<Coupon> autoSendCoupon(CouponSenderBO couponSenderBO) {
        Long mId = couponSenderBO.getMId();
        Merchant merchant = merchantMapper.selectOneByMid(mId);
        if (StringUtils.isBlank(merchant.getSize())) {
            merchant.setSize(couponSenderBO.getSize());
            merchant.setAdminId(couponSenderBO.getAdminId());
        }

        CouponSenderSetup couponSenderSetupCondition = new CouponSenderSetup();
        couponSenderSetupCondition.setSenderType(couponSenderBO.getSenderType().getType());
        couponSenderSetupCondition.setStatus(IN_EFFECT.getStatus());

        //大客户走发放设置为品牌管理类型
        if (MerchantSizeEnum.MAJOR_CUSTOMER.getValue().equals(merchant.getSize())) {
            if (merchant.getAdminId() == null) {
                log.info("CouponSenderContext[]autoSendCoupon[]adminId is null! couponSenderBO:{}", JSON.toJSONString(couponSenderBO));
                return Collections.emptyList();
            }

            couponSenderSetupCondition.setType(CouponScopeTypeEnum.ADMIN.getCode());
            couponSenderSetupCondition.setStartTime(LocalDateTime.now());
            List<CouponSenderSetup> adminCouponSenderSetups = couponSenderSetupMapper.selectByAll(couponSenderSetupCondition);
            if (CollectionUtils.isEmpty(adminCouponSenderSetups)) {
                log.info("CouponSenderContext[]autoSendCoupon[]adminCouponSenderSetups is empty! couponSenderBO:{}", JSON.toJSONString(couponSenderBO));
                return Collections.emptyList();
            }

            //查询发放设置的圈人规则
            List<Integer> sendIds = adminCouponSenderSetups.stream()
                    .map(CouponSenderSetup::getId).collect(Collectors.toList());
            List<CouponSenderRule> couponSenderRules =  couponSenderRuleMapper.getListBySendIdsAndScopeTypeAndScopeId(sendIds,
                    CouponScopeTypeEnum.ADMIN.getCode(), merchant.getAdminId().longValue());
            if (CollectionUtil.isEmpty(couponSenderRules)) {
                log.info("CouponSenderContext[]autoSendCoupon[]couponSenderRules is empty! couponSenderBO:{}", JSON.toJSONString(couponSenderBO));
                return Collections.emptyList();
            }
            Set<Integer> couponSenderIds = couponSenderRules.stream().map(CouponSenderRule::getCouponSenderId).collect(Collectors.toSet());

            //过滤出满足条件的发放设置
            adminCouponSenderSetups = adminCouponSenderSetups.stream().filter(e -> couponSenderIds.contains(e.getId())).collect(Collectors.toList());
            log.info("品牌客户最终发放设置id集合为:{}", JSON.toJSONString(couponSenderIds));
            return autoSendCoupon(merchant, adminCouponSenderSetups, false);
        }

        Integer areaNo = merchant.getAreaNo();

        // 目前自动发券的只允许配置区域，所以查询条件增加区域规则限制
        couponSenderSetupCondition.setType(CouponScopeTypeEnum.CITY.getCode());
        couponSenderSetupCondition.setStartTime(LocalDateTime.now());
        List<CouponSenderSetup> couponSenderSetups = couponSenderSetupMapper.selectByAll(couponSenderSetupCondition);

        //先排除兜底的发放设置
        couponSenderSetups = couponSenderSetupService.excludeDefaultSetup(couponSenderSetups);
        log.info("当前有效发放配置为：{}", JSON.toJSONString(couponSenderSetups));
        if(CollectionUtils.isEmpty(couponSenderSetups)){
            // 查询兜底发放设置
            return sendDefaultCoupon(merchant, couponSenderBO);
        }

        // 基于放已生效的发设置以及区域获取到当前区域对应的发放设置
        Set<Integer> couponSenderSetupIds = couponSenderSetups.stream()
                .map(CouponSenderSetup::getId).collect(Collectors.toSet());
        if(REGISTRATION_SEND == couponSenderBO.getSenderType() && couponSenderBO.getAreaNo() != null){
            areaNo = couponSenderBO.getAreaNo();
        }

        //查询发放设置的圈人规则
        List<Integer> sendIds = new ArrayList<>(couponSenderSetupIds);
        List<CouponSenderRule> couponSenderRules =  couponSenderRuleMapper.getListBySendIdsAndScopeTypeAndScopeId(sendIds, CouponScopeTypeEnum.CITY.getCode(), areaNo.longValue());
        if (CollectionUtil.isEmpty(couponSenderRules)) {
            // 查询兜底发放设置
            return sendDefaultCoupon(merchant, couponSenderBO);
        }
        Set<Integer> couponSenderSetupIdCollect = couponSenderRules.stream().map(CouponSenderRule::getCouponSenderId).collect(Collectors.toSet());

        log.info("最终发放设置id集合为:{}", JSON.toJSONString(couponSenderSetupIdCollect), JSON.toJSONString(couponSenderBO));

        List<CouponSenderSetup> senderSetupList = couponSenderSetups.stream().
                filter(couponSenderSetup -> couponSenderSetupIdCollect.contains(couponSenderSetup.getId())).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(senderSetupList)){
            // 正常情况不会执行到这一步
            log.error("匹配发放设置异常，未匹配到相应的发放设置:{},目标发放设置id:{}", JSON.toJSONString(couponSenderSetups), JSON.toJSONString(couponSenderSetupIdCollect));
            return sendDefaultCoupon(merchant, couponSenderBO);
        }
        return autoSendCoupon(merchant, senderSetupList, false);

    }

    private List<Coupon> autoSendCoupon(Merchant merchant, List<CouponSenderSetup> senderSetupList, boolean isDefault) {
        List<Integer> couponSenderSetupIdList = senderSetupList.stream().map(CouponSenderSetup::getId).collect(Collectors.toList());

        // 查询优惠券数据发放卡券
        List<CouponSenderRelation> couponSenderRelations =
                couponSenderRelationMapper.selectByCouponSenderIdIn(couponSenderSetupIdList);
        Set<Integer> couponIds = couponSenderRelations.stream().map(CouponSenderRelation::getCouponId).collect(Collectors.toSet());
        List<Coupon> coupons = couponMapper.selectByIdIn(couponIds);

        //发放设置有重复的卡劵发放多次
        Map<Integer, Coupon> couponMap = coupons.stream().collect(Collectors.toMap(Coupon::getId, coupon -> coupon, (p1, p2) -> p2));
        List<Coupon> couponList = new ArrayList<>(couponSenderRelations.size());
        couponSenderRelations.stream().forEach(couponSenderRelation -> {
            Coupon coupon = couponMap.get(couponSenderRelation.getCouponId());
            if (Objects.nonNull(coupon)) {
                couponList.add(coupon);
            }
        });
        CouponSenderSetup couponSenderSetup = senderSetupList.get(0);

        //发放卡劵 --采用分布式锁模式 防止定时任务和MQ同时执行导致发放多张卡劵
        RLock lock = redissonClient.getLock("lock:auto-send-coupon:" + merchant.getmId());
        try {
            lock.lock();
            sendCoupon(couponList, merchant, couponSenderSetup, CouponReceiveTypeEnum.AUTO_RECEIVE.getCode(), isDefault);
        } finally {
            lock.unlock();
        }
        return coupons;
    }

    private List<Coupon> sendDefaultCoupon(Merchant merchant, CouponSenderBO couponSenderBO) {
        String configKey;
        if(couponSenderBO.getSenderType() == REGISTRATION_SEND){
            configKey = DEFAULT_NEW_REGISTRATION_COUPON.getKey();
        }else{
            configKey = DEFAULT_INVITE_COUPON.getKey();
        }

        //查询兜底色设置卡劵
        String config = configService.getValue(configKey);
        if(config == null || StringUtils.isBlank(config)){
            throw new DefaultServiceException("发放默认券失败，原因：默认卡券发放规则未配置");
        }

        int couponSenderSetupId = Integer.parseInt(config);
        CouponSenderSetup couponSenderSetup = couponSenderSetupMapper.selectByPrimaryKey(couponSenderSetupId);

        //隐藏推荐好友发放卡劵功能--下次放开的话 直接修改推荐好友下单发放配置的状态即可
        if (Objects.isNull(couponSenderSetup) || !Objects.equals(couponSenderSetup.getStatus(), IN_EFFECT.getStatus())) {
            log.error("发放默认券失败，原因：隐藏推荐好友发放卡劵功能,couponSenderBO:{}", JSON.toJSONString(couponSenderBO));
            return Collections.emptyList();
        }
        return autoSendCoupon(merchant, Collections.singletonList(couponSenderSetup), true);
    }

    /**
     * 用户领取卡券--主动领取卡劵情况下需要校验领取次数
     * @param couponSenderBO 用户领取业务逻辑参数
     * @return
     */
    private List<Coupon> userReceive(CouponSenderBO couponSenderBO) {
        Long mId = couponSenderBO.getMId();
        Integer couponId = couponSenderBO.getCouponId();
        Coupon coupon = couponMapper.selectByPrimaryKey(couponId);

        if(coupon == null){
            throw new BizException("优惠券不存在！");
        }

        if(coupon.getStatus() != null && !CouponStatus.isEffect(coupon.getStatus())){
            throw new BizException("抱歉您来晚啦，优惠券已过期！");
        }

        if(Objects.nonNull(coupon.getVaildDate()) && coupon.getVaildDate().isBefore(couponSenderBO.getTriggerTime())){
            throw new BizException("抱歉您来晚啦，优惠券已过期！");
        }

        CouponSenderSetup couponSenderSetup;
        if (Objects.nonNull(couponSenderBO.getCouponSenderId())) {
            couponSenderSetup = couponSenderSetupMapper.selectByPrimaryKey(couponSenderBO.getCouponSenderId());
        } else {
            couponSenderSetup = couponSenderSetupMapper.selectByCouponIdAndStatus(couponId, IN_EFFECT.getStatus(), couponSenderBO.getSenderType().getType());
        }

        // 是否有效校验
        couponSenderSetupCheck(couponSenderSetup, couponSenderBO.getTriggerTime());

        // 发放设置中的规则校验
        boolean ruleCheckResult = senderRuleCheck(mId, couponSenderSetup);
        if(!ruleCheckResult){
            throw new DefaultServiceException("很抱歉，您不符合领取条件！");
        }

        //校验当前卡劵是否未使用且未过期
        List<ReceiveIdCountBO> receiveIdCountBOList = merchantCouponMapper.selectUnusedByReceiveIdIn(Collections.singleton(couponId), mId);
        if (CollectionUtils.isNotEmpty(receiveIdCountBOList)) {
            throw new DefaultServiceException("领取失败，您已领取过当前优惠券！");
        }

        //校验卡劵限制张数和次数
        List<ReceiveIdCountBO> receiveIdCountBos = merchantCouponMapper.getUserReceiveCount(Collections.singletonList(couponId), mId, CouponReceiveTypeEnum.RECEIVE.getCode());
        Map<Integer, ReceiveIdCountBO> countBOMap = receiveIdCountBos.stream().collect(Collectors.toMap(x -> x.getCouponId(), Function.identity(), (p1, p2) -> p2));

        //1、限制领取次数的情况下判断次数是否用完
        if (coupon.getQuantityClaimed() > 0) {
            ReceiveIdCountBO receiveIdCountBO = countBOMap.get(coupon.getId());
            if (Objects.nonNull(receiveIdCountBO) && receiveIdCountBO.getNum() >= coupon.getQuantityClaimed()) {
                throw new BizException("抱歉，优惠券领取已达上限！");
            }
        }

        //2、限制张数情况下判断余量是否充足
        if (coupon.getGrantLimit() > 0 && coupon.getGrantAmount() <= 0) {
            throw new BizException("抱歉您来晚啦，优惠券已抢光！");
        }

        //3、扣减卡劵数量 领取限制张数情况下
        if (Objects.nonNull(coupon.getGrantAmount()) && coupon.getGrantLimit() > 0) {
            int update = couponMapper.updateGrantAmount(1, coupon.getId());
            if (update <= 0) {
                throw new BizException("抱歉您来晚啦，优惠券已抢光！");
            }
        }

        //用户卡劵信息
        MerchantCoupon merchantCoupon = MerchantCouponBuilder.build(coupon, mId,
                couponSenderSetup.getName(), LocalDateTime.now());
        //重新设置优惠券类型
        merchantCoupon.setReceiveType(CouponReceiveTypeEnum.RECEIVE.getCode());

        //日志表
        CouponReceiveLog couponReceiveLog = CouponReceiveLogBuild.build(mId,
                couponId,couponSenderSetup.getId(), LocalDateTime.now());

        //保存信息
        merchantCouponMapper.insertSelective(merchantCoupon);
        couponReceiveLogMapper.insertSelective(couponReceiveLog);
        return Collections.singletonList(coupon);
    }

    /**
     * @description: 校验卡劵发放规则--目前只校验圈人
     * @author: lzh
     * @date: 2023/7/28 15:57
     * @param: [mId, couponSenderSetup]
     * @return: boolean
     **/
    private boolean senderRuleCheck(Long mId, CouponSenderSetup couponSenderSetup) {
        if(Objects.equals(couponSenderSetup.getType(), CouponScopeTypeEnum.CROWD.getCode())){
            return circleRuleCheck(mId, couponSenderSetup);
        }else {
            return Boolean.TRUE;
        }
    }

    /**
     * @description: 校验卡劵发放规则--圈人2.0
     * @author: lzh
     * @date: 2023/7/28 15:57
     * @param: [mId, couponSenderSetup]
     * @return: boolean
     **/
    private boolean circleRuleCheck(Long mId, CouponSenderSetup couponSenderSetup) {
        //查询圈人规则
        List<CouponSenderRule> senderRuleList = couponSenderRuleMapper.getListBySendId(couponSenderSetup.getId(), CouponScopeTypeEnum.CROWD.getCode());
        if (CollectionUtils.isEmpty(senderRuleList)) {
            return Boolean.FALSE;
        }
        Set<Long> poolInfoIds = senderRuleList.stream().map(CouponSenderRule::getScopeId).collect(Collectors.toSet());

        //获取圈人信息
        List<MerchantPoolDetail> detailByMIdWithCache = merchantPoolService.getDetailByMIdWithCache(mId);
        if (CollectionUtil.isEmpty(detailByMIdWithCache)) {
            log.info("圈人信息为空，mId={}", mId);
            return Boolean.FALSE;
        }
        List<MerchantPoolDetail> merchantPoolDetailList = detailByMIdWithCache.stream().filter(x -> poolInfoIds.contains(x.getPoolInfoId()))
                .collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(merchantPoolDetailList);
    }

    /**
     * 卡券发放设置是否有效校验
     * @param couponSenderSetup
     * @param triggerTime
     */
    private void couponSenderSetupCheck(CouponSenderSetup couponSenderSetup,
                                        LocalDateTime triggerTime) {
        // 查询卡券发放设置
        if(couponSenderSetup == null){
            throw new DefaultServiceException("发放设置不存在，卡券已过期");
        }

        //校验是否新人注册劵-可能用户卡包里面的不能再次领取
        if (!Objects.equals(couponSenderSetup.getSenderType(), USER_RECEIVE.getType())) {
            throw new DefaultServiceException("卡券不能用户领取！");
        }

        if(triggerTime.isAfter(couponSenderSetup.getEndTime())){
            throw new DefaultServiceException("很抱歉，该优惠券已失效");
        }
    }

    /**
     * 参数合法性验证
     * @param couponSenderBO 卡券领取id
     */
    private void checkParams(CouponSenderBO couponSenderBO) {
        if (couponSenderBO == null) {
            throw new DefaultServiceException("无效的参数，参数为null");
        }

        CouponSenderSetupSenderTypeEnum senderType = couponSenderBO.getSenderType();

        Long mId = couponSenderBO.getMId();

        if(senderType == null){
            throw new DefaultServiceException("无效的发放方式");
        }

        Integer couponId = couponSenderBO.getCouponId();
        boolean couponIdIsValid = couponId == null || couponId < 0;
        if(senderType == USER_RECEIVE && couponIdIsValid){
            throw new DefaultServiceException("无效的优惠券");
        }

        if(mId == null || mId < 0){
            throw new DefaultServiceException("无效的商户id");
        }
    }

    /**
     * 生成商户券记录--有余量的卡劵发放成功，被动领取卡劵情况下不需要校验领取次数
     * @param coupons 优惠券
     * @param merchant 商户
     * @param couponSenderSetup 优惠券发放设置
     * @param receiveType
     */
    private void sendCoupon(List<Coupon> coupons, Merchant merchant, CouponSenderSetup couponSenderSetup, Integer receiveType, boolean isDefault){
        log.info("CouponSenderContext[]sendCoupon[]start[]coupons:{}, merchant:{}, couponSenderSetup:{}, receiveType:{}, isDefault:{}",
                JSON.toJSONString(coupons), JSON.toJSONString(merchant), JSON.toJSONString(couponSenderSetup), receiveType, isDefault);

        //获取是否已经发放过卡劵 防止重复领取
        Long mId = merchant.getmId();
        List<Integer> couponIds = coupons.stream().map(Coupon::getId).collect(Collectors.toList());
        List<ReceiveIdCountBO> userReceiveCount = merchantCouponMapper.getUserReceiveCount(couponIds, mId, receiveType);
        if (!CollectionUtils.isEmpty(userReceiveCount)) {
            log.error("CouponSenderContext[]sendCoupon[]repeat draw userReceiveCount:{},mId:{}", JSON.toJSONString(userReceiveCount), mId);
            return;
        }

        LocalDateTime nowTime = LocalDateTime.now();
        List<CouponReceiveLog> couponReceiveLogs = new ArrayList<>();
        List<MerchantCoupon> merchantCoupons = new ArrayList<>();
        for (Coupon coupon : coupons) {
            Integer couponId = coupon.getId();

            if(coupon.getStatus() != null && !CouponStatus.isEffect(coupon.getStatus())){
                log.error("CouponSenderContext[]sendCoupon[]coupon is effect couponSenderSetup:{},coupon:{},mId:{}", JSON.toJSONString(couponSenderSetup), JSON.toJSONString(coupon), mId);
                continue;
            }

            //限制张数情况下判断余量是否充足
            if (coupon.getGrantLimit() > 0 && coupon.getGrantAmount() <= 0) {
                log.error("CouponSenderContext[]sendCoupon[]quantity is not enough couponSenderSetup:{},coupon:{},mId:{}", JSON.toJSONString(couponSenderSetup), JSON.toJSONString(coupon), mId);
                continue;
            }

            //扣减卡劵数量 领取限制张数情况下
            if (Objects.nonNull(coupon.getGrantAmount()) && coupon.getGrantLimit() > 0) {
                int update = couponMapper.updateGrantAmount(1, coupon.getId());
                if (update <= 0) {
                    log.error("CouponSenderContext[]sendCoupon[]quantity is not enough couponSenderSetup:{},coupon:{},mId:{}", JSON.toJSONString(couponSenderSetup), JSON.toJSONString(coupon), mId);
                    continue;
                }
            }

            MerchantCoupon merchantCoupon = MerchantCouponBuilder.build(coupon, mId,
                    couponSenderSetup.getName(), nowTime);
            //优惠券类型
            merchantCoupon.setReceiveType(receiveType);
            //领卷
            merchantCoupons.add(merchantCoupon);
            //日志表
            CouponReceiveLog couponReceiveLog = CouponReceiveLogBuild.build(mId,
                    couponId,couponSenderSetup.getId(),nowTime);
            couponReceiveLogs.add(couponReceiveLog);
        }

        //发送失败情况下发放兜底劵--POP大客户不发放兜底券
        if (CollectionUtils.isEmpty(merchantCoupons) && Objects.equals(merchant.getSize(), MerchantSizeEnum.SINGLE_SHOP.getValue()) && !isDefault) {
            log.error("CouponSenderContext[]sendCoupon[]merchantCoupons is empty! start sendDefaultCoupon, couponSenderSetup:{}, merchant:{}",
                    JSON.toJSONString(couponSenderSetup), JSON.toJSONString(merchant));
            CouponSenderBO couponSenderBO = new CouponSenderBO();
            couponSenderBO.setMId(mId);
            if (couponSenderSetup.getSenderType().equals(REGISTRATION_SEND.getType())) {
                couponSenderBO.setSenderType(REGISTRATION_SEND);
            } else if (couponSenderSetup.getSenderType().equals(RECOMMENDED_ORDER.getType())){
                couponSenderBO.setSenderType(RECOMMENDED_ORDER);
            }
            this.sendDefaultCoupon(merchant, couponSenderBO);
        }

        //保存数据
        if (CollectionUtils.isNotEmpty(merchantCoupons)) {
            merchantCouponMapper.insertList(merchantCoupons);
        }
        if (CollectionUtils.isNotEmpty(couponReceiveLogs)) {
            couponReceiveLogMapper.insertList(couponReceiveLogs);
        }
    }

    /**
     * @description: 活动发放领劵统一入口-目前针对抽奖和满返活动
     * @author: lzh
     * @date: 2023/7/28 14:34
     * @param: [couponSenderBO]
     * @return: java.lang.Boolean
     **/
    @Transactional(rollbackFor = Exception.class)
    public Boolean sendActivityCoupon(CouponSenderBO couponSenderBO) {
        Coupon coupon = couponSenderBO.getCoupon();
        if (coupon == null) {
            log.error("CouponSenderContext[]sendActivityCoupon[]coupon not exist couponSenderBO:{}", JSON.toJSONString(couponSenderBO));
            return Boolean.FALSE;
        }

        if(coupon.getStatus() != null && !CouponStatus.isEffect(coupon.getStatus())){
            log.error("CouponSenderContext[]sendActivityCoupon[]coupon lose efficacy couponSenderBO:{}", JSON.toJSONString(couponSenderBO));
            return Boolean.FALSE;
        }

        if (Objects.nonNull(coupon.getVaildDate()) && coupon.getVaildDate().isBefore(LocalDateTime.now())) {
            log.error("CouponSenderContext[]sendActivityCoupon[]coupon expire coupon:{}", JSON.toJSONString(coupon));
            return Boolean.FALSE;
        }

        //限制张数情况下判断余量是否充足
        if (coupon.getGrantLimit() > 0 && coupon.getGrantAmount() <= 0) {
            log.error("CouponSenderContext[]sendActivityCoupon[]quantity is not enough,coupon:{},mId:{}", JSON.toJSONString(coupon), couponSenderBO.getMId());
            return Boolean.FALSE;
        }

        //扣减卡劵数量 领取限制张数情况下
        if (Objects.nonNull(coupon.getGrantAmount()) && coupon.getGrantLimit() > 0) {
            int update = couponMapper.updateGrantAmount(1, coupon.getId());
            if (update <= 0) {
                log.error("CouponSenderContext[]sendActivityCoupon[]quantity is not enough,coupon:{},mId:{}",  JSON.toJSONString(coupon), couponSenderBO.getMId());
                return Boolean.FALSE;
            }
        }

        MerchantCoupon merchantCoupon = MerchantCouponBuilder.build(coupon, couponSenderBO.getMId(),
                null, LocalDateTime.now());
        if (Objects.nonNull(couponSenderBO.getReceiveType())) {
            merchantCoupon.setReceiveType(couponSenderBO.getReceiveType());
        }
        merchantCoupon.setRelatedId(couponSenderBO.getRelatedId());
        log.info("CouponSenderContext[]sendActivityCoupon[]end send merchantCoupon:{}", JSON.toJSONString(merchantCoupon));
        merchantCouponMapper.insertSelective(merchantCoupon);
        return Boolean.TRUE;
    }

    /**
     * @description: 批量发放卡劵 活动发放领劵统一入口-目前针对抽奖和满返活动 抽奖和满返属于被动领劵只需要判断余量即可
     * @author: lzh
     * @date: 2023/7/28 15:09
     * @param: [couponSenderBO]
     * @return: java.util.List<net.summerfarm.mall.model.domain.MerchantCoupon>
     **/
    @Transactional(rollbackFor = Exception.class)
    public List<MerchantCoupon> batchSendActivityCoupon(CouponSenderBO couponSenderBO) {
        List<MerchantCoupon> merchantCoupons = new ArrayList<>();
        Long mId = couponSenderBO.getMId();
        Integer receiveType = couponSenderBO.getReceiveType();
        LocalDateTime now = LocalDateTime.now();
        couponSenderBO.getCoupons().stream().forEach(coupon -> {
            //卡劵是否到期-设置了到期时间
            if (Objects.nonNull(coupon.getVaildDate()) && coupon.getVaildDate().isBefore(LocalDateTime.now())) {
                log.error("CouponSenderContext[]batchSendActivityCoupon[]coupon expire coupon:{},mId:{}", JSON.toJSONString(coupon), mId);
                return;
            }

            //限制张数情况下判断余量是否充足
            if (coupon.getGrantLimit() > 0 && coupon.getGrantAmount() <= 0) {
                log.error("CouponSenderContext[]batchSendActivityCoupon[]quantity is not enough coupon:{},mId:{}", JSON.toJSONString(coupon), mId);
                return;
            }

            //扣减卡劵数量 领取限制张数情况下
            if (Objects.nonNull(coupon.getGrantAmount()) && coupon.getGrantLimit() > 0) {
                int update = couponMapper.updateGrantAmount(1, coupon.getId());
                if (update <= 0) {
                    log.error("CouponSenderContext[]batchSendActivityCoupon[]quantity is not enough coupon:{},mId:{}", JSON.toJSONString(coupon), mId);
                    return;
                }
            }

            MerchantCoupon merchantCoupon = MerchantCouponBuilder.build(coupon, mId,
                    null, now);

            //优惠券类型
            merchantCoupon.setReceiveType(receiveType);
            merchantCoupons.add(merchantCoupon);
        });

        if (CollectionUtils.isEmpty(merchantCoupons)) {
            return null;
        }

        merchantCouponMapper.insertList(merchantCoupons);
        return merchantCoupons;
    }

    /**
     * @description: 卡劵发放-不走发放设置-主动领取场景
     * @author: lzh
     * @date: 2023/8/4 17:52
     * @param: [couponSenderBO]
     * @return: void
     **/
    @Transactional(rollbackFor = Exception.class)
    public void receiveNew(CouponSenderBO couponSenderBO) {
        Integer couponId = couponSenderBO.getCouponId();
        Coupon coupon = couponMapper.selectByPrimaryKey(couponId);
        if (coupon == null) {
            couponSenderBO.setErrorEnum(ReceiveCouponErrorEnum.NOT_EXIST);
            throw new BizException(ReceiveCouponErrorEnum.NOT_EXIST.getValue());
        }

        if (coupon.getStatus() != null && !CouponStatus.isEffect(coupon.getStatus())) {
            couponSenderBO.setErrorEnum(ReceiveCouponErrorEnum.INVALID);
            throw new BizException(ReceiveCouponErrorEnum.INVALID.getValue());
        }

        if (Objects.nonNull(coupon.getVaildDate()) && coupon.getVaildDate().isBefore(LocalDateTime.now())) {
            log.error("CouponSenderContext[]receiveNew[]coupon expire coupon:{}", JSON.toJSONString(coupon));
            couponSenderBO.setErrorEnum(ReceiveCouponErrorEnum.INVALID);
            throw new BizException(ReceiveCouponErrorEnum.INVALID.getValue());
        }

        //校验当前卡劵是否未使用且未过期
        List<ReceiveIdCountBO> receiveIdCountBOList = merchantCouponMapper.selectUnusedByReceiveIdIn(Collections.singleton(couponId), couponSenderBO.getMId());
        if (CollectionUtils.isNotEmpty(receiveIdCountBOList)) {
            couponSenderBO.setErrorEnum(ReceiveCouponErrorEnum.EXIST_UNUSED);
            throw new BizException(ReceiveCouponErrorEnum.EXIST_UNUSED.getValue());
        }

        //校验卡劵限制张数和次数
        Long mId = couponSenderBO.getMId();
        List<ReceiveIdCountBO> receiveIdCountBos = merchantCouponMapper.getUserReceiveCount(Collections.singletonList(couponId), mId, CouponReceiveTypeEnum.RECEIVE.getCode());
        Map<Integer, ReceiveIdCountBO> countBOMap = receiveIdCountBos.stream().collect(Collectors.toMap(x -> x.getCouponId(), Function.identity(), (p1, p2) -> p2));

        //1、限制领取次数的情况下判断次数是否用完
        if (coupon.getQuantityClaimed() > 0) {
            ReceiveIdCountBO receiveIdCountBO = countBOMap.get(coupon.getId());
            if (Objects.nonNull(receiveIdCountBO) && receiveIdCountBO.getNum() >= coupon.getQuantityClaimed()) {
                couponSenderBO.setErrorEnum(ReceiveCouponErrorEnum.OVER_LIMIT);
                throw new BizException(ReceiveCouponErrorEnum.OVER_LIMIT.getValue());
            }
        }

        //限制张数情况下判断余量是否充足
        if (coupon.getGrantLimit() > 0 && coupon.getGrantAmount() <= 0) {
            log.error("CouponSenderContext[]receiveNew[]quantity is not enough,coupon:{},mId:{}", JSON.toJSONString(coupon), couponSenderBO.getMId());
            couponSenderBO.setErrorEnum(ReceiveCouponErrorEnum.END);
            throw new BizException(ReceiveCouponErrorEnum.END.getValue());
        }

        //扣减卡劵数量 领取限制张数情况下
        if (Objects.nonNull(coupon.getGrantAmount()) && coupon.getGrantLimit() > 0) {
            int update = couponMapper.updateGrantAmount(1, coupon.getId());
            if (update <= 0) {
                log.error("CouponSenderContext[]receiveNew[]quantity is not enough,coupon:{},mId:{}", JSON.toJSONString(coupon), couponSenderBO.getMId());
                couponSenderBO.setErrorEnum(ReceiveCouponErrorEnum.END);
                throw new BizException(ReceiveCouponErrorEnum.END.getValue());
            }
        }

        MerchantCoupon merchantCoupon = MerchantCouponBuilder.build(coupon, couponSenderBO.getMId(),
                null, LocalDateTime.now());
        if (Objects.nonNull(couponSenderBO.getReceiveType())) {
            merchantCoupon.setReceiveType(couponSenderBO.getReceiveType());
        }
        merchantCouponMapper.insertSelective(merchantCoupon);
    }
}
