package net.summerfarm.mall.service;

import net.summerfarm.mall.model.domain.CustomizationRequest;
import net.summerfarm.mall.model.input.CustomizationRequestEnabledInput;
import net.summerfarm.mall.model.input.CustomizationRequestInput;
import net.summerfarm.mall.model.vo.CustomizationRequestVO;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.model.vo.ProductInfoVO;

import java.util.List;

/**
 * 定制需求服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface CustomizationRequestService {
    /**
     * 保存定制需求 并返回对应的复制出来的sku
     * @param input
     * @return
     */
    List<ProductInfoVO> querySkuAndSave(CustomizationRequestInput input, MerchantSubject merchantSubject);
    /**
     * 根据订单号查询定制需求
     * @param orderNo 订单号
     * @return 定制需求信息
     */
    CustomizationRequestVO getCustomizationRequestByOrderNo(String orderNo, MerchantSubject merchantSubject);
    /**
     * 拒绝定制需求
     */
    void refuse(CustomizationRequestEnabledInput input, MerchantSubject merchantSubject);
    /**
     * 通过定制需求
     */
    void confirm(CustomizationRequestEnabledInput input, MerchantSubject merchantSubject);
}
