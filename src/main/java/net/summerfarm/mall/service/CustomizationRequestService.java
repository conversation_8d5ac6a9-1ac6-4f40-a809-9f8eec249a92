package net.summerfarm.mall.service;

import net.summerfarm.mall.model.input.CustomizationRequestsInput;
import net.summerfarm.mall.model.input.CustomizationRequestsInput;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * 定制需求服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface CustomizationRequestService {

    /**
     * 保存定制需求
     * @param input 定制需求输入参数
     * @return 保存结果
     */
    CommonResult<Long> saveCustomizationRequest(CustomizationRequestsInput input);

    /**
     * 根据ID查询定制需求
     * @param id 主键ID
     * @return 定制需求信息
     */
    CommonResult<CustomizationRequest> getCustomizationRequestById(Long id);

    /**
     * 根据订单号查询定制需求
     * @param orderNo 订单号
     * @return 定制需求信息
     */
    CommonResult<CustomizationRequest> getCustomizationRequestByOrderNo(String orderNo);

    /**
     * 根据客户ID查询定制需求列表
     * @param mId 客户ID
     * @return 定制需求列表
     */
    CommonResult<List<CustomizationRequest>> getCustomizationRequestsByMerchantId(Long mId);

    /**
     * 根据状态查询定制需求列表
     * @param status 状态
     * @return 定制需求列表
     */
    CommonResult<List<CustomizationRequest>> getCustomizationRequestsByStatus(Integer status);

    /**
     * 更新定制需求状态
     * @param id 主键ID
     * @param status 新状态
     * @return 更新结果
     */
    CommonResult<Boolean> updateCustomizationRequestStatus(Long id, Integer status);

    /**
     * 更新设计效果图
     * @param id 主键ID
     * @param designImage 设计效果图URL
     * @return 更新结果
     */
    CommonResult<Boolean> updateDesignImage(Long id, String designImage);

    /**
     * 客户确认设计
     * @param id 主键ID
     * @param approved 是否通过
     * @param refuseReason 拒绝原因（不通过时必填）
     * @return 确认结果
     */
    CommonResult<Boolean> confirmDesign(Long id, Boolean approved, String refuseReason);

    /**
     * 添加沟通记录
     * @param id 主键ID
     * @param communicationNote 沟通记录
     * @return 添加结果
     */
    CommonResult<Boolean> addCommunicationNote(Long id, String communicationNote);

    /**
     * 删除定制需求
     * @param id 主键ID
     * @return 删除结果
     */
    CommonResult<Boolean> deleteCustomizationRequest(Long id);
}
