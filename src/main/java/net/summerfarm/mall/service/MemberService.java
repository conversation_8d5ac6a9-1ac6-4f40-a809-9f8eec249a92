package net.summerfarm.mall.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.model.domain.Coupon;
import net.xianmu.common.result.CommonResult;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public interface MemberService {
    /**
     * 查询会员信息
     * @return
     */
    AjaxResult selectMember();

    Integer calculGrade(Long mId);

    AjaxResult selectOrders(int pageIndex, int pageSize);

    BigDecimal getUsedAmount(LocalDateTime date, Long mId, Integer grade);

    BigDecimal getRefundAmountByGrade(Integer grade);

    Integer getOutTimesByGrade(Integer grade);

}
