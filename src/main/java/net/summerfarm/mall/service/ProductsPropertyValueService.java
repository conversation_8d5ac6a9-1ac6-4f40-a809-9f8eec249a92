package net.summerfarm.mall.service;

import java.util.List;
import java.util.Map;

import net.summerfarm.mall.model.vo.ProductsPropertyValueVO;

/**
 *  商品属性值服务接口
 */
public interface ProductsPropertyValueService {

    /**
     * 根据商品id列表获取商品属性值映射
     * @param productIds 商品id列表
     * @return 商品id和属性值列表的映射
     */
    Map<Long, List<ProductsPropertyValueVO>> getProductsPropertyValueMap(List<Long> productIds);

    /**
     * 根据商品id列表从缓存获取商品属性值映射
     * @param productIds 商品id列表
     * @return 商品id和属性值列表的映射
     */
    Map<Long, List<ProductsPropertyValueVO>> getProductsPropertyValueCache(List<Long> productIds);
}
