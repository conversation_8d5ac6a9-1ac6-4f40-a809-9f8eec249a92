package net.summerfarm.mall.service.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.BaseUtil;
import net.summerfarm.mall.model.dto.market.activity.ActivityInfoDTO;
import net.summerfarm.mall.service.ActivityService;
import net.xianmu.common.cache.InMemoryCache;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName MerchantActivityCache
 * @Description 用户活动缓存--根据用户和区域获取当前用户所有活动信息
 * <AUTHOR>
 * @Date 14:32 2024/4/3
 * @Version 1.0
 **/
@Component
@Slf4j
public class MerchantActivityCache {

    @Resource
    @Lazy
    private ActivityService activityService;

    private final LoadingCache<String, List<ActivityInfoDTO>> MERCHANT_ALL_ACTIVITY_CACHE = CacheBuilder.newBuilder()
            .concurrencyLevel(Runtime.getRuntime().availableProcessors())
            // 设置初始容量
            .initialCapacity(100)
            // 缓存池大小
            .maximumSize(1000)
            // 设置 设定时间 后 刷新缓存
            .refreshAfterWrite(30, TimeUnit.SECONDS)
            .build(new CacheLoader<String, List<ActivityInfoDTO>>() {
                @Override
                public List<ActivityInfoDTO> load(String key) throws Exception {
                    if (BaseUtil.isEmpty(key)) {
                        return Lists.newArrayList();
                    }
                    List<String> strings = Arrays.asList(key.split("_"));
                    if (CollectionUtils.isEmpty(strings) || strings.size() < 2) {
                        return Lists.newArrayList();
                    }
                    Integer areaNo = Integer.valueOf(strings.get(0));
                    Long mId = Long.valueOf(strings.get(1));
                    return activityService.listActivityInfoDTOs(areaNo, mId);
                }
            });


    public List<ActivityInfoDTO> getMerchantAllActivity(Integer areaNo, Long mId) {
        List<ActivityInfoDTO> activityInfoDTOS = Lists.newArrayList();
        String key = areaNo + "_" + mId;
        try {
            activityInfoDTOS = MERCHANT_ALL_ACTIVITY_CACHE.get(key);
        } catch (Exception e) {
            log.warn("MerchantActivityCache[]getMerchantAllActivity[]error[]key:{},cause:{}",key, e);
        }
        return activityInfoDTOS;
    }

    @InMemoryCache(expiryTimeInSeconds = 1 * 60)
    public List<ActivityInfoDTO> getMerchantAllActivityCache(Integer areaNo, Long mId) {
        return activityService.listActivityInfoDTOs(areaNo, mId);
    }
}
