package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.MD5Util;
import net.summerfarm.common.util.RandomCodeUtils;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.login.MallTokenUtil;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.AdminIsDisabledEnum;
import net.summerfarm.enums.MerchantLeadsStatus;
import net.summerfarm.enums.SubAccountStatus;
import net.summerfarm.enums.SubAccountType;
import net.summerfarm.mall.common.config.DynamicConfig;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.mq.crm.MessageBusiness;
import net.summerfarm.mall.common.redis.KeyConstant;
import net.summerfarm.mall.common.util.*;
import net.summerfarm.mall.constant.Constants;
import net.summerfarm.mall.constant.MerchantConstants;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.facade.auth.AuthQueryFacade;
import net.summerfarm.mall.facade.usercenter.MerchantQueryFacade;
import net.summerfarm.mall.facade.wnc.FenceFacade;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.mapper.offline.CrmMerchantDayGmvMapper;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.login.LoginMerchantCacheDTO;
import net.summerfarm.mall.model.dto.login.MerchantLoginDto;
import net.summerfarm.mall.model.input.DataStorageInput;
import net.summerfarm.mall.model.input.DataStorageQueryInput;
import net.summerfarm.mall.model.input.product.HotListSearchInput;
import net.summerfarm.mall.model.input.product.ProductQueryInput;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.model.vo.merchant.subaccount.MerchantSubAccountQuery;
import net.summerfarm.mall.model.vo.merchant.subaccount.SubAccountLoginQuery;
import net.summerfarm.mall.payments.common.config.PaymentConfig;
import net.summerfarm.mall.payments.common.enums.CmbTransferWechatDirectPayEnum;
import net.summerfarm.mall.repository.MerchantRepository;
import net.summerfarm.mall.repository.MerchantSubAccountRepository;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.facade.WncDeliveryFenceQueryFacade;
import net.summerfarm.mall.service.facade.WncDeliveryRuleQueryFacade;
import net.summerfarm.mall.service.facade.dto.FenceCloseTimeReq;
import net.summerfarm.mall.service.sellingEntity.SellingEntityService;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.client.dto.login.AuthQueryWechatInfoDTO;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.common.cache.InMemoryCache;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.rocketmq.support.producer.MqProducer;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static net.summerfarm.mall.contexts.MQDelayConstant.THREE_DELAY_LEVEL_LONG;

/**
 * @Package: net.summerfarm.customer.service.impl
 * @Description: 商户业务类
 * @author: <EMAIL>
 * @Date: 2016/9/30
 */
@Service
@Slf4j
public class MerchantServiceImpl implements MerchantService {

    private static final Logger logger = LoggerFactory.getLogger(MerchantService.class);

    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private MerchantRepository merchantRepository;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private AccountChangeMapper accountChangeMapper;
    @Resource
    private MerchantCouponMapper merchantCouponMapper;
    @Resource
    private InvitecodeMapper invitecodeMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private MemberService memberService;
    @Resource
    private FollowUpRelationMapper followUpRelationMapper;
    @Resource
    private ContactService contactService;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private MerchantSubAccountMapper subAccountMapper;
    @Resource
    private MerchantSubAccountService subAccountService;
    @Resource
    private MerchantLeadsMapper merchantLeadsMapper;
    @Resource
    private MerchantGroupPurchaseMapper merchantGroupPurchaseMapper;
    @Resource
    private MerchantCluePoolMapper merchantCluePoolMapper;
    @Resource
    private ConfigService configService;
    @Resource
    private WarehouseLogisticsService logisticsService;
    @Resource
    private AdminSkinMapper adminSkinMapper;
    @Resource
    private FenceService fenceService;
    @Resource
    private MerchantExtMapper merchantExtMapper;
    @Resource
    private DistributionRuleService distributionRuleService;
    @Resource
    private AreaService areaService;
    @Resource
    private MerchantSubAccountMapper merchantSubAccountMapper;
    @Resource
    private CrmMerchantDayGmvMapper crmMerchantDayGmvMapper;
    @Resource
    private WncDeliveryRuleQueryFacade deliveryRuleQueryFacade;
    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Autowired
    MqProducer mqProducer;
    @Resource
    MerchantSubAccountRepository merchantSubAccountRepository;
    @Resource
    AuthQueryFacade authQueryFacade;
    @Resource
    MerchantQueryFacade merchantQueryFacade;
    @Resource
    DynamicConfig dynamicConfig;
    @Resource
    ProvinceConverterV2 provinceConverterV2;
    @Resource
    private PaymentConfig paymentConfig;
    @Resource
    private FenceFacade fenceFacade;
    @Resource
    private WncDeliveryFenceQueryFacade wncDeliveryFenceQueryFacade;
    @Autowired
    private SellingEntityService sellingEntityService;


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult updateOpenId(String mcontact, boolean reBindPopMerchant) {
        log.info("接收到有原手机号更换微信请求：mcontact：{}, reBindPopMerchant:{}, LoginMerchantCacheDTO:{}", mcontact, reBindPopMerchant, JSON.toJSONString(RequestHolder.getLoginMerchantCache()));
        String openId = RequestHolder.getOpenId();
        Long mId = RequestHolder.getLoginMerchantCache().getMerchantId();
        Long accountId = RequestHolder.getLoginMerchantCache().getAccountId();
        if (StringUtils.isBlank(openId) || accountId == null) {
            log.error("当前缓存信息不存在！");
            return AjaxResult.getError();
        }
        String phone = RequestHolder.getLoginMerchantCache().getPhone();

        MerchantSubAccount account = subAccountService.selectOneNew(Global.ACCOUNT_ID, accountId.toString());
        if (SubAccountType.MANAGER.ordinal() != account.getType()) {
            return AjaxResult.getErrorWithMsg("子账号不可重新绑定");
        }
        Merchant merchant = merchantMapper.selectByMid(mId);
        if(merchant == null) {
            log.info("门店不存在。mid：{}", mId);
            return AjaxResult.getErrorWithMsg("无可绑定门店！");
        }

        if(isPreRegisterWithoutLoginMerchant(merchant)){
            log.info("门店预注册后暂未激活。merchant：{}", JSON.toJSONString(merchant));
            return AjaxResult.getErrorWithMsg("当前门店暂不支持换绑，点击首页上方手机号一键登录即可进入商城");
        }

        if((reBindPopMerchant && !isPopMerchantV2(merchant.getBusinessLine())) || (!reBindPopMerchant && isPopMerchantV2(merchant.getBusinessLine()))) {
            log.info("不允许鲜沐、pop门店进行切换。mid：{}", mId);
            return AjaxResult.getErrorWithMsg("无可绑定门店！");
        }

        //List<MerchantSubAccount> accountList = subAccountMapper.selectByMId(mId, SubAccountStatus.AUDIT_SUC.ordinal());
        List<MerchantSubAccount> accountList = merchantSubAccountRepository.selectByMid(mId);
        boolean flag = accountList.stream()
                .filter(el -> !Objects.equals(mId, el.getmId()))
                .anyMatch(el -> Objects.equals(mcontact, el.getContact()));
        if (flag) {
            return AjaxResult.getErrorWithMsg("账号名称已存在，请重新输入");
        }

        //更新子账号信息
        account.setOpenid(openId);
        account.setUnionid(RequestHolder.getUnionid());
        account.setMpOpenid("");
        if (StringUtils.isNotBlank(mcontact) && StringUtils.isNotBlank(phone)) {
            account.setPhone(phone);
            account.setContact(mcontact);
        }
        subAccountMapper.updateByPrimaryKeySelective(account);
        Map<String, Object> selectKeys = new HashMap<>();
        selectKeys.put("openid", openId);
        Merchant openidMerchant = merchantMapper.selectOne(selectKeys);
        if (openidMerchant != null && !Objects.equals(mId, openidMerchant.getmId())) {
            log.info("门店 {}的openId已经存在 需要特殊处理", openidMerchant.getMname());
            throw new BizException("该微信已经绑定其他门店信息 " + openidMerchant.getMname());
        }
        Merchant record = new Merchant();
        record.setOpenid(openId);
        record.setUnionid(RequestHolder.getUnionid());
        record.setMpOpenid("");
        record.setmId(mId);
        if (StringUtils.isNotBlank(mcontact) && StringUtils.isNotBlank(phone)) {
            record.setPhone(phone);
            record.setMcontact(mcontact);
        }
        merchantMapper.updateByPrimaryKeySelective(record);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 新销售主体用户协议
                sellingEntityService.agreeNewSellingEntityAgreement(mId);
            }
        });
        //返回数据
        Merchant result = new Merchant();
        result.setMname(merchant.getMname());
        result.setMcontact(merchant.getMcontact());

        return AjaxResult.getOK(result);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult updateOpenIdNot(String mname, String openid, String unionid, String oldPhone, String newContact, String newPhone, boolean reBindPopMerchant) {
        log.info("接收到没有原手机号更换微信请求：mname：{}, oldPhone：{}, newContact：{}, newPhone：{},reBindPopMerchant:{}, LoginMerchantCacheDTO:{}", mname, oldPhone, newContact, newPhone, reBindPopMerchant, JSON.toJSONString(RequestHolder.getLoginMerchantCache()));

        //新的手机号做判断验证
        if (StringUtils.isNotBlank(newPhone)) {
            Merchant merchant = merchantMapper.selectIsUserPhone(newPhone);
            if (merchant != null) {
                return AjaxResult.getErrorWithMsg("当前手机号已注册");
            }
        }
        MerchantSubAccount account = subAccountService.selectOneNew("phone", oldPhone);
        if (account == null) {
            log.info("原账户不存在。oldPhone：{}", oldPhone);
            return AjaxResult.getError(ResultConstant.MERCHANT_NOT_FOUND);
        }
        //子账号联系店长处理
        if (SubAccountType.STAFF.ordinal() == account.getType()) {
            return AjaxResult.getErrorWithMsg("该账号暂不支持重新绑定，请联系店长处理");
        }
        Merchant merchant = merchantMapper.selectByMid(account.getmId());
        if(merchant == null) {
            log.info("门店不存在。mid：{}", account.getmId());
            return AjaxResult.getErrorWithMsg("无可绑定门店！");
        }

        if((reBindPopMerchant && !isPopMerchantV2(merchant.getBusinessLine())) || (!reBindPopMerchant && isPopMerchantV2(merchant.getBusinessLine()))) {
            log.info("不允许鲜沐、pop门店进行切换。mid：{}", account.getmId());
            return AjaxResult.getErrorWithMsg("无可绑定门店！");
        }


        //一个微信号不能同时申请多个账户更换绑定  一个可用账户 不能被多个微信号申请更换绑定
        AccountChange select = new AccountChange();
        select.setOpenid(openid);
        select.setStatus(1);
        AccountChange record = accountChangeMapper.select(select);
        select.setOpenid(null);
        select.setmId(account.getmId());
        AccountChange record2 = accountChangeMapper.select(select);
        if (record != null || record2 != null) {
            return AjaxResult.getError(ResultConstant.DEFAULT_FAILED, "不能同时申请多个账号更换绑定");
        }
        AccountChange accountChange = new AccountChange();
        accountChange.setmId(account.getmId());
        accountChange.setMname(mname);
        accountChange.setOldPhone(oldPhone);
        accountChange.setNewContact(newContact);
        accountChange.setNewPhone(newPhone);
        accountChange.setStatus(1);
        accountChange.setOpenid(openid);
        if (StringUtils.isNotBlank(unionid)) {
            accountChange.setUnionid(unionid);
        }
        accountChange.setCreateTime(LocalDateTime.now());
        accountChangeMapper.insert(accountChange);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 新销售主体用户协议
                sellingEntityService.agreeNewSellingEntityAgreement(account.getmId());
            }
        });
        return AjaxResult.getOK();
    }

    /**
     * 判断是否预注册还未登录的门店
     * @return
     */
    private boolean isPreRegisterWithoutLoginMerchant(Merchant merchant){
        return MerchantConstants.MERCHANT_PRE_REGISTER_FLAG.equals(merchant.getPreRegisterFlag()) && StringUtils.isMobile(merchant.getOpenid());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult verifyYZM(String yzm, String type, String token) {
        LoginMerchantCacheDTO loginMerchantCache = RequestHolder.getLoginMerchantCache();
        if (Objects.isNull(loginMerchantCache)) {
            throw new DefaultServiceException(ResultConstant.LOGIN_FIRST);
        }
        String phone = loginMerchantCache.getPhone();
        String _code = loginMerchantCache.getCode();
        logger.info("type ={},code ={}", type, _code);
        if (yzm.equals(_code)) {
            if ("login".equals(type)) {
                MerchantSubAccount subAccount = subAccountService.selectOneNew("phone", phone);
                if (subAccount != null) {
                    loginMerchantCache.setCode(null);
                    loginMerchantCache.setMerchantId(subAccount.getmId());
                    loginMerchantCache.setAccountId(subAccount.getAccountId());
                    RequestHolder.setLoginMerchantCache(token, loginMerchantCache);
                } else {
                    return AjaxResult.getError(ResultConstant.MERCHANT_NOT_FOUND);
                }
            }

            //外层校验是否是茶报道预注册用户
            Merchant merchant = preRegister(phone);
            if (Objects.nonNull(merchant)) {
                return AjaxResult.getOK(merchant);
            }
            return AjaxResult.getOK();
        }
        return AjaxResult.getErrorWithMsg("请输入正确的验证码");
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult verifyBL(Merchant merchant, Boolean matchAreaFlag) {
        log.info("开始用户注册：merchant：{}, loginMerchantCache:{}, ", JSON.toJSONString(merchant), JSON.toJSONString(RequestHolder.getLoginMerchantCache()));
        // 1.数据初始化
        this.initForRegister(merchant);

        // 2.前置业务校验
        AjaxResult ajaxResult = this.validateForRegister(merchant);
        if(!ajaxResult.isSuccess()) {
            log.info("注册校验未通过！msg:{}", JSON.toJSONString(ajaxResult));
            return ajaxResult;
        }

        // 3.数据包装、转换处理
        this.wrapForRegister(merchant);

        // 4.保存用户数据
        provinceConverterV2.convert(merchant, "verifyBL");
        merchant.setChannelCode(getChannelCode());
        merchantMapper.insertSelective(merchant);
        //保存账户数据
        MerchantSubAccount account = createSubAccount(merchant);
        //保存地址数据
        contactService.addContactForRegister(merchant);
        log.info("数据基本信息保存完毕, 开始后置处理. mid：{}", merchant.getmId());

        //5.后置处理
        this.doAfterRegister(merchant);
        log.info("门店注册后置处理完毕. mid：{}", merchant.getmId());
        return AjaxResult.getOK(account.getAccountId());
    }

    private void initForRegister(Merchant merchant){
        log.info("开始门店注册数据预处理：merchant:{}", JSON.toJSONString(merchant));
        merchant.setRegisterTime(new Date());
        boolean isPopMerchant = merchant.isPopMerchant();
        if(isPopMerchant) {
            log.info("pop用户无需初始化~");
            return;
        }
        String mname = merchant.getMname();
        String type = merchant.getType();
        String doorPic = merchant.getDoorPic();
        String houseNumber = merchant.getHouseNumber();
        boolean crmRegisterFlag = merchant.isCrmRegisterFlag();

        // 自然流入的客户才需要初始化
        if(!crmRegisterFlag) {
            boolean needAdditional = StringUtils.isBlank(mname) || StringUtils.isBlank(type) || StringUtils.isBlank(doorPic);
            if(StringUtils.isBlank(mname)) {
                // 未填写名字，需要完善信息
                String defaultMerchantName = this.getDefaultMerchantName(merchant.getPhone());
                merchant.setMname(defaultMerchantName);
                merchant.setMcontact(defaultMerchantName);
            }

            Integer operateStatus = needAdditional ? MerchantEnum.OperateStatusEnum.PENDING_SUBMISSION.getCode() : MerchantEnum.OperateStatusEnum.PENDING_VERIFICATION.getCode();
            merchant.setOperateStatus(operateStatus);
            merchant.setSize("大客户");
            merchant.setAdminId(dynamicConfig.getNoAuditMerchantDefaultAdminId());
            merchant.setDirect(MerchantDirectEnum.PAY_IMMEDIATELY.getType());
            merchant.setSkuShow(1);
            // 填了门牌号就代表地址是用户检验过可以直接下单的地址
            Integer addressCompletionFlag = StringUtils.isEmpty(houseNumber) ?  AddressCompletionStatusEnum.UNCOMPLETED.getCode() : AddressCompletionStatusEnum.COMPLETED.getCode();
            merchant.setAddressCompletionFlag(addressCompletionFlag);
        }
    }

    private String getDefaultMerchantName(String phone) {
        String lastFourDigits  = phone.substring(phone.length() - 4);
        String randomString = RandomUtil.randomString(4);
        return MerchantConstants.DEFAULT_MERCHANT_NAME_PRE + lastFourDigits + randomString;
    }


    private AjaxResult validateForRegister(Merchant merchant){
        String mname = merchant.getMname().trim();
        String mcontact = merchant.getMcontact().trim();
        boolean isPopMerchant = merchant.isPopMerchant();
        boolean crmRegisterFlag = merchant.isCrmRegisterFlag();
        if (StringUtils.isBlank(RequestHolder.getOpenId())) {
            log.info("openid为空!.cache:{}", JSON.toJSONString(RequestHolder.getLoginMerchantCache()));
            return AjaxResult.getError("WECHAT_ONLY", "提交超时，请退出重新进入");
        }
        if (!StringUtils.isMname(mname)) {
            return AjaxResult.getErrorWithMsg("店铺名称只能由汉字、数字和英文字母组成，请重新输入");
        }
        if (mname.length() > 25) {
            return AjaxResult.getErrorWithMsg("店铺名称不可超过25个字哦");
        }
        if (!StringUtils.isMname(mcontact)) {
            return AjaxResult.getErrorWithMsg("账号名称只能由汉字、数字和英文字母组成，请重新输入");
        }
        if (StringUtils.isNotBlank(merchant.getHouseNumber())) {
            String houseNumber = merchant.getHouseNumber().trim();
            if (!StringUtils.isHouseNumber(houseNumber)) {
                return AjaxResult.getErrorWithMsg("门牌号不允许输入特殊符号，请重新输入");
            }
        }
        MerchantStoreResultResp nameUsedMerchant = merchantQueryFacade.getMerchantByExactStoreName(mname);
        if (nameUsedMerchant != null) {
            return AjaxResult.getErrorWithMsg("该店铺名已存在,请重新输入");
        }
        MerchantSubAccount currenAccount = subAccountService.selectOneNew("phone", RequestHolder.getLoginMerchantCache().getPhone());
        if (Objects.nonNull(currenAccount)) {
            return AjaxResult.getErrorWithMsg("该手机号已存在,请重新输入");
        }

        Map<String, String> openidSelectkeys = new HashMap<>();
        openidSelectkeys.put("openid", merchant.getOpenid());
        int openidNum = merchantMapper.count(openidSelectkeys);
        if (openidNum != 0) {
            logger.info("上传用户信息店铺名称：{}，手机号：{}，openid：{}", mname, RequestHolder.getLoginMerchantCache().getPhone(), merchant.getOpenid());
            return AjaxResult.getErrorWithMsg("该微信已绑定店铺,请联系客服处理");
        }

        Map<String, String> selectKeys = new HashMap<>();
        selectKeys.put("phone", merchant.getPhone());
        int phoneNum = merchantMapper.count(selectKeys);
        if (phoneNum != 0) {
            logger.info("上传用户信息店铺名称：{}，手机号：{}，openid：{}", mname, RequestHolder.getLoginMerchantCache().getPhone(), merchant.getOpenid());
            return AjaxResult.getErrorWithMsg("该手机号已绑定店铺,请联系客服处理");
        }

        //匹配截单区域（只处理鲜沐的客户）
        if (!crmRegisterFlag) {
            Integer areaNo = fenceFacade.getAreaNoByAddress(merchant.getCity(), merchant.getArea(), isPopMerchant);
            merchant.setAreaNo(areaNo);
        }
        return AjaxResult.getOK();
    }

    private void wrapForRegister(Merchant merchant){
        log.info("数据校验完成, 开始数据转换处理. merchant：{}", JSON.toJSONString(merchant));
        boolean isPopMerchant = merchant.isPopMerchant();
        boolean crmRegisterFlag = merchant.isCrmRegisterFlag();
        String inviterCode = merchant.getInvitecode();

        //判断是不是大客户邀请码
        if(!crmRegisterFlag) {
            Invitecode invitecode = invitecodeMapper.selectMajor(inviterCode);
            log.info("根据推荐码查询信息如下：{}", JSON.toJSONString(invitecode));
            if (invitecode != null && invitecode.getAdminId() != null) {
                logger.info("大客户邀请注册，邀请码:{}", inviterCode);
                merchant.setSize("大客户");
                merchant.setAdminId(invitecode.getAdminId());
                merchant.setOperateStatus(MerchantOperateStatusEnum.NORMAL.getCode());
                merchant.setSkuShow(1);
                merchant.setDirect(MerchantDirectEnum.PAY_IMMEDIATELY.getType());
                Admin admin = adminMapper.selectByPrimaryKey(invitecode.getAdminId());
                if (admin != null) {
                    merchant.setInvitecode(invitecodeMapper.selectSale(admin.getSalerId()));
                }
            }
        }

        // 兜底处理
        if (StringUtils.isBlank(merchant.getInvitecode())) {
            merchant.setInvitecode("seeegj");
        }

        // pop商城默认绑定虚拟运营区域、虚拟大客户
        if (isPopMerchant) {
            merchant.setSize("单店");
            //merchant.setAreaNo(dynamicConfig.getPopDefaultAreaNo());
            merchant.setSkuShow(1);
            merchant.setBusinessLine(MerchantEnum.BusinessLineEnum.POP.getCode());
            // merchant.setAdminId(dynamicConfig.getPopDefaultAdminId());
            // merchant.setDirect(MerchantDirectEnum.PAY_IMMEDIATELY.getType());
        }
        //获取unionid
        String openid = RequestHolder.getOpenId();
        String wxChannelCode = isPopMerchant ? WxOfficialAccountsChannelEnum.POP_MALL.channelCode : WxOfficialAccountsChannelEnum.XM_MALL.channelCode;
        String unionId = getUnionId(openid, wxChannelCode);
        if (StringUtils.isNotBlank(unionId)) {
            merchant.setUnionid(unionId);
        }

        log.info("门店数据转换完毕：merchant:{}", JSON.toJSONString(merchant));
    }



    private void doAfterRegister(Merchant merchant) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    // 判断门店是否重复
                    MQData mqData = new MQData();
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("mId", merchant.getmId());
                    mqData.setData(jsonObject.toJSONString());
                    mqData.setBusiness(MessageBusiness.REPEATED_MERCHANT);
                    mqData.setType(MType.MERCHANT_REPEATED_JUDGMENT.name());
                    mqProducer.sendDelay(RocketMqMessageConstant.CRM_LIST, null, mqData, THREE_DELAY_LEVEL_LONG);


                    boolean isPopMerchant = merchant.isPopMerchant();
                    boolean crmRegisterFlag = merchant.isCrmRegisterFlag();
                    boolean autoAuditFlag = merchant.isAutoAuditFlag();
                    // 自动审核：
                    if (crmRegisterFlag || autoAuditFlag || isPopMerchant) {
                        // 扫码注册，预留自动审标记，pop直接审核通过免复核
                        logger.info("发送后台自动审核请求{}", merchant.getmId());
                        mqProducer.send(RocketMqMessageConstant.MERCHANT_LEADS_AUDIT, null, merchant.getmId());
                    } else {
                        // 需要复核
                        mqProducer.send(RocketMqMessageConstant.MERCHANT_LEADS_AUDIT, null, merchant.getmId());
                    }
                    // 新销售主体用户协议
                    sellingEntityService.agreeNewSellingEntityAgreement(merchant.getmId());


                    //非地推码创建校验风控门店信息(暂时屏蔽，后续可能还需要-2024-07-19)
                    /*if (!crmRegisterFlag) {
                        // 风控门店
                        if (StrUtil.isEmpty(merchant.getDoorPicOcr()) || StrUtil.similar(merchant.getDoorPicOcr(), merchant.getMname()) <= 0.5) {
                            MQData riskMq = new MQData();
                            JSONObject riskJson = new JSONObject();
                            riskJson.put("doorPicOcr", merchant.getDoorPicOcr());
                            riskJson.put("mId", merchant.getmId());
                            riskMq.setData(riskJson.toJSONString());
                            riskMq.setBusiness(MessageBusiness.RISK_MERCHANT);
                            riskMq.setType(MType.RISK_MERCHANT.name());
                            mqProducer.sendDelay(RocketMqMessageConstant.CRM_LIST, null, riskMq, THREE_DELAY_LEVEL_LONG);
                        }
                    }*/
                } catch (Exception e) {
                    logger.error("门店注册后置处理失败.", e);
                }
            }
        });
    }


    @Override
    public AjaxResult login(Integer type, String code, String phone, String sign) {
        //模拟登陆
        if (StringUtils.isNotBlank(phone)) {
            getMockLoginInfo(phone, sign, true);
            return loginInternal(type, null, null, phone, sign);
        } else {
            MerchantLoginDto loginDto = new MerchantLoginDto(type, code, false, phone, sign);
            String ipAddress = IPUtil.getIpAddress(RequestHolder.getRequest());
            log.info("获取登陆信息  code {} type{}, ip{}", code, type, ipAddress);
            if (type != null && type == 2) {
                //小程序登录
                return loginForWechatMiniProgram(loginDto);
            } else {
                //公众号登录
                return loginForWeChatOfficialAccount(loginDto);
            }
        }
    }

    @Override
    public AjaxResult loginV2(MerchantLoginDto merchantLoginDto) {
        String phone = merchantLoginDto.getPhone();
        String sign = merchantLoginDto.getSign();
        Integer type = merchantLoginDto.getType();
        //模拟登陆
        if (StringUtils.isNotBlank(phone)) {
            getMockLoginInfo(phone, sign, true);
            return loginInternal(merchantLoginDto.getType(), null, null, phone, sign);
        } else {
            String ipAddress = IPUtil.getIpAddress(RequestHolder.getRequest());
            log.info("获取登陆信息  merchantLoginDto:{}, ip{}", JSON.toJSONString(merchantLoginDto), ipAddress);
            if (type != null && type == 2) {
                //小程序登录
                return loginForWechatMiniProgram(merchantLoginDto);
            } else {
                //公众号登录
                return loginForWeChatOfficialAccount(merchantLoginDto);
            }
        }
    }


    /**
     * 小程序登录
     *
     * @return
     */
    private AjaxResult loginForWechatMiniProgram(MerchantLoginDto loginDto) {
        logger.info("小程序登录：{}", JSONObject.toJSONString(loginDto));
        LoginMerchantCacheDTO loginMerchantCacheDTO = new LoginMerchantCacheDTO();
        String unionid = null;
        String openId = null;
        String mpOpenId = null;
        SubAccountLoginQuery accountLoginQuery = new SubAccountLoginQuery();
        accountLoginQuery.setAuthTypeEnum(AuthTypeEnum.WEI_CHAT);
        accountLoginQuery.setCode(loginDto.getCode());
        accountLoginQuery.setPopMerchant(loginDto.isPopMerchant());
        AuthQueryWechatInfoDTO dto = authQueryFacade.queryWxInfo(accountLoginQuery);
        if (dto == null) {
            log.error("获取微信授权信息失败!");
            throw new BizException("微信授权失败");
        }
        logger.info("小程序openid：{}", JSONObject.toJSONString(dto));
        mpOpenId = dto.getOpenid();
        unionid = dto.getUnionid();
        if (StringUtils.isAllBlank(mpOpenId, unionid)) {
            return AjaxResult.getError(ResultConstant.OPENID_NOT_FOUND, Collections.singletonMap("openId", openId));
        }

        loginMerchantCacheDTO.setMpOpenId(mpOpenId);
        loginMerchantCacheDTO.setUnionid(unionid);
        loginMerchantCacheDTO.setLoginWay(Global.WECHAT_MNG);
        return loginInternal(loginDto.getType(), mpOpenId, unionid, loginDto.getPhone(), loginDto.getSign());
    }


    /**
     * 公众号登录
     *
     * @return
     */
    private AjaxResult loginForWeChatOfficialAccount(MerchantLoginDto loginDto) {
        logger.info("公众号登录：{}", JSONObject.toJSONString(loginDto));
        LoginMerchantCacheDTO loginMerchantCacheDTO = new LoginMerchantCacheDTO();
        String unionid = null;
        String openId = null;
        String mpOpenId = null;
        Integer type = 1;
        SubAccountLoginQuery accountLoginQuery = new SubAccountLoginQuery();
        accountLoginQuery.setAuthTypeEnum(AuthTypeEnum.OFFICIAL_WE_CHAT);
        accountLoginQuery.setCode(loginDto.getCode());
        accountLoginQuery.setPopMerchant(loginDto.isPopMerchant());
        AuthQueryWechatInfoDTO dto = authQueryFacade.queryWxInfo(accountLoginQuery);
        if (dto == null) {
            return AjaxResult.getError(ResultConstant.OPENID_NOT_FOUND, Collections.singletonMap("openId", openId));
        }
        logger.info("公众号openid：{}", JSONObject.toJSONString(dto));
        openId = dto.getOpenid();
        unionid = dto.getUnionid();
        if (StringUtils.isBlank(openId)) {
            return AjaxResult.getError(ResultConstant.OPENID_NOT_FOUND, Collections.singletonMap("openId", openId));
        }
        loginMerchantCacheDTO.setOpenId(openId);
        loginMerchantCacheDTO.setLoginWay(Global.WECHAT_GZH);

        if (StringUtils.isNotBlank(unionid)) {
            loginMerchantCacheDTO.setUnionid(unionid);
        }
        return loginInternal(type, openId, unionid, loginDto.getPhone(), loginDto.getSign());
    }

    /**
     * 灰度的版本
     *
     * @param type  登录类型：0、公众号 1、小程序
     * @param phone
     * @param sign
     * @return
     */
    private AjaxResult loginInternal(Integer type, String openId, String unionid, String phone, String sign) {
        String mpOpenId = null;
        if (type != null && type == 2) {
            mpOpenId = openId;
        }
        logger.info("获取到登陆信息 type {}, oepnid{}, unionid{}, phone {}, sgin{}", type, openId, unionid, phone, sign);
        LoginMerchantCacheDTO loginMerchantCacheDTO = new LoginMerchantCacheDTO();
        SubAccountLoginQuery accountLoginQuery = new SubAccountLoginQuery();
        //模拟登陆
        if (StringUtils.isNotBlank(phone)) {
            MerchantSubAccount account = getMockLoginInfo(phone, sign, true);
            type = 1;
            unionid = account.getUnionid();
            openId = account.getOpenid();
            mpOpenId = account.getMpOpenid();
            loginMerchantCacheDTO.setLoginWay(Global.WECHAT_GZH);
            Long accounId = account.getAccountId();
            accountLoginQuery.setAccountId(accounId);
        } else {
            //小程序登陆
            if (type != null && type == 2) {
                accountLoginQuery.setAuthTypeEnum(AuthTypeEnum.WEI_CHAT);
                loginMerchantCacheDTO.setMpOpenId(mpOpenId);
                loginMerchantCacheDTO.setUnionid(unionid);
                loginMerchantCacheDTO.setLoginWay(Global.WECHAT_MNG);
            } else {
                accountLoginQuery.setAuthTypeEnum(AuthTypeEnum.OFFICIAL_WE_CHAT);
                loginMerchantCacheDTO.setLoginWay(Global.WECHAT_GZH);
                loginMerchantCacheDTO.setOpenId(openId);
                loginMerchantCacheDTO.setUnionid(unionid);
            }
            accountLoginQuery.setOpenid(openId);
            accountLoginQuery.setUnionid(unionid);
        }


        // 先将当前用户openid unionid以及mpopenid进行缓存， 此时的token相当于是一个临时token
        String token = MallTokenUtil.getToken(type, null);
        String oldtoken = token;
        RequestHolder.setLoginMerchantCache(token, loginMerchantCacheDTO);
        Map<String, String> tokenMap = new HashMap<>();
        tokenMap.put("token", token);
        tokenMap.put("openId", openId);
        tokenMap.put("unionId", unionid);
        AuthLoginDto authLoginDto = authQueryFacade.authLogin(accountLoginQuery);
        if (authLoginDto == null) {
            return AjaxResult.getError(ResultConstant.MERCHANT_NOT_FOUND, tokenMap);
        }
        token = authLoginDto.getToken();

        //小程序登陆
        if (type == 2) {
            if (StringUtils.isNotBlank(authLoginDto.getOpenId())) {
                loginMerchantCacheDTO.setMpOpenId(authLoginDto.getOpenId());
            }
            if (StringUtils.isNotBlank(authLoginDto.getUnionId())) {
                loginMerchantCacheDTO.setUnionid(authLoginDto.getUnionId());
            }
        } else {
            if (StringUtils.isNotBlank(authLoginDto.getUnionId())) {
                loginMerchantCacheDTO.setUnionid(authLoginDto.getUnionId());
            }
            if (StringUtils.isNotBlank(authLoginDto.getOpenId())) {
                loginMerchantCacheDTO.setOpenId(authLoginDto.getOpenId());
            }
        }


        //账号信息
        MerchantSubAccount account = subAccountService.selectOneNew(Global.ACCOUNT_ID, authLoginDto.getBizUserId().toString());
        //兼容部分用户没有保存unionid
        if (account == null) {
            if (type == 1) {
                account = subAccountService.selectOneNew(Global.OPEN_ID, openId);
            } else {
                account = subAccountService.selectOneNew(Global.MP_OPEN_ID, mpOpenId);
            }

        }
        //补充openid、mpOpenid信息
        else if ((StringUtils.isBlank(account.getOpenid()) && type == 1) || (StringUtils.isBlank(account.getMpOpenid()) && type == 2)) {
            log.info("开始补充openid、mpOpenid信息。type:{}, openid:{}", type, openId);
            MerchantSubAccount update = new MerchantSubAccount();
            update.setAccountId(account.getAccountId());
            if (type == 1 && !StringUtils.isEmpty(openId)) {
                update.setOpenid(openId);
            }
            if (type == 2 && !StringUtils.isEmpty(openId)) {
                update.setMpOpenid(openId);
            }
            update.setLoginTime(new Date());
            subAccountMapper.updateByPrimaryKeySelective(update);
        }
        //补充unionid信息
        if (account != null && !StringUtils.isEmpty(unionid)) {
            MerchantSubAccount update = new MerchantSubAccount();
            update.setAccountId(account.getAccountId());
            update.setLoginTime(new Date());
            if (!StringUtils.isEmpty(unionid)) {
                update.setUnionid(unionid);
            }
            subAccountMapper.updateByPrimaryKeySelective(update);
        }

        //账号不存在
        if (account == null) {
            return AjaxResult.getError(ResultConstant.MERCHANT_NOT_FOUND, tokenMap);
        }

        //判断用户是否在更换账号
        AccountChange select = new AccountChange();
        select.setOpenid(openId);
        AccountChange change = accountChangeMapper.select(select);
        if (change != null && change.getStatus() == 1) {
            Map<String, Object> result = new HashMap<>();
            result.put("mname", change.getMname());
            result.put("mcontact", change.getNewContact());
            result.put("createTime", change.getCreateTime());
            result.put("token", token);
            return AjaxResult.getError(ResultConstant.MERCHANT_CHANGEING, result);
        }

        //店铺查询
        Merchant merchant = merchantMapper.selectOneByMid(account.getmId());

        //店铺不存在
        if (merchant == null) {
            return AjaxResult.getError(ResultConstant.MERCHANT_NOT_FOUND, tokenMap);
        }
        tokenMap.put("mname", merchant.getMname());
        //店铺被拉黑
        if (ReviewStatus.PULLED_BLACK.getIslock() == merchant.getIslock()) {
            logger.info("店铺已被拉黑mid：{}", merchant.getmId());
            return AjaxResult.getError(ResultConstant.PULLED_BLACK, tokenMap);
        }

        //店铺已注销
        if (ReviewStatus.STORE_CLOSURE.getIslock() == merchant.getIslock()) {
            logger.info("已注销店铺mid:{}，将删除redis中的登录token:{}，使用户所有的session均立即失效", merchant.getmId(), token);
            redisTemplate.delete(KeyConstant.MALL_TOKEN_PREFIX + token);
            return AjaxResult.getError(ReviewStatus.STORE_CLOSURE.getState(), tokenMap);
        }

        //门店所属大客户被禁用
        if (merchant.getAdminId() != null) {
            Admin admin = adminMapper.selectByPrimaryKey(merchant.getAdminId());
            if (Objects.isNull(admin) || Objects.isNull(admin.getIsDisabled())) {
                return AjaxResult.getError(ResultConstant.PULLED_BLACK, tokenMap);
            }
            int isDisabled = admin.getIsDisabled() ? 1 : 0;
            if (AdminIsDisabledEnum.DISABLED.ordinal() == isDisabled) {
                return AjaxResult.getError(ResultConstant.PULLED_BLACK, tokenMap);
            }
        }


        //存储登录信息
        MerchantSubject merchantSubject = new MerchantSubject(merchant.getmId(), merchant.getMname(), account.getOpenid(), account.getMpOpenid(), merchant.getPhone(), type, merchant.getIslock());
        merchantSubject.setUnionid(unionid);
        merchantSubject.setAdminId(merchant.getAdminId());
        merchantSubject.setPopMerchant(isPopMerchantV2(merchant.getBusinessLine()));
        merchantSubject.setBusinessLine(merchant.getBusinessLine());
        merchantSubject.setDirect(merchant.getDirect());
        merchantSubject.setSkuShow(merchant.getSkuShow());
        merchantSubject.setSize(merchant.getSize());
        merchantSubject.setRegisterTime(DateUtils.date2LocalDateTime(merchant.getRegisterTime()));
        merchantSubject.setAccount(account);
        merchantSubject.setServer(merchant.getServer());
        merchantSubject.setOperateStatus(merchant.getOperateStatus());
        //用户城市信息
        Area area = areaMapper.selectByAreaNo(merchant.getAreaNo());
        merchantSubject.setArea(area);
        //用户权益
        List<MemberVO> memberVOS = new ArrayList<>();
        Integer grayscale = isGrayscale(merchant, area.getMemberRule(), memberVOS);
        merchantSubject.setGrayscale(grayscale);
        merchantSubject.setMemberVOS(memberVOS);
        //客户类型
        Integer merchantType = merchantType(merchant);
        merchantSubject.setMerchantType(merchantType);

        //客户业态
        merchantSubject.setType(merchant.getType());

        loginMerchantCacheDTO.setMerchantSubject(merchantSubject);
        loginMerchantCacheDTO.setMerchantId(merchant.getmId());
        loginMerchantCacheDTO.setAccountId(account.getAccountId());
        //
        // 生成新的token 删除上方的临时token
        redisTemplate.delete(KeyConstant.MALL_TOKEN_PREFIX + oldtoken);
        redisTemplate.opsForHash().put(KeyConstant.MA_ID_2_TOKEN, merchant.getmId() + "_" + account.getAccountId(), token);
        RequestHolder.setLoginMerchantCache(token, loginMerchantCacheDTO);
        logger.info("登陆获取信息 accountId {} value {}", account.getAccountId(), JSONUtil.toJsonStr(loginMerchantCacheDTO));

        AjaxResult loginInfo = getLoginInfo();
        Object data = loginInfo.getData();
        if (data instanceof LoginResponseVO) {
            ((LoginResponseVO) data).setToken(token);
        }
        return loginInfo;
    }

    public boolean isPopMerchantV2(Integer businessLine) {
        return MerchantEnum.BusinessLineEnum.POP.getCode().equals(businessLine);
    }


    public void refreshLoginMerchantCache(Long mid, Long accountId){
        log.info("开始刷新用户缓存：mid：{}, accountId:{}", mid, accountId);
        if(!dynamicConfig.getRefreshMerchantCacheSwitch()) {
            log.info("用户信息变更刷新redis缓存开关处于【关闭】状态.");
            return;
        }
        Merchant merchant = merchantMapper.selectOneByMid(mid);
        if(merchant == null) {
            log.warn("门店信息不存在！mid:{}", mid);
            return;
        }
        log.info("门店信息：{}", JSON.toJSONString(merchant));
        //账号信息
        MerchantSubAccount account = subAccountService.selectOneNew(Global.ACCOUNT_ID, accountId.toString());
        if(account == null) {
            log.warn("账户信息不存在！accountId:{}", accountId);
            return;
        }
        log.info("账户信息：{}", JSON.toJSONString(account));

        //存储登录信息
        LoginMerchantCacheDTO loginMerchantCacheDTO = new LoginMerchantCacheDTO();
        MerchantSubject merchantSubject = new MerchantSubject(merchant.getmId(), merchant.getMname(), account.getOpenid(), account.getMpOpenid(), merchant.getPhone(), null, merchant.getIslock());
        merchantSubject.setUnionid(account.getUnionid());
        merchantSubject.setAdminId(merchant.getAdminId());
        merchantSubject.setPopMerchant(isPopMerchantV2(merchant.getBusinessLine()));
        merchantSubject.setBusinessLine(merchant.getBusinessLine());
        merchantSubject.setDirect(merchant.getDirect());
        merchantSubject.setSkuShow(merchant.getSkuShow());
        merchantSubject.setSize(merchant.getSize());
        merchantSubject.setRegisterTime(DateUtils.date2LocalDateTime(merchant.getRegisterTime()));
        merchantSubject.setAccount(account);
        merchantSubject.setServer(merchant.getServer());
        merchantSubject.setOperateStatus(merchant.getOperateStatus());
        //用户城市信息
        Area area = areaMapper.selectByAreaNo(merchant.getAreaNo());
        merchantSubject.setArea(area);
        //用户权益
        List<MemberVO> memberVOS = new ArrayList<>();
        Integer grayscale = isGrayscale(merchant, area.getMemberRule(), memberVOS);
        merchantSubject.setGrayscale(grayscale);
        merchantSubject.setMemberVOS(memberVOS);
        //客户类型
        Integer merchantType = merchantType(merchant);
        merchantSubject.setMerchantType(merchantType);

        //客户业态
        merchantSubject.setType(merchant.getType());

        loginMerchantCacheDTO.setMerchantSubject(merchantSubject);
        loginMerchantCacheDTO.setMerchantId(merchant.getmId());
        loginMerchantCacheDTO.setAccountId(account.getAccountId());
        // 获取token
        String maId = merchant.getmId() + "_" + account.getAccountId();
        log.info("开始获取用户token：key：{}, hashKey:{}", KeyConstant.MA_ID_2_TOKEN, maId);
        String token = (String) redisTemplate.opsForHash().get(KeyConstant.MA_ID_2_TOKEN, maId);
        if(token == null) {
            log.warn("token为空！mid:{}, accountId:{}.", mid, accountId);
            return;
        }
        logger.info("开始更新用户缓存：token：{}, loginMerchantCacheDTO:{}", token, JSON.toJSONString(loginMerchantCacheDTO));
        RequestHolder.setLoginMerchantCache(token, loginMerchantCacheDTO);
    }

    @Override
    public boolean isPopMerchant(Integer adminId) {
        return dynamicConfig.getPopDefaultAdminId().equals(adminId);
    }

    public boolean isPreApprovedMerchant(Integer adminId) {
        return dynamicConfig.getNoAuditMerchantDefaultAdminId().equals(adminId);
    }

    @Override
    public AjaxResult resubjectMerchant() {
        Map selectKeys = new HashMap();
        selectKeys.put("mId", RequestHolder.getMId());
        Merchant merchant = merchantMapper.selectOne(selectKeys);
        merchant.setOpenid(null);
        merchant.setmId(null);
        return AjaxResult.getOK(merchant);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult channelCode(Long mId) {
        return AjaxResult.getOK(getChannelCode(mId));
    }

    @Override
    public String getChannelCode(Long mId) {
        String channelCode = merchantMapper.selectChannelCode(mId);
        if (StringUtils.isBlank(channelCode)) {
            channelCode = getChannelCode();
            merchantMapper.updateChannelCode(mId, channelCode);
        }
        return channelCode;
    }

    private String getChannelCode() {
        boolean repeat = true;
        String channelCode = null;
        Map selectKeys = new HashMap();
        while (repeat) {
            channelCode = RandomCodeUtils.toSerialNumber(6);
            logger.info("新建渠道码：{}", channelCode);
            selectKeys.put("channelCode", channelCode);
            repeat = merchantMapper.count(selectKeys) != 0;
        }
        return channelCode;
    }

    @Override
    public AjaxResult selectInviteInfo() {
        Long mId = RequestHolder.getMId();
        Map selectKeys = new HashMap<>();
        selectKeys.put("mId", mId);
        selectKeys.put("grouping", CouponGrouping.INVITE_REWARD.ordinal());
        List<MerchantCouponVO> merchantCoupons = merchantCouponMapper.select(selectKeys, true);

        InvitedInfoVO invitedInfoVO = new InvitedInfoVO();
        Integer totalReward = 0;
        Integer totalInvite = 0;
        if (!CollectionUtils.isEmpty(merchantCoupons)) {
            for (MerchantCouponVO merchantCouponVO : merchantCoupons) {
                totalInvite++;
                totalReward += merchantCouponVO.getMoney().intValue();
            }
        }
        invitedInfoVO.setTotalInvite(totalInvite);
        invitedInfoVO.setTotalReward(totalReward);
        return AjaxResult.getOK(invitedInfoVO);
    }

    /**
     * 创建子账号
     *
     * @param merchant
     * @return
     */
    private MerchantSubAccount createSubAccount(Merchant merchant) {
        MerchantSubAccount account = new MerchantSubAccount();
        account.setmId(merchant.getmId());
        account.setContact(merchant.getMcontact());
        account.setPhone(merchant.getPhone());
        account.setUnionid(merchant.getUnionid());
        account.setOpenid(merchant.getOpenid());
        account.setMpOpenid(merchant.getMpOpenid());
        account.setFirstPopView(1);
        account.setStatus(0);
        account.setDeleteFlag(1);
        account.setRegisterTime(merchant.getRegisterTime());
        account.setType(SubAccountType.MANAGER.ordinal());
        subAccountMapper.insertSelective(account);
        return account;
    }


    /**
     * 获取默认地址
     */
    public String defaultAddress(Merchant merchant) {
        List<Contact> contacts = contactMapper.selectByMid(merchant.getmId(), 1);
        String houseNumber = "";
        if (!CollectionUtils.isEmpty(contacts)) {
            for (Contact contact : contacts) {
                if (Objects.equals(contact.getIsDefault(), 1)) {

                    if (!StringUtils.isEmpty(contact.getHouseNumber())) {
                        houseNumber = contact.getHouseNumber();
                    }
                    return contact.getProvince() + contact.getCity() + contact.getArea() + contact.getAddress() + houseNumber;
                }
            }
            //如果没有默认地址就取第一个地址
            if (!StringUtils.isEmpty(contacts.get(0).getHouseNumber())) {
                houseNumber = contacts.get(0).getHouseNumber();
            }
            return contacts.get(0).getProvince() + contacts.get(0).getCity() + contacts.get(0).getArea() + contacts.get(0).getAddress() + houseNumber;
        }
        if (!StringUtils.isEmpty(merchant.getHouseNumber())) {
            houseNumber = merchant.getHouseNumber();
        }
        return merchant.getProvince() + merchant.getCity() + merchant.getArea() + merchant.getAddress() + houseNumber;
    }


    public int isGrayscale(Merchant merchant, String memberRule, List<MemberVO> memberVOS) {
        if ("大客户".equals(merchant.getSize())) {
            return 0;
        }
        List<MemberVO> rules = new ArrayList<>();
        if (StringUtils.isNotBlank(memberRule)) {
            rules = JSON.parseArray(memberRule, MemberVO.class);
        }
        if (CollectionUtils.isEmpty(rules)) {
            return 0;
        }
        //根据等级倒序排序
        memberVOS.addAll(rules.stream().sorted((o1, o2) -> o2.getGrade().compareTo(o1.getGrade())).collect(Collectors.toList()));
        return 1;
    }

    /**
     * 获取用户类型
     *
     * @return 0:本月注册客户 1:沉睡客户(上月未下单客户)  2: 老客户
     */
    public Integer merchantType(Merchant merchant) {
        LocalDate now = LocalDate.now();
        if (Objects.equals(DateUtils.date2LocalDateTime(merchant.getRegisterTime()).getYear(), now.getYear()) &&
                Objects.equals(DateUtils.date2LocalDateTime(merchant.getRegisterTime()).getMonthValue(), now.getMonthValue())) {
            return 0;
        }
        int orders = ordersMapper.countOrderByDate(merchant.getmId(), now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()),
                now.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()));
        return orders > 1 ? 2 : 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult del() {
        Map<String, Object> query = new HashMap<>();
        //兼容微信公众号的注册情况
        String openid = RequestHolder.getOpenId();
        if (StringUtils.isBlank(openid)) {
            openid = RequestHolder.getMerchantSubject() == null ? "" : RequestHolder.getMerchantSubject().getOpenId();
        }
        if (StringUtils.isBlank(openid)) {
            return AjaxResult.getError("注册信息异常");
        }
        query.put("openid", openid);
        Merchant merchant = merchantMapper.selectOne(query);

        if (merchant == null
                || (!Objects.equals(ReviewStatus.REVIEWING.getIslock(), merchant.getIslock())
                && !Objects.equals(ReviewStatus.REVIEW_NOT_PASS.getIslock(), merchant.getIslock()))) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }

        int r1 = merchantMapper.deleteByPrimaryKey(merchant.getmId());
        int r2 = subAccountMapper.deleteByMId(merchant.getmId());
        contactMapper.deleteByMId(merchant.getmId());
        if (r1 != 1 || r2 != 1) {
            throw new DefaultServiceException(1, "注册信息异常");
        }
        String token = RequestHolder.getRequest().getHeader("token");
        if (!StringUtils.isEmpty(token)) {
            //清楚账号删掉token
            redisTemplate.delete(KeyConstant.MALL_TOKEN_PREFIX + token);
        }
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult claimMerchant(String merchantLeadsId, String yzm, String token, String type) {
        logger.info("进入bd扫码注册：merchantLeadsId：{}, yzm:{}, token:{}, type:{}", merchantLeadsId, yzm, token, type);
        logger.info("当前用户缓存：{}", RequestHolder.getLoginMerchantCache());
        merchantLeadsId = merchantLeadsId.replaceAll("_", "=");
        String openId = RequestHolder.getOpenId();
        String mpOpenId = RequestHolder.getMpOpenId();
        if (StringUtils.isBlank(openId) && StringUtils.isBlank(mpOpenId)) {
            logger.info("用户openid、mpOpenId都不存在!");
            return AjaxResult.getErrorWithMsg("获取身份信息失败，请重新操作");
        }


        Long leadsId = Long.valueOf(new String(Base64.getDecoder().decode(merchantLeadsId)));
        MerchantLeads merchantLeads = merchantLeadsMapper.selectOne(leadsId);
        if (merchantLeads == null || merchantLeads.getStatus() == 0 || merchantLeads.getStatus() == 3) {
            return AjaxResult.getErrorWithMsg("无效的二维码");
        }
        if (merchantLeads.getStatus() == 2) {
            return AjaxResult.getError("registered", "该店铺已注册");
        }

         boolean popMerchant = MerchantLeadsEnum.MerchantTypeEnum.POP.getCode().equals(merchantLeads.getMerchantType());

        if(popMerchant) {
            logger.info("pop用户校验手机号验证码");
            AjaxResult ajaxResult = verifyYZM(yzm, type, token);
            if (!Objects.equals(AjaxResult.DEFAULT_SUCCESS, ajaxResult.getCode())) {
                logger.error("验证码校验不通过！");
                return ajaxResult;
            }
        } else {
            logger.info("鲜沐用户扫码注册无需校验验证码");
        }

        String inviteCode = invitecodeMapper.selectSale(merchantLeads.getAdminId());
        Long merchantAutoAuditLeadsId = dynamicConfig.getMerchantAutoAuditLeadsId();
        boolean activityLeadsId = merchantAutoAuditLeadsId.equals(leadsId);
        // 开始注册流程
        String phone = RequestHolder.getLoginMerchantCache().getPhone();
        String unionid = RequestHolder.getLoginMerchantCache().getUnionid();
        merchantLeads.setPhone(phone);
        if(activityLeadsId) {
            merchantLeads.setMname(getMerchantNameByPrefix(merchantLeads.getMname(), phone));
        }
        Merchant merchant = new Merchant(merchantLeads);
        merchant.setOpenid(openId);
        merchant.setMpOpenid(mpOpenId);
        merchant.setUnionid(unionid);
        merchant.setInvitecode(inviteCode);
        //扫码注册统一为选择了线索池
        merchant.setCluePool(1);
        logger.info("bd扫码注册开始");
        merchant.setCrmRegisterFlag(true);
        AjaxResult result = verifyBL(merchant, false);
        if (!Objects.equals(AjaxResult.DEFAULT_SUCCESS, result.getCode())) {
            return result;
        }
        MerchantCluePool query = new MerchantCluePool();
        query.setMlId(leadsId);
        MerchantCluePool resultPool = merchantCluePoolMapper.queryMerchantClue(query);
        if (resultPool != null) {
            MerchantCluePool updatePool = new MerchantCluePool();
            updatePool.setId(resultPool.getId());
            updatePool.setMId(merchant.getmId());
            merchantCluePoolMapper.updateCluePool(updatePool);
        }

/*        logger.info("发送后台审核请求{}", merchant.getmId());
        mqProducer.send(RocketMqMessageConstant.MERCHANT_LEADS_AUDIT, null, merchant.getmId());*/


        // 如果是活动临时用的线索，就不修改线索状态
        if (!activityLeadsId) {
            MerchantLeads updateML = new MerchantLeads();
            updateML.setId(leadsId);
            updateML.setmId(merchant.getmId());
            updateML.setStatus(MerchantLeadsStatus.REGISTER.ordinal());
            merchantLeadsMapper.updateByPrimaryKeySelective(updateML);
        }

        // 产品设计：小程序直接跳首页，不展示二维码
        if(openId == null) {
            logger.info("小程序扫码注册结束");
            return AjaxResult.getOK();
        }
        logger.info("公众号获取用户信息.openid:{}", openId);
        String wxChannelCode = merchant.isPopMerchant() ? WxOfficialAccountsChannelEnum.POP_MALL.channelCode : WxOfficialAccountsChannelEnum.XM_MALL.channelCode;
        JSONObject userInfo = WeChatUtils.getUserInfo(openId, wxChannelCode);
        return AjaxResult.getOK(userInfo);
    }

    //微信注册获取untionid
    private String getUnionId(String openId, String wxChannelCode) {
        if (StringUtils.isEmpty(openId) || openId.length() < 12) {
            return null;
        }
        JSONObject userInfo = WeChatUtils.getUserInfo(openId, wxChannelCode);
        if (userInfo == null) {
            return null;
        }
        return userInfo.getString("unionid");
    }

    /**
     * 匹配截单区域
     *
     * @param merchant 商户
     */
    private AjaxResult matchAreaNo(Merchant merchant) {
        Contact contact = new Contact();
        contact.setCity(merchant.getCity());
        contact.setArea(merchant.getArea());
        contact.setPoiNote(merchant.getPoiNote());

        AjaxResult ajaxResult = fenceService.checkCityAreaFence(contact);
        if (!AjaxResult.isSuccess(ajaxResult)) {
            return ajaxResult;
        }
        Fence fence = (Fence) ajaxResult.getData();
        merchant.setAreaNo(fence.getAreaNo());
        return null;
    }

    @Override
    public AjaxResult updateLoginTime() {
        String openId = RequestHolder.getOpenId();
        if (StringUtils.isNotBlank(openId)) {
            subAccountMapper.updateLoginTime(openId, LocalDateTime.now());
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult insertGroupPurchase() {

        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        Long merchantId = merchantSubject.getMerchantId();
        MerchantGroupPurchase groupPurchase = new MerchantGroupPurchase();
        MerchantSubAccount merchantSubAccount = subAccountMapper.selectMangerByMId(merchantId);

        Long mId = merchantSubAccount.getmId();
        MerchantGroupPurchase query = merchantGroupPurchaseMapper.selectGroupPurchase(mId);
        if (query != null) {
            return AjaxResult.getErrorWithMsg("已参加团购");
        }
        Merchant merchant = merchantMapper.selectOneByMid(mId);
        FollowUpRelation followUpRelation = followUpRelationMapper.selectByMId(mId);
        if (followUpRelation != null) {
            groupPurchase.setAdminId(followUpRelation.getAdminId());
            groupPurchase.setAdminName(followUpRelation.getAdminName());
        }
        groupPurchase.setMId(mId);
        groupPurchase.setMName(merchant.getMname());
        groupPurchase.setPhone(merchant.getPhone());
        groupPurchase.setAddTime(new Date());
        try {
            merchantGroupPurchaseMapper.insertGroupPurchase(groupPurchase);
        } catch (Exception e) {
            return AjaxResult.getErrorWithMsg("已参加团购");
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult<LoginResponseVO> getLoginInfo() {
      /*   Map<String, String> query = new HashMap<>();
       query.put("accountId", RequestHolder.getAccountId().toString());
        MerchantSubAccount account = subAccountMapper.selectOne(query);*/
        MerchantSubAccountQuery merchantSubAccountQuery = new MerchantSubAccountQuery();
        merchantSubAccountQuery.setAccountId(RequestHolder.getAccountId());
        MerchantSubAccount account = merchantSubAccountRepository.selectOne(merchantSubAccountQuery);
        if (account == null) {
            return AjaxResult.getError(ResultConstant.MERCHANT_NOT_FOUND);
        }

        Merchant merchant = merchantMapper.selectOneByMid(account.getmId());
        MerchantExt merchantExt = areaService.getFreeDay(account.getmId());
        if (merchant == null) {
            return AjaxResult.getError(ResultConstant.MERCHANT_NOT_FOUND);
        }
        //切仓中
        if (RequestHolder.inChange()) {
            log.info("切仓中。merchant：{}", JSON.toJSONString(merchant));
            return AjaxResult.getError(ResultConstant.IN_CHANGE);
        }

        //封装返回到前端的参数
        LoginResponseVO loginResponseVO = new LoginResponseVO();
        loginResponseVO.setMId(merchant.getmId());
        loginResponseVO.setAdminId(merchant.getAdminId());
        loginResponseVO.setPopMerchant(isPopMerchantV2(merchant.getBusinessLine()));
        loginResponseVO.setPreApprovedMerchant(isPreApprovedMerchant(merchant.getAdminId()));
        loginResponseVO.setMname(merchant.getMname());
        loginResponseVO.setMcontact(account.getContact());
        loginResponseVO.setPhone(account.getPhone());
        loginResponseVO.setIslock(merchant.getIslock());
        loginResponseVO.setAddress(defaultAddress(merchant));
        loginResponseVO.setMergePop(account.getFirstPopView());
        loginResponseVO.setFirstLoginPop(merchant.getFirstLoginPop());
        loginResponseVO.setChangePop(merchant.getChangePop());
        loginResponseVO.setDirect(merchant.getDirect());
        loginResponseVO.setSize(merchant.getSize());
        loginResponseVO.setCreateTime(merchant.getRegisterTime());
        loginResponseVO.setDisplayButton(merchant.getDisplayButton());
        loginResponseVO.setOperateStatus(merchant.getOperateStatus());
        if (!Objects.isNull(merchantExt)) {
            if (RequestHolder.isMajor()) {
                DistributionRuleVO distributionRuleVO = distributionRuleService.queryByAdminId();
                if (Objects.isNull(distributionRuleVO)) {
                    loginResponseVO.setDisplayFreeDay(merchantExt.getClickFlag());
                }
            } else {
                loginResponseVO.setDisplayFreeDay(merchantExt.getClickFlag());

            }
        }

        //审核中
        if (Objects.equals(1, merchant.getIslock())) {
            log.info("审核中。merchant：{}", JSON.toJSONString(merchant));
            return AjaxResult.getError(ResultConstant.REVIEWING, loginResponseVO);
        }

        //审核失败
        if (Objects.equals(2, merchant.getIslock())) {
            loginResponseVO.setRemark(merchant.getRemark());
            if(MerchantOperateStatusEnum.VERIFICATION_REJECTED.getCode().equals(merchant.getOperateStatus())) {
                log.info("核验未通过。merchant：{}", JSON.toJSONString(merchant));
                return AjaxResult.getError(Constants.VERIFY_NOT_PASS, loginResponseVO);
            } else {
                log.info("审核未通过。merchant：{}", JSON.toJSONString(merchant));
                return AjaxResult.getError(ResultConstant.REVIEW_NOT_PASS, loginResponseVO);
            }
        }

        //账号在审核中
        if (SubAccountStatus.UN_AUDIT.ordinal() == account.getStatus()) {
            Map<String, Object> map = new HashMap<>(4);
            map.put("accountId", account.getAccountId());
            map.put("mname", merchant.getMname());
            map.put("contact", account.getContact());
            map.put("createTime", account.getRegisterTime());
            map.put("accountStatus", 1);
            if (SubAccountType.STAFF.ordinal() == account.getType()) {
                map.put("mergeKey", merchant.getChannelCode());
            } else {
                map.put("remark", merchant.getRemark());
            }
            return AjaxResult.getError(ResultConstant.IN_MERGE, map);
        }

        //是否参与团购 0 参加过 1 没参加
        MerchantGroupPurchase groupPurchase = merchantGroupPurchaseMapper.selectGroupPurchase(account.getmId());
        loginResponseVO.setGroupPurchase(1);
        if (groupPurchase != null) {
            loginResponseVO.setGroupPurchase(0);
        }
        //等级
        Integer grade = memberService.calculGrade(account.getmId());
        loginResponseVO.setGrade(grade);
        loginResponseVO.setMemberList(RequestHolder.getMemberVOS());
        //区域 支付渠道
        Integer areaNo = merchant.getAreaNo();
        Area area = areaMapper.selectByAreaNo(areaNo);
        Contact contacts = contactMapper.selectIsDefaultByMid(merchant.getmId());
        if (Objects.isNull(contacts)) {
            logger.info("{} 用户地址不存在,请查看", account.getmId());
            return AjaxResult.getError(Constants.NO_CONTACT, "不存在收货地址,添加收货地址后进行操作");
        }
        // 下单地址校验
        List<LocalDate> deliveryRuleList = new ArrayList<>();
        try {
            deliveryRuleList = deliveryRuleQueryFacade.getDeliveryRuleList(LocalDateTime.now(), merchant.getmId(), contacts.getContactId(),
                    null, null, DistOrderSourceEnum.getSourceEnum(merchant.getBusinessLine()));
        } catch (Exception e) {
            logger.info("用户地址获取配送时间异常，用户ID>>>{}, 用户地址ID>>>{}", merchant.getmId(), contacts.getContactId(), e);
        }
        loginResponseVO.setAreaName(area.getAreaName());
        loginResponseVO.setAreaNo(area.getAreaNo());
        loginResponseVO.setStatus(area.getStatus());
        loginResponseVO.setStoreNo(area.getParentNo());
        loginResponseVO.setDeliveryFrequentNew(deliveryRuleList);
        loginResponseVO.setPayChannel(area.getPayChannel());
        Set<Integer> blackAreaNos = paymentConfig.getCmbTransferWechatDirectPayBlackAreas();
        if (!blackAreaNos.contains(area.getAreaNo())) {
            loginResponseVO.setCmbTransferWechatDirectPay(paymentConfig.getCmbTransferWechatDirectPay());
        } else {
            loginResponseVO.setCmbTransferWechatDirectPay(CmbTransferWechatDirectPayEnum.NOT_SUPPORT.getCode());
        }

        //取配送时间
        if (!CollectionUtils.isEmpty(deliveryRuleList)) {
            loginResponseVO.setNextDeliveryDate(deliveryRuleList.get(0));
        }

        //大客户提前截单
        if (Objects.equals(merchant.getSize(), Global.BIG_MERCHANT)) {
            Admin admin = adminMapper.selectByPrimaryKey(merchant.getAdminId());
            logger.info("大客户截单类型{}", admin.getCloseOrderType());
            loginResponseVO.setCloseOrderType(admin.getCloseOrderType());
            loginResponseVO.setCloseOrderTime(admin.getCloseOrderTime());
            AdminSkin adminSkin = adminSkinMapper.selectByAdminId(merchant.getAdminId());
            if (adminSkin != null) {
                loginResponseVO.setShowFlag(adminSkin.getShowFlag());
                loginResponseVO.setLogo(adminSkin.getLogo());
                loginResponseVO.setBackgroundImage(adminSkin.getBackgroundImage());
            }
        }
        // 是否享受会员权益
        List<MemberVO> memberVOS = new ArrayList<>();
        Integer grayscale = isGrayscale(merchant, area.getMemberRule(), memberVOS);
        loginResponseVO.setGrayscale(grayscale);
        //是否享红包权益
        Integer merchantType = merchantType(merchant);
        //是否是茶百道
        Boolean cbd = isCbd(merchant.getAdminId());
        loginResponseVO.setCbdFlag(cbd);
        //配送仓截单时间
        LocalDateTime storeCloseOrderTime = getTime(merchant);
        Integer supportAddOrder = area.getSupportAddOrder();
        loginResponseVO.setSupportAddOrder(supportAddOrder);
        loginResponseVO.setStoreCloseOrderTime(storeCloseOrderTime);
        // 查询用户所属区域的运营大区
        Integer largeAreaNo = area.getLargeAreaNo();
        loginResponseVO.setLargeAreaNo(largeAreaNo);


        //判断是否是团长信息
        MerchantExt ext = merchantExtMapper.selectByMid(merchant.getmId(), null);
        if (ext != null) {
            loginResponseVO.setGroupHeadFlag(ext.getGroupHeadFlag());
        }

        return AjaxResult.getOK(loginResponseVO);
    }

    @Override
    @Transactional
    public AjaxResult registerToCBD(Merchant merchant) {
        logger.info("merchant ={}", JSON.toJSONString(merchant));
        logger.info("registerToCBD 获取登陆信息 {}", RequestHolder.getMerchantSubject() == null ? "" : JSON.toJSONString(RequestHolder.getMerchantSubject()));

        MerchantStoreAndExtendResp resultMerchant = merchantQueryFacade.getMerchantExtendsByMid(merchant.getmId());
        if (Objects.isNull(resultMerchant) || !Objects.equals(resultMerchant.getPhone(), merchant.getPhone())) {
            return AjaxResult.getErrorWithMsg("获取信息失败");
        }
        String mContact = merchant.getMcontact().trim();
        merchant.setPhone(resultMerchant.getPhone());
        if (!StringUtils.isMname(mContact)) {
            return AjaxResult.getErrorWithMsg("账号名称只能由汉字、数字和英文字母组成，请重新输入");
        }

        if (StringUtils.isBlank(RequestHolder.getOpenId()) && StringUtils.isBlank(RequestHolder.getMpOpenId())) {
            return AjaxResult.getError("WECHAT_ONLY", "提交超时，请退出重新进入");
        }

        Map<String, String> openidSelectkeys = new HashMap<>();
        openidSelectkeys.put("openid", RequestHolder.getOpenId());
        int openidNum = merchantMapper.count(openidSelectkeys);
        if (openidNum != 0) {
            logger.info("预注册信息店铺名称：{}，手机号：{}，openid：{}", resultMerchant.getStoreName(), resultMerchant.getPhone(), RequestHolder.getOpenId());
            return AjaxResult.getErrorWithMsg("该微信已绑定店铺,请联系客服处理");
        }
        MerchantSubAccountQuery merchantSubAccountQuery = new MerchantSubAccountQuery();
        merchantSubAccountQuery.setPhone(merchant.getPhone());
        MerchantSubAccount merchantSubAccount1 = merchantSubAccountRepository.selectOne(merchantSubAccountQuery);
        if (merchantSubAccount1 != null && Objects.equals(merchantSubAccount1.getStatus(), 1)) {
            throw new BizException("手机号已注册！");
        }
        //匹配截单区域
        AjaxResult checkCityAreaFence = matchAreaNo(merchant);
        if (Objects.nonNull(checkCityAreaFence)) {
            return checkCityAreaFence;
        }

        Long adminIdLong = resultMerchant.getAdminId();
        if (adminIdLong != null) {
            Integer adminId = adminIdLong.intValue();
            Admin admin = adminMapper.selectByPrimaryKey(adminId);
            //邀请码
            merchant.setInvitecode(invitecodeMapper.selectSale(admin.getSalerId()));
        }

        contactService.addContactForPreRegister(merchant);
        //MerchantSubAccount resultAccount = subAccountMapper.selectMangerByMId(merchant.getmId());
        MerchantSubAccount resultAccount = merchantSubAccountRepository.selectMangerByMId(merchant.getmId());
        if (resultAccount == null) {
            throw new BizException("店长信息不存在: " + merchant.getmId());
        }
        merchant.setOpenid(RequestHolder.getOpenId());
        merchantMapper.updateByPrimaryKeySelective(merchant);
        MerchantSubAccount merchantSubAccount = new MerchantSubAccount();
        merchantSubAccount.setmId(merchant.getmId());
        merchantSubAccount.setOpenid(RequestHolder.getOpenId());
        merchantSubAccount.setContact(mContact);
        merchantSubAccount.setAccountId(resultAccount.getAccountId());
        merchantSubAccount.setStatus(1);
        subAccountMapper.updateByPrimaryKeySelective(merchantSubAccount);
        return AjaxResult.getOK();
    }

    @Override
    public boolean checkCBD(Long mId) {
        Merchant merchant = merchantMapper.selectOneByMid(mId);
        if (Objects.isNull(merchant.getAdminId())) {
            return false;
        }
        String adminIdStr = configService.getValue(Global.CBD_ADMIN_ID);
        return Arrays.stream(adminIdStr.split(StringUtils.SEPARATING_SYMBOL)).anyMatch(el -> el.equals(merchant.getAdminId().toString()));
    }

    @Override
    public Merchant preRegister(String phone) {
        MerchantSubAccount subAccount = subAccountService.selectOneNew("phone", phone);
        if (Objects.nonNull(subAccount)) {
            Merchant resultMerchant = merchantRepository.getMerchantByMid(subAccount.getmId());
            if (Objects.nonNull(resultMerchant)) {
                //为导入的茶百道客户
                if (MerchantConstants.MERCHANT_PRE_REGISTER_FLAG.equals(resultMerchant.getPreRegisterFlag()) && StringUtils.isMobile(subAccount.getOpenid())) {
                    //推送门店信息
                    //pushMerchantInfo(resultMerchant.getmId());
                    return resultMerchant;
                }
            }
        }
        return null;
    }


    /**
     * 判断是否是茶百道
     *
     * @param adminId 大客户id
     * @return
     */
    private Boolean isCbd(Integer adminId) {
        if (adminId == null) {
            return false;
        }

        String value = configService.getValue("CBD_admin_id");
        if (StringUtils.isNotBlank(value)) {
            String[] split = value.split(",");
            for (String s : split) {
                if (Objects.equals(adminId, Integer.valueOf(s))) {
                    return true;
                }
            }
        }

        return false;
    }

    private MerchantSubAccount getMockLoginInfo(String phone, String sign, Boolean gray) {
        logger.info("模拟登陆phone：{}", phone);
        if (gray == null) {
            gray = false;
        }

        String deStr = phone + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + "login";
        if (!Objects.equals(sign, MD5Util.string2MD5(deStr))) {
            throw new DefaultServiceException("签名失败");
        }

        Map<String, String> map = new HashMap<>();
        map.put("phone", phone);
        MerchantSubAccountQuery merchantSubAccountQuery = new MerchantSubAccountQuery();
        merchantSubAccountQuery.setPhone(phone);
        MerchantSubAccount account = gray ? merchantSubAccountRepository.selectOne(merchantSubAccountQuery) : subAccountMapper.selectOne(map);
        if (account == null) {
            throw new DefaultServiceException(ResultConstant.MERCHANT_NOT_FOUND);
        }
        MerchantStoreAndExtendResp merchant = merchantQueryFacade.getMerchantExtendsByMid(account.getmId());
        if (merchant == null) {
            throw new DefaultServiceException("该账号不允许mock login");
        }
        if (Objects.equals(1, merchant.getMockLoginFlag())) {
            return account;
        } else {
            MerchantExt ext = merchantExtMapper.selectByMid(merchant.getMId(), null);
            if (ext != null && Objects.equals(1, ext.getGroupHeadFlag())) {
                return account;
            }
            throw new DefaultServiceException("该账号不允许mock login");
        }
    }

    private LocalDateTime getTime(Merchant merchant) {
        List<Contact> contacts = contactMapper.selectByMidOrderByDefault(merchant.getmId(), 1);
        if (CollectionUtils.isEmpty(contacts)) {
            log.error("当前客户地址为空，返回默认截单时间！mid:{}", merchant.getmId());
            return LocalDateTime.of(LocalDate.now(), Global.CLOSING_ORDER_TIME);
        }
        Integer storeNo = CollectionUtils.isEmpty(contacts) ? null : contacts.get(0).getStoreNo();
        LocalTime closeTime = logisticsService.selectCloseTime(storeNo);
        log.info("老的方式获取的截单时间：{}", closeTime);

        FenceCloseTimeReq req = new FenceCloseTimeReq();
        req.setSource(DistOrderSourceEnum.getSourceEnum(merchant.getBusinessLine()));
        req.setContactId(contacts.get(0).getContactId());
        req.setArea(contacts.get(0).getArea());
        req.setCity(contacts.get(0).getCity());
        closeTime = wncDeliveryFenceQueryFacade.queryCloseTime(req);
        return LocalDateTime.of(LocalDate.now(), closeTime);
    }

    @Override
    public AjaxResult select(Long mId) {
        //查出门店开关和adminId
        MerchantVO merchantVO = merchantMapper.selectMallByMid(mId);
        if (merchantVO == null) {
            throw new BizException("店铺已被合并或不存在");
        }
        //店铺商城开关
        String storeMallSwitch = configService.getValue("storeMallSwitch");
        //品牌商城开关
        String brandMallSwitch = configService.getValue("brandMallSwitch");
        merchantVO.setStoreMallSwitch(storeMallSwitch);
        merchantVO.setBrandMallSwitch(brandMallSwitch);
        return AjaxResult.getOK(merchantVO);
    }

    /**
     * 点击获取免配日的值
     *
     * @return 免配日的值
     */
    @Override
    public AjaxResult getFreeDay() {
        Long mId = RequestHolder.getMId();
        //查询该用户的待生效信息
        MerchantExt merchant = merchantExtMapper.selectByMid(mId, FreeDayEnum.CHECK_FALSE.ordinal());
        //更新信息
        merchant.setClickFlag(FreeDayEnum.CHECK_TRUE.ordinal());
        merchantExtMapper.updateByPrimaryKeySelective(merchant);
        return AjaxResult.getOK(merchant.getFreeDay());
    }

    @InMemoryCache
    @Override
    public Merchant getMerchantFromCache(Long mId) {
        return merchantMapper.selectOneByMid(mId);
    }

    @Override
    @InMemoryCache(expiryTimeInSeconds = 60 * 30)// 30 分钟过期
    public boolean checkCoreCustomers(Long mId) {
        if (Objects.isNull(mId)) {
            return false;
        }

        Merchant merchant = redisCacheUtil.getDataWithCache(Global.CACHE_MERCHANT_INFO + mId, 300, Merchant.class, () -> merchantMapper.selectOneByMid(mId));
        //是否是大客户
        if (!Objects.isNull(merchant) && (
                Objects.equals(Global.BIG_MERCHANT, merchant.getSize()) ||
                        Objects.equals(merchant.getGrade(), 3))) {
            logger.info("mid={},customer=true", mId);
            return true;
        }
        //是否是核心客户
        List<CrmMerchantDayGmv> crmMerchantDayGmvList = crmMerchantDayGmvMapper.selectByPrimaryKey(mId);
        if (!CollectionUtils.isEmpty(crmMerchantDayGmvList)) {
            return true;
        }
        return false;
    }

    @Override
    public AjaxResult queryBdInfo() {
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        Long merchantId = merchantSubject.getMerchantId();

        FollowUpRelation followUpRelation = followUpRelationMapper.selectByMId(merchantId);
        if (Objects.isNull(followUpRelation)) {
            return AjaxResult.getOK();
        }
        Admin admin = adminMapper.selectAdminInfoByadminId(followUpRelation.getAdminId());

        FollowUpRelationVO followUpRelationVO = new FollowUpRelationVO();
        followUpRelationVO.setAdmin(admin);
        return AjaxResult.getOK(followUpRelationVO);
    }

    public Boolean cancelMerchant(Long mId, Boolean keepMainAccount) {
        logger.info("MerchantServiceImpl[]cancelMerchant[]start[]mId:{}", mId);
        //获取门店的基本基本信息
        Merchant merchant = merchantMapper.selectOneByMid(mId);
        if (Objects.isNull(merchant)) {
            logger.warn("MerchantServiceImpl[]cancelMerchant[]merchant not exist mId:{}", mId);
            throw new BizException("当前门店信息不存在！");
        }

        //获取门店所有账号信息
        List<MerchantSubAccount> accountList = merchantSubAccountMapper.selectAllAccountByMId(mId);
        if (CollectionUtils.isEmpty(accountList) && !keepMainAccount) {
            logger.warn("MerchantServiceImpl[]cancelMerchant[]accountList not exist mId:{}", mId);
            //throw new BizException("当前门店账户信息不存在！");
        }
        List<MerchantSubAccount> mainAccount = accountList.stream().filter(it -> Objects.equals(SubAccountType.MANAGER.ordinal(), it.getType())).collect(Collectors.toList());

        //更新门店表 将phone、openid、mp_openid、unionid拼上后缀并新增注销状态
        Merchant newMerchant = new Merchant();
        newMerchant.setmId(merchant.getmId());
        newMerchant.setIslock(ReviewStatus.STORE_CLOSURE.getIslock());
        newMerchant.setPhone(merchant.getPhone() + "_" + mId);
        newMerchant.setOpenid(merchant.getOpenid() + "_" + mId);
        newMerchant.setMname(merchant.getMname() + "_" + mId);
        if (StringUtils.isNotBlank(merchant.getUnionid())) {
            newMerchant.setUnionid(merchant.getUnionid() + "_" + mId);
        }
        if (StringUtils.isNotBlank(merchant.getMpOpenid())) {
            newMerchant.setMpOpenid(merchant.getMpOpenid() + "_" + mId);
        }
        merchantMapper.updateByPrimaryKeySelective(newMerchant);
        // crm和bd绑定关系删除
        FollowUpRelation followUpRelation = followUpRelationMapper.selectByMId(mId);
        if (followUpRelation != null) {
            followUpRelationMapper.deleteByMid(mId);
        }
        if (keepMainAccount) {
            accountList = accountList.stream().filter(it -> Objects.equals(SubAccountType.STAFF.ordinal(), it.getType())).collect(Collectors.toList());
        }
        //子账号为空不处理 但是主账号踢出登陆
        if (CollectionUtils.isEmpty(accountList)) {
            if (!CollectionUtils.isEmpty(mainAccount)) {
                MerchantSubAccount account = mainAccount.get(0);
                //踢出主账号
                loginOut(account);

            }
            return true;
        }
        //更新子账户表 并将phone、openid、mp_openid、unionid拼上后缀并新增注销状态
        accountList.stream().forEach(e -> {
            MerchantSubAccount merchantSubAccount = new MerchantSubAccount();
            merchantSubAccount.setAccountId(e.getAccountId());
            merchantSubAccount.setStatus(MerchantSubStatusEnum.CANCELLED.getCode());
            merchantSubAccount.setPhone(e.getPhone() + "_" + mId);
            merchantSubAccount.setOpenid(e.getOpenid() + "_" + mId);
            if (StringUtils.isNotBlank(e.getUnionid())) {
                merchantSubAccount.setUnionid(e.getUnionid() + "_" + mId);
            }
            if (StringUtils.isNotBlank(e.getMpOpenid())) {
                merchantSubAccount.setMpOpenid(e.getMpOpenid() + "_" + mId);
            }
            //逻辑移除账户
            merchantSubAccount.setDeleteFlag(CommonStatus.NO.getCode());
            merchantSubAccountMapper.updateByPrimaryKeySelective(merchantSubAccount);

            //踢出
            loginOut(e);
        });
        logger.info("MerchantServiceImpl[]cancelMerchant[]end[]mId:{}", mId);
        return Boolean.TRUE;
    }

    private void loginOut(MerchantSubAccount merchantSubAccount) {
        if (merchantSubAccount == null) {
            return;
        }
        //踢出登录信息
        String maId = merchantSubAccount.getmId() + "_" + merchantSubAccount.getAccountId();
        String token = (String) redisTemplate.opsForHash().get(KeyConstant.MA_ID_2_TOKEN, maId);
        if (StringUtils.isNotBlank(token)) {
            redisTemplate.delete(KeyConstant.MALL_TOKEN_PREFIX + token);
        }
        redisTemplate.opsForHash().delete(KeyConstant.MA_ID_2_TOKEN, maId);
        logger.info("店长移除账号，踢出登录信息：{}", maId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelMerchant(Long mId) {
        //判断门店状态是否正常--防止子账号被迁移
        Merchant merchant = merchantMapper.selectByMid(mId);
        if (ReviewStatus.APPROVED.getIslock() == merchant.getIslock() ||
                ReviewStatus.PULLED_BLACK.getIslock() == merchant.getIslock()) {
            return cancelMerchant(mId, false);
        }
        log.info("MerchantServiceImpl[]cancelMerchant[]is not approve merchant:{}", JSON.toJSONString(merchant));
        return Boolean.FALSE;
    }

    @Override
    public Merchant queryMerchantInfo(Long mId) {
        Merchant merchant = merchantMapper.selectOneByMid(mId);
        return merchant;
    }

    @Override
    public Boolean urgeAudit() {
        Long mId = RequestHolder.getMId();
        MQData mqData = new MQData();
        mqData.setData(mId);
        mqData.setType(MType.MERCHANT_URGE_AUDIT.name());
        mqProducer.send(RocketMqMessageConstant.MALL_LIST, null, JSON.toJSONString(mqData));
        return Boolean.TRUE;
    }
    @Override
    public CommonResult<Void> completeInformation(Merchant merchantInput) {
        LoginMerchantCacheDTO merchantCache = RequestHolder.getLoginMerchantCache();
        Long merchantId = merchantCache.getMerchantId();
        String mname = merchantInput.getMname();

        Merchant merchant = merchantMapper.selectByPrimaryKey(merchantId);
        if(merchant == null) {
            log.error("门店信息不存在");
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "门店信息不存在");
        }
        if(!MerchantEnum.OperateStatusEnum.PENDING_SUBMISSION.getCode().equals(merchant.getOperateStatus())) {
            log.error("门店信息已完善, merchant:{}", JSON.toJSONString(merchant));
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "门店信息已经完善，试试重新进入商城~");
        }

        if (!StringUtils.isMname(mname)) {
            log.error("店铺名称只能由汉字、数字和英文字母组成，请重新输入");
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "店铺名称只能由汉字、数字和英文字母组成，请重新输入");
        }
        if (mname.length() > 25) {
            log.error("店铺名称不可超过25个字哦");
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "店铺名称不可超过25个字哦");
        }


        if(!Objects.equals(merchant.getMname(), mname)) {
            MerchantStoreResultResp nameUsedMerchant = merchantQueryFacade.getMerchantByExactStoreName(mname);
            if (nameUsedMerchant != null) {
                log.error("门店名称重复");
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "该店铺名已存在,请重新输入");
            }
        }

        String oldMerchantName = merchant.getMname();
        MerchantSubAccount merchantSubAccount = merchantSubAccountMapper.selectMangerByMId(merchant.getmId());
        if(merchantSubAccount != null && Objects.equals(oldMerchantName, merchantSubAccount.getContact())) {
            // 只处理默认的店长名称
            MerchantSubAccount subAccountUpdate = new MerchantSubAccount();
            subAccountUpdate.setAccountId(merchantSubAccount.getAccountId());
            subAccountUpdate.setContact(mname);
            merchantSubAccountMapper.updateByPrimaryKeySelective(subAccountUpdate);
        }

        List<Contact> contacts = contactMapper.selectByMidOrderByDefault(merchant.getmId(), 1);
        if(CollUtil.isNotEmpty(contacts)) {
            // 未提交的门店一般只有一个地址
            contacts.sort(Comparator.comparing(Contact::getContactId));
            Contact contact = contacts.get(0);
            contact.setContact(mname);
            log.info("开始处理地址数据：contact:{}", JSON.toJSONString(contact));
            contactMapper.updateByPrimaryKeySelective(contact);
        }

        merchant.setSubmitReviewTime(new Date());
        merchant.setMname(mname);
        merchant.setMcontact(mname);
        merchant.setType(merchantInput.getType());
        merchant.setDoorPic(merchantInput.getDoorPic());
        merchant.setOperateStatus(MerchantEnum.OperateStatusEnum.PENDING_VERIFICATION.getCode());
        merchantMapper.updateByPrimaryKeySelective(merchant);
        return CommonResult.ok();
    }

    @Override
    public CommonResult<Void> goBackPendingSubmit(Long mid) {
        log.info("审核不通过门店，返回待提交核验：mid:{}, cache:{}", mid, JSON.toJSONString(RequestHolder.getLoginMerchantCache()));
        if(mid == null) {
            log.error("请求参数异常");
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "请求参数异常");
        }
        Merchant merchant = merchantMapper.selectOneByMid(mid);
        if (merchant == null) {
            logger.error("门店不存在");
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "门店不存在");
        }

        // 校验是否核验拒绝
        boolean verificationRejected = ReviewStatus.REVIEW_NOT_PASS.getIslock() == merchant.getIslock() && MerchantOperateStatusEnum.VERIFICATION_REJECTED.getCode().equals(merchant.getOperateStatus());
        if(!verificationRejected) {
            logger.error("门店状态非核验拒绝！");
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "当前状态暂不支持重新打开商城，请联系客服");
        }

        merchant.setIslock(0);
        merchant.setOperateStatus(MerchantOperateStatusEnum.PENDING_SUBMISSION.getCode());
        merchant.setDirect(2);
        merchant.setSkuShow(1);
        merchant.setAdminId(dynamicConfig.getNoAuditMerchantDefaultAdminId());
        merchant.setSize("大客户");
        merchantMapper.updateByPrimaryKeySelective(merchant);
        log.info("审核不通过门店，返回待提交核验操作完成：mid:{}", mid);
        return CommonResult.ok();
    }

    private String getMerchantNameByPrefix(String namePrefix, String phone) {
        String lastFourDigits  = phone.substring(phone.length() - 4);
        String randomString = RandomUtil.randomString(2);
        return namePrefix + randomString + lastFourDigits;
    }

    @Override
    public void upsertTemporaryData(DataStorageInput input) {
        Long accountId = RequestHolder.getAccountId();
        if (accountId == null) {
            throw new ParamsException("参数异常,无法获取accountId");
        }

        redisTemplate.opsForValue()
                .set(String.format("%s-%s-%s", KeyConstant.TEMPORARY_FRONTEND_DATA, input.getBizType(), accountId),
                        JSON.toJSONString(input.getData()), 1, TimeUnit.DAYS);
    }

    @Override
    public JSONObject getTemporaryData(DataStorageQueryInput input) {
        Long accountId = RequestHolder.getAccountId();
        if (accountId == null) {
            throw new ParamsException("参数异常,无法获取accountId");
        }

        String value = redisTemplate.opsForValue()
                .get(String.format("%s-%s-%s", KeyConstant.TEMPORARY_FRONTEND_DATA,
                        input.getBizType(),
                        accountId));

        return JSON.parseObject(value);
    }

    @Override
    public List<String> wantBuy(ProductQueryInput input) {
        if (RequestHolder.isMajor()) {
            return new ArrayList<>();
        }

        if (input == null || StringUtils.isBlank(input.getTitleSuggest())) {
            log.info("搜索词为空");
            return new ArrayList<>();
        }
        try {
            Object o = redisTemplate.opsForHash()
                    .get(KeyConstant.RELATED_QUERIES, input.getTitleSuggest());
            return o == null ? new ArrayList<>() : Arrays.asList(String.valueOf(o).split(","));
        } catch (Exception e) {
            log.error("获取可能想买搜索词数据异常", e);
            return new ArrayList<>();
        }
    }
}
