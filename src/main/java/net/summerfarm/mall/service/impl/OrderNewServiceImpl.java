package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.*;
import net.summerfarm.mall.common.config.DynamicConfig;
import net.summerfarm.mall.common.delayqueue.OrderCancelItem;
import net.summerfarm.mall.common.mq.DelayData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.common.util.PageInfoHelper;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.enums.MajorPriceMallShowEnum;
import net.summerfarm.mall.enums.SaleStockChangeTypeEnum;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.enums.order.AddOnEnum;
import net.summerfarm.mall.enums.order.FulfillmentWayEnum;
import net.summerfarm.mall.enums.order.OrderItemErrorEnum;
import net.summerfarm.mall.facade.fms.SellingEntityQueryFacade;
import net.summerfarm.mall.facade.goods.PopBuyerQueryFacade;
import net.summerfarm.mall.facade.goods.dto.PopBuyerInfoDTO;
import net.summerfarm.mall.facade.market.ProductsSaleRuleFacade;
import net.summerfarm.mall.facade.market.dto.ProductsSaleRuleDto;
import net.summerfarm.mall.facade.ofc.OfcLogisticsQueryFacade;
import net.summerfarm.mall.facade.pms.PopSkuAdditionQueryFacade;
import net.summerfarm.mall.facade.pms.dto.PopSkuAdditionInfoDTO;
import net.summerfarm.mall.facade.pms.dto.PopSkuAdditionQueryDto;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.converter.order.OrderItemConverter;
import net.summerfarm.mall.model.converter.order.OrderResultConverter;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDTO;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDetailDTO;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.dto.order.OrderItemInfoDTO;
import net.summerfarm.mall.model.dto.order.PlaceOrderCalcDTO;
import net.summerfarm.mall.model.dto.trolley.ShoppingCartDTO;
import net.summerfarm.mall.model.input.order.OrderPageInput;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.model.vo.neworder.*;
import net.summerfarm.mall.model.vo.order.OrderDetailVO;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.order.AbstractOrderItemHandler;
import net.summerfarm.mall.order.AbstractPlaceOrderHandler;
import net.summerfarm.mall.order.OrderCalcFollowConfig;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.facade.MarketItemFacade;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.WncDeliveryRuleQueryFacade;
import net.summerfarm.mall.service.facade.dto.*;
import net.summerfarm.mall.service.helper.OrderCalcServiceHelper;
import net.summerfarm.mall.service.helper.OrderServiceHelper;
import net.summerfarm.mall.service.popmall.PopHelpOrderService;
import net.summerfarm.mall.task.AsyncTaskService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @create: 2023/10/11
 */
@Slf4j
@Service
public class OrderNewServiceImpl implements OrderNewService {

    @Resource
    private MerchantCouponService merchantCouponService;
    @Resource
    private MerchantCouponMapper merchantCouponMapper;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private MajorPriceService majorPriceService;
    @Resource
    private MajorCategoryMapper majorCategoryMapper;
    @Resource
    private MajorPriceMapper majorPriceMapper;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private DiscountCardService discountCardService;
    @Resource
    private MarketRuleHistoryMapper marketRuleHistoryMapper;
    @Resource
    private OrderPreferentialMapper orderPreferentialMapper;
    @Resource
    private OrderService orderService;
    @Resource
    @Lazy
    private AsyncTaskService asyncTaskService;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private PrepayInventoryService prepayInventoryService;
    @Resource
    private AreaSkuService areaSkuService;
    @Resource
    private OrderItemPreferentialMapper orderItemPreferentialMapper;
    @Lazy
    @Resource
    ActivityService activityService;
    @Resource
    private OrderCalcServiceHelper orderCalcServiceHelper;
    @Resource
    private DeliveryService deliveryService;
    @Resource
    private ActivitySkuPurchaseQuantityMapper activitySkuPurchaseQuantityMapper;
    @Resource
    private ExpandActivityQuantityMapper expandActivityQuantityMapper;
    @Resource
    private WncDeliveryRuleQueryFacade deliveryRuleQueryFacade;
    @Resource
    private MarketPriceControlProductsMapper marketPriceControlProductsMapper;
    @Resource
    private OrderRelationService orderRelationService;
    @Resource
    private RechargeRecordService rechargeRecordService;
    @Resource
    private CategoryMapper categoryMapper;
    @Resource
    private SkuDiscountMapper skuDiscountMapper;
    @Resource
    private CollocationService collocationService;
    @Resource
    DeliverPlanRemarkSnapshotService deliverPlanRemarkSnapshotService;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private ConfigService configService;
    @Resource
    private WmsAreaStoreFacade wmsAreaStoreFacade;
    @Resource
    private OrderServiceHelper orderServiceHelper;
    @Resource
    private MasterOrderMapper masterOrderMapper;
    @Resource
    private ShoppingCartService shoppingCartService;
    @Resource
    private OrderRelationMapper orderRelationMapper;
    @Resource
    private DeliveryPlanService deliveryPlanService;
    @Resource
    private TimingOrderRefundTimeMapper timingOrderRefundTimeMapper;
    @Resource
    private MarketItemFacade marketItemFacade;
    @Resource
    private MerchantService merchantService;
    @Resource
    private PopBuyerQueryFacade popBuyerQueryFacade;
    @Resource
    private PopSkuAdditionQueryFacade popSkuAdditionQueryFacade;

    @Resource
    private OrderItemExtraMapper orderItemExtraMapper;

    @Resource
    private ProductsSaleRuleFacade productsSaleRuleFacade;
    @Resource
    private DynamicConfig dynamicConfig;
    @Resource
    private AreaSkuPriceMarkupConfigMapper areaSkuPriceMarkupConfigMapper;

    @Resource
    private SellingEntityQueryFacade sellingEntityQueryFacade;
    @Autowired
    private OfcLogisticsQueryFacade ofcLogisticsQueryFacade;

    @Resource
    private ProductService productService;

    @Resource
    private PopHelpOrderService popHelpOrderService;

    @Override
    public OrderNewResultVO preOrder(PlaceOrderVO placeOrderVO) {
        OrderNewResultVO resultVO = new OrderNewResultVO();
        //补充必要信息
        orderCalcServiceHelper.initPlaceOrder(placeOrderVO);
        //下单前置校验
        orderFrontCheck(placeOrderVO);

        //暂时还是转成Trolley对象处理
        List<Trolley> trolleyList;
        if (!CollectionUtil.isEmpty(placeOrderVO.getOrderNow())) {
            //代下单
            trolleyList = placeOrderVO.getOrderNow();
        } else {
            //商城下单
            trolleyList = OrderItemConverter.toTrolleyList(placeOrderVO.getOrderItemList());
        }

        //订单项构建：原价、营销策略 、大客户mallshow
        List<OrderItemCalcDTO> itemCalcVOList = orderItemBuild(trolleyList, placeOrderVO,Objects.equals(placeOrderVO.getHelpOrder(), 0));

        //上下架信息校验、库存、大客户mallshow校验
        List<OrderItemErrorVO> itemErrorVOList = orderCheck(itemCalcVOList,placeOrderVO);
        if (CollectionUtil.isNotEmpty(itemErrorVOList)) {
            resultVO.setOrderItemErrorList(itemErrorVOList);
            List<OrderItemErrorVO> notOnSaleErrorList = itemErrorVOList.stream()
                    .filter(x -> Objects.equals(x.getReason(),
                            OrderItemErrorEnum.NOT_ON_SALE.getCode())).collect(Collectors.toList());
            //所有包裹内的商品均在对应切换的区域没有进行上架，则不可以切换地址
            if (notOnSaleErrorList.size() == itemCalcVOList.size()) {
                resultVO.setCanChangeAddress(0);
            } else {
                resultVO.setCanChangeAddress(1);
            }
            return resultVO;
        }

        // 配送日期处理
        this.assignDeliveryDate(placeOrderVO);
        //营销策略、优惠券处理
        OrderResultVO orderResultVO = orderResultBuild(itemCalcVOList, placeOrderVO);
        resultVO = OrderResultConverter.toOrderNewResultVO(orderResultVO);
        //按配送时间对子订单排序
        List<OrderResultVO> subOrderList = placeOrderVO.getSubOrderResultMap().values().stream()
                .sorted(Comparator.comparing(OrderResultVO::getDeliveryDate)).collect(
                        Collectors.toList());
        resultVO.setSubOrderList(subOrderList);

        // spu起售规则
        this.spuSaleQuantityCheck(resultVO, itemCalcVOList);
        if (CollectionUtil.isNotEmpty(resultVO.getSpuErrorList())) {
            log.info("存在不满足spu起购规则的商品。resultVO：{}", JSON.toJSONString(resultVO));
            return resultVO;
        }

        //订单金额校验，不满足校验中断下单流程
        log.info("订单返回结果明细--orderResultVO:{}", JSON.toJSONString(orderResultVO));
        afterCheck(orderResultVO);
        return resultVO;
    }


    private void spuSaleQuantityCheck(OrderNewResultVO resultVO, List<OrderItemCalcDTO> itemCalcDTOList){
        // 获取spu起购信息
        final Map<Long, Integer> spuAmountMap = itemCalcDTOList.stream().collect(Collectors.groupingBy(
                OrderItemCalcDTO::getPdId, // 按 pdId 分组
                Collectors.summingInt(OrderItemCalcDTO::getAmount)// 计算 amount 总和
        ));

        Map<Long, ProductsSaleRuleDto> productsSaleRuleDtoMap = productsSaleRuleFacade.queryProductsSaleRuleMapByPdIds(new ArrayList<>(spuAmountMap.keySet()));
        List<SpuErrorVO> spuErrorList = Lists.newArrayList();
        spuAmountMap.forEach((k, v) -> {
            ProductsSaleRuleDto productsSaleRuleDto = productsSaleRuleDtoMap.get(k);
            // 如果没达到spu的起购配置
            if(productsSaleRuleDto != null && productsSaleRuleDto.getBaseSaleQuantity() > v) {
                SpuErrorVO vo = new SpuErrorVO();
                vo.setPdId(k);
                vo.setBaseSaleQuantity(productsSaleRuleDto.getBaseSaleQuantity());
                spuErrorList.add(vo);
            }
        });

        if(CollectionUtil.isNotEmpty(spuErrorList)) {
            resultVO.setSpuErrorList(spuErrorList);
        }
    }

    @Override
    public PlaceOrderResultVO placeOrder(PlaceOrderVO placeOrderVO) {
        PlaceOrderResultVO placeOrderResultVO = new PlaceOrderResultVO();
        placeOrderVO.setIsTakePrice(PlaceOrderPriceEnum.NOTAKEPRICE.getCode());
        placeOrderVO.setTakePriceFlag(Boolean.FALSE);

        //调用预下单接口、结算订单金额
        OrderNewResultVO resultVO = preOrder(placeOrderVO);
        if (resultVO == null) {
            throw new BizException("下单异常，请重试！");
        }
        // 查询销售主体名称
        placeOrderVO.setSellingEntityName(sellingEntityQueryFacade.querySellingEntityByMId(placeOrderVO.getMId()));
        //优先提示库存不足和未上架
        if (CollectionUtil.isNotEmpty(resultVO.getOrderItemErrorList())) {
            placeOrderResultVO.setOrderItemErrorList(resultVO.getOrderItemErrorList());
            return placeOrderResultVO;
        }
        List<OrderItemErrorVO> orderItemErrorList = checkDeliveryDate(placeOrderVO);
        if (CollectionUtil.isNotEmpty(orderItemErrorList)) {
            placeOrderResultVO.setOrderItemErrorList(orderItemErrorList);
            return placeOrderResultVO;
        }

        //不满足起送金额订单
        if (CollectionUtil.isNotEmpty(resultVO.getSubOrderFailVOS())) {
            placeOrderResultVO.setSubOrderFailVOS(resultVO.getSubOrderFailVOS());
            return placeOrderResultVO;
        }

        // 不满足spu起售规则
        if(CollectionUtil.isNotEmpty(resultVO.getSpuErrorList())) {
            placeOrderResultVO.setSpuErrorList(resultVO.getSpuErrorList());
            log.info("不满足spu起售规则。spuErrorList：{}", JSON.toJSONString(resultVO.getSpuErrorList()));
            return placeOrderResultVO;
        }

        //核心品低于低价
        if (CollectionUtil.isNotEmpty(resultVO.getFloorPriceFailVOS())) {
            placeOrderResultVO.setFloorPriceFailVOS(resultVO.getFloorPriceFailVOS());
            log.info("存在均摊后价格低于低价的订单项。floorPriceFailVOS：{}", JSON.toJSONString(resultVO.getFloorPriceFailVOS()));
            return placeOrderResultVO;
        }

        placeOrderVO.setFirstFlag(true);
        //订单数据入库
        String orderNo = ((OrderNewService) AopContext.currentProxy()).saveOrderData(placeOrderVO,
                resultVO);

        List<String> subOrderNos = resultVO.getSubOrderList().stream().map(x -> x.getOrderNo())
                .collect(Collectors.toList());
        placeOrderResultVO.setSubOrderNos(subOrderNos);
        //返回数据处理
        placeOrderResultVO.setMasterOrderNo(orderNo);
        placeOrderResultVO.setMName(placeOrderVO.getMname());
        //是否全部商品为代仓或预付，前端需要处理展示逻辑
        boolean skuActing = resultVO.getItemList().stream().allMatch(el ->
                Objects.equals(SkuTypeEnum.AGENT.ordinal(), el.getSkuType()) || (
                        el.getIsPrePay() != null && el.getIsPrePay()));
        placeOrderResultVO.setSkuActing(skuActing);
        if (Objects.equals(1, placeOrderVO.getHelpOrder()) && ObjectUtil.equal(
                PayTypeEnum.HELP_CASH.getType(), placeOrderVO.getPayType())) {
            Merchant merchant = merchantMapper.selectByMid(placeOrderVO.getMId());
            placeOrderResultVO.setRechargeAmount(merchant.getRechargeAmount());
            placeOrderResultVO.setActualAmount(resultVO.getActualTotalPrice());
        }
        return placeOrderResultVO;
    }

    /**
     * 校验下单配送时间
     *
     * @param placeOrderVO
     * @return
     */
    private List<OrderItemErrorVO> checkDeliveryDate(PlaceOrderVO placeOrderVO) {
        List<OrderItemErrorVO> orderItemErrorList = Lists.newArrayList();
        //对提交的商品预计送达时间校验
        Map<LocalDate, OrderResultVO> placeOrderMap = placeOrderVO.getSubPlaceOrderMap();
        if (CollectionUtil.isNotEmpty(placeOrderMap)) {
            for (Entry<LocalDate, OrderResultVO> entry : placeOrderMap.entrySet()) {
                Map<String, OrderItemCalcDTO> itemCalcDTOMap = entry.getValue().getItemList()
                        .stream()
                        .collect(Collectors.toMap(OrderItemCalcDTO::getSku, Function.identity(),
                                (a, b) -> b));
                for (Entry<String, OrderItemCalcDTO> itemEntry : itemCalcDTOMap.entrySet()) {
                    String sku = itemEntry.getKey();
                    OrderItemCalcDTO itemCalcDTO = itemEntry.getValue();
                    //判断预计送达时间发生变化
                    for (Entry<LocalDate, OrderResultVO> voEntry : placeOrderVO.getSubOrderResultMap()
                            .entrySet()) {
                        Optional<OrderItemCalcDTO> find = voEntry.getValue().getItemList().stream()
                                .filter(x -> Objects.equals(x.getSku(), sku)).findFirst();
                        if (find.isPresent() && !voEntry.getKey().isEqual(entry.getKey())) {
                            OrderItemCalcDTO calcDTO = find.get();
                            OrderItemErrorVO errorVO = new OrderItemErrorVO();
                            errorVO.setSku(sku);
                            errorVO.setPdName(calcDTO.getPdName());
                            errorVO.setWeight(calcDTO.getWeight());
                            errorVO.setPicturePath(calcDTO.getPicturePath());
                            errorVO.setReason(OrderItemErrorEnum.DELIVERY_DATE_CHANGE.getCode());
                            errorVO.setPredictDeliveryDate(voEntry.getKey());
                            orderItemErrorList.add(errorVO);
                        }
                    }
                }
            }
        }
        return orderItemErrorList;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String saveOrderData(PlaceOrderVO placeOrderVO, OrderNewResultVO resultVO) {
        //插入订单(包含主订单和子订单)
        String masterOrderNo = insertOrder(placeOrderVO, resultVO);
        Map<String, DiscountCardUseRecord> cardUseRecordMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(resultVO.getDiscountCardUseRecords())) {
            cardUseRecordMap = resultVO.getDiscountCardUseRecords()
                    .stream().collect(Collectors.toMap(DiscountCardUseRecord::getSku, Function.identity()));
        }
        for (OrderResultVO orderResultVO : resultVO.getSubOrderList()) {
            //子订单号
            String subOrderNo = orderResultVO.getOrderNo();
            //插入订单项
            insertOrderItem(subOrderNo, orderResultVO, placeOrderVO);
            //插入配送时间
            DeliveryPlan deliveryPlan = insertDeliveryPlan(subOrderNo, placeOrderVO, orderResultVO);
            deliverPlanRemarkSnapshotService.addDeliveryPlan(placeOrderVO.getContactId(),
                    deliveryPlan);
            //备份运费数据
            deliveryService.backupDelivery(placeOrderVO.getArea(), subOrderNo,
                    placeOrderVO.getAdminId(), orderResultVO.getUsedDeliveryCouponId(),
                    orderResultVO.getDeliveryFee(), orderResultVO.getHitRuleDetailSnapshot());
            //更新预付商品使用次数
            if (placeOrderVO.getAdminId() != null && CollectionUtil.isNotEmpty(
                    orderResultVO.getPrepayUseMap())) {
                for (Map.Entry<String, Integer> entry : orderResultVO.getPrepayUseMap()
                        .entrySet()) {
                    prepayInventoryService.decrease(placeOrderVO.getAdminId(), entry.getKey(),
                            subOrderNo, entry.getValue());
                }
            }
            //
            List<DiscountCardUseRecord> discountCardUseRecords = Lists.newArrayList();
            for (OrderItemCalcDTO itemCalcDTO : orderResultVO.getItemList()) {
                DiscountCardUseRecord cardUseRecord = cardUseRecordMap.get(itemCalcDTO.getSku());
                if (cardUseRecord != null) {
                    discountCardUseRecords.add(cardUseRecord);
                }
            }
            orderResultVO.setDiscountCardUseRecords(discountCardUseRecords);
            //更新优惠卡次数
            discountCardService.useDiscountCard(subOrderNo,
                    orderResultVO.getDiscountCardUseRecords());
            //插入满减&满返记录，只记录主单信息，且记录一次
            if (!CollectionUtil.isEmpty(resultVO.getMarketRuleHistoryList()) && placeOrderVO.getFirstFlag()) {
                resultVO.getMarketRuleHistoryList().forEach(el -> {
                    el.setOrderNo(masterOrderNo);
                    marketRuleHistoryMapper.insert(el);
                });
                placeOrderVO.setFirstFlag(false);
            }

            //插入优惠记录
            insertOrderPreferential(subOrderNo, orderResultVO.getPreferentialList());
            //更新库存
            updateStoreQuantity(subOrderNo, placeOrderVO, orderResultVO);

            // pop订单自动代下单分支流程
            popHelpOrderService.savePopOrderDataPostProcessor(subOrderNo, orderResultVO, placeOrderVO);

            //打印订单配送类型日志
            if (!CollectionUtils.isEmpty(placeOrderVO.getStoreMap())){
                AreaStoreQueryRes areaStoreQueryRes = new AreaStoreQueryRes();
                List<OrderItemCalcDTO> itemList = orderResultVO.getItemList();
                for (OrderItemCalcDTO orderItemCalcDTO : itemList){
                    areaStoreQueryRes = placeOrderVO.getStoreMap().get(orderItemCalcDTO.getSku());
                    if (!ObjectUtils.isEmpty(areaStoreQueryRes)){
                        break;
                    }
                }
                log.info("*****订单配送类型（0日配，1非日配）***** 订单号：{},配送类型：{}",subOrderNo, areaStoreQueryRes.getIsEveryDayFlag());
            }

        }

        //更新优惠券使用（用主单orderNo）
        merchantCouponService.updateMerchantCoupon(masterOrderNo, resultVO.getUsedMerchantCouponId());

        // 已收款消费鲜沐卡-只有代下单需要
        if (Objects.equals(1, placeOrderVO.getHelpOrder()) && ObjectUtil.equal(
                PayTypeEnum.HELP_CASH.getType(), placeOrderVO.getPayType())) {
            // 消费
            String transactionNumber = rechargeRecordService.insert(placeOrderVO.getMId(), null,
                    RechargeRecordType.CONSUMPTION.getId(), placeOrderVO.getOrderNo(),
                    resultVO.getActualTotalPrice().negate());
            //扣减余额
            Payment payment = new Payment();
            payment.setPayType(Global.XIANMU_CARD);
            payment.setOrderNo(masterOrderNo);
            payment.setMoney(resultVO.getActualTotalPrice());
            payment.setEndTime(new Date());
            payment.setStatus(1);
            payment.setCompanyAccountId(null);
            payment.setOnlinePayEndTime(new Date());
            payment.setTransactionNumber(transactionNumber);
            paymentMapper.insertSelective(payment);
        }

        //从购物车下单删除购物车下单商品、搭配购拆分，立即下单不处理
        if (CollectionUtil.isEmpty(placeOrderVO.getOrderNow())) {
//            trolleyService.handleAfterOrder(placeOrderVO.getMId(), placeOrderVO.getAccountId());
            List<ShoppingCartDTO> cartList = resultVO.getItemList().stream().map(x -> {
                ShoppingCartDTO cartDTO = new ShoppingCartDTO();
                cartDTO.setSku(x.getSku());
                cartDTO.setProductType(x.getProductType());
                return cartDTO;
            }).collect(Collectors.toList());
            shoppingCartService.batchDelete(cartList);
        }

        //添加延迟队列、代下单订单推送、销量计算
        if (placeOrderVO.getHelpOrder() == 0) {
            //添加事件到延迟队列
            OrderCancelItem orderCancelItem = new OrderCancelItem("cancelorder" + masterOrderNo,
                    LocalDateTime.now(),
                    30 * 60 * 1000L, placeOrderVO.getMId(), placeOrderVO.getOpenId(),
                    placeOrderVO.getArea().getAreaNo(), masterOrderNo);
            DelayData delayData = new DelayData();
            delayData.setType(MType.MASTER_ORDER_TIMEOUT_CLOSE.name());
            delayData.setData(JSONObject.toJSONString(orderCancelItem));
            mqProducer.sendDelay(MQTopicConstant.MALL_DELAY_LIST, null,
                    JSONObject.toJSONString(delayData), 30 * 60 * 1000L);
        } else {
            //代下单订单推送、销量计算
            orderService.handleFruitSales(masterOrderNo);
        }

        return masterOrderNo;
    }

    @Override
    public CommonResult<OrderDetailVO> getOrderDetail(String orderNo) {
        if (StringUtils.isBlank(orderNo)){
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "订单编号异常");
        }

        Orders record = ordersMapper.selectOne(orderNo);
        if (Objects.isNull(record)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "暂未查到该订单，请稍后重试");
        }

        OrderDetailVO orderDetailVO = new OrderDetailVO();
        if (Objects.equals(record.getType(), OrderTypeEnum.NORMAL.getId()) || Objects.equals(
                record.getType(), OrderTypeEnum.PANIC_BUY.getId()) || Objects.equals(
                record.getType(), OrderTypeEnum.POP.getId())) {
            orderDetailVO.setOrderVO(orderService.normalOrderDetail(orderNo));
        } else if (Objects.equals(record.getType(), OrderTypeEnum.TIMING.getId())) {
            orderDetailVO.setTimingOrderVO(orderServiceHelper.buildTimingOrderDetail(orderNo));
        } else {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "订单类型有误");
        }

        return CommonResult.ok(orderDetailVO);
    }

    @Override
    public CommonResult<PageInfo<OrderVO>> queryOrderPage(OrderPageInput input) {
        //查询出对应状态的订单
        PageHelper.startPage(input.getPageIndex(), input.getPageSize())
                .setOrderBy("o.order_id DESC ");
        List<OrderVO> orderVOs = ordersMapper.selectOrderList(RequestHolder.getMId(),
                input.getOrderStatus() != null ? input.getOrderStatus().shortValue() : null);

        //批量查询订单主单号
        List<String> orderNoList = orderVOs.stream().map(Orders::getOrderNo)
                .collect(Collectors.toList());
        Map<String, OrderRelation> orderNoMap = orderRelationService.queryMasterOrderNoByOrderNo(
                orderNoList);
        // 查询订单的履约方式
        Map<String, Integer> orderFulfillmentTypeMap = ofcLogisticsQueryFacade.queryNormalOrderFulfillmentType(orderNoList);
        //查询对应订单的商品条目
        for (OrderVO orderVO : orderVOs) {
            List<OrderItemVO> orderItems = orderItemMapper.selectOrderItemVO(orderVO.getOrderNo());

            //计算商品总额
            orderItems.forEach(el -> {
                BigDecimal originalTotalPrice = el.getOriginalPrice().multiply(BigDecimal.valueOf(el.getAmount()));
                el.setOriginalTotalPrice(originalTotalPrice);
            });

            //获取订单快照信息
            getOrderItemInfo(orderItems);

            //设置订单配送状态。
            List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectDeliveryPlanOnlyByOrderNo(
                    orderVO.getOrderNo());
            if (!CollectionUtils.isEmpty(deliveryPlanVOS)) {
                orderServiceHelper.handleDeliveryPlans(orderVO, deliveryPlanVOS);
            } else {
                orderVO.setDeliveryStatus(DeliveryStatusEnum.NOT_YET.getStatus());
            }

            orderVO.setOrderItems(orderItems);
            orderVO.setmName(RequestHolder.getName());

            //补充主单编号
            if (orderNoMap.containsKey(orderVO.getOrderNo()) && orderNoMap.get(orderVO.getOrderNo()) != null) {
                orderVO.setMasterOrderNo(orderNoMap.get(orderVO.getOrderNo()).getMasterOrderNo());
            }

            //省心送订单补充退款时间
            if (Objects.equals(orderVO.getType(), OrderTypeEnum.TIMING.getId())) {
                TimingOrderRefundTime timingOrderRefundTime =  timingOrderRefundTimeMapper.selectByOrderNo(orderVO.getOrderNo());
                if (!ObjectUtils.isEmpty(timingOrderRefundTime)){
                    orderVO.setRefundTime(timingOrderRefundTime.getRefundTime());
                }
            }

            // 设置订单履约方式
            if (!OrderTypeEnum.TIMING.getId().equals(orderVO.getType())) {
                Integer orderFulfillmentType = orderFulfillmentTypeMap.get(orderVO.getOrderNo());
                orderVO.setOrderFulfillmentType(orderFulfillmentType);
                if (FulfillmentWayEnum.EXPRESS.getValue().equals(orderFulfillmentType) && null != orderVO.getDeliveryTime()){
                    orderVO.setShipmentDate(orderVO.getDeliveryTime().minusDays(1L));
                }
            }
        }
        return CommonResult.ok(PageInfoHelper.createPageInfo(orderVOs));
    }

    @Override
    public CommonResult<List<OrderSimpleVO>> queryOrderByMasterOrderNo(String masterOrderNo) {
        List<Orders> ordersList = orderRelationService.selectOrdersByMasterOrderNo(masterOrderNo);
        if (CollectionUtils.isEmpty(ordersList)) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "订单信息不存在");
        }

        List<OrderSimpleVO> orderSimpleList = new ArrayList<>();
        for (Orders orders : ordersList) {
            OrderSimpleVO orderSimpleVO = new OrderSimpleVO();
            orderSimpleVO.setOrderNo(orders.getOrderNo());
            orderSimpleVO.setTotalActualPrice(orders.getTotalPrice());

            //配送时间
            List<DeliveryPlanVO> voList = deliveryPlanService.selectByOrderNo(orders.getOrderNo());
            if (!CollectionUtils.isEmpty(voList)) {
                orderSimpleVO.setDeliveryTime(voList.get(0).getDeliveryTime());
            }

            //订单明细
            List<OrderItem> orderItems = orderItemMapper.selectOrderItem(orders.getOrderNo());
            int count = 0;
            List<OrderItemSimpleVO> simpleVOList = new ArrayList<>();
            for (OrderItem orderItem : orderItems) {
                OrderItemSimpleVO itemSimpleVO = new OrderItemSimpleVO();
                itemSimpleVO.setPicturePath(orderItem.getPicturePath());
                simpleVOList.add(itemSimpleVO);

                count += orderItem.getAmount();
            }
            orderSimpleVO.setTotalQuantity(count);
            orderSimpleVO.setOrderItemList(simpleVOList);

            orderSimpleList.add(orderSimpleVO);
        }

        return CommonResult.ok(orderSimpleList);
    }

    @Override
    public List<MerchantCouponVO> queryFullReturn(String masterOrderNo) {
        if (StringUtils.isEmpty(masterOrderNo)) {
            log.warn("OrderNewService[]queryFullReturn[]masterOrderNo is null!");
            return Collections.emptyList();
        }

        //1、查询满返记录 2、查询是否支付完才返券 3、根据卡券ID查询用户卡券信息并组装信息返回
        List<MarketRuleHistory> marketRuleHistoryList = marketRuleHistoryMapper.select(masterOrderNo,
                MarketRuleHistoryTypeEnum.FULL_RETURN.getCode());
        if (CollectionUtil.isEmpty(marketRuleHistoryList)) {
            log.info("OrderNewService[]queryFullReturn[]marketRuleHistoryList is null masterOrderNo:{}", masterOrderNo);
            return Collections.emptyList();
        }

        Set<Long> couponIds = new HashSet<>(marketRuleHistoryList.size());
        Set<Integer> relatedIds = new HashSet<>(marketRuleHistoryList.size());
        for (MarketRuleHistory marketRuleHistory : marketRuleHistoryList) {
            String detail = marketRuleHistory.getDetail();
            if (StringUtils.isBlank(detail)) {
                log.warn("OrderNewService[]queryFullReturn[]detail is null marketRuleHistory:{}", JSON.toJSONString(marketRuleHistory));
                continue;
            }

            //校验状态
            if (!Objects.equals(marketRuleHistory.getSendStatus(), MarketRuleHistorySendStatusEnum.ISSUED.getCode())) {
                continue;
            }

            MarketRule marketRule = JSON.parseObject(detail, MarketRule.class);
            if (Objects.equals(marketRule.getCouponRule(), MarketRuleCouponRuleEnum.PAYMENT_COMPLETION.getCode())) {
                couponIds.add(marketRuleHistory.getValue().longValue());
                relatedIds.add(marketRuleHistory.getId());
            }
        }

        if (CollectionUtils.isEmpty(couponIds)) {
            return Collections.emptyList();
        }
        log.info("OrderNewService[]queryFullReturn[]couponId:{}", JSON.toJSONString(couponIds));

        //查询用户卡包的满返券
        List<MerchantCouponVO> merchantCouponVOS = merchantCouponMapper.getMerchantCoupon(RequestHolder.getMId(), couponIds, CouponReceiveTypeEnum.FULL_RETURN.getCode(), relatedIds);
        if (CollectionUtils.isEmpty(merchantCouponVOS)) {
            return Collections.emptyList();
        }

        //查询卡券范围
        merchantCouponService.getSkuScope(merchantCouponVOS);
        return merchantCouponVOS;
    }

    @Deprecated
    @Override
    public void getOrderItemInfo(List<OrderItemVO> orderItemVOS) {
        orderItemVOS.forEach(el -> {
            //组装订单快照信息
            OrderItemInfoDTO orderItemInfoDTO;
            if (StringUtils.isNotBlank(el.getInfo()) && el.getInfo().contains("{") && el.getInfo().contains("}")){
                try {
                    orderItemInfoDTO = JSON.parseObject(el.getInfo(), OrderItemInfoDTO.class);
                    el.setOrderItemInfoDTO(orderItemInfoDTO);
                } catch (Exception e) {
                    log.warn("解析商品快照信息失败，orderItemVO:{}", JSON.toJSONString(el));
                    //兼容老逻辑 之前逻辑info只存储了有效期快照
                    orderItemInfoDTO = new OrderItemInfoDTO();
                    orderItemInfoDTO.setValidity(el.getInfo());
                    el.setOrderItemInfoDTO(orderItemInfoDTO);
                }
            } else if (StringUtils.isNotBlank(el.getInfo())) {
                //兼容老逻辑 之前逻辑info只存储了有效期快照
                orderItemInfoDTO = new OrderItemInfoDTO();
                orderItemInfoDTO.setValidity(el.getInfo());
                el.setOrderItemInfoDTO(orderItemInfoDTO);
            }
        });
    }

    /**
     * 下单数据校验
     *
     * @param placeOrderVO 下单数据
     */
    private void orderFrontCheck(PlaceOrderVO placeOrderVO) {
        //地址信息校验
        Contact contact = contactMapper.selectByPrimaryKey(placeOrderVO.getContactId());
        if (contact == null) {
            BizException exception = new BizException("请选择配送地址");
            log.error("预下单接口下单数据校验失败,找不到contact, mName:{}, contactId:{} \n", placeOrderVO.getMname(), placeOrderVO.getContactId(), exception);
            throw exception;
        }
        if (Objects.isNull(contact.getStoreNo())) {
            throw new BizException("当前地址不在配送范围");
        }

        List<LocalDate> deliveryDateList = deliveryRuleQueryFacade.getDeliveryRuleList(
                LocalDateTime.now(), placeOrderVO.getMId(), placeOrderVO.getContactId(), null, null,
                DistOrderSourceEnum.getSourceEnum(placeOrderVO.getBusinessLine()));
        if (CollectionUtils.isEmpty(deliveryDateList)) {
            throw new BizException(ResultConstant.ADDRESS_NOT_OPNE);
        }

        //超时加单校验
        if (placeOrderVO.getOutTimes() == 1 && RequestHolder.getGrayscale() == 0) {
            throw new BizException("超时加单权益暂不可用");
        }

        //优惠券基础校验
        Set<Integer> agioTypeSet = new HashSet<>(4);
        if (!CollectionUtil.isEmpty(placeOrderVO.getMerchantCouponId())) {
            for (Integer mcId : placeOrderVO.getMerchantCouponId()) {
                MerchantCouponVO merchantCouponVO = merchantCouponMapper.selectMerchantCouponVO(
                        mcId);
                if (merchantCouponVO == null || merchantCouponVO.getUsed() == 1) {
                    throw new BizException(ResultConstant.COUPON_INVALID);
                }
                if (merchantCouponVO.getStartTime().isAfter(LocalDateTime.now())
                        || merchantCouponVO.getVaildDate().isBefore(LocalDateTime.now())) {
                    throw new BizException("优惠券未在有效的时间区间内");
                }
                if (agioTypeSet.contains(merchantCouponVO.getAgioType())) {
                    throw new BizException("同一类型优惠券不可叠加使用");
                }
                agioTypeSet.add(merchantCouponVO.getAgioType());
            }
        }
    }

    /**
     * 库存校验
     *
     * @param itemCalcDTOList 订单项计算数据
     * @param placeOrderVO    下单信息
     * @return 校验结果
     */
    private List<OrderItemErrorVO> orderCheck(List<OrderItemCalcDTO> itemCalcDTOList,
            PlaceOrderVO placeOrderVO) {
        if (CollectionUtil.isEmpty(itemCalcDTOList)) {
            throw new BizException("商品都失效了，请重新加购");
        }
        //如果下单商品中包含换购商品，必须包含有普通商品
        orderCalcServiceHelper.checkExchangeBuyCondition(itemCalcDTOList, placeOrderVO.getBizId());
        Map<String, Integer> quantityMap = new HashMap<>(4);
        Area area = placeOrderVO.getArea();

        //批量获取库存信息
        List<String> skus = itemCalcDTOList.stream().map(x -> x.getSku()).distinct()
                .collect(Collectors.toList());
        AreaStoreQueryReq queryReq = new AreaStoreQueryReq();
        queryReq.setContactId(placeOrderVO.getContactId());
        queryReq.setSkuCodeList(skus);
        queryReq.setMId(placeOrderVO.getMId());
        queryReq.setSource(DistOrderSourceEnum.getDistOrderSource(placeOrderVO.getBusinessLine()));
        queryReq.setAddOrderFlag(Objects.equals(placeOrderVO.getOutTimes(), 1) ? true : false);
        Map<String, AreaStoreQueryRes> storeMap = Maps.newHashMap();
        try {
            storeMap = wmsAreaStoreFacade.getInfo(queryReq);
        } catch (Exception e) {
            log.warn("获取库存信息异常,cause:{}", Throwables.getStackTraceAsString(e));
        }
        placeOrderVO.setStoreMap(storeMap);

        List<OrderItemErrorVO> errorSkuList = Lists.newArrayList();
        Map<String, AreaSku> areaSkuMap = placeOrderVO.getAreaSkuMap();
        for (OrderItemCalcDTO dto : itemCalcDTOList) {
            String sku = dto.getSku();
            OrderItemErrorVO errorVO = new OrderItemErrorVO();
            errorVO.setSku(sku);
            errorVO.setProductType(dto.getProductType());
            errorVO.setPdName(dto.getPdName());
            errorVO.setWeight(dto.getWeight());
            errorVO.setPicturePath(dto.getPicturePath());

            //上下架判断
            AreaSku areaSku = areaSkuMap.get(sku);
            if (areaSku == null || Objects.equals(areaSku.getOnSale(), false)) {
                errorVO.setQuantity(0);
                errorVO.setReason(OrderItemErrorEnum.NOT_ON_SALE.getCode());
                errorSkuList.add(errorVO);
                continue;
            }

            //库存判断
            AreaStoreQueryRes skuStore = storeMap.get(sku);
            if (skuStore == null) {
                errorVO.setQuantity(0);
                errorVO.setReason(OrderItemErrorEnum.LOW_STOCK.getCode());
                errorSkuList.add(errorVO);
                continue;
            }

            int onlineQuantity = skuStore.getOnlineQuantity();
            if (dto.getAmount() > onlineQuantity) {
                errorVO.setQuantity(onlineQuantity);
                errorVO.setReason(OrderItemErrorEnum.LOW_STOCK.getCode());
                errorSkuList.add(errorVO);
                continue;
            }

            // 购物车数量调整
            ShoppingCartDTO cartDTO = new ShoppingCartDTO();
            cartDTO.setSku(sku);
            cartDTO.setProductType(dto.getProductType());
            //校验起售量，剩余库存不足起售量时不校验 最小起售量 = 最小起售数量 * 售卖规格
            int minSale = dto.getBaseSaleQuantity() * dto.getBaseSaleUnit();
            if (minSale > dto.getAmount()
                    && onlineQuantity > minSale) {
                //代下单单独处理报错信息
                if (Objects.nonNull(placeOrderVO.getHelpOrder()) && Objects.equals(placeOrderVO.getHelpOrder(), CommonStatus.YES.getCode())) {
                    throw new BizException(dto.getPdName() + "小于起售量：" + minSale + "，请重新调整后再下单！");
                }

                //调整数量
                cartDTO.setQuantity(minSale);
                shoppingCartService.updateQuantity(cartDTO);
                throw new BizException(dto.getPdName() + "小于起售量，已给您进行调整");
            } else if (minSale > dto.getAmount()
                    && onlineQuantity < dto.getAmount()) {
                if (Objects.nonNull(placeOrderVO.getHelpOrder()) && Objects.equals(placeOrderVO.getHelpOrder(), CommonStatus.YES.getCode())) {
                    throw new BizException(dto.getPdName() + "大于库存数量：" + onlineQuantity + "，请重新调整后再下单！");
                }

                //调整数量
                cartDTO.setQuantity(onlineQuantity);
                shoppingCartService.updateQuantity(cartDTO);
                throw new BizException(dto.getPdName() + "小于起售量，已给您进行调整");
            }

            //校验起售规格，剩余库存不足时不校验
            if (dto.getBaseSaleUnit() > 1) {
                if (dto.getAmount() % dto.getBaseSaleUnit() != 0
                        && onlineQuantity > dto.getBaseSaleUnit()) {
                    throw new BizException(dto.getPdName() + "不为整数倍，请调整数量");
                }
            }

            //大客户预付校验
            if (placeOrderVO.getMajorMerchant() && dto.getPrepayUsableQuantity() != null) {
                if (dto.getPrepayUsableQuantity() < dto.getAmount()) {
                    if (Objects.nonNull(placeOrderVO.getHelpOrder()) && Objects.equals(placeOrderVO.getHelpOrder(), CommonStatus.YES.getCode())) {
                        throw new BizException(dto.getPdName() + "大于大客户预付数量：" + dto.getPrepayUsableQuantity() + "，请重新调整后再下单！");
                    }

                    //调整预付数量
                    cartDTO.setQuantity(dto.getPrepayUsableQuantity());
                    shoppingCartService.updateQuantity(cartDTO);
                    throw new BizException(dto.getPdName() + "超过预付数量，已给您进行调整");
                }
            }

            //校验限购
            if (!Objects.equals(SalesMode.DAY_LIMITED.getId(), areaSku.getSalesMode())) {
                continue;
            }

            //累计下单量
            LocalTime closeTime = skuStore.getCloseTime();
            LocalDateTime startTime = Global.getStartTime(closeTime);
            LocalDateTime endTime = startTime.plusDays(1);
            List<LimitedSaleVO> voList = orderItemMapper.selectDayLimiteds(placeOrderVO.getMId(),
                    DateUtils.localDateTime2Date(startTime), DateUtils.localDateTime2Date(endTime),
                    area.getAreaNo());
            for (LimitedSaleVO vo : voList) {
                if (Objects.equals(vo.getSku(), sku)) {
                    int usableQuantity = areaSku.getLimitedQuantity() - vo.getQuantity();
                    if (usableQuantity < dto.getAmount()) {
                        if (Objects.nonNull(placeOrderVO.getHelpOrder()) && Objects.equals(placeOrderVO.getHelpOrder(), CommonStatus.YES.getCode())) {
                            throw new BizException(dto.getPdName() + "超过限购，还可以购买数量为：" + usableQuantity + "，请重新调整后再下单！");
                        }

                        //调整限购数量
                        cartDTO.setQuantity(usableQuantity);
                        shoppingCartService.updateQuantity(cartDTO);
                        throw new BizException(dto.getPdName() + "超过限购，已给您进行调整");
                    }
                    break;
                }
            }

            //单次限购
            if (dto.getAmount() > areaSku.getLimitedQuantity()) {
                if (Objects.nonNull(placeOrderVO.getHelpOrder()) && Objects.equals(placeOrderVO.getHelpOrder(), CommonStatus.YES.getCode())) {
                    throw new BizException(dto.getPdName() + "超过限购，单次限购为：" + areaSku.getLimitedQuantity() + "，请重新调整后再下单！");
                }

                cartDTO.setQuantity(areaSku.getLimitedQuantity());
                shoppingCartService.updateQuantity(cartDTO);
                throw new BizException(dto.getPdName() + "超过限购，已给您进行调整");
            }

            quantityMap.put(sku, quantityMap.getOrDefault(sku, 0) + dto.getAmount());
        }

        List<String> errorSkus = errorSkuList.stream().map(x -> x.getSku())
                .collect(Collectors.toList());
        //校验累加库存
        for (Map.Entry<String, Integer> entry : quantityMap.entrySet()) {
            String sku = entry.getKey();
            if (errorSkus.contains(sku)) {
                continue;
            }
            OrderItemErrorVO errorVO = new OrderItemErrorVO();
            errorVO.setSku(sku);
            //库存判断
            AreaStoreQueryRes skuStore = storeMap.get(sku);
            if (skuStore == null) {
                errorVO.setQuantity(0);
                errorVO.setReason(OrderItemErrorEnum.LOW_STOCK.getCode());
                errorSkuList.add(errorVO);
                continue;
            }

            int onlineQuantity = skuStore.getOnlineQuantity();
            if (entry.getValue() > onlineQuantity) {
                errorVO.setQuantity(onlineQuantity);
                errorVO.setReason(OrderItemErrorEnum.LOW_STOCK.getCode());
                errorSkuList.add(errorVO);
            }
        }
        return errorSkuList;
    }


    /**
     * 构建订单数据
     *
     * @param itemCalcDTOList 订单详情
     * @param placeOrderVO    下单数据
     * @return OrderResultVO
     */
    private OrderResultVO orderResultBuild(List<OrderItemCalcDTO> itemCalcDTOList,
            PlaceOrderVO placeOrderVO) {
        List<AbstractOrderItemHandler> itemHandlerList;
        List<AbstractPlaceOrderHandler> orderHandlerList = Lists.newArrayList();
        List<AbstractPlaceOrderHandler> orderHandlerSecondList;
        if (placeOrderVO.getMajorMerchant()) {
            if (Objects.equals(placeOrderVO.getDirect(), 1)) {
                itemHandlerList = OrderCalcFollowConfig.PERIOD_ITEM_HANDLER;
                orderHandlerSecondList = OrderCalcFollowConfig.PERIOD_ORDER_NEW_HANDLER;
            } else {
                itemHandlerList = OrderCalcFollowConfig.CASH_ITEM_HANDLER;
                orderHandlerList = OrderCalcFollowConfig.CASH_MASTER_ORDER_HANDLER;
                orderHandlerSecondList = OrderCalcFollowConfig.CASH_SUB_ORDER_HANDLER;
            }
        } else {
            itemHandlerList = OrderCalcFollowConfig.NORMAL_ITEM_HANDLER;
            orderHandlerList = OrderCalcFollowConfig.NORMAL_MASTER_ORDER_HANDLER;
            orderHandlerSecondList = OrderCalcFollowConfig.NORMAL_SUB_ORDER_HANDLER;
        }

        //责任链-主订单订单项处理
        OrderResultVO resultVO = new OrderResultVO();
        resultVO.setItemList(itemCalcDTOList);
        resultVO.setOriginTotalPrice(BigDecimal.ZERO);
        resultVO.setItemOriginalPrice(BigDecimal.ZERO);
        for (OrderItemCalcDTO dto : itemCalcDTOList) {
            BigDecimal totalOriginalPrice = dto.getActualTotalOriginalPrice();
            resultVO.setItemOriginalPrice(resultVO.getItemOriginalPrice().add(totalOriginalPrice));
            resultVO.setOriginTotalPrice(resultVO.getOriginTotalPrice().add(totalOriginalPrice));
            for (AbstractOrderItemHandler itemHandler : itemHandlerList) {
                boolean oiJointCalc = itemHandler.handleOrderItem(dto, placeOrderVO, resultVO);
                //当前优惠计算过，不再计算后续优惠
                if (oiJointCalc) {
                    log.info("用户：{}下单订单项金额计算，sku：{}使用了{}，总原价：{}，使用后价格：{}",
                            placeOrderVO.getMId(), dto.getSku(),
                            itemHandler.getClass().getSimpleName(),
                            dto.getActualTotalOriginalPrice(), dto.getActualTotalPrice());
                    break;
                }
            }
        }

        //责任链-主订单订单处理，需要均摊
        PlaceOrderCalcDTO orderCalcDTO = new PlaceOrderCalcDTO();
        orderCalcDTO.setPlaceOrderVO(placeOrderVO);
        orderCalcDTO.setItemCalcDTOList(itemCalcDTOList);
        //订单计算
        StringBuilder sb = new StringBuilder("用户：" + placeOrderVO.getMId() + "下单总订单订单金额计算");
        for (AbstractPlaceOrderHandler orderHandler : orderHandlerList) {
            boolean orderJointCalc = orderHandler.handlePlaceOrder(orderCalcDTO, resultVO);
            sb.append("，").append(orderHandler.getClass().getSimpleName()).append("：")
                    .append(orderJointCalc);
        }

        //计算订单项的总实付（商品实付总金额，已处理各种优惠之后）
        resultVO.setActualTotalPrice(BigDecimal.ZERO);
        itemCalcDTOList.forEach(x -> resultVO.setActualTotalPrice(
                resultVO.getActualTotalPrice().add(x.getActualTotalPrice())));

        //根据履约时间拆单处理(代下单也会被处理，但是由于代下单不支持代销不入仓的商品，理论上不会拆单)
        Map<String, AreaStoreQueryRes> storeMap = placeOrderVO.getStoreMap();
        itemCalcDTOList.stream().forEach(e -> {
            if (CollectionUtils.isEmpty(storeMap) || Objects.isNull(storeMap.get(e.getSku()))) {
                return;
            }
            AreaStoreQueryRes queryRes = storeMap.get(e.getSku());
            e.setIsEveryDayFlag(queryRes.getIsEveryDayFlag());
        });
        Map<LocalDate, List<OrderItemCalcDTO>> deliveryTypeMap = itemCalcDTOList.stream()
                .collect(Collectors.groupingBy(x -> {
                    AreaStoreQueryRes areaSkuStore = storeMap.get(x.getSku());
                    //前面已判断过，这里不可能为Null
                    return areaSkuStore.getDeliveryTime();
                }));
        Map<LocalDate, OrderResultVO> subOrderResultMap = buildSubOrderResult(deliveryTypeMap);
        placeOrderVO.setSubOrderResultMap(subOrderResultMap);
        Map<String, Integer> prepayUseMap = resultVO.getPrepayUseMap();
        //预付信息匹配
        if (CollectionUtil.isNotEmpty(prepayUseMap)) {
            for (Entry<LocalDate, OrderResultVO> entry : subOrderResultMap.entrySet()) {
                OrderResultVO orderResultVO = entry.getValue();
                List<String> skus = orderResultVO.getItemList().stream().map(x -> x.getSku())
                        .distinct().collect(
                                Collectors.toList());
                Map<String, Integer> subPrepayUseMap = prepayUseMap.entrySet().stream()
                        .filter(x -> skus.contains(x.getKey()))
                        .collect(Collectors.toMap(Entry::getKey, Entry::getValue, (a, b) -> b));
                orderResultVO.setPrepayUseMap(subPrepayUseMap);
            }
        }


        for (AbstractPlaceOrderHandler handler : orderHandlerSecondList) {
            boolean orderJointCalc = handler.handlePlaceOrder(orderCalcDTO, resultVO);
            sb.append("，").append(handler.getClass().getSimpleName()).append("：")
                    .append(orderJointCalc);
        }
        log.info(sb.toString());

        return resultVO;
    }

    private Map<LocalDate, OrderResultVO> buildSubOrderResult(
            Map<LocalDate, List<OrderItemCalcDTO>> deliveryTypeMap) {
        Map<LocalDate, OrderResultVO> subOrderResultMap = Maps.newHashMap();
        for (Entry<LocalDate, List<OrderItemCalcDTO>> entry : deliveryTypeMap.entrySet()) {
            LocalDate deliveryDate = entry.getKey();
            OrderResultVO subResultVO = new OrderResultVO();
            subResultVO.setOrderDeliveryType(
                    deliveryDate.isAfter(LocalDate.now().plusDays(1)) ? 1 : 0);
            List<OrderItemCalcDTO> itemList = entry.getValue();
            long count = itemList.stream()
                    .filter(x -> Objects.equals(x.getSubType(), 1)).count();
            //判断是否还有代销不入仓的商品
            if (count > 0) {
                subResultVO.setHasSaleNotInStore(1);
                //全部是代销不入仓商品
                subResultVO.setAddOn(count == itemList.size() ? AddOnEnum.SALE_NOT_IN_STORE.getCode() : AddOnEnum.ALL.getCode());
            } else {
                subResultVO.setHasSaleNotInStore(0);
                subResultVO.setAddOn(AddOnEnum.NO_SALE_NOT_IN_STORE.getCode());
            }
            subResultVO.setItemList(itemList);
            subResultVO.setDeliveryDate(deliveryDate);
            subResultVO.setShipmentDate(deliveryDate.minusDays(1L));
            subResultVO.setOriginTotalPrice(BigDecimal.ZERO);
            subResultVO.setActualTotalPrice(BigDecimal.ZERO);
            subResultVO.setItemOriginalPrice(BigDecimal.ZERO);
            List<OrderItemPreferential> itemPreferentialLists = Lists.newArrayList();
            //计算商品原总价、子订单原总价、子订单实付总价
            itemList.forEach(x -> {
                subResultVO.setIsEveryDayFlag(x.getIsEveryDayFlag());
                subResultVO.setItemOriginalPrice(
                        subResultVO.getItemOriginalPrice().add(x.getActualTotalOriginalPrice()));
                subResultVO.setOriginTotalPrice(
                        subResultVO.getOriginTotalPrice().add(x.getActualTotalOriginalPrice()));
                subResultVO.setActualTotalPrice(
                        subResultVO.getActualTotalPrice().add(x.getActualTotalPrice()));
                //订单优惠记录
                if (CollectionUtil.isNotEmpty(x.getItemPreferentialList())) {
                    itemPreferentialLists.addAll(x.getItemPreferentialList());
                }
            });
            if (CollectionUtil.isNotEmpty(itemPreferentialLists)) {
                Map<String, List<OrderItemPreferential>> preferentialMap = itemPreferentialLists.stream()
                        .collect(Collectors.groupingBy(t -> t.getType() + ":" + t.getRelatedId()));
                preferentialMap.entrySet().forEach(p -> {
                    String[] key = p.getKey().split(":");
                    List<OrderItemPreferential> preferential = p.getValue();
                    if (OrderPreferentialTypeEnum.isDeliveryDiscount(Integer.valueOf(key[0])) || CollectionUtil.isEmpty(preferential)) {
                        return;
                    }
                    BigDecimal totalDiscount = preferential.stream().map(m -> m.getAmount())
                            .reduce(BigDecimal::add).get();
                    OrderPreferential orderPreferential = new OrderPreferential();
                    orderPreferential.setType(Integer.valueOf(key[0]));
                    orderPreferential.setActivityName(preferential.get(0).getActivityName());
                    orderPreferential.setRelatedId(
                            StringUtils.isNotBlank(key[1]) ? Long.valueOf(key[1]) : null);
                    orderPreferential.setAmount(totalDiscount);
                    subResultVO.addOrderPreferential(orderPreferential);
                });
            }
            //提前计算好子订单的各类金额
            subOrderResultMap.put(deliveryDate, subResultVO);
        }
        return subOrderResultMap;
    }

    /**
     * 下单计算item构造
     *
     * @param trolleyList  购物车、立即下单list
     * @param placeOrderVO 下单信息
     * @return 订单计算item
     */
    private List<OrderItemCalcDTO> orderItemBuild(List<Trolley> trolleyList,
            PlaceOrderVO placeOrderVO,boolean selfOrder) {
        List<OrderItemCalcDTO> result = new ArrayList<>();
        if (CollectionUtil.isEmpty(trolleyList)) {
            return result;
        }
        Integer areaNo = placeOrderVO.getArea().getAreaNo();
        Integer largeAreaNo = placeOrderVO.getArea().getLargeAreaNo();
        List<String> skus = trolleyList.stream().filter(x -> x != null).map(x -> x.getSku()).distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(skus)) {
            return result;
        }
        placeOrderVO.setAreaSkuMap(Maps.newHashMap());
        List<AreaSku> areaSkus = areaSkuMapper.selectAreaSkuByAreaNoAndSkus(areaNo, skus);
        if (CollectionUtil.isNotEmpty(areaSkus)) {
            Map<String, AreaSku> areaSkuMap = areaSkus.stream()
                    .collect(Collectors.toMap(AreaSku::getSku, Function.identity()));
            placeOrderVO.setAreaSkuMap(areaSkuMap);
        }
        //不是大客户
        if (!placeOrderVO.getMajorMerchant()) {
            List<String> filterSkus = areaSkus.stream().filter(x -> x.getOnSale())
                    .map(x -> x.getSku())
                    .collect(Collectors.toList());

            //组装获取活动信息
            List<ActivitySkuDTO> activitySkuDTOS = new ArrayList<>(filterSkus.size());
            Map<String, Trolley> trolleyMap = trolleyList.stream().collect(Collectors.toMap(x -> x.getSku(), Function.identity(), (a, b) -> b));
            filterSkus.forEach(sku -> {
                if (trolleyMap.containsKey(sku)) {
                    ActivitySkuDTO activitySkuDTO = new ActivitySkuDTO();
                    activitySkuDTO.setSku(sku);
                    activitySkuDTO.setQuantity(trolleyMap.get(sku).getQuantity());
                    activitySkuDTOS.add(activitySkuDTO);
                }
            });
            Map<String, ActivitySkuDetailDTO> setActivitySkuDetailMap = activityService.listByActivitySku(
                    activitySkuDTOS, areaNo, placeOrderVO.getMId());
            placeOrderVO.setActivitySkuDetailMap(setActivitySkuDetailMap);
        }

        List<String> skuList = trolleyList.stream().map(Trolley::getSku)
                .collect(Collectors.toList());

        Map<String, AreaSku> areaSkuMap = placeOrderVO.getAreaSkuMap();
        Map<String, ActivitySkuDetailDTO> activitySkuDetailMap = placeOrderVO.getActivitySkuDetailMap();
        List<Inventory> inventoryList = inventoryMapper.listBySkus(skuList);
        if (CollectionUtil.isEmpty(inventoryList)) {
            return result;
        }
        Map<String, Inventory> inventoryMap = inventoryList.stream()
                .collect(Collectors.toMap(x -> x.getSku(), Function.identity(), (a, b) -> b));
        //大客户 sku 报价单
        Map<String, Integer> majorPriceShowMap = Collections.emptyMap ();
        Map<String, MajorPrice> majorPriceMap = Collections.emptyMap ();

        log.info ("orderItemBuild.placeOrderVO 参数={} ",JSON.toJSONString (placeOrderVO));
        if (placeOrderVO.getMajorMerchant()) {
            List<MajorPrice> majorPrices = majorPriceMapper.selectSkusMajorPriceList (
                    placeOrderVO.getAdminId (), placeOrderVO.getDirect (),
                    placeOrderVO.getArea ().getAreaNo (), skuList);
            majorPriceShowMap = majorPrices.stream()
                    .collect(Collectors.toMap(
                            MajorPrice::getSku,
                            MajorPrice::getMallShow,
                            (existing, replacement) -> existing // 保留第一个出现的 mallShow
                    ));
            // 创建 majorPriceMap，取重复 SKU 的第一个
            majorPriceMap = majorPrices.stream().collect(Collectors.toMap(MajorPrice::getSku,Function.identity(),(existing, replacement) -> existing));
        }

        //后台类目
        List<Products> productsList = productsMapper.listBySkuList(skuList);
        Map<Long, Products> productsMap = Collections.emptyMap ();

        //查询核心品类保底价信息
        Map<String, BigDecimal> coreProductBasePriceMap = productService.getCoreProductFloorPriceMap(skuList, largeAreaNo);

        //大客户 后台类目 报价单
        Map<Integer, Integer>  majorCatogryMap = Collections.emptyMap ();
        if (CollectionUtil.isNotEmpty (productsList)) {
            List<Integer> categoryIds = productsList.stream().map(Products::getCategoryId).collect(Collectors.toList());
            if (placeOrderVO.getMajorMerchant()) {
                List<MajorCategory> majorCategories = majorCategoryMapper.selectMajorCategoryList (placeOrderVO.getAdminId (), placeOrderVO.getArea ().getAreaNo (), placeOrderVO.getDirect (), categoryIds);
                log.info ("orderItemBuild.majorCategories 参数={} ",JSON.toJSONString (majorCategories));
                majorCatogryMap = majorCategories.stream().collect(Collectors.toMap(MajorCategory::getCategoryId,MajorCategory::getType));
            }
            productsMap = productsList.stream().collect(Collectors.toMap(x -> x.getPdId (), Function.identity(), (a, b) -> b));
        }

        Map<String, Integer> mallShowMap = majorPriceService.alculateMallShowMap (inventoryList, productsMap, majorPriceShowMap, majorCatogryMap);

        //获取控价品信息
        List<MarketPriceControlProducts> marketPriceControlProducts = marketPriceControlProductsMapper.selectBySkuIds(skuList, MarketControlPriceHideEnum.HIDE.getCode());

        Map<String, MarketPriceControlProducts> marketPriceControlProductsMap = null;
        if (CollectionUtil.isNotEmpty (marketPriceControlProducts)) {
            marketPriceControlProductsMap = marketPriceControlProducts.stream()
                    .collect(Collectors.toMap(MarketPriceControlProducts::getSku, Function.identity(), (a, b) -> b));
        }

        // 买手
        boolean isPopMerchant = merchantService.isPopMerchantV2(placeOrderVO.getBusinessLine());
        Map<String, Long> skuToBuyerIdMap = new HashMap<>();
        Map<Long, PopBuyerInfoDTO> buyerMap = new HashMap<>();
        if(isPopMerchant) {
            log.info("pop客户开始组装买手信息。placeOrderVO:{}", JSON.toJSONString(placeOrderVO));
            // 获取到sku-买手id集合
            skuToBuyerIdMap = inventoryList.stream().collect(Collectors.toMap(Inventory::getSku, Inventory::getBuyerId));
            // 根据买手id获取名称集合
            List<Long> buyerIds = inventoryList.stream().map(Inventory::getBuyerId).distinct().collect(Collectors.toList());
            List<PopBuyerInfoDTO> popBuyerInfoDTOS = popBuyerQueryFacade.queryPopBuyerByIdList(buyerIds);
            buyerMap = popBuyerInfoDTOS.stream().collect(Collectors.toMap(PopBuyerInfoDTO::getBuyerId, Function.identity()));
        }

        for (Trolley trolley : trolleyList) {
            Inventory inventory = inventoryMap.get(trolley.getSku());
            if (inventory == null) {
                continue;
            }

            Products products = productsMap.get(inventory.getPdId());
            if (products == null) {
                continue;
            }

            //商品原价处理：大客户=定价+返点；单店=定价；
            BigDecimal originalPrice = null;
            String ladderPrice = null;
            Integer prepayUsableQuantity = null;
            BigDecimal prepaySetPrice = null;
            MajorRebate usableRebate = null;
            if (placeOrderVO.getMajorMerchant()) {
                // 校验大客户报价单是否是 可见
                if (selfOrder && placeOrderVO.getMajorMerchant() && MajorPriceMallShowEnum.HIDE.getCode ().equals (mallShowMap.get (trolley.getSku ()))) {
                    throw new BizException("哎呀，很抱歉，您选购的" + products.getPdName() + "失效了");
                }
                boolean isHelpOrder = Objects.equals(1, placeOrderVO.getHelpOrder());
                usableRebate = majorPriceService.queryUsableRebate(placeOrderVO.getAdminId(),
                        placeOrderVO.getArea().getAreaNo(), trolley.getSku(),
                        products.getCategoryId());
                originalPrice = majorPriceService.queryPriceDealWithRebate(placeOrderVO,
                        products.getCategoryId(), trolley.getSku(), trolley.getQuantity(), usableRebate);

                //预付商品
                prepayUsableQuantity = prepayInventoryService.skuUsableQuantity(
                        placeOrderVO.getAdminId(), placeOrderVO.getDirect(), trolley.getSku());
                if (prepayUsableQuantity != null) {
                    MajorPrice majorPrice = majorPriceMap.get (trolley.getSku());
                    //全量客户无报价单查询商城价
                    if (majorPrice != null) {
                        // 如果是商城价先判断是否开启了低价监控：开启-走特价，不开启-走原价
                        prepaySetPrice = majorPriceService.getMajorMallPrice(placeOrderVO.getArea().getAreaNo(), trolley.getSku(), placeOrderVO.getAdminId(), placeOrderVO.getMId(), isHelpOrder,majorPrice.getPriceAdjustmentValue (),majorPrice.getPriceType(),majorPrice.getPrice ());
                    } else {
                        prepaySetPrice = areaSkuService.selectValidPrice(
                                placeOrderVO.getArea().getAreaNo(), trolley.getSku());
                    }
                }
            } else {
                AreaSku areaSku = areaSkuMap.get(trolley.getSku());
                if (areaSku != null) {
                    ActivitySkuDetailDTO skuDetailDTO = activitySkuDetailMap.get(
                            areaSku.getSku());
                    originalPrice = areaSku.getPrice();
                    if (skuDetailDTO == null) {
                        ladderPrice = areaSku.getLadderPrice();
                    }
                }
            }
            if (originalPrice == null) {
                throw new BizException("哎呀，很抱歉，您选购的" + products.getPdName() + "失效了");
            }

            //价格信息，计算前原价总价、实付总价、免邮门槛总价相同
            OrderItemCalcDTO dto = new OrderItemCalcDTO();
            dto.setOriginalPrice(originalPrice);
            dto.setPrice(originalPrice);
            dto.setLadderPrice(ladderPrice);
            BigDecimal totalPrice = originalPrice.multiply(
                    BigDecimal.valueOf(trolley.getQuantity()));
            dto.setActualTotalPrice(totalPrice);
            dto.setCalcPartDeliveryFee(totalPrice);
            dto.setActualTotalOriginalPrice(totalPrice);
            dto.setPrepayUsableQuantity(prepayUsableQuantity);
            dto.setPrepaySetPrice(prepaySetPrice);

            //返点信息
            if (usableRebate != null) {
                dto.setRebateType(usableRebate.getType());
                dto.setRebateNumber(usableRebate.getNumber());
            }

            //组合包信息
            dto.setSuitId(trolley.getSuitId());

            //商品信息
            dto.setPdName(products.getPdName());
            dto.setSkuName(inventory.getSkuName());
            dto.setWeight(inventory.getWeight());
            dto.setBaseSaleUnit(inventory.getBaseSaleUnit());
            dto.setBaseSaleQuantity(inventory.getBaseSaleQuantity());
            dto.setMaturity(inventory.getMaturity());
            dto.setSkuType(inventory.getType());
            dto.setSubType(inventory.getSubType());
            dto.setCategoryId(products.getCategoryId());
            dto.setPicturePath(
                    Optional.ofNullable(inventory.getSkuPic()).orElse(products.getPicturePath()));
            dto.setStorageLocation(products.getStorageLocation());
            dto.setVolume(inventory.getVolume());
            dto.setWeightNum(inventory.getWeightNum());
            dto.setPdId(inventory.getPdId());
            dto.setInvId(inventory.getInvId());

            // 买手
            Long buyerId = skuToBuyerIdMap.get(trolley.getSku());
            if(buyerId != null) {
                dto.setBuyerId(buyerId);
                PopBuyerInfoDTO popBuyerInfoDTO = buyerMap.get(buyerId);
                if(popBuyerInfoDTO != null) {
                    dto.setBuyerName(popBuyerInfoDTO.getBuyerName());
                }
            }

            //类目信息
            Category category = categoryMapper.selectByPrimaryKey(products.getCategoryId());
            dto.setCategoryType(category.getType());

            //购物车信息
            dto.setSku(trolley.getSku());
            dto.setParentSku(trolley.getParentSku());
            dto.setProductType(trolley.getProductType());
            dto.setAmount(trolley.getQuantity());
            dto.setAddTime(new Date());

            //是否隐藏实付价
            if (marketPriceControlProductsMap != null && marketPriceControlProductsMap.containsKey(inventory.getSku())) {
                dto.setPriceHide(marketPriceControlProductsMap.get(inventory.getSku()).getPriceHide());
            }

            //代仓支付方式
            if (placeOrderVO.getMajorMerchant() && Objects.equals(SkuTypeEnum.AGENT.ordinal(),
                    inventory.getType())) {
                MajorPrice majorPrice = majorPriceMap.get(dto.getSku());
                if (majorPrice != null) {
                    dto.setAgentPayMethod(majorPrice.getPayMethod());
                } //未配置支付方式，默认不支付
                else {
                    dto.setAgentPayMethod(0);
                }
            }
            if (Objects.equals(1, placeOrderVO.getHelpOrder()) && ObjectUtil.equal(
                    PayTypeEnum.HELP_CASH.getType(), placeOrderVO.getPayType())) {
                log.info("已收款代下单打印商品信息和支付类型:{},{}", JSONUtil.toJsonStr(inventory),
                        dto.getAgentPayMethod());
            }

            //核心品低价
            if (!CollectionUtils.isEmpty(coreProductBasePriceMap) && coreProductBasePriceMap.containsKey(trolley.getSku())) {
                dto.setFloorPrice(coreProductBasePriceMap.get(trolley.getSku()));
            }
            result.add(dto);
        }

        return result;
    }

    /**
     * 插入订单数据
     *
     * @param placeOrderVO  下单信息
     * @param orderResultVO 订单处理信息
     * @return orderNo
     */
    private String insertOrder(PlaceOrderVO placeOrderVO, OrderNewResultVO orderResultVO) {
        //生成订单号
        String masterOrderNo;
        Integer orderType;
        short orderStatus;
        PayTypeEnum payType;
        boolean isPopMerchant = merchantService.isPopMerchantV2(placeOrderVO.getBusinessLine());
        if (Objects.equals(1, placeOrderVO.getHelpOrder())) {
            orderStatus = 3;
            masterOrderNo = placeOrderVO.getOrderNo();
            orderType = OrderTypeEnum.HELP.getId();
            payType = PayTypeEnum.getHelpOrderType(placeOrderVO.getPayType());
            if (payType == null) {
                throw new BizException("代下单类型不正确");
            }
            placeOrderVO.setOrderNo(masterOrderNo);
            orderResultVO.setOrderStatus(orderStatus);
            Orders order = new Orders(masterOrderNo, placeOrderVO.getMId(), placeOrderVO.getAccountId(),
                    new Date(), orderType, null);
            order.setDeliveryFee(orderResultVO.getOriginalDeliveryFee());
            order.setOutTimesFee(orderResultVO.getOutTimesFee());
            order.setTotalPrice(orderResultVO.getActualTotalPrice());
            order.setOriginPrice(orderResultVO.getOriginTotalPrice());
            order.setOutTimes(orderResultVO.getOutTimesFee() != null ? 1 : 0);
            order.setStatus(orderStatus);
            order.setRemark(placeOrderVO.getRemark());
            order.setAreaNo(placeOrderVO.getArea().getAreaNo());
            order.setDirect(placeOrderVO.getDirect());
            order.setSkuShow(placeOrderVO.getSkuShow());
            order.setmSize(placeOrderVO.getSize());
            order.setAdminId(placeOrderVO.getAdminId());
            order.setOperateId(placeOrderVO.getOperateId());
            order.setOrderPayType(payType.getType());
            order.setSellingEntityName(placeOrderVO.getSellingEntityName());
            ordersMapper.insertSelective(order);
            orderResultVO.getSubOrderList().stream().forEach(subOrder -> {
                subOrder.setOrderStatus((short) OrderStatusEnum.DELIVERING.getId());
                subOrder.setOrderNo(masterOrderNo);
                subOrder.setType(orderType);
            });
        } else {
            orderStatus = 1;
            //主订单号
            masterOrderNo = isPopMerchant ? Global.createOrderNo(Global.POP_MASTER_ORDER_CODE) : Global.createOrderNo(Global.NORMAL_MASTER_ORDER_CODE);
            orderType = isPopMerchant ? OrderTypeEnum.POP.getId() : OrderTypeEnum.NORMAL.getId();
            payType = placeOrderVO.getDirect() == null ? PayTypeEnum.CASH
                    : PayTypeEnum.getHelpOrderType(placeOrderVO.getDirect());
            if (CollectionUtil.isNotEmpty(orderResultVO.getSubOrderList())) {
                orderResultVO.getSubOrderList().sort(Comparator.comparing(OrderResultVO::getDeliveryDate).reversed());
                List<Orders> subOrders = Lists.newArrayList();
                //优先生成配送时间晚的订单
                for (OrderResultVO resultVO : orderResultVO.getSubOrderList()) {
                    //构建子订单入参
                    //生成子订单号
                    String subOrderNo = isPopMerchant ? Global.createOrderNo(Global.POP_ORDER_CODE) : Global.createOrderNo(Global.NORMAL_ORDER_CODE);
                    resultVO.setOrderNo(subOrderNo);
                    resultVO.setOrderStatus(orderStatus);
                    resultVO.setType(orderType);
                    resultVO.setAreaNo(placeOrderVO.getArea().getAreaNo());
                    Orders order = new Orders(subOrderNo, placeOrderVO.getMId(),
                            placeOrderVO.getAccountId(), new Date(), orderType, null);
                    order.setDeliveryFee(resultVO.getOriginalDeliveryFee());
                    order.setOutTimesFee(resultVO.getOutTimesFee());
                    order.setTotalPrice(resultVO.getActualTotalPrice());
                    order.setOriginPrice(resultVO.getOriginTotalPrice());
                    order.setOutTimes(resultVO.getOutTimesFee() != null ? 1 : 0);
                    order.setStatus(orderStatus);
                    order.setRemark(placeOrderVO.getRemark());
                    order.setAreaNo(placeOrderVO.getArea().getAreaNo());
                    order.setDirect(placeOrderVO.getDirect());
                    order.setSkuShow(placeOrderVO.getSkuShow());
                    order.setmSize(placeOrderVO.getSize());
                    order.setAdminId(placeOrderVO.getAdminId());
                    order.setOperateId(placeOrderVO.getOperateId());
                    order.setOrderPayType(Optional.ofNullable(payType).map(PayTypeEnum::getType).orElse(null));
                    order.setSellingEntityName(placeOrderVO.getSellingEntityName());
                    // 先保证逻辑，后面再考虑改成批量写
                    ordersMapper.insertSelective(order);
                    //同时创建主子订单关联关系
                    OrderRelation orderRelation = new OrderRelation();
                    orderRelation.setMasterOrderNo(masterOrderNo);
                    orderRelation.setOrderNo(subOrderNo);
                    orderRelation.setPrecisionDeliveryFee(resultVO.getPrecisionDeliveryFee());
                    orderRelationMapper.insertSelective(orderRelation);
                    subOrders.add(order);
                }
            }
            //主订单数据
            MasterOrder masterOrder = new MasterOrder();
            masterOrder.setMasterOrderNo(masterOrderNo);
            masterOrder.setMId(placeOrderVO.getMId());
            masterOrder.setOrderTime(LocalDateTime.now());
            masterOrder.setType(orderType);
            masterOrder.setStatus(Integer.valueOf(orderStatus));
            masterOrder.setTotalPrice(orderResultVO.getActualTotalPrice());
            masterOrder.setOriginPrice(orderResultVO.getOriginTotalPrice());
            masterOrder.setAreaNo(placeOrderVO.getArea().getAreaNo());
            masterOrder.setMSize(placeOrderVO.getSize());
            masterOrder.setAccountId(placeOrderVO.getAccountId());
            masterOrder.setAdminId(placeOrderVO.getAdminId());
            masterOrderMapper.insertSelective(masterOrder);
        }

        return masterOrderNo;
    }

    /**
     * 插入订单项（合并搭配购、精准送作为订单项记录）
     *
     * @param orderNo       订单编号
     * @param orderResultVO 下单处理结果
     */
    private void insertOrderItem(String orderNo, OrderResultVO orderResultVO, PlaceOrderVO placeOrderVO) {
        List<OrderItem> insertItemList = new ArrayList<>();
        List<OrderItemPreferential> preferentialList = new ArrayList<>();
        List<ActivitySkuPurchaseQuantity> skuPurchaseQuantities = new ArrayList<>();
        List<ExpandActivityQuantity> expandActivityQuantities = new ArrayList<>();

        //订单项处理
        Map<String, List<OrderItemCalcDTO>> collect = orderResultVO.getItemList()
                .stream().collect(Collectors.groupingBy(
                        el -> el.getProductType() + ":" + el.getSuitId() + ":" + el.getSku()));

        //查询商品标签快照信息并保存
        List<Long> invIds = orderResultVO.getItemList()
                .stream().map(OrderItemCalcDTO::getInvId).collect(Collectors.toList());
        List<MarketItemDetailDTO> itemDetailDTOList = marketItemFacade.getItemDetailByOutId(invIds);
        Map<Long, MarketItemDetailDTO> labelMap = null;
        if (!CollectionUtils.isEmpty(itemDetailDTOList)) {
            labelMap = itemDetailDTOList.stream().collect(Collectors.toMap(x -> x.getOutId(), Function.identity(),
                    (a, b) -> a));
        }
        OrderItemInfoDTO orderItemInfoDTO;
        for (List<OrderItemCalcDTO> dtoList : collect.values()) {
            OrderItemCalcDTO item = dtoList.get(0);
            item.setOrderNo(orderNo);
            item.setStatus(Integer.valueOf(orderResultVO.getOrderStatus()));

            //赠品suit设置为-1，避免售后订单关联重复
            if (Objects.equals(TrolleyProductTypeEnum.GIFT.ordinal(), item.getProductType())) {
                item.setSuitId(-1);
            }

            //合并搭配购
            for (int i = 1; i < dtoList.size(); i++) {
                OrderItemCalcDTO dto = dtoList.get(i);
                dto.setNotNeedStock(true);
                item.setAmount(item.getAmount() + dto.getAmount());
                item.setActualTotalPrice(item.getActualTotalPrice().add(dto.getActualTotalPrice()));
            }

            BigDecimal price = item.getActualTotalPrice()
                    .divide(BigDecimal.valueOf(item.getAmount()), RoundingMode.FLOOR);
            item.setPrice(price);

            // 设置商品标签快照以及控价品实付价是否隐藏
            if (!CollectionUtils.isEmpty(labelMap) && labelMap.containsKey(item.getInvId())) {
                MarketItemDetailDTO marketItemDetailDTO = labelMap.get(item.getInvId());
                if(marketItemDetailDTO != null && StringUtils.isNotBlank(marketItemDetailDTO.getItemLabel())){
                    orderItemInfoDTO = new OrderItemInfoDTO();
                    orderItemInfoDTO.setItemLabel(marketItemDetailDTO.getItemLabel());
                    orderItemInfoDTO.setPriceHide(item.getPriceHide());
                    item.setInfo(JSON.toJSONString(orderItemInfoDTO));
                } else if (item.getPriceHide() != null) {
                    orderItemInfoDTO = new OrderItemInfoDTO();
                    orderItemInfoDTO.setPriceHide(item.getPriceHide());
                    item.setInfo(JSON.toJSONString(orderItemInfoDTO));
                }
            } else if (item.getPriceHide() != null) {
                orderItemInfoDTO = new OrderItemInfoDTO();
                orderItemInfoDTO.setPriceHide(item.getPriceHide());
                item.setInfo(JSON.toJSONString(orderItemInfoDTO));
            }
            insertItemList.add(item);
        }
        log.info("开始记录订单明细 orderNo >>> {}, insertItemList >>> {}", orderNo, JSON.toJSONString(insertItemList));
        orderItemMapper.insertBatch(insertItemList);
        //订单项优惠插入处理
        orderResultVO.getItemList().forEach(item -> {
            String itemKey = item.getProductType() + ":" + item.getSuitId() + ":" + item.getSku();
            insertItemList.forEach(insertItem -> {
                String insertItemKey =
                        insertItem.getProductType() + ":" + insertItem.getSuitId() + ":"
                                + insertItem.getSku();
                if (itemKey.equals(insertItemKey)) {
                    //订单项优惠明细记录
                    if (!CollectionUtils.isEmpty(item.getItemPreferentialList())) {
                        item.getItemPreferentialList().forEach(preList -> {
                            preList.setOrderItemId(insertItem.getId());
                            preList.setOrderNo(orderNo);
                            preferentialList.add(preList);
                        });
                    }
                    //活动优惠用户购买数量记录->用于活动库存和限购数量
                    if (!ObjectUtils.isEmpty(item.getActivitySkuPurchaseQuantity())) {
                        item.getActivitySkuPurchaseQuantity().setOrderNo(orderNo);
                        item.getActivitySkuPurchaseQuantity().setOrderItemId(insertItem.getId());
                        skuPurchaseQuantities.add(item.getActivitySkuPurchaseQuantity());
                    }
                    //拓展活动优惠用户购买数量记录->用于限购数量
                    if (!ObjectUtils.isEmpty(item.getExpandActivityQuantity())) {
                        item.getExpandActivityQuantity().setOrderNo(orderNo);
                        item.getExpandActivityQuantity().setOrderItemId(insertItem.getId());
                        expandActivityQuantities.add(item.getExpandActivityQuantity());
                    }
                }
            });
        });

        // pop订单记录快照信息
        if(OrderTypeEnum.POP.getId().equals(orderResultVO.getType())){
            log.info("*****开始记录pop订单快照******" + orderResultVO.getOrderNo());
            this.insertOrderItemExtra(insertItemList, placeOrderVO.getContact().getStoreNo(), placeOrderVO.getArea().getAreaNo());
        }

        log.info("*****订单项优惠插入处理日志******" + JSON.toJSONString(preferentialList));
        if (!CollectionUtils.isEmpty(preferentialList)) {
            orderItemPreferentialMapper.insertBatch(preferentialList);
        }

        log.info("*****活动sku用户购买数量******" + JSON.toJSONString(skuPurchaseQuantities));
        if (!CollectionUtils.isEmpty(skuPurchaseQuantities)) {
            activityService.handleActivityQuantity(skuPurchaseQuantities);
            activitySkuPurchaseQuantityMapper.insertBatch(skuPurchaseQuantities);
        }

        log.info("*****拓展活动sku用户购买数量******" + JSON.toJSONString(expandActivityQuantities));
        if (!CollectionUtils.isEmpty(expandActivityQuantities)) {
            expandActivityQuantityMapper.insertBatch(expandActivityQuantities);
        }

        log.info("准备更新pop订单明细快照 >>> {}", JSON.toJSONString(orderResultVO));
        if (OrderTypeEnum.HELP.getId().equals(orderResultVO.getType())){
            log.info("*****开始更新pop订单明细快照****** 源POP订单号 {}, 代下单订单号 {}", placeOrderVO.getSourcePopSubOrderNo(), orderResultVO.getOrderNo());
            this.updateOrderItemExtra(insertItemList, placeOrderVO.getSourcePopSubOrderNo(), placeOrderVO.getPopSkuMapping());
        }

    }

    /**
     * 更新pop订单明细快照
     * @param helpOrderItemList  代下单明细
     * @param sourcePopSubOrderNo 源pop子订单号
     * @param popSkuMapping pop商品sku对应关系
     */
    private void updateOrderItemExtra(List<OrderItem> helpOrderItemList,
                                      String sourcePopSubOrderNo,
                                      Map<String, String> popSkuMapping) {
        if (StringUtils.isEmpty(sourcePopSubOrderNo)) {
            return;
        }
        if (CollectionUtils.isEmpty(helpOrderItemList)) {
            log.warn("代下单明细为空!");
            return;
        }

        Map<String, Long> helpOrderSkuAndItemIdMap = helpOrderItemList.stream().collect(Collectors.toMap(OrderItem::getSku, OrderItem::getId, (v1, v2) -> v1));
        List<OrderItem> popOrderItems = orderItemMapper.selectByOrderNos(Collections.singleton(sourcePopSubOrderNo));
        if (CollectionUtils.isEmpty(popOrderItems)){
            log.warn("源POP订单 {} 的订单项不存在", sourcePopSubOrderNo);
            return;
        }
        // key: itemId, value: OrderItem
        Map<Long, OrderItem> popOrderItemMap = popOrderItems.stream().collect(Collectors.toMap(OrderItem::getId, Function.identity(), (v1, v2) -> v1));
        List<OrderItemExtra> popOrderItemExtraList = orderItemExtraMapper.selectByOrderItemIds(popOrderItems.stream().map(OrderItem::getId).collect(Collectors.toList()));
        for (OrderItemExtra orderItemExtra : popOrderItemExtraList) {
            OrderItem popOrderItem = popOrderItemMap.get(orderItemExtra.getOrderItemId());
            if (null == popOrderItem || StringUtils.isEmpty(popOrderItem.getSku())){
                continue;
            }
            String xianmuSku = popSkuMapping.get(popOrderItem.getSku());
            if (StringUtils.isEmpty(xianmuSku)){
                continue;
            }
            Long helpOrderItemId = helpOrderSkuAndItemIdMap.get(xianmuSku);
            if (null == helpOrderItemId){
                continue;
            }
            OrderItemExtra orderItemExtraToUpdate = new OrderItemExtra();
            orderItemExtraToUpdate.setId(orderItemExtra.getId());
            orderItemExtraToUpdate.setOrderItemId(orderItemExtra.getOrderItemId());
            orderItemExtraToUpdate.setHelpOrderItemId(helpOrderItemId);
            orderItemExtraMapper.updateSelectiveByOrderItemId(orderItemExtraToUpdate);
        }
    }

    private void insertOrderItemExtra(List<OrderItem> itemList, Integer storeNo, Integer areaNo){
        if(CollectionUtil.isEmpty(itemList)) {
            log.warn("订单项为空!");
            return;
        }

        List<String> skus = itemList.stream().map(OrderItem::getSku).distinct().collect(Collectors.toList());
        List<Integer> popMarkupPriceAreaNos = dynamicConfig.getPopMarkupPriceAreaNos();
        // 查买手id
        List<Inventory> inventories = inventoryMapper.listBySkus(skus);
        // 获取到sku-买手id集合
        Map<String, Long> skuToBuyerIdMap = inventories.stream().collect(Collectors.toMap(Inventory::getSku, Inventory::getBuyerId));
        // 根据买手id获取名称集合
        List<Long> buyerIds = inventories.stream().map(Inventory::getBuyerId).distinct().collect(Collectors.toList());
        List<PopBuyerInfoDTO> popBuyerInfoDTOS = popBuyerQueryFacade.queryPopBuyerByIdList(buyerIds);
        Map<Long, PopBuyerInfoDTO> buyerMap = popBuyerInfoDTOS.stream().collect(Collectors.toMap(PopBuyerInfoDTO::getBuyerId, Function.identity()));
        // 查佣金-代理商
        List<PopSkuAdditionQueryDto> skuQueryList = itemList.stream()
                .map(a -> new PopSkuAdditionQueryDto(a.getSku(), a.getAmount(), null))
                .collect(Collectors.toList());
        List<PopSkuAdditionInfoDTO> popSkuAdditionInfoDTOS = popSkuAdditionQueryFacade.queryPopSkuAdditionBatch(skuQueryList, storeNo);
        // pop商城没有阶梯，按sku返回匹配到的第一个就可以
        Map<String, PopSkuAdditionInfoDTO> skuToAdditionInfoDtoMap = popSkuAdditionInfoDTOS.stream()
                .collect(Collectors.groupingBy(PopSkuAdditionInfoDTO::getSku,
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> list.isEmpty() ? null : list.get(0))));

        Map<String, Inventory> skuInfoMap = inventories.stream().collect(Collectors.toMap(Inventory::getSku, item -> item));
        Map<String, AreaSkuPriceMarkupConfig> priceMarkupConfigMap = new HashMap<>();


        if(popMarkupPriceAreaNos.contains(areaNo)) {
            List<AreaSkuPriceMarkupConfig> areaSkuPriceMarkupConfigs = areaSkuPriceMarkupConfigMapper.selectBySkusAndAreaNo(skus, areaNo);
            priceMarkupConfigMap = areaSkuPriceMarkupConfigs.stream().collect(Collectors.toMap(AreaSkuPriceMarkupConfig::getSku, Function.identity()));
        }
        log.info("查询加价配置结果。priceMarkupConfigMap:{}, skus:{}, areaNo:{}", JSON.toJSONString(priceMarkupConfigMap), skus, areaNo);

        // 构建拓展项数据
        List<OrderItemExtra> orderItemExtras = new ArrayList<>();
        for (OrderItem orderItem : itemList) {
            OrderItemExtra orderItemExtra = new OrderItemExtra();
            String sku = orderItem.getSku();
            orderItemExtra.setOrderItemId(orderItem.getId());
            orderItemExtra.setCreateTime(LocalDateTime.now());
            orderItemExtra.setUpdateTime(LocalDateTime.now());
            // 买手
            Long buyerId = skuToBuyerIdMap.get(sku);
            if(buyerId != null) {
                orderItemExtra.setBuyerId(buyerId);
                PopBuyerInfoDTO popBuyerInfoDTO = buyerMap.get(buyerId);
                if(popBuyerInfoDTO != null) {
                    Long adminIdLong = popBuyerInfoDTO.getAdminId() == null ? null : Long.valueOf(popBuyerInfoDTO.getAdminId());
                    orderItemExtra.setBuyerAdminId(adminIdLong);
                    orderItemExtra.setBuyerName(popBuyerInfoDTO.getBuyerName());
                }
            }

            // 佣金
            PopSkuAdditionInfoDTO dto = skuToAdditionInfoDtoMap.get(sku);
            if(dto != null) {
                orderItemExtra.setSupplierId(dto.getSupplierId());
                orderItemExtra.setPlatformCommissionRatio(dto.getKickbackRatio());
                orderItemExtra.setCost(dto.getSupplierWeightPrice());
                orderItemExtra.setSpecifications(dto.getSkuWeight());
                orderItemExtra.setQuoteType(dto.getQuoteType());
                orderItemExtra.setTotalUnitCost(dto.getTotalUnitCost());
            }

            // 自动售后相关信息
            Inventory inventory = skuInfoMap.get(sku);
            if (inventory != null) {
                // 订单快照记录最新提报的类型，不再使用inventory的类型
                // orderItemExtra.setQuoteType(inventory.getQuoteType());
                orderItemExtra.setMinAutoAfterSaleThreshold(inventory.getMinAutoAfterSaleThreshold());
            }

            // 分销商加价
            AreaSkuPriceMarkupConfig markupConfig = priceMarkupConfigMap.get(sku);
            if(markupConfig != null && markupConfig.getMarkupValue() != null) {
                orderItemExtra.setMarkupAmount(markupConfig.getMarkupValue());
            }

            orderItemExtras.add(orderItemExtra);
        }

        if(CollectionUtil.isNotEmpty(orderItemExtras)) {
            orderItemExtraMapper.insertBatch(orderItemExtras);
        }
    }

    /**
     * 插入配送数据
     *
     * @param orderNo       订单编号
     * @param placeOrderVO  下单信息
     * @param orderResultVO 下单结果
     */
    private DeliveryPlan insertDeliveryPlan(String orderNo, PlaceOrderVO placeOrderVO,
            OrderResultVO orderResultVO) {
        DeliveryPlan deliveryPlan = new DeliveryPlan();
        deliveryPlan.setOrderNo(orderNo);
        deliveryPlan.setQuantity(0);
        deliveryPlan.setDeliveryTime(orderResultVO.getDeliveryDate());
        deliveryPlan.setContactId(placeOrderVO.getContactId());
        deliveryPlan.setTimeFrame(orderResultVO.getUsedTimeFrame());
        deliveryPlan.setAccountId(placeOrderVO.getAccountId());
        deliveryPlan.setStatus(Integer.valueOf(orderResultVO.getOrderStatus()));
        deliveryPlan.setDeliverytype(false);
        deliveryPlan.setOrderStoreNo(placeOrderVO.getContact().getStoreNo());
        deliveryPlanMapper.insert(deliveryPlan);
        //记录快照表
        return deliveryPlan;
    }

    /**
     * 合并（活动不合并）&插入优惠信息
     *
     * @param orderNo          订单编号
     * @param preferentialList 优惠list
     */
    private void insertOrderPreferential(String orderNo, List<OrderPreferential> preferentialList) {
        if (CollectionUtil.isEmpty(preferentialList)) {
            return;
        }

        List<OrderPreferential> insertList = new ArrayList<>();
        Map<Integer, List<OrderPreferential>> listMap = preferentialList.stream()
                .filter(item -> {
                    //部分优惠不合并
                    if (OrderPreferentialTypeEnum.isNotNeedMergeDiscount(item.getType())) {
                        item.setOrderNo(orderNo);
                        insertList.add(item);
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.groupingBy(OrderPreferential::getType));

        for (Map.Entry<Integer, List<OrderPreferential>> entry : listMap.entrySet()) {
            OrderPreferential orderPreferential = new OrderPreferential();
            orderPreferential.setType(entry.getKey());
            orderPreferential.setOrderNo(orderNo);

            BigDecimal amount = BigDecimal.ZERO;
            for (OrderPreferential op : entry.getValue()) {
                amount = amount.add(op.getAmount());
            }
            orderPreferential.setAmount(amount);
            insertList.add(orderPreferential);
        }
        orderPreferentialMapper.insertBatch(insertList);
    }

    /**
     * 更新库存数据
     *
     * @param orderNo       订单编号
     * @param placeOrderVO  下单信息
     * @param orderResultVO 订单计算
     */
    private void updateStoreQuantity(String orderNo, PlaceOrderVO placeOrderVO,
            OrderResultVO orderResultVO) {
        //同个sku合并 & 使用sku排序
        Map<String, Integer> skuQuantityMap = new TreeMap<>();
        for (OrderItemCalcDTO dto : orderResultVO.getItemList()) {
            if (Objects.equals(dto.getNotNeedStock(), true)) {
                continue;
            }
            Integer quantity = skuQuantityMap.getOrDefault(dto.getSku(), 0);
            skuQuantityMap.put(dto.getSku(), quantity + dto.getAmount());
        }

        Integer areaNo = placeOrderVO.getArea().getAreaNo();
        //库存处理
        AreaStoreLockReq storeLockReq = new AreaStoreLockReq();
        storeLockReq.setOrderNo(orderNo);
        storeLockReq.setMerchantId(placeOrderVO.getMId());
        storeLockReq.setContactId(placeOrderVO.getContactId());
        storeLockReq.setOrderType(SaleStockChangeTypeEnum.PLACE_ORDER.getTypeName());
        storeLockReq.setOperatorNo(orderNo);
        storeLockReq.setIdempotentNo(orderNo);
        storeLockReq.setOperatorName(placeOrderVO.getMname());
        storeLockReq.setSource(DistOrderSourceEnum.getDistOrderSource(placeOrderVO.getBusinessLine()));
        List<OrderLockSkuDetailReqDTO> lockSkuDetails = skuQuantityMap.entrySet().stream()
                .map(x -> {
                    OrderLockSkuDetailReqDTO reqDTO = new OrderLockSkuDetailReqDTO();
                    reqDTO.setSkuCode(x.getKey());
                    reqDTO.setOccupyQuantity(x.getValue());
                    return reqDTO;
                }).sorted(Comparator.comparing(t -> t.getSkuCode())).collect(Collectors.toList());
        storeLockReq.setOrderLockSkuDetailReqDTOS(lockSkuDetails);
        wmsAreaStoreFacade.storeLock(storeLockReq);

        //处理到货提醒
        PurchasesConfig purchasesConfig = new PurchasesConfig();
        purchasesConfig.setAreaNo(areaNo);
        purchasesConfig.setSkus(new ArrayList<>(skuQuantityMap.keySet()));
        asyncTaskService.purchasesArrival(purchasesConfig);
    }

    /**
     * 预下单后置校验
     *
     * @param orderResultVO 下单数据
     * @return 校验是否通过
     */
    private void afterCheck(OrderResultVO orderResultVO) {
        BigDecimal totalDiscount = BigDecimal.ZERO;
        List<OrderPreferential> preferentialList = orderResultVO.getPreferentialList();
        //优惠金额记录
        if (CollectionUtil.isNotEmpty(preferentialList)) {
            for (OrderPreferential preferential : preferentialList) {
                totalDiscount = totalDiscount.add(preferential.getAmount());
            }
        }

        if (orderResultVO.getActualTotalPrice().add(totalDiscount)
                .compareTo(orderResultVO.getOriginTotalPrice()) != 0) {
            log.warn("订单所有优惠明细:{}, actualTotalPrice:{}, totalDiscount:{}, originTotalPrice:{}", JSON.toJSONString(preferentialList),
                    orderResultVO.getActualTotalPrice(), totalDiscount, orderResultVO.getOriginTotalPrice());
            throw new BizException("下单失败，金额异常");
        }
    }


    /**
     * 预下单处理下单参数 - 配送日期
     *
     * @param placeOrderVO 下单参数
     */
    private void assignDeliveryDate(PlaceOrderVO placeOrderVO) {
        if (null == placeOrderVO.getAssignDeliveryDate()){
            return;
        }
        log.info("预下单处理下单参数，将订单的配送日期强制修改为 >>> {}", placeOrderVO.getAssignDeliveryDate());
        placeOrderVO.getStoreMap().forEach((date, orderResultVO) -> {
            orderResultVO.setDeliveryTime(placeOrderVO.getAssignDeliveryDate());
        });
    }
}
