package net.summerfarm.mall.service.impl;

import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.mapper.CompleteDeliveryMapper;
import net.summerfarm.mall.service.CompleteDeliveryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/15 16:52
 */
@Service
public class CompleteDeliveryServiceImpl implements CompleteDeliveryService {

    @Resource
    private CompleteDeliveryMapper completeDeliveryMapper;

    @Override
    public String getCompleteDeliveryTime(String city, String area) {
        String completionDeliveryDate = completeDeliveryMapper.selectCompleteDeliveryTime(city, area);
        if (StringUtils.isBlank(completionDeliveryDate)) {
            completionDeliveryDate = Global.DEFAULT_COMPLETE_DELIVERY_TIME;
        }
        return completionDeliveryDate;
    }
}
