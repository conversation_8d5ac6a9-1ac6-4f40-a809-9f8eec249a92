package net.summerfarm.mall.service.impl;

import net.summerfarm.mall.mapper.MarketPriceControlProductsMapper;
import net.summerfarm.mall.model.domain.MarketPriceControlProducts;
import net.summerfarm.mall.service.MarketPriceControlProductsService;
import net.xianmu.common.cache.InMemoryCache;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description 控价品
 * @date 2024/06/07 11:49:56
 */
@Service
public class MarketPriceControlProductsServiceImpl implements MarketPriceControlProductsService {

    @Resource
    private MarketPriceControlProductsMapper marketPriceControlProductsMapper;

    @Override
    @InMemoryCache(expiryTimeInSeconds = 1 * 60)
    public Map<String, MarketPriceControlProducts> selectAllControlProductsByCache() {
        List<MarketPriceControlProducts> marketPriceControlProducts = marketPriceControlProductsMapper.selectAllControlProducts();
        Map<String, MarketPriceControlProducts> controlProductsMap = marketPriceControlProducts.stream().collect(Collectors.toMap(MarketPriceControlProducts::getSku,
                Function.identity(), (k1, k2) -> k2));
        return controlProductsMap;
    }
}
