package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.enums.MerchantEnum;
import net.summerfarm.mall.enums.PlaceOrderPriceEnum;
import net.summerfarm.mall.mapper.InventoryMapper;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.model.domain.AreaSku;
import net.summerfarm.mall.model.domain.MerchantSubAccount;
import net.summerfarm.mall.model.domain.Trolley;
import net.summerfarm.mall.model.dto.merchant.MerchantDTO;
import net.summerfarm.mall.model.dto.order.UserContextParam;
import net.summerfarm.mall.model.dto.price.BatchTakePriceRequest;
import net.summerfarm.mall.model.dto.price.BatchTakePriceResponse;
import net.summerfarm.mall.model.vo.InventoryVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.model.vo.price.TakeActualPriceVO;
import net.summerfarm.mall.repository.MerchantRepository;
import net.summerfarm.mall.repository.MerchantSubAccountRepository;
import net.summerfarm.mall.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 批量价格查询服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BatchPriceQueryServiceImpl implements BatchPriceQueryService {

    private static final String SIGN_KEY = "summerfarm-mall";

    @Resource
    private OrderCalcService orderCalcService;

    @Resource
    private MerchantRepository merchantRepository;

    @Resource
    private MerchantSubAccountRepository merchantSubAccountRepository;

    @Resource
    private AreaSkuService areaSkuService;

    @Resource
    private AreaService areaService;

    @Resource
    private InventoryMapper inventoryMapper;

    @Override
    public BatchTakePriceResponse batchQueryTakePrice(BatchTakePriceRequest request) {
        log.info("批量查询到手价开始 - mId: {}, sku: {}", request.getMId(), request.getSkuList());

        BatchTakePriceResponse response = new BatchTakePriceResponse();
        response.setMId(request.getMId());
        response.setQueryTime(System.currentTimeMillis());

        try {
            // 1. 获取商户信息
            MerchantDTO merchantDTO = merchantRepository.getMerchantByMid(request.getMId());
            if (merchantDTO == null) {
                log.warn("商户不存在 - mId: {}", request.getMId());
                response.setSkuPrices(createErrorSkuPrices(request.getSkuList(), "商户不存在"));
                return response;
            }

            // 2. 填充商户基本信息
            response.setMerchantName(merchantDTO.getMname());
            response.setIsMajorMerchant("大客户".equals(merchantDTO.getSize()));
            response.setBusinessLine(MerchantEnum.BusinessLineEnum.XM.getCode());

            Area area = areaService.selectAreaWithCache(merchantDTO.getAreaNo());
            if (area == null) {
                log.warn("区域不存在 - areaNo: {}", merchantDTO.getAreaNo());
                response.setSkuPrices(createErrorSkuPrices(request.getSkuList(), "区域不存在"));
                return response;
            }

            MerchantSubAccount resultAccount = merchantSubAccountRepository.selectMangerByMId(merchantDTO.getmId());
            if (resultAccount == null) {
                log.warn("店长信息不存在 - mId: {}", merchantDTO.getmId());
                response.setSkuPrices(createErrorSkuPrices(request.getSkuList(), "店长信息不存在"));
                return response;
            }


            // 3. 构建用户上下文
            UserContextParam userContext = buildUserContext(merchantDTO);
            userContext.setArea(area);
            userContext.setAccountId(resultAccount.getAccountId()); // 使用店长的accountId

            // 4. 构建订单信息
            PlaceOrderVO orderVO = buildPlaceOrderVO(request);
            log.info("构建的订单信息: {}, userContext:{}", JSON.toJSONString(orderVO), JSON.toJSONString(userContext));

            // 5. 调用价格计算服务
            List<TakeActualPriceVO> priceResults = orderCalcService.takePriceHandler(orderVO, userContext);

            // 6. 转换结果
            response.setSkuPrices(convertToSkuPrices(request.getSkuList(), priceResults, merchantDTO));

            log.info("批量查询到手价完成 - mId: {}, 成功: {}", request.getMId(), response.getSkuPrices().size());

        } catch (Exception e) {
            log.error("批量查询到手价异常 - mId: " + request.getMId(), e);
            response.setSkuPrices(createErrorSkuPrices(request.getSkuList(), "系统异常: " + e.getMessage()));
        }

        return response;
    }

    @Override
    public boolean verifySignature(BatchTakePriceRequest request) {
        if (StringUtils.isEmpty(request.getSignature())) {
            return false;
        }
        try {
            String today = new SimpleDateFormat("yyyyMMdd").format(new Date());
            String expectedSignature = DigestUtil.md5Hex(SIGN_KEY + today);
            return expectedSignature.equalsIgnoreCase(request.getSignature());
        } catch (Exception e) {
            log.error("签名验证异常", e);
            return false;
        }
    }

    /**
     * 构建用户上下文参数
     */
    private UserContextParam buildUserContext(MerchantDTO merchantDTO) {
        UserContextParam userContext = new UserContextParam();

        userContext.setMId(merchantDTO.getmId());
        userContext.setAccountId(merchantDTO.getTenantId()); // 使用tenantId作为accountId
        userContext.setMname(merchantDTO.getMname());
        userContext.setMajorMerchant("大客户".equals(merchantDTO.getSize()));
        userContext.setAdminId(merchantDTO.getAdminId());
        userContext.setDirect(merchantDTO.getDirect());
        userContext.setSkuShow(merchantDTO.getSkuShow());
        userContext.setServer(merchantDTO.getServer());
        userContext.setBusinessLine(merchantDTO.getBusinessLine());
        userContext.setSize(merchantDTO.getSize());
        userContext.setHelpOrder(0);
        userContext.setOutTimes(0);

        // 设置区域信息
        if (merchantDTO.getAreaNo() != null) {
            Area area = new Area();
            area.setAreaNo(merchantDTO.getAreaNo());
            userContext.setArea(area);
        }

        return userContext;
    }

    /**
     * 构建订单信息
     */
    private PlaceOrderVO buildPlaceOrderVO(BatchTakePriceRequest request) {
        PlaceOrderVO orderVO = new PlaceOrderVO();
        orderVO.setTakePriceFlag(true); // 获取明细，不均摊优惠
        orderVO.setIsTakePrice(PlaceOrderPriceEnum.ISTAKEPRICE.getCode()); // 到手价计算
        orderVO.setMinLadderPrice(request.getMinLadderPrice()); // 设置阶梯价参数

        // 转换SKU列表
        List<Trolley> trolleyList = request.getSkuList().stream()
                .map(item -> {
                    Trolley trolley = new Trolley();
                    trolley.setSku(item.getSku());
                    trolley.setQuantity(item.getQuantity());
                    trolley.setProductType(0); // 普通商品
                    trolley.setSuitId(0);
                    return trolley;
                })
                .collect(Collectors.toList());

        orderVO.setOrderNow(trolleyList);
        return orderVO;
    }

    /**
     * 转换价格结果
     */
    private List<BatchTakePriceResponse.SkuPriceInfo> convertToSkuPrices(
            List<BatchTakePriceRequest.BatchTakePriceItem> requestItems,
            List<TakeActualPriceVO> priceResults,
            MerchantDTO merchantDTO) {

        List<BatchTakePriceResponse.SkuPriceInfo> skuPrices = new ArrayList<>();
        Set<String> allSkus = requestItems.stream()
                .map(BatchTakePriceRequest.BatchTakePriceItem::getSku)
                .collect(Collectors.toSet());

        // Step 1: Pre-fetch all original prices if merchantDTO and areaNo are available
        Map<String, AreaSku> allOriginalPricesMap = Collections.emptyMap();
        if (merchantDTO != null && merchantDTO.getAreaNo() != null) {
            log.info("批量查询原始价格，SKUs: {}, AreaNo: {}", allSkus, merchantDTO.getAreaNo());
            allOriginalPricesMap = areaSkuService.areaSkuMapper(allSkus, merchantDTO.getAreaNo());
            log.info("AreaSkuService 批量返回的 Map: {}", allOriginalPricesMap);
        } else {
            log.warn("无法批量获取原始价格，merchantDTO 或 AreaNo 为 null. merchantDTO: {}", merchantDTO);
        }

        List<InventoryVO> inventoryVOList = inventoryMapper.selectBySkuList(allSkus);
        Map<String, InventoryVO> inventoryVOMap = Collections.EMPTY_MAP;
        if (CollectionUtil.isNotEmpty(inventoryVOList)) {
            inventoryVOMap = inventoryVOList.stream().collect(Collectors.toMap(InventoryVO::getSku, Function.identity()));
        }else {
            log.warn("未能批量获取商品信息，skus: {}", allSkus);
        }

        for (BatchTakePriceRequest.BatchTakePriceItem requestItem : requestItems) {
            BatchTakePriceResponse.SkuPriceInfo skuPrice = new BatchTakePriceResponse.SkuPriceInfo();
            skuPrice.setSku(requestItem.getSku());
            skuPrice.setQuantity(requestItem.getQuantity());

            // Set original price first from the pre-fetched map
            BigDecimal currentOriginalPrice = BigDecimal.ZERO;
            AreaSku originalAreaSku = allOriginalPricesMap.get(requestItem.getSku());
            if (originalAreaSku != null && originalAreaSku.getPrice() != null) {
                currentOriginalPrice = originalAreaSku.getPrice();
                log.info("为 SKU: {} 设置原始价格: {}", requestItem.getSku(), currentOriginalPrice);
            } else {
                log.warn("未能在批量原始价格查询中找到 SKU: {} 的价格，或价格为 null", requestItem.getSku());
            }
            skuPrice.setOriginalPrice(currentOriginalPrice);
            skuPrice.setProductName(Optional.ofNullable(inventoryVOMap.get(skuPrice.getSku())).map(InventoryVO::getPdName).orElse(""));


            // 查找对应的价格结果 (到手价)
            TakeActualPriceVO priceResult = priceResults.stream()
                    .filter(p -> requestItem.getSku().equals(p.getSku()))
                    .findFirst()
                    .orElse(null);

            if (priceResult != null) {
                // 增加一层保护：如果 orderCalcService 返回的到手价和原价相同，则认为没有优惠
                if (priceResult.getPrice() != null && priceResult.getPrice().compareTo(priceResult.getTakeActualPrice()) == 0) {
                    skuPrice.setIsValid(true);
                    skuPrice.setErrorMessage("SKU无优惠，原价售卖");
                    skuPrice.setTakeActualPrice(currentOriginalPrice); // 使用我们自己查的更可靠的原价
                    skuPrice.setDiscountAmount(BigDecimal.ZERO);
                } else {
                    // Existing logic for discounted price
                    skuPrice.setTakeActualPrice(priceResult.getTakeActualPrice());

                    // 计算优惠金额
                    if (priceResult.getPrice() != null && priceResult.getTakeActualPrice() != null) {
                        BigDecimal discountAmount = priceResult.getPrice()
                                .multiply(BigDecimal.valueOf(requestItem.getQuantity()))
                                .subtract(priceResult.getTakeActualPrice()
                                        .multiply(BigDecimal.valueOf(requestItem.getQuantity())));
                        skuPrice.setDiscountAmount(discountAmount);
                    }

                    skuPrice.setIsValid(true);
                }
                // TakeActualPriceVO 没有这些字段，暂时设置为空或默认值
                skuPrice.setProductName(""); // 商品名称需要从其他地方获取
                skuPrice.setSpecification(""); // 规格需要从其他地方获取
                skuPrice.setUnit(""); // 单位需要从其他地方获取
            } else {
                // New logic for no discounted price
                skuPrice.setIsValid(true); // SKU is valid, just no discount
                skuPrice.setErrorMessage("SKU无优惠，原价售卖"); // New error message
                skuPrice.setTakeActualPrice(currentOriginalPrice); // Take actual price is original price
                skuPrice.setDiscountAmount(BigDecimal.ZERO); // No discount
            }

            skuPrices.add(skuPrice);
        }

        return skuPrices;
    }

    /**
     * 创建错误的SKU价格列表
     */
    private List<BatchTakePriceResponse.SkuPriceInfo> createErrorSkuPrices(
            List<BatchTakePriceRequest.BatchTakePriceItem> skuList, String errorMessage) {

        return skuList.stream()
                .map(item -> {
                    BatchTakePriceResponse.SkuPriceInfo skuPrice = new BatchTakePriceResponse.SkuPriceInfo();
                    skuPrice.setSku(item.getSku());
                    skuPrice.setQuantity(item.getQuantity());
                    skuPrice.setIsValid(false);
                    skuPrice.setErrorMessage(errorMessage);
                    skuPrice.setOriginalPrice(BigDecimal.ZERO);
                    skuPrice.setTakeActualPrice(BigDecimal.ZERO);
                    skuPrice.setDiscountAmount(BigDecimal.ZERO);
                    return skuPrice;
                })
                .collect(Collectors.toList());
    }
}