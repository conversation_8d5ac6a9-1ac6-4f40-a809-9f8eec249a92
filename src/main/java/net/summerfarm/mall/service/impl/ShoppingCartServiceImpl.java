package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.facade.market.ProductsSaleRuleFacade;
import net.summerfarm.mall.facade.market.dto.ProductsSaleRuleDto;
import net.summerfarm.mall.mapper.AreaMapper;
import net.summerfarm.mall.mapper.AreaSkuMapper;
import net.summerfarm.mall.mapper.ContactMapper;
import net.summerfarm.mall.mapper.DeliveryPlanMapper;
import net.summerfarm.mall.mapper.InventoryMapper;
import net.summerfarm.mall.mapper.MajorPriceMapper;
import net.summerfarm.mall.mapper.MajorRebateMapper;
import net.summerfarm.mall.mapper.OrderItemMapper;
import net.summerfarm.mall.mapper.ProductsMapper;
import net.summerfarm.mall.mapper.ShoppingCartMapper;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.delivery.*;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDTO;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDetailDTO;
import net.summerfarm.mall.model.dto.trolley.ShoppingCartDTO;
import net.summerfarm.mall.model.input.ShoppingCartDeleteInput;
import net.summerfarm.mall.model.input.ShoppingCartGetInput;
import net.summerfarm.mall.model.input.ShoppingCartInsertInput;
import net.summerfarm.mall.model.input.ShoppingCartOneMoreInput;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.facade.DistributionRulesFacade;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryReq;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryRes;
import net.summerfarm.mall.service.facade.dto.DeliveryFeeRuleReq;
import net.summerfarm.mall.service.helper.ExchangeBuyServiceHelper;
import net.xianmu.common.exception.BizException;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description 新版购物车
 * @date 2023/10/10 11:50:36
 */
@Service
@Slf4j
public class ShoppingCartServiceImpl implements ShoppingCartService {

    @Resource
    private ShoppingCartMapper shoppingCartMapper;

    @Resource
    private PrepayInventoryService prepayInventoryService;

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private WmsAreaStoreFacade wmsAreaStoreFacade;

    @Resource
    private ContactMapper contactMapper;

    @Resource
    private AreaMapper areaMapper;

    @Resource
    private MajorPriceService majorPriceService;

    @Resource
    private ExchangeBuyServiceHelper exchangeBuyServiceHelper;

    @Resource
    private AreaSkuMapper areaSkuMapper;

    @Resource
    private InventoryService inventoryService;

    @Resource
    private MajorRebateMapper majorRebateMapper;

    @Resource
    private ProductsMapper productsMapper;

    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;

    @Resource
    private DistributionRulesService distributionRulesService;

    @Resource
    private ActivityService activityService;

    @Resource
    private OrderItemMapper orderItemMapper;

    @Resource
    private MajorPriceMapper majorPriceMapper;

    @Resource
    private CategoryService categoryService;

    @Resource
    private MarketPriceControlProductsService marketPriceControlProductsService;

    @Resource
    private DistributionRulesFacade distributionRulesFacade;

    @Resource
    private ProductsSaleRuleFacade productsSaleRuleFacade;

    @Resource
    @Lazy
    private DeliveryPlanService deliveryPlanService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(ShoppingCartInsertInput input) {
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        Long merchantId = merchantSubject.getMerchantId();
        Long accountId = merchantSubject.getAccount().getAccountId();
        String sku = input.getSku();
        Integer quantity = input.getQuantity();
        String parentSku = input.getParentSku();
        Long contactId = input.getContactId();
        Integer purchaseType = Objects.isNull(input.getPurchaseType()) ? CommonStatus.YES.getCode() : input.getPurchaseType();

        ShoppingCart shoppingCart = new ShoppingCart();
        shoppingCart.setSku(sku);
        shoppingCart.setProductType(input.getProductType());
        shoppingCart.setMId(merchantId);
        shoppingCart.setAccountId(accountId);
        shoppingCart.setQuantity(quantity);
        shoppingCart.setBizId(input.getBizId());

        //搭配购赋予默认值
        if (Objects.isNull(input.getParentSku()) || RequestHolder.isMajor()) { //NOSONAR
            parentSku = "0";
        }

        //查询当前加购商品是否已经存在
        shoppingCart.setParentSku(parentSku);
        ShoppingCart cart = shoppingCartMapper.selectByEntity(shoppingCart);

        //大客户校验合同数量
        if (RequestHolder.isMajor()) { //NOSONAR
            //查询合同数量
            Map<String, Integer> prepayAmountMap = prepayInventoryService.queryUsableMap(merchantSubject.getAdminId(), sku);
            if (!CollectionUtils.isEmpty(prepayAmountMap)) {
                int prepayAmount = prepayAmountMap.get(sku) != null ? prepayAmountMap.get(sku) : 0;
                Integer sum = quantity;
                if (Objects.nonNull(cart) && purchaseType.equals(CommonStatus.YES.getCode())) {
                    sum += cart.getQuantity();
                }
                //小于等于0没有合同
                if (prepayAmount > 0 && sum > prepayAmount) {
                    StringBuffer errMsg = new StringBuffer("合同剩");
                    errMsg.append(prepayAmount)
                            .append("件，若超量购买，请分两次下单");
                    throw new BizException(errMsg.toString());
                }
            }
        }

        //sku信息校验--上下架、限购数量
        AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(merchantSubject.getArea().getAreaNo(), sku);
        if (Objects.isNull(areaSku) || !areaSku.getOnSale()) { //NOSONAR
            throw new BizException("抱歉！当前加购商品信息不存在或已下架");
        }
        if (Objects.nonNull(areaSku.getSalesMode()) && areaSku.getSalesMode().equals(SalesModeEnum.YES.getCode())
                && areaSku.getLimitedQuantity().compareTo(quantity) < 0) {
            throw new BizException("抱歉！当前加购商品数量超过限购数量，当前商品限购" + areaSku.getLimitedQuantity() + "件");
        }

        //获取库存信息 假如地址ID为空则获取默认地址ID
        Map<String, AreaStoreQueryRes> storeQueryResMap = getAreaStoreQueryRes(contactId, merchantId, Collections.singletonList(sku));
        if (CollectionUtils.isEmpty(storeQueryResMap) || Objects.isNull(storeQueryResMap.get(sku))
                || Objects.isNull(storeQueryResMap.get(sku).getOnlineQuantity())) {
            throw new BizException("抱歉！当前加购商品的库存信息不存在");
        }
        AreaStoreQueryRes areaStoreQueryRes = storeQueryResMap.get(sku);
        Integer onlineQuantity = areaStoreQueryRes.getOnlineQuantity();
        if (onlineQuantity <= 0) {
            throw new BizException("抱歉！当前加购的商品暂无库存");
        }

        //获取sku加购上限，为空则不限制,大客户不限制
        if (!RequestHolder.isMajor()){ //NOSONAR
            Integer purchaseCeiling = inventoryService.getPurchaseCeiling(sku, onlineQuantity, merchantId);
            if (!ObjectUtils.isEmpty(purchaseCeiling) && quantity.compareTo(purchaseCeiling) > 0){
                throw new BizException("抱歉！该商品单次购买上限为" + purchaseCeiling + "件");
            }
        }

        //获取购物车中sku存在的数量信息（包括普通和换购）
        Integer sumQuantity = shoppingCartMapper.getSumQuantity(merchantId, accountId, sku, parentSku);
        if (Objects.nonNull(sumQuantity) && sumQuantity > 0) {
            Integer addQuantity = quantity;
            if (Objects.nonNull(cart) && purchaseType.equals(CommonStatus.NO.getCode())) {
                //购物车列表加减--扣减当前数据库已经加购的数量
                addQuantity -= cart.getQuantity();
            }
            if (addQuantity + sumQuantity > onlineQuantity) {
                throw new BizException("抱歉！您添加的当前商品数量已经超过上限，您还可以添加" + (onlineQuantity - sumQuantity) + "件商品");
            }
        } else {
            if (quantity > onlineQuantity) {
                throw new BizException("抱歉！您添加的当前商品数量已经超过上限，最多只能添加" + onlineQuantity + "件商品");
            }
        }

        //保存购物车数据
        Long id;
        if (Objects.isNull(cart)) {
            shoppingCartMapper.insertSelective(shoppingCart);
            id = shoppingCart.getId();
        } else {
            id = cart.getId();
            if (purchaseType.equals(CommonStatus.YES.getCode())) {
                quantity += cart.getQuantity();
            }
            shoppingCartMapper.updateQuantityById(id, quantity);
        }
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ShoppingCartVO> getAll(ShoppingCartGetInput input) {
        Long contactId = input.getContactId();
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        Long mId = merchantSubject.getMerchantId();
        Long accountId = merchantSubject.getAccount().getAccountId();

        //获取客户最新服务区域信息
        Area area = areaMapper.selectByMId(mId);

        //地址信息查询
        Contact contact;
        if (Objects.isNull(contactId)) {
            contact = contactMapper.selectIsDefaultByMid(mId);
            contactId = contact.getContactId();
        } else {
            contact = contactMapper.selectByPrimaryKey(contactId);
            if (Objects.isNull(contact)) {
                log.warn("ShoppingCartServiceImpl[]getDistributionRules[]contact[]error!");
                return null;
            }
        }

        //清空蹭品数据：避免代下单加入购物车后数据异常（老逻辑）
        ShoppingCart shoppingCart = new ShoppingCart();
        shoppingCart.setMId(mId);
        shoppingCart.setAccountId(accountId);
        shoppingCart.setProductType(ProductTypeEnum.GIFT.getCode());
        shoppingCartMapper.deleteByEntity(shoppingCart);

        //获取所有购物车数据
        List<ShoppingCart> shoppingCarts = shoppingCartMapper.selectAll(mId, accountId);
        if (CollectionUtils.isEmpty(shoppingCarts)) {
            return null;
        }

        //获取区域有效的购物车数据
        List<ShoppingCartVO> shoppingCartVOS = shoppingCartMapper.getShoppingCarts(merchantSubject.getMerchantId(), merchantSubject.getAccount().getAccountId(),
                area.getAreaNo(), merchantSubject.getAdminId(), merchantSubject.getDirect());
        if (CollectionUtils.isEmpty(shoppingCartVOS)) {
            //自动清空全部无效数据-商品数据不存在
            List<Long> deleteIds = shoppingCarts.stream().map(ShoppingCart::getId).collect(Collectors.toList());
            shoppingCartMapper.batchDelete(deleteIds);
            log.warn("ShoppingCartServiceImpl[]getAll[]batchDelete[]deleteIds:{}", JSON.toJSONString(deleteIds));
            return shoppingCartVOS;
        }

        //清空部分无效数据-商品数据不存在
        List<Long> ids = shoppingCartVOS.stream().map(ShoppingCartVO::getId).collect(Collectors.toList());
        List<Long> deleteIds = shoppingCarts.stream().filter(e -> !ids.contains(e.getId()))
                .collect(Collectors.toList()).stream().map(ShoppingCart::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deleteIds)) {
            shoppingCartMapper.batchDelete(deleteIds);
        }

        //大客户：清空定量客户不在报价单里的sku 单店：清空大客户专享商品 避免数据异常
        clearMajorPrice(merchantSubject, area, shoppingCartVOS);
        if (CollectionUtils.isEmpty(shoppingCartVOS)) {
            log.warn("ShoppingCartServiceImpl[]getAll[]clearMajorPrice[] is empty");
            return shoppingCartVOS;
        }

        //保存需要更新加购数量的数据
        Map<Long, ShoppingCart> updateMap = new HashMap<>(shoppingCartVOS.size());

        //设置兜底名称和图片、是否失效、mallshow、均价
        checkSalesMode(shoppingCartVOS, updateMap, merchantSubject.getAdminId(), area.getAreaNo(), merchantSubject.getDirect());

        //过滤出失效和未失效商品数据
        Map<Integer, List<ShoppingCartVO>> onSaleMap = shoppingCartVOS.stream().collect(Collectors.groupingBy(ShoppingCartVO::getEffectiveness));
        List<ShoppingCartVO> loseEfficacyList = onSaleMap.get(ShoppingCartEfficacyEnum.YES.getCode());
        shoppingCartVOS = onSaleMap.get(ShoppingCartEfficacyEnum.NO.getCode());
        if (CollectionUtils.isEmpty(shoppingCartVOS)) {
            log.warn("ShoppingCartServiceImpl[]getAll[]efficacyShoppingCartVOS[] is empty");
            return loseEfficacyList;
        }

        //活动商品处理--校验活动信息以及获取活动相关信息
        deleteIds = new ArrayList<>();
        checkActivityInfo(mId, area, shoppingCartVOS, deleteIds, updateMap);
        if (!CollectionUtils.isEmpty(deleteIds)) {
            shoppingCartMapper.batchDelete(deleteIds);
        }
        if (CollectionUtils.isEmpty(shoppingCartVOS)) {
            //更新购物车数量
            updateQuantityByMap(updateMap);
            log.warn("ShoppingCartServiceImpl[]getAll[]checkActivityInfo[] is empty");
            return loseEfficacyList;
        }

        //查询库存信息
        List<String> skus = shoppingCartVOS.stream().map(ShoppingCartVO::getSku).collect(Collectors.toList());
        Map<String, AreaStoreQueryRes> storeQueryResMap = getAreaStoreQueryRes(contactId, mId, skus);
        if (CollectionUtils.isEmpty(storeQueryResMap)) {
            //将购物车商品置为失效--兜底逻辑
            shoppingCartVOS.stream().forEach(e -> {
                e.setEffectiveness(CommonStatus.YES.getCode());
                e.setPopoverFlag(Boolean.FALSE);
            });
            if (!CollectionUtils.isEmpty(loseEfficacyList)) {
                shoppingCartVOS.addAll(loseEfficacyList);
            }
            log.warn("ShoppingCartServiceImpl[]getAll[]getAreaStoreQueryRes[] is empty");
            return shoppingCartVOS;
        }

        //设置履约时效等信息
        LocalTime closeTime = null;
        Integer isEveryDayFlag = null;
        for (ShoppingCartVO shoppingCartVO : shoppingCartVOS) {
            AreaStoreQueryRes areaStoreQueryRes = storeQueryResMap.get(shoppingCartVO.getSku());
            if (Objects.nonNull(areaStoreQueryRes)) {
                shoppingCartVO.setDeliveryTime(areaStoreQueryRes.getDeliveryTime());
                shoppingCartVO.setShipmentDate(null == areaStoreQueryRes.getDeliveryTime() ? null : areaStoreQueryRes.getDeliveryTime().minusDays(1L));
                shoppingCartVO.setIsEveryDayFlag(areaStoreQueryRes.getIsEveryDayFlag());
                isEveryDayFlag = areaStoreQueryRes.getIsEveryDayFlag();
                closeTime = areaStoreQueryRes.getCloseTime();
            }
        }

        if (Objects.isNull(isEveryDayFlag)) {
            log.error("ShoppingCartServiceImpl[]getAll[]isEveryDayFlag is null storeQueryResMap:{}", JSON.toJSONString(storeQueryResMap));
            throw new BizException("抱歉！获取履约时效信息有误，请刷新购物车重试！");
        }

        //检测是否超出限购--目前针对日限购
        checkOverPurchase(mId, area, closeTime, shoppingCartVOS, updateMap);

        //购物车数量调整--对商品售卖规格调整过的商品进行处理
        checkBaseSaleUnit(storeQueryResMap, shoppingCartVOS, updateMap);

        //购物车商品进行分组
        Map<String, List<ShoppingCartVO>> listMap = shoppingCartVOS.stream().collect(Collectors.groupingBy(ShoppingCartVO::getSku));
        deleteIds = new ArrayList<>();

        //校验购物车商品加购数量
        checkQuantity(deleteIds, storeQueryResMap, listMap, updateMap);
        if (!CollectionUtils.isEmpty(deleteIds)) {
            Iterator<ShoppingCartVO> voIterator = shoppingCartVOS.iterator();
            while (voIterator.hasNext()) {
                ShoppingCartVO next = voIterator.next();
                if (deleteIds.contains(next.getId())) {
                    voIterator.remove();
                }
            }

            //删除购物车商品信息
            shoppingCartMapper.batchDelete(deleteIds);
        }

        if (CollectionUtils.isEmpty(shoppingCartVOS)) {
            //更新购物车数量
            updateQuantityByMap(updateMap);
            log.warn("ShoppingCartServiceImpl[]getAll[]checkQuantity[] is empty");
            return loseEfficacyList;
        }

        //大客户价格处理以及普通用户加购上限校验
        majorPriceAndPurchaseCeiling(merchantSubject, shoppingCartVOS, updateMap);

        //更新购物车数量
        updateQuantityByMap(updateMap);

        //获取配送规则 -- 兼容老逻辑
        if (Objects.equals(input.getDeliveryRulesType(), CommonStatus.YES.getCode())) {
            getNewDistributionRules(contact, mId, area, shoppingCartVOS, isEveryDayFlag);
        } else {
            getDistributionRules(contactId, mId, area, shoppingCartVOS, isEveryDayFlag);
        }

        //查询控价品是否隐藏实付价
        getMarketControlPriceHide(shoppingCartVOS);

        // spu起购规则(数据补充 + 排序)
        shoppingCartVOS = this.wrapAndSortBySpuSaleQuantity(shoppingCartVOS);

        //按照配送时间升序 配送时间为空的放最后
        shoppingCartVOS = shoppingCartVOS.stream().filter(e -> e.getQuantity() > 0).sorted(Comparator.comparing(ShoppingCartVO::getDeliveryTime,
                Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(loseEfficacyList)) {
            shoppingCartVOS.addAll(loseEfficacyList);
        }
        return shoppingCartVOS;
    }


    private List<ShoppingCartVO> wrapAndSortBySpuSaleQuantity(List<ShoppingCartVO> shoppingCartVOS){
        // 获取spu起购信息
         List<Long> pdIds = shoppingCartVOS.stream().map(ShoppingCartVO::getPdId).distinct().collect(Collectors.toList());
        Map<Long, ProductsSaleRuleDto> productsSaleRuleDtoMap = productsSaleRuleFacade.queryProductsSaleRuleMapByPdIds(pdIds);
        if(CollectionUtils.isEmpty(productsSaleRuleDtoMap)) {
            log.info("无spu起购规则");
            return shoppingCartVOS;
        }
        shoppingCartVOS.forEach(vo -> {
            ProductsSaleRuleDto productsSaleRuleDto = productsSaleRuleDtoMap.get(vo.getPdId());
            if(null != productsSaleRuleDto) {
                vo.setSpuBaseSaleQuantity(productsSaleRuleDto.getBaseSaleQuantity());
            }
        });

        return this.sortAndGroup(shoppingCartVOS, new ArrayList<>(productsSaleRuleDtoMap.keySet()));

    }


    @Override
    public Boolean delete(ShoppingCartDeleteInput input) {
        //包含搭配购商品会一起删除（需要前端传搭配购一起的ID）
        int delete = shoppingCartMapper.batchDelete(input.getIds());
        return delete > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer count(ShoppingCartGetInput input) {
        int count = 0;
        Long contactId = input.getContactId();
        try {
            MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();

            // 获取客户最新服务区域信息
            Area area = areaMapper.selectByMId(merchantSubject.getMerchantId());

            List<ShoppingCartVO> shoppingCartVOS = shoppingCartMapper.getShoppingCarts(merchantSubject.getMerchantId(), merchantSubject.getAccount().getAccountId(),
                    area.getAreaNo(), merchantSubject.getAdminId(), merchantSubject.getDirect());

            //不包含换购、搭配购、无效商品
            shoppingCartVOS = shoppingCartVOS.stream().filter(e -> Objects.equals(e.getOnSale(), OnSaleStatusEnum.LIST.getCode())
                    && Objects.equals(e.getParentSku(), "0") && Objects.equals(e.getProductType(), ProductTypeEnum.NORMAL.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shoppingCartVOS)) {
                return count;
            }

            //大客户：清空定量客户不在报价单里的sku 单店：清空大客户专享商品 避免数据异常
            clearMajorPrice(merchantSubject, area, shoppingCartVOS);
            if (CollectionUtils.isEmpty(shoppingCartVOS)) {
                return count;
            }

            //查询库存信息
            List<String> skus = shoppingCartVOS.stream().map(ShoppingCartVO::getSku).collect(Collectors.toList());
            Map<String, AreaStoreQueryRes> storeQueryResMap = getAreaStoreQueryRes(contactId, merchantSubject.getMerchantId(), skus);
            if (CollectionUtils.isEmpty(storeQueryResMap)) {
                return count;
            }
            for (String sku : skus) {
                AreaStoreQueryRes areaStoreQueryRes = storeQueryResMap.get(sku);
                if (Objects.nonNull(areaStoreQueryRes) && Objects.nonNull(areaStoreQueryRes.getOnlineQuantity())
                        && areaStoreQueryRes.getOnlineQuantity() > 0) {
                    count++;
                }
            }
        } catch (Exception e) {
            //防止未登录情况下 客户访问商城不报错
            log.warn("ShoppingCartServiceImpl[]count[]error[]cause:{}", JSON.toJSONString(e));
        }
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateQuantity(ShoppingCartDTO shoppingCartDTO) {
        if (Objects.isNull(shoppingCartDTO) || Objects.isNull(shoppingCartDTO.getSku())
                || Objects.isNull(shoppingCartDTO.getQuantity())) {
            return Boolean.FALSE;
        }
        if (shoppingCartDTO.getQuantity() <= 0) {
            log.warn("ShoppingCartServiceImpl[]updateQuantity[]error[]shoppingCartDTO:{}", JSON.toJSONString(shoppingCartDTO));
            return Boolean.FALSE;
        }

        ShoppingCart shoppingCart = new ShoppingCart();
        shoppingCart.setSku(shoppingCartDTO.getSku());
        shoppingCart.setMId(RequestHolder.getMerchantId());
        shoppingCart.setParentSku("0");
        shoppingCart.setAccountId(RequestHolder.getAccountId());
        shoppingCart.setProductType(shoppingCartDTO.getProductType());
        shoppingCart = shoppingCartMapper.selectByEntity(shoppingCart);
        if (Objects.isNull(shoppingCart)) {
            return Boolean.FALSE;
        }
        shoppingCart.setQuantity(shoppingCartDTO.getQuantity());
        shoppingCartMapper.updateByPrimaryKeySelective(shoppingCart);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDelete(List<ShoppingCartDTO> shoppingCartDTOS) {
        if (CollectionUtils.isEmpty(shoppingCartDTOS)) {
            return Boolean.FALSE;
        }
        Long mId = RequestHolder.getMerchantId();
        Long accountId = RequestHolder.getAccountId();
        shoppingCartDTOS.stream().forEach(shoppingCartDTO -> {
            if (Objects.isNull(shoppingCartDTO.getSku()) || Objects.isNull(shoppingCartDTO.getProductType())) {
                return;
            }
            ShoppingCart shoppingCart = new ShoppingCart();
            shoppingCart.setMId(mId);
            shoppingCart.setAccountId(accountId);
            shoppingCart.setSku(shoppingCartDTO.getSku());
            shoppingCart.setParentSku("0");
            shoppingCart.setProductType(shoppingCartDTO.getProductType());
            shoppingCartMapper.deleteByEntity(shoppingCart);
        });
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShoppingCartOneMoreVO oneMoreOrder(ShoppingCartOneMoreInput input) {
        ShoppingCartOneMoreVO shoppingCartOneMoreVO = new ShoppingCartOneMoreVO();
        List<OrderItemVO> orderItemVOS = orderItemMapper.selectOrderItemVO(input.getOrderNo());
        if (CollectionUtils.isEmpty(orderItemVOS)) {
            return shoppingCartOneMoreVO;
        }

        //普通商品--过滤精准送sku（老逻辑）
        List<OrderItemVO> normalItems = orderItemVOS.stream().filter(item ->
                !Global.TIME_FRAME_FEE_SKU.equals(item.getSku())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(normalItems)) {
            return shoppingCartOneMoreVO;
        }

        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        Long mId = RequestHolder.getMId();
        Long accountId = RequestHolder.getAccountId();
        Long contactId = input.getContactId();
        String parentSku = "0";
        if (Objects.isNull(contactId)) {
            Contact contact = contactMapper.selectIsDefaultByMid(mId);
            contactId = contact.getContactId();
        } else {
            Contact contact = contactMapper.selectByPrimaryKey(contactId);
            if (Objects.isNull(contact)) {
                throw new BizException("当前地址信息异常！");
            }
        }

        //批量获取库存信息
        List<String> skuList = normalItems.stream().map(OrderItemVO::getSku).collect(Collectors.toList());
        Map<String, AreaStoreQueryRes> storeQueryResMap = getAreaStoreQueryRes(contactId, mId, skuList);
        if (CollectionUtils.isEmpty(storeQueryResMap)) {
            List<String> pdNames = normalItems.stream().map(OrderItemVO::getPdName).distinct().collect(Collectors.toList());
            shoppingCartOneMoreVO.setPdNames(pdNames);
            return shoppingCartOneMoreVO;
        }

        //sku信息批量查询
        List<AreaSku> areaSkus = areaSkuMapper.selectAreaSkuByAreaNoAndSkus(merchantSubject.getArea().getAreaNo(), skuList);
        if (CollectionUtils.isEmpty(areaSkus)) {
            List<String> pdNames = normalItems.stream().map(OrderItemVO::getPdName).distinct().collect(Collectors.toList());
            shoppingCartOneMoreVO.setPdNames(pdNames);
            return shoppingCartOneMoreVO;
        }
        Map<String, AreaSku> areaSkuMap = areaSkus.stream().collect(Collectors.toMap(AreaSku::getSku, Function.identity(), (p1, p2) -> p2));

        List<Long> ids = new ArrayList<>();
        List<String> pdNames = new ArrayList<>();
        for (OrderItemVO itemVO : normalItems) {
            String sku = itemVO.getSku();
            Integer quantity = itemVO.getAmount();
            if (RequestHolder.isMajor() && RequestHolder.getSkuShow() == 1) { //NOSONAR
                //取该大客户的该sku是否在生效时间内
                int i = majorPriceMapper.selectByMid(mId, LocalDateTime.now(), sku, RequestHolder.getMerchantAreaNo());
                if (i == 0) {
                    pdNames.add(itemVO.getPdName());
                    continue;
                }
            }

            //sku信息校验--上下架、限购数量
            AreaSku areaSku = areaSkuMap.get(sku);
            if (Objects.isNull(areaSku) || !areaSku.getOnSale()) { //NOSONAR
                pdNames.add(itemVO.getPdName());
                continue;
            }

            //获取库存信息 假如地址ID为空则获取默认地址ID
            if (Objects.isNull(storeQueryResMap.get(sku)) || Objects.isNull(storeQueryResMap.get(sku).getOnlineQuantity())) {
                pdNames.add(itemVO.getPdName());
                continue;
            }
            AreaStoreQueryRes areaStoreQueryRes = storeQueryResMap.get(sku);
            Integer onlineQuantity = areaStoreQueryRes.getOnlineQuantity();
            if (onlineQuantity <= 0) {
                pdNames.add(itemVO.getPdName());
                continue;
            }

            //获取购物车中sku存在的数量信息（包括普通和换购）
            Integer sumQuantity = shoppingCartMapper.getSumQuantity(mId, accountId, sku, parentSku);
            if (Objects.nonNull(sumQuantity) && sumQuantity > 0) {
                if (quantity + sumQuantity > onlineQuantity) {
                    pdNames.add(itemVO.getPdName());
                    quantity = onlineQuantity - sumQuantity;
                    if (quantity <= 0) {
                        continue;
                    }
                }
            } else {
                if (quantity > onlineQuantity) {
                    pdNames.add(itemVO.getPdName());
                    quantity = onlineQuantity;
                }
            }

            //组装数据新增或更新购物车数据
            ShoppingCart shoppingCart = new ShoppingCart();
            shoppingCart.setSku(sku);
            shoppingCart.setProductType(ProductTypeEnum.NORMAL.getCode());
            shoppingCart.setMId(mId);
            shoppingCart.setAccountId(accountId);
            shoppingCart.setQuantity(quantity);
            shoppingCart.setParentSku(parentSku);
            ShoppingCart cart = shoppingCartMapper.selectByEntity(shoppingCart);
            if (Objects.isNull(cart)) {
                shoppingCartMapper.insertSelective(shoppingCart);
                ids.add(shoppingCart.getId());
            } else {
                quantity += cart.getQuantity();
                shoppingCartMapper.updateQuantityById(cart.getId(), quantity);
                ids.add(cart.getId());
            }
        }
        pdNames = pdNames.stream().distinct().collect(Collectors.toList());
        shoppingCartOneMoreVO.setIds(ids);
        shoppingCartOneMoreVO.setPdNames(pdNames);
        return shoppingCartOneMoreVO;
    }

    /**
     * @description: 大客户：清空定量客户不在报价单里的sku 单店：清空大客户专享商品 避免数据异常
     * @author: lzh
     * @date: 2023/10/11 18:12
     * @param: [merchantSubject, area, shoppingCartVOS]
     * @return: void
     **/
    private void clearMajorPrice(MerchantSubject merchantSubject, Area area, List<ShoppingCartVO> shoppingCartVOS) {
        List<Long> delete = new ArrayList<>(shoppingCartVOS.size());
        if (RequestHolder.isMajor() && Objects.equals(merchantSubject.getSkuShow(), MajorSkuShowEnum.PART.getCode())) { //NOSONAR
            //查询用户类目报价单是否为空
            Boolean majorCategoryFlag = majorPriceService.queryMajorCategory(merchantSubject.getAdminId(), merchantSubject.getDirect(), area.getAreaNo(),null);

            Iterator<ShoppingCartVO> iterator = shoppingCartVOS.iterator();
            while (iterator.hasNext()) {
                ShoppingCartVO shoppingCartDTO = iterator.next();
                if (!majorPriceService.queryMajorPrice(merchantSubject.getAdminId(), merchantSubject.getDirect(), area.getAreaNo(), shoppingCartDTO.getSku(), majorCategoryFlag, shoppingCartDTO.getCategoryId())) { //NOSONAR
                    delete.add(shoppingCartDTO.getId());
                    iterator.remove();
                }
            }
        } else if (!RequestHolder.isMajor()){ //NOSONAR
            Iterator<ShoppingCartVO> iterator = shoppingCartVOS.iterator();
            while (iterator.hasNext()) {
                ShoppingCartVO shoppingCartDTO = iterator.next();
                if (shoppingCartDTO.getMType().equals(CommonStatus.YES.getCode())) {
                    delete.add(shoppingCartDTO.getId());
                    iterator.remove();
                }
            }
        }

        if (!CollectionUtils.isEmpty(delete)) {
            shoppingCartMapper.batchDelete(delete);
        }
    }

    /**
     * @description: 获取库存信息
     * @author: lzh
     * @date: 2023/10/11 15:21
     * @param: [contactId, mId, skus]
     * @return: java.util.List<net.summerfarm.mall.service.facade.dto.AreaStoreQueryRes>
     **/
    private Map<String, AreaStoreQueryRes> getAreaStoreQueryRes(Long contactId, Long mId, List<String> skus) {
        if (Objects.isNull(contactId)) {
            Contact contact = contactMapper.selectIsDefaultByMid(mId);
            contactId = contact.getContactId();
        }  else {
            Contact contact = contactMapper.selectByPrimaryKey(contactId);
            if (Objects.isNull(contact)) {
                throw new BizException("当前地址信息异常！");
            }
        }
        AreaStoreQueryReq areaStoreQueryReq = new AreaStoreQueryReq();
        areaStoreQueryReq.setContactId(contactId);
        areaStoreQueryReq.setSkuCodeList(skus);
        areaStoreQueryReq.setMId(mId);
        areaStoreQueryReq.setSource(DistOrderSourceEnum.getDistOrderSource(RequestHolder.getBusinessLine()));
        Map<String, AreaStoreQueryRes> storeQueryResMap = wmsAreaStoreFacade.getInfo(areaStoreQueryReq);
        return storeQueryResMap;
    }

    /**
     * @description: 查询换购商品信息
     * @author: lzh
     * @date: 2023/10/11 17:28
     * @param: [shoppingCartVO]
     * @return: net.summerfarm.mall.model.vo.ProductInfoVO
     **/
    private ProductInfoVO handleExchangeItemPrice(ShoppingCartVO shoppingCartVO) {
        //活动范围id
        Long bizId = shoppingCartVO.getBizId();
        if (bizId == null) {
            return null;
        }
        OrderItemVO orderItemVO = new OrderItemVO();
        orderItemVO.setSku(shoppingCartVO.getSku());
        ProductInfoVO productInfoVO = exchangeBuyServiceHelper.checkAndGetProductInfo(orderItemVO,
                bizId, true);
        if (productInfoVO == null) {
            return null;
        }
        shoppingCartVO.setSalePrice(BigDecimal.valueOf(productInfoVO.getSalePrice()));
        shoppingCartVO.setLimitedQuantity(productInfoVO.getLimitedQuantity());

        //参与活动商品不展示阶梯价
        shoppingCartVO.setLadderPrice(null);
        return productInfoVO;
    }

    /**
     * @description: 大客户购物车价格信息处理
     * @author: lzh
     * @date: 2023/10/16 18:02
     * @param: [merchantSubject, shoppingCartVO]
     * @return: void
     **/
    private void majorPrice(MerchantSubject merchantSubject, ShoppingCartVO shoppingCartVO) {
        //大客户无阶梯价
        shoppingCartVO.setLadderPrice(null);
        if (shoppingCartVO.getPrice() != null) {
            // 先取报价单价格
            shoppingCartVO.setSalePrice(shoppingCartVO.getPrice());
        } else if (shoppingCartVO.getActivityOriginPrice() != null) {
            // 再取活动原价
            shoppingCartVO.setSalePrice(shoppingCartVO.getActivityOriginPrice());
        }
        boolean isPrePaySku = false;
        BigDecimal originalPrice = new BigDecimal(BigInteger.ZERO);
        Integer actualQuantity = shoppingCartVO.getQuantity();

        //预付支持大客户账期
        Map<String, Integer> prepayAmountMap = prepayInventoryService.queryUsableMap(merchantSubject.getAdminId(), null);
        List<PrepayInventory> prepayInventories = prepayInventoryService.queryUsableList(merchantSubject.getAdminId(), null);
        Map<String, List<PrepayInventory>> collect = prepayInventories.stream().collect(Collectors.groupingBy(PrepayInventory::getSku));

        //校验大客户锁量
        if (!CollectionUtils.isEmpty(prepayAmountMap) && prepayAmountMap.get(shoppingCartVO.getSku()) != null) {
            //设置价格
            List<PrepayInventory> prepayInventoryList = collect.get((shoppingCartVO.getSku()));
            for (PrepayInventory prepayInventory : prepayInventoryList) {
                Integer prepayAmount = prepayInventory.getPrepayAmount();
                Integer usedAmount = prepayInventory.getUsedAmount();
                int canUseAmount = prepayAmount - usedAmount;

                //预付款单价
                BigDecimal unitPrice = prepayInventory.getPrepayPrice().divide(BigDecimal.valueOf(prepayAmount), 2, BigDecimal.ROUND_HALF_EVEN);
                if (actualQuantity <= canUseAmount) {
                    originalPrice = originalPrice.add(unitPrice.multiply(BigDecimal.valueOf(actualQuantity)));
                    break;
                }
                originalPrice = originalPrice.add(unitPrice.multiply(BigDecimal.valueOf(canUseAmount)));
                actualQuantity = actualQuantity - canUseAmount;
            }
            originalPrice = originalPrice.divide(BigDecimal.valueOf(shoppingCartVO.getQuantity()), 2, BigDecimal.ROUND_HALF_EVEN);
            Integer prePayAmount = prepayAmountMap.get(shoppingCartVO.getSku());
            isPrePaySku = true;
            shoppingCartVO.setPrePayAmount(prePayAmount);
        }

        //账期且是预付(大客户锁量)商品单价为0
        if (merchantSubject.getDirect().equals(MajorDirectEnum.PERIOD.getType()) && isPrePaySku) {
            shoppingCartVO.setSalePrice(BigDecimal.ZERO);
        }

        //现结的有价格策略
        if (merchantSubject.getDirect().equals(MajorDirectEnum.CASH.getType())) {
            //先判断 sku
            MajorRebate select = new MajorRebate();
            select.setSku(shoppingCartVO.getSku());
            select.setAreaNo(merchantSubject.getArea().getAreaNo());
            select.setAdminId(merchantSubject.getAdminId());
            select.setCate(2);
            MajorRebate majorRebate = majorRebateMapper.selectOne(select);
            if (majorRebate == null) {
                //先结无返点 预付(大客户锁量)商品 实付为0
                if (isPrePaySku) {
                    shoppingCartVO.setSalePrice(BigDecimal.ZERO);
                }
                select.setCate(1);
                select.setSku(null);
                Inventory inventoryVO = inventoryMapper.selectBySku(shoppingCartVO.getSku());
                Products products = productsMapper.selectByPrimaryKey(inventoryVO.getPdId());
                select.setSku(String.valueOf(products.getCategoryId()));
                majorRebate = majorRebateMapper.selectOne(select);
            }
            if (majorRebate != null) {
                shoppingCartVO.setRebateNumber(majorRebate.getNumber());
                shoppingCartVO.setRebateType(majorRebate.getType());
                shoppingCartVO.setMPrice(shoppingCartVO.getSalePrice().doubleValue());

                //定额钱
                if (majorRebate.getType() == 1) {
                    shoppingCartVO.setSalePrice(new BigDecimal(shoppingCartVO.getMPrice() + majorRebate.getNumber()));
                } else if (majorRebate.getType() == 2) {
                    Double calprice = shoppingCartVO.getMPrice() * (1 + shoppingCartVO.getRebateNumber() / 100);
                    shoppingCartVO.setSalePrice(new BigDecimal(calprice).setScale(2,BigDecimal.ROUND_HALF_UP));
                }

                //预付(大客户锁量)商品，且存在返点金额
                if (isPrePaySku) {
                    BigDecimal rebatePrice = BigDecimal.valueOf(majorRebate.getNumber());
                    if (majorRebate.getType() == 2) {
                        Double calePrice = Math.ceil(shoppingCartVO.getMPrice() * (shoppingCartVO.getRebateNumber() / 100) * 10) / 10;
                        rebatePrice = BigDecimal.valueOf(calePrice);
                    }
                    shoppingCartVO.setSalePrice(rebatePrice);
                    originalPrice = rebatePrice;
                }
            }
        }

        //非预付商品取售卖价
        if (!isPrePaySku) {
            originalPrice = shoppingCartVO.getSalePrice();
        }

        //大客户低价监控逻辑
        if (shoppingCartVO.getPriceType() != null && MajorPriceTypeEnum.MALL_RELATED.contains (shoppingCartVO.getPriceType())) {
            // 如果是商城价先判断是否开启了低价监控：开启-走特价，不开启-走原价
            BigDecimal prepaySetPrice = majorPriceService.getMajorMallPrice(merchantSubject.getArea().getAreaNo(), shoppingCartVO.getSku(), merchantSubject.getAdminId(), merchantSubject.getMerchantId(), false,shoppingCartVO.getPriceAdjustmentValue(),shoppingCartVO.getPriceType(),null);
            shoppingCartVO.setPrepaySetPrice(prepaySetPrice);
        }
        shoppingCartVO.setPrice(shoppingCartVO.getSalePrice());
        shoppingCartVO.setOriginalPrice(originalPrice);
    }

    /**
     * @description: 校验购物车商品加购数量--目前可能存在的问题是假如只剩下换购商品调整的话（历史逻辑会调整换购和普通的加购数量都为剩余库存）
     * （目前商城页面换购不按照售卖规格进行添加删除，但是后端调整是按照售卖规格调整）不按照售卖规格调整下单会校验是否为整数倍 优化就是换购商品的售卖规格必须为1
     * @author: lzh
     * @date: 2023/10/17 11:22
     * @param: [deleteIds, storeQueryResMap, listMap, updateQuantityList]
     * @return: void
     **/
    private void checkQuantity(List<Long> deleteIds, Map<String, AreaStoreQueryRes> storeQueryResMap,
                               Map<String, List<ShoppingCartVO>> listMap, Map<Long, ShoppingCart> updateMap) {
        for (String sku : listMap.keySet()) {
            List<ShoppingCartVO> shoppingCartVOList = listMap.get(sku);
            if (CollectionUtils.isEmpty(shoppingCartVOList)) {
                continue;
            }
            AreaStoreQueryRes areaStoreQueryRes = storeQueryResMap.get(sku);
            if (Objects.isNull(areaStoreQueryRes)) {
                //设置为失效中--兜底逻辑
                shoppingCartVOList.stream().forEach(e -> e.setEffectiveness(CommonStatus.YES.getCode()));
                continue;
            }

            //设置履约时效、在线库存
            Integer onlineQuantity = areaStoreQueryRes.getOnlineQuantity();
            shoppingCartVOList.stream().forEach(e -> {
                e.setDeliveryTime(areaStoreQueryRes.getDeliveryTime());
                e.setOnlineQuantity(Objects.isNull(onlineQuantity) ? 0 : onlineQuantity);
            });

            //库存校验
            if (Objects.isNull(onlineQuantity) || onlineQuantity <= 0) {
                continue;
            }

            //假如当前sku加购数量小于在线库存数量则 调整购物车数量（假如只存在一种商品类型则直接调整，假如存在两种类型则需要先调整换购类型-假如换购类型商品库存为0则需要删除）
            int sum = shoppingCartVOList.stream().mapToInt(ShoppingCartVO::getQuantity).sum();
            if (onlineQuantity.compareTo(sum) < 0) {
                Map<Integer, List<ShoppingCartVO>> map = shoppingCartVOList.stream().filter(e ->
                        !Objects.equals(e.getProductType(), ProductTypeEnum.GIFT.getCode()))
                        .collect(Collectors.groupingBy(ShoppingCartVO::getProductType));
                ShoppingCartVO shoppingCartVO = shoppingCartVOList.get(0);

                //获取最小起购数量
                int minSale = shoppingCartVO.getBaseSaleUnit() * shoppingCartVO.getBaseSaleQuantity();
                if (map.size() == 1) {
                    //调整加购数量
                    setQuantity(onlineQuantity, minSale, shoppingCartVO.getBaseSaleUnit(), shoppingCartVO);
                    shoppingCartVO.setPopoverFlag(Boolean.TRUE);
                    setUpdateMap(updateMap, shoppingCartVO);
                } else {
                    //先扣减换购活动商品加购数量 注意：目前只考虑普通和加购商品类型
                    List<ShoppingCartVO> exchangeList = map.get(ProductTypeEnum.EXCHANGE.getCode());
                    List<ShoppingCartVO> normalList = map.get(ProductTypeEnum.NORMAL.getCode());
                    if (CollectionUtils.isEmpty(exchangeList) || CollectionUtils.isEmpty(normalList)) {
                        continue;
                    }
                    ShoppingCartVO exchange = exchangeList.get(0);
                    ShoppingCartVO normal = normalList.get(0);

                    //普通类型加购数量大于在线库存直接调整普通加购商品数量再删除换购类型加购数量
                    if (normal.getQuantity().compareTo(onlineQuantity) > 0) {
                        deleteIds.add(exchange.getId());

                        //调整加购数量
                        setQuantity(onlineQuantity, minSale, shoppingCartVO.getBaseSaleUnit(), normal);
                        normal.setPopoverFlag(Boolean.TRUE);
                        setUpdateMap(updateMap, normal);
                    } else if (normal.getQuantity().compareTo(onlineQuantity) == 0){
                        //普通类型加购数量等于在线库存直接删除换购类型商品
                        deleteIds.add(exchange.getId());
                    } else {
                        //剩余库存进行计算 =  在线库存 - 普通商品加购数量
                        Integer surplusQuantity = onlineQuantity - normal.getQuantity();

                        //调整加购数量
                        setQuantity(surplusQuantity, minSale, shoppingCartVO.getBaseSaleUnit(), exchange);
                        exchange.setPopoverFlag(Boolean.TRUE);
                        setUpdateMap(updateMap, exchange);
                    }
                }
            }
        }
    }

    /**
     * @description: 修改购物车数量-根据在线库存校验
     * @author: lzh
     * @date: 2023/11/7 18:41
     * @param: [onlineQuantity, minSale, baseSaleUnit, newShoppingCartVO]
     * @return: void
     **/
    private void setQuantity(Integer onlineQuantity, int minSale, Integer baseSaleUnit, ShoppingCartVO newShoppingCartVO) {
        Integer quantity = newShoppingCartVO.getQuantity();
        if (onlineQuantity > minSale && baseSaleUnit > 1) {
            //先计算出n（向上取整） 然后再计算可以加购的数量
            double x = quantity - onlineQuantity;
            double y = baseSaleUnit;
            int n = (int) Math.ceil(x/y);
            int afterQuantity = quantity - n * baseSaleUnit;
            newShoppingCartVO.setQuantity(afterQuantity >= minSale ? afterQuantity : minSale);
        } else {
            newShoppingCartVO.setQuantity(onlineQuantity);
        }
    }

    /**
     * @description: 获取配送规则
     * @author: lzh
     * @date: 2023/10/17 18:14
     * @param: [contactId, mId, area, shoppingCartVOS]
     * @return: void
     **/
    private void getDistributionRules(Long contactId, Long mId, Area area, List<ShoppingCartVO> shoppingCartVOS, Integer isEveryDayFlag) {
        Map<LocalDate, List<ShoppingCartVO>> dateListMap = shoppingCartVOS.stream().filter
                (e -> Objects.nonNull(e.getDeliveryTime())).collect(Collectors.groupingBy(ShoppingCartVO::getDeliveryTime));
        if (CollectionUtils.isEmpty(dateListMap)) {
            log.warn("ShoppingCartServiceImpl[]getDistributionRules[]dateListMap is null");
            return;
        }

        //查询配送规则
        DistributionRulesQueryDTO distributionRulesQueryDTO = new DistributionRulesQueryDTO();
        distributionRulesQueryDTO.setContactId(contactId);
        distributionRulesQueryDTO.setAreaNo(area.getAreaNo());
        if (RequestHolder.isMajor()) { //NOSONAR
            distributionRulesQueryDTO.setAdminId(RequestHolder.getAdminId().longValue());
        }
        DistributionRulesDTO distributionRulesDTO = distributionRulesService.getInfo(distributionRulesQueryDTO);
        if (Objects.isNull(distributionRulesDTO) || CollectionUtils.isEmpty(distributionRulesDTO.getRulesDTOS())) {
            shoppingCartVOS.stream().forEach(e -> e.setSatisfyFreeDelivery(CommonStatus.YES.getCode()));
            log.warn("ShoppingCartServiceImpl[]getDistributionRules[]distributionRulesService[]getInfo[] is null");
            return;
        }
        Map<Integer, List<RulesDTO>> map = distributionRulesDTO.getRulesDTOS().stream()
                .collect(Collectors.groupingBy(RulesDTO::getAgeing));

        //1、判断日配或者非日配 假如非日配获取T+N配送规则
        //2、假如为日配 则根据商品属性来判断 商品经销：T+1 商品代销不入仓：T+N
        if (isEveryDayFlag.equals(DeliveryEveryDayEnum.DAYCARE.getCode())) {
            for (LocalDate deliveryDate : dateListMap.keySet()) {
                //配送日当天是否已经存在履约单
                int delivery = deliveryPlanMapper.countByDeliveryTime(mId, contactId, deliveryDate);
                dateListMap.get(deliveryDate).stream().forEach(e -> {
                    if (InventoryEnums.SubType.SELF_NOT_INTO_WAREHOUSE.getSubType().equals(e.getSubType())) {
                        //T+N
                        List<RulesDTO> rulesDTOS = map.get(CommonStatus.YES.getCode());

                        //配送规则明细排序 全部 》 非乳制品 》 乳制品
                        sortCondition(rulesDTOS, delivery, e);
                    } else {
                        //T+1
                        List<RulesDTO> rulesDTOS = map.get(CommonStatus.NO.getCode());

                        //配送规则明细排序 全部 》 非乳制品 》 乳制品
                        sortCondition(rulesDTOS, delivery, e);
                    }
                });
            }
        } else if (isEveryDayFlag.equals(DeliveryEveryDayEnum.NON_DAYCARE.getCode())) {
            for (LocalDate deliveryDate : dateListMap.keySet()) {
                //配送日当天是否已经存在履约单
                int delivery = deliveryPlanMapper.countByDeliveryTime(mId, contactId, deliveryDate);

                //T+N
                List<RulesDTO> rulesDTOS = map.get(CommonStatus.YES.getCode());
                dateListMap.get(deliveryDate).stream().forEach(e -> {
                    //配送规则明细排序 全部 》 非乳制品 》 乳制品
                    sortCondition(rulesDTOS, delivery, e);
                });
            }
        } else {
            log.warn("ShoppingCartServiceImpl[]getDistributionRules[]isEveryDayFlag[]error!");
        }
    }

    /**
     * @description: 设置配送规则明细
     * @author: lzh
     * @date: 2023/10/18 18:28
     * @param: [rulesDTOS, e]
     * @return: void
     **/
    private void sortCondition(List<RulesDTO> rulesDTOS, int delivery, ShoppingCartVO e) {
        if (!CollectionUtils.isEmpty(rulesDTOS)) {
            e.setDeliveryFee(rulesDTOS.get(0).getDeliveryFee());
            e.setExpressFee(rulesDTOS.get(0).getExpressFee());
            if (delivery <= 0) {
                e.setSatisfyFreeDelivery(CommonStatus.NO.getCode());

                //配送规则明细排序 全部 》 非乳制品 》 乳制品
                List<ConditionsDTO> conditionsDTOS = rulesDTOS.get(0).getConditionsDTOS();
                if (CollectionUtils.isEmpty(conditionsDTOS)) {
                    e.setConditionsDTOS(null);
                } else {
                    List<ConditionsDTO> sortConditionsDTOS = new ArrayList<>(conditionsDTOS.size());
                    List<ConditionsDTO> all = conditionsDTOS.stream().filter(conditionsDTO -> conditionsDTO.getProductType()
                            .equals(DistributionRulesProductTypeEnum.ALL.getCode())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(all)) {
                        sortConditionsDTOS.addAll(all);
                    }
                    List<ConditionsDTO> nonDairy = conditionsDTOS.stream().filter(conditionsDTO -> conditionsDTO.getProductType()
                            .equals(DistributionRulesProductTypeEnum.NON_DAIRY.getCode())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(nonDairy)) {
                        sortConditionsDTOS.addAll(nonDairy);
                    }
                    List<ConditionsDTO> lactation = conditionsDTOS.stream().filter(conditionsDTO -> conditionsDTO.getProductType()
                            .equals(DistributionRulesProductTypeEnum.LACTATION.getCode())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(lactation)) {
                        sortConditionsDTOS.addAll(lactation);
                    }
                    e.setConditionsDTOS(sortConditionsDTOS);
                }
            } else {
                e.setSatisfyFreeDelivery(CommonStatus.YES.getCode());
            }
        }
    }


    /**
     * @description: 获取配送规则
     * @author: lzh
     * @date: 2023/10/17 18:14
     * @param: [contactId, mId, area, shoppingCartVOS]
     * @return: void
     **/
    private void getNewDistributionRules(Contact contact, Long mId, Area area, List<ShoppingCartVO> shoppingCartVOS, Integer isEveryDayFlag) {
        Map<LocalDate, List<ShoppingCartVO>> dateListMap = shoppingCartVOS.stream().filter
                (e -> Objects.nonNull(e.getDeliveryTime())).collect(Collectors.groupingBy(ShoppingCartVO::getDeliveryTime));
        if (CollectionUtils.isEmpty(dateListMap)) {
            log.warn("ShoppingCartServiceImpl[]getDistributionRules[]dateListMap is null");
            return;
        }

        //查询配送规则
        DeliveryFeeRuleReq deliveryFeeRuleReq = new DeliveryFeeRuleReq();
        deliveryFeeRuleReq.setContactId(contact.getContactId().intValue());
        deliveryFeeRuleReq.setAreaNo(area.getAreaNo());
        deliveryFeeRuleReq.setXmAdminId(Optional.ofNullable(RequestHolder.getAdminId()).orElse(null));
        deliveryFeeRuleReq.setAddress(contact.getAddress());
        deliveryFeeRuleReq.setCity(contact.getCity());
        deliveryFeeRuleReq.setArea(contact.getArea());
        deliveryFeeRuleReq.setProvince(contact.getProvince());
        List<DeliveryFeeRuleInfoDTO> deliveryFeeRuleInfoDTOS = distributionRulesFacade.queryDeliveryFeeRule(deliveryFeeRuleReq);
        if (CollectionUtils.isEmpty(deliveryFeeRuleInfoDTOS)) {
            log.warn("ShoppingCartServiceImpl[]getDistributionRules[]all deliveryFeeRuleInfoDTOS is null");
            shoppingCartVOS.stream().forEach(e -> e.setSatisfyFreeDelivery(CommonStatus.YES.getCode()));
            return;
        }

        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanService.getAllDeliveryPlanByMIdAndContactId(mId, contact.getContactId());
        Map<LocalDate, List<DeliveryPlanVO>> localDateListMap = deliveryPlanVOS.stream().collect(Collectors.groupingBy(DeliveryPlanVO::getDeliveryTime));

        Map<Integer, List<DeliveryFeeRuleInfoDTO>> listMap = deliveryFeeRuleInfoDTOS.stream().collect(Collectors.groupingBy(DeliveryFeeRuleInfoDTO::getAgeing));
        for (LocalDate deliveryDate : dateListMap.keySet()) {
            List<ShoppingCartVO> cartVOS = dateListMap.get(deliveryDate);
            if (CollectionUtils.isEmpty(cartVOS)) {
                continue;
            }
            isEveryDayFlag = cartVOS.get(0).getIsEveryDayFlag();

            //1、判断日配或者非日配 假如非日配获取T+N配送规则
            //2、假如为日配 则根据商品属性来判断 商品经销：T+1 商品代销不入仓：T+N
            if (isEveryDayFlag.equals(DeliveryEveryDayEnum.DAYCARE.getCode())) {
                boolean isSelfNotIntoWarehouse = cartVOS.stream().anyMatch(shoppingCartVO -> shoppingCartVO.getSubType().
                        equals(InventoryEnums.SubType.SELF_NOT_INTO_WAREHOUSE.getSubType()));
                isEveryDayFlag = isSelfNotIntoWarehouse ? DeliveryEveryDayEnum.NON_DAYCARE.getCode() : isEveryDayFlag;
            }

            if (!listMap.containsKey(isEveryDayFlag) || CollectionUtils.isEmpty(listMap.get(isEveryDayFlag))) {
                log.warn("ShoppingCartServiceImpl[]getDistributionRules[]deliveryFeeRuleInfoDTOS is null");
                cartVOS.stream().forEach(e -> e.setSatisfyFreeDelivery(CommonStatus.YES.getCode()));
                continue;
            }
            Integer finalIsEveryDayFlag = isEveryDayFlag;
            cartVOS.forEach(shoppingCartVO -> {
                shoppingCartVO.setDeliveryFeeRuleInfoDTOS(listMap.get(finalIsEveryDayFlag).get(0));
                if (!CollectionUtils.isEmpty(localDateListMap) && localDateListMap.containsKey(deliveryDate)) {
                    shoppingCartVO.setSatisfyFreeDelivery(CommonStatus.YES.getCode());
                } else {
                    shoppingCartVO.setSatisfyFreeDelivery(CommonStatus.NO.getCode());
                }
            });
        }
    }

    /**
     * @description: 设置兜底名称和图片、是否失效
     * @author: lzh
     * @date: 2023/10/19 18:39
     * @param: [shoppingCartVOS, updateQuantitys]
     * @return: void
     **/
    private void checkSalesMode(List<ShoppingCartVO> shoppingCartVOS, Map<Long, ShoppingCart> updateMap,Integer adminId, Integer areaNo,Integer direct) {
        //Set<Integer> xxsg = categoryService.getChildCategoryIdsByParentName ("新鲜水果");

        Map<String, Integer> mallShowMap = Collections.emptyMap ();
        if (RequestHolder.isMajor()){ //NOSONAR
            List<String> skus = shoppingCartVOS.stream ().map (ShoppingCartVO::getSku).collect (Collectors.toList ());
            mallShowMap = majorPriceService.queryMallShowMap (skus, adminId, areaNo, direct);
        }

        for (ShoppingCartVO e : shoppingCartVOS) {
            //设置兜底名称和图片
            e.resetSkuNameAndSkuPic();
            e.setPopoverFlag(Boolean.FALSE);
            e.setOverPurchase(CommonStatus.NO.getCode());
            if (Objects.equals(e.getOnSale(), OnSaleStatusEnum.DELIST.getCode())) {
                e.setEffectiveness(ShoppingCartEfficacyEnum.YES.getCode());
            } else {
                // 如果是大客户 且 报价单.门店可见该商品=否 则商品失效
                if (RequestHolder.isMajor() && MajorPriceMallShowEnum.HIDE.getCode ().equals (mallShowMap.get (e.getSku ()))){ //NOSONAR
                    e.setEffectiveness(ShoppingCartEfficacyEnum.YES.getCode());
                } else {
                    e.setEffectiveness (ShoppingCartEfficacyEnum.NO.getCode ());
                }
                //调整sku限购数量
                if (Objects.nonNull(e.getSalesMode()) && e.getSalesMode().equals(SalesModeEnum.YES.getCode())
                        && e.getLimitedQuantity().compareTo(e.getQuantity()) < 0) {
                    e.setQuantity(e.getLimitedQuantity());
                    e.setPopoverFlag(Boolean.TRUE);
                    setUpdateMap(updateMap, e);
                }
            }
            fillAvgInfo(e);
        }
    }
    private void fillAvgInfo(ShoppingCartVO e) {
        Set<Integer> xxsg = categoryService.getChildCategoryIdsByParentName ("新鲜水果");
        if(xxsg.contains (e.getCategoryId ())) {
            AvgInfoVO avgInfoVO = inventoryService.getAvgInfo(e.getCategoryId (),e.getWeight ());
            e.setAvgNumerator(avgInfoVO.getAvgNumerator ());
            e.setAvgUnit(avgInfoVO.getAvgUnit ());
        }
        if(ObjectUtil.isNotNull (e.getWeightNum ())){
            e.setWeightNum (e.getWeightNum ().multiply (new BigDecimal (2)));
            if(e.getWeightNum ().compareTo (new BigDecimal (1))<0){
                e.setWeightNum (null);
            }
        }
        if(ObjectUtil.isNotNull (e.getNetWeightNum ())){
            e.setNetWeightNum (e.getNetWeightNum ().multiply (new BigDecimal (2)));
            if(e.getNetWeightNum ().compareTo (new BigDecimal (1))<0){
                e.setNetWeightNum (null);
            }
        }
    }
    /**
     * @description: 更新map集合
     * @author: lzh
     * @date: 2023/11/1 11:41
     * @param: [updateMap, e]
     * @return: void
     **/
    private void setUpdateMap(Map<Long, ShoppingCart> updateMap, ShoppingCartVO shoppingCartVO) {
        if (Objects.nonNull(updateMap.get(shoppingCartVO.getId()))) {
            ShoppingCart update = updateMap.get(shoppingCartVO.getId());
            update.setQuantity(shoppingCartVO.getQuantity());
        } else {
            ShoppingCart update = new ShoppingCart();
            update.setId(shoppingCartVO.getId());
            update.setQuantity(shoppingCartVO.getQuantity());
            updateMap.put(shoppingCartVO.getId(), update);
        }
    }

    /**
     * @description: 校验活动信息以及获取活动相关信息
     * @author: lzh
     * @date: 2023/10/19 18:42
     * @param: [mId, area, shoppingCartVOS, deleteIds]
     * @return: void
     **/
    private void checkActivityInfo(Long mId, Area area, List<ShoppingCartVO> shoppingCartVOS,
                                   List<Long> deleteIds, Map<Long, ShoppingCart> updateMap) {
        //批量获取活动信息
        List<ActivitySkuDTO> activitySkuDTOS = new ArrayList<>(shoppingCartVOS.size());
        shoppingCartVOS.forEach(shoppingCartVO -> {
            ActivitySkuDTO activitySkuDTO = new ActivitySkuDTO();
            activitySkuDTO.setSku(shoppingCartVO.getSku());
            activitySkuDTO.setQuantity(shoppingCartVO.getQuantity());
            activitySkuDTOS.add(activitySkuDTO);
        });
        Map<String, ActivitySkuDetailDTO> activitySkuMap = activityService.listCacheByActivitySku(activitySkuDTOS, area.getAreaNo(), mId, Boolean.TRUE);

        Iterator<ShoppingCartVO> iterator = shoppingCartVOS.iterator();
        while (iterator.hasNext()) {
            ShoppingCartVO shoppingCartVO = iterator.next();
            if (Objects.equals(shoppingCartVO.getProductType(), ProductTypeEnum.EXCHANGE.getCode())) {
                //换购商品取消展示阶梯价
                shoppingCartVO.setLadderPrice(null);
                try {
                    //查询换购商品信息-是否失效 失效则剔除该商品
                    ProductInfoVO productInfoVO = this.handleExchangeItemPrice(shoppingCartVO);
                    if (Objects.isNull(productInfoVO)) {
                        deleteIds.add(shoppingCartVO.getId());
                        iterator.remove();
                    } else {
                        //换购活动限领数量修改
                        if (shoppingCartVO.getQuantity().compareTo(shoppingCartVO.getLimitedQuantity()) > 0) {
                            shoppingCartVO.setQuantity(shoppingCartVO.getLimitedQuantity());
                            shoppingCartVO.setPopoverFlag(Boolean.TRUE);
                            setUpdateMap(updateMap, shoppingCartVO);
                        }
                    }
                } catch (Exception e) {
                    log.warn("checkActivityInfo[]getAll[]handleExchangeItemPrice[]error cause:{}", JSON.toJSONString(e));
                    deleteIds.add(shoppingCartVO.getId());
                    iterator.remove();
                }
            } else {
                //其他活动商品处理
                try {
                    ActivitySkuDetailDTO activitySku = null;
                    if (!CollectionUtils.isEmpty(activitySkuMap) && activitySkuMap.containsKey(shoppingCartVO.getSku())) {
                        activitySku = activitySkuMap.get(shoppingCartVO.getSku());
                    }
                    if (activitySku != null) {
                        shoppingCartVO.setActivityLadderPrice(activitySku.getLadderPrice());
                        if (activitySku.getActivityPrice() != null) {
                            Integer limitQuantity = activityService.getActivityLimitQuantity(activitySku);
                            if (limitQuantity > 0) {
                                shoppingCartVO.setSalePrice(activitySku.getActivityPrice());
                                shoppingCartVO.setRemainingQuantity(limitQuantity);
                            }
                        }
                        shoppingCartVO.setActivityLimitedQuantity(activitySku.getLimitQuantity());
                    }
                } catch (Exception e) {
                    log.warn("checkActivityInfo[]getAll[]getActivitySku[]error cause:{}", JSON.toJSONString(e));
                }
            }
        }
    }

    /**
     * @description: 购物车数量调整--对商品售卖规格调整过的普通商品进行处理，修改了历史遗留逻辑（换购数量小于起售量的话会调整为起售量）
     * @author: lzh
     * @date: 2023/10/26 15:05
     * @param: [storeQueryResMap, shoppingCartVOS, updateQuantityList]
     * @return: void
     **/
    private void checkBaseSaleUnit(Map<String, AreaStoreQueryRes> storeQueryResMap,
                                   List<ShoppingCartVO> shoppingCartVOS, Map<Long, ShoppingCart> updateMap) {
        for (ShoppingCartVO shoppingCartVO : shoppingCartVOS) {
            if (!shoppingCartVO.getProductType().equals(ProductTypeEnum.NORMAL.getCode())) {
                continue;
            }
            AreaStoreQueryRes queryRes = storeQueryResMap.get(shoppingCartVO.getSku());
            if (Objects.isNull(queryRes) || Objects.isNull(queryRes.getOnlineQuantity())
                    || queryRes.getOnlineQuantity() <= 0) {
                continue;
            }
            Integer onlineQuantity = queryRes.getOnlineQuantity();

            //调整最小加购数量-防止sku最小起售数量变化
            int minSale = shoppingCartVO.getBaseSaleUnit() * shoppingCartVO.getBaseSaleQuantity();
            if (shoppingCartVO.getQuantity() < minSale && onlineQuantity >= minSale) {
                shoppingCartVO.setQuantity(minSale);
                shoppingCartVO.setPopoverFlag(Boolean.TRUE);
                setUpdateMap(updateMap, shoppingCartVO);
            }

            //假如售卖规格大于1 并且 加购数量求余售卖规格大于0 就需要调整加购数量
            Integer quantity = shoppingCartVO.getQuantity();
            Integer baseSaleUnit = shoppingCartVO.getBaseSaleUnit();
            if (shoppingCartVO.getBaseSaleUnit() > 1 && quantity % baseSaleUnit > 0) {
                int remainder  = quantity % baseSaleUnit;
                Integer afterQuantity = quantity;
                if (quantity - remainder + baseSaleUnit > onlineQuantity) {
                    if (baseSaleUnit > onlineQuantity) {
                        afterQuantity = onlineQuantity;
                    } else {
                        afterQuantity -= remainder;
                    }
                } else {
                    afterQuantity = quantity - remainder + baseSaleUnit;
                }
                if (afterQuantity.compareTo(quantity) == 0) {
                    continue;
                }
                shoppingCartVO.setQuantity(afterQuantity);
                shoppingCartVO.setPopoverFlag(Boolean.TRUE);
                setUpdateMap(updateMap, shoppingCartVO);
            }
        }
    }

    /**
     * @description: 检测是否超出限购--针对日限购，目前可能存在的问题是假如修改了限购数量
     * 但是假如修改数量不为整数倍下单会报异常（历史遗留问题） 优化--针对日限购商品售卖规格必须为1
     * @author: lzh
     * @date: 2023/10/31 18:41
     * @param: [mId, area, shoppingCartVOS, updateQuantityList]
     * @return: void
     **/
    private void checkOverPurchase(Long mId, Area area, LocalTime closeTime,
                                   List<ShoppingCartVO> shoppingCartVOS, Map<Long, ShoppingCart> updateMap) {
        //查询当日已经下单数量
        if (Objects.isNull(closeTime)) {
            return;
        }
        LocalDateTime startTime = Global.getStartTime(closeTime);
        LocalDateTime endTime = startTime.plusDays(1);
        List<LimitedSaleVO> voList = orderItemMapper.selectDayLimiteds(mId,
                DateUtils.localDateTime2Date(startTime), DateUtils.localDateTime2Date(endTime), area.getAreaNo());
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        Map<String, LimitedSaleVO> saleVOMap = voList.stream().collect(Collectors.
                toMap(LimitedSaleVO::getSku, Function.identity(), (p1, p2) -> p2));

        for (ShoppingCartVO shoppingCartVO : shoppingCartVOS) {
            if (!Objects.equals(SalesModeEnum.YES.getCode(), shoppingCartVO.getSalesMode())) {
                continue;
            }

            LimitedSaleVO limitedSaleVO = saleVOMap.get(shoppingCartVO.getSku());
            if (Objects.isNull(limitedSaleVO)) {
                continue;
            }

            //获取当前还可以购买数量假如小于等于0则不处理
            int usableQuantity = shoppingCartVO.getLimitedQuantity() - limitedSaleVO.getQuantity();
            if (usableQuantity <= 0) {
                shoppingCartVO.setOverPurchase(CommonStatus.YES.getCode());
            } else {
                if (usableQuantity >= shoppingCartVO.getQuantity()) {
                    continue;
                }
                shoppingCartVO.setQuantity(usableQuantity);
                shoppingCartVO.setPopoverFlag(Boolean.TRUE);
                shoppingCartVO.setOverPurchase(CommonStatus.NO.getCode());
                setUpdateMap(updateMap, shoppingCartVO);
            }
        }
    }

    /**
     * @description: 大客户价格处理以及普通用户加购上限校验
     * @author: lzh
     * @date: 2023/11/1 17:38
     * @param: [merchantSubject, shoppingCartVOS, updateMap]
     * @return: void
     **/
    private void majorPriceAndPurchaseCeiling(MerchantSubject merchantSubject, List<ShoppingCartVO> shoppingCartVOS, Map<Long, ShoppingCart> updateMap) {
        for (ShoppingCartVO shoppingCartVO : shoppingCartVOS) {
            //获取sku加购上限，为空则不限制,大客户不限制
            if (!RequestHolder.isMajor()) { //NOSONAR
                Integer purchaseCeiling = inventoryService.getPurchaseCeiling(shoppingCartVO.getSku(), shoppingCartVO.getOnlineQuantity(), RequestHolder.getMId());
                if (!ObjectUtils.isEmpty(purchaseCeiling)){
                    if (shoppingCartVO.getQuantity().compareTo(purchaseCeiling) > 0){
                        shoppingCartVO.setQuantity(purchaseCeiling);
                        shoppingCartVO.setPopoverFlag(Boolean.TRUE);
                        setUpdateMap(updateMap, shoppingCartVO);
                    }
                    shoppingCartVO.setPurchaseCeiling(purchaseCeiling);
                }
            } else {
                //大客户处理
                majorPrice(merchantSubject, shoppingCartVO);
            }
        }
    }

    /**
     * @description: 更新数据库数量
     * @author: lzh
     * @date: 2023/11/1 11:47
     * @param: [updateMap]
     * @return: void
     **/
    private void updateQuantityByMap(Map<Long, ShoppingCart> updateMap) {
        if (CollectionUtils.isEmpty(updateMap)) {
            return;
        }

        List<ShoppingCart> shoppingCarts = new ArrayList<>(updateMap.values());
        if (CollectionUtils.isEmpty(shoppingCarts)) {
            return;
        }
        shoppingCartMapper.batchUpdateQuantity(shoppingCarts);
    }


    /**
     * @description: 获取价格隐藏状态
     * @param shoppingCartVOS
     */
    private void getMarketControlPriceHide(List<ShoppingCartVO> shoppingCartVOS) {
        if (CollectionUtils.isEmpty(shoppingCartVOS)) {
            return;
        }

        //获取全部控价品信息
        Map<String, MarketPriceControlProducts> controlProductsMap = marketPriceControlProductsService.selectAllControlProductsByCache();
        shoppingCartVOS.forEach(shoppingCartVO -> {
            if (CollectionUtils.isEmpty(controlProductsMap) || !controlProductsMap.containsKey(shoppingCartVO.getSku())) {
                return;
            }
            MarketPriceControlProducts marketPriceControlProducts = controlProductsMap.get(shoppingCartVO.getSku());
            if (marketPriceControlProducts != null) {
                shoppingCartVO.setPriceHide(marketPriceControlProducts.getPriceHide());
                shoppingCartVO.setFacePriceHide(marketPriceControlProducts.getFacePriceHide());
            }
        });
    }


    private List<ShoppingCartVO> sortAndGroup(List<ShoppingCartVO> listA, List<Long> listB) {
        if(CollectionUtil.isEmpty(listB)) {
            return listA;
        }
        // 将 pdId 在 listB 中的元素加入列表，按 id 降序排列
        List<ShoppingCartVO> inB = listA.stream()
                .filter(vo -> listB.contains(vo.getPdId()))
                .collect(Collectors.toList());

        if(CollectionUtil.isEmpty(inB)) {
            return listA;
        }

        // 将 pdId 不在 listB 中的元素加入列表，按 id 降序排列
        List<ShoppingCartVO> notInB = listA.stream()
                .filter(vo -> !listB.contains(vo.getPdId()))
                .collect(Collectors.toList());


        Map<Long, List<ShoppingCartVO>> groupedAndSorted = inB.stream()
                .collect(Collectors.groupingBy(
                        ShoppingCartVO::getPdId, // 按 pdId 分组
                        Collectors.collectingAndThen(
                                Collectors.toList(), // 收集到列表
                                list -> list.stream() // 将列表转换为流
                                        .sorted(Comparator.comparing(ShoppingCartVO::getId).reversed()) // 按 id 降序排序
                                        .collect(Collectors.toList()) // 重新收集为列表
                        )
                ));

        // 从每个组中选择 id 最大的 ShoppingCartVO
        List<ShoppingCartVO> maxIdItems = groupedAndSorted.values().stream()
                .map(group -> group.get(0)) // 获取每个组中的第一个元素（id 最大的）
                .collect(Collectors.toList());
        notInB.addAll(maxIdItems);

        List<ShoppingCartVO> sortedList = notInB.stream()
                .sorted(Comparator.comparing(ShoppingCartVO::getId).reversed())  // id 降序
                .collect(Collectors.toList());

        // 将排序的代表替换为原本的数据
        List<ShoppingCartVO> resultList = new ArrayList<>();
        sortedList.forEach(vo -> {
            List<ShoppingCartVO> shoppingCartVOS = groupedAndSorted.get(vo.getPdId());
            if(CollectionUtil.isNotEmpty(shoppingCartVOS)) {
                resultList.addAll(shoppingCartVOS);
            } else {
                resultList.add(vo);
            }
        });
        return resultList;
    }
}
