package net.summerfarm.mall.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.Cache;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.mapper.ProductsPropertyValueMapper;
import net.summerfarm.mall.model.vo.ProductsPropertyValueVO;
import net.summerfarm.mall.service.ProductsPropertyValueService;

/**
 * 商品属性值服务实现类
 */
@Component
@Slf4j
public class ProductsPropertyValueServiceImpl implements ProductsPropertyValueService {

    @Autowired
    private ProductsPropertyValueMapper productsPropertyValueMapper;

    private Cache<Long, List<ProductsPropertyValueVO>> productsPropertyValueCache = CacheBuilder.newBuilder()
            .maximumSize(2000)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    /**
     * 根据商品id列表获取商品属性值映射
     * 
     * @param productIds 商品id列表
     * @return 商品id和属性值列表的映射
     */
    @Override
    public Map<Long, List<ProductsPropertyValueVO>> getProductsPropertyValueMap(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.EMPTY_MAP;
        }
        Map<Long, List<ProductsPropertyValueVO>> result = new HashMap<>();

        List<ProductsPropertyValueVO> productsPropertyValueVOs = productsPropertyValueMapper.selectByPdIds(productIds);
        if (CollectionUtils.isNotEmpty(productsPropertyValueVOs)) {
            for (ProductsPropertyValueVO productsPropertyValueVO : productsPropertyValueVOs) {
                List<ProductsPropertyValueVO> list = result.computeIfAbsent(productsPropertyValueVO.getPdId(), k -> new ArrayList<>());
                list.add(productsPropertyValueVO);
                result.put(productsPropertyValueVO.getPdId(), list);                
            }
        }
        if (MapUtils.isNotEmpty(result)) {
            productsPropertyValueCache.putAll(result);
        }
        return result;
    }

    /**
     * 根据商品id列表从缓存获取商品属性值映射
     * 
     * @param productIds 商品id列表
     * @return 商品id和属性值列表的映射
     */
    @Override
    public Map<Long, List<ProductsPropertyValueVO>> getProductsPropertyValueCache(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.EMPTY_MAP;
        }
        Map<Long, List<ProductsPropertyValueVO>> result = new HashMap<>();
        List<Long> missIds = new ArrayList<>();
        for (Long productId : productIds) {
            List<ProductsPropertyValueVO> productsPropertyValueVOS = productsPropertyValueCache
                    .getIfPresent(productId);
            if (CollectionUtils.isNotEmpty(productsPropertyValueVOS)) {
                // 创建防御性副本，避免多线程并发修改同一个List实例
                result.put(productId, new ArrayList<>(productsPropertyValueVOS));
            } else {
                missIds.add(productId);
            }
        }

        if (CollectionUtils.isNotEmpty(missIds)) {
            log.info("missing in cache:{}", missIds);
            Map<Long, List<ProductsPropertyValueVO>> missingProductsPropertyValueVOs = getProductsPropertyValueMap(
                    missIds);
            if (MapUtils.isNotEmpty(missingProductsPropertyValueVOs)) {
                result.putAll(missingProductsPropertyValueVOs);
            }
        } else {
            log.info("read all from cache:{}", productIds);
        }
        return result;
    }
}
