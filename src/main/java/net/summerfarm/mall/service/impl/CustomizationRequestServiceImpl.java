package net.summerfarm.mall.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.mapper.CustomizationRequestMapper;
import net.summerfarm.mall.model.domain.CustomizationRequest;
import net.summerfarm.mall.model.input.CustomizationRequestEnabledInput;
import net.summerfarm.mall.model.input.CustomizationRequestInput;
import net.summerfarm.mall.model.vo.CustomizationRequestVO;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.service.CustomizationRequestService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import net.summerfarm.mall.enums.CustomizationRequestStatusEnum;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 定制需求服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Slf4j
@Service
public class CustomizationRequestServiceImpl implements CustomizationRequestService {

    @Resource
    private CustomizationRequestMapper customizationRequestMapper;

    @Transactional(rollbackFor = Exception.class)
    private Long saveCustomizationRequest(CustomizationRequestInput input) {
        // 构建定制需求对象
        CustomizationRequest customizationRequest = new CustomizationRequest();
        BeanUtils.copyProperties(input, customizationRequest);

        // 设置客户ID（从当前登录用户获取）
        customizationRequest.setMId(RequestHolder.getMId());

        // 设置默认值
        if (customizationRequest.getColorCount() == null) {
            customizationRequest.setColorCount(1);
        }
        if (customizationRequest.getStatus() == null) {
            customizationRequest.setStatus(0); // 默认状态：定制需求待生成
        }

        // 设置提交时间
        customizationRequest.setDesignSubmitTime(LocalDateTime.now ());

        // 保存到数据库
        customizationRequestMapper.insertSelective(customizationRequest);

        return customizationRequest.getId();
    }


    @Override
    public List<ProductInfoVO> querySkuAndSave(CustomizationRequestInput input) {
        //查询模版sku
//        queryTemplateSku(input.getPdId (),input.getColorCount ());
        //根据模版sku复制sku

        //保存定制需求及sku映射关系

        return Collections.emptyList ();
    }

    @Override
    public CustomizationRequestVO getCustomizationRequestByOrderNo(String orderNo) {
        CustomizationRequest customizationRequest = customizationRequestMapper.selectByOrderNo(orderNo);
        return CustomizationRequestVO;
    }

    @Override
    public void refuse(CustomizationRequestEnabledInput input) {
        CustomizationRequest customizationRequest = customizationRequestMapper.selectByPrimaryKey(input.getId ());
        if (customizationRequest == null){
            throw new BizException ("定制需求不存在");
        }
        if (!CustomizationRequestStatusEnum.canTransition (customizationRequest.getStatus (),CustomizationRequestStatusEnum.CUSTOMER_REJECTED.getCode ())){
            throw new BizException ("定制需求现在是" + CustomizationRequestStatusEnum.getByCode (customizationRequest.getStatus ()).getDescription () + "状态,不可拒绝");
        }
        customizationRequest.setStatus (CustomizationRequestStatusEnum.CUSTOMER_REJECTED.getCode ());
        customizationRequest.setRefuseReason (input.getRefuseReason ());
        customizationRequestMapper.updateByPrimaryKeySelective(customizationRequest);
    }

    @Override
    public void confirm(CustomizationRequestEnabledInput input) {
        CustomizationRequest customizationRequest = customizationRequestMapper.selectByPrimaryKey(input.getId ());
        if (customizationRequest == null){
            throw new BizException ("定制需求不存在");
        }
        if (!CustomizationRequestStatusEnum.canTransition (customizationRequest.getStatus (),CustomizationRequestStatusEnum.CUSTOMER_APPROVED.getCode ())){
            throw new BizException ("定制需求现在是" + CustomizationRequestStatusEnum.getByCode (customizationRequest.getStatus ()).getDescription () + "状态,不可通过");
        }
        customizationRequest.setStatus (CustomizationRequestStatusEnum.CUSTOMER_APPROVED.getCode ());
        customizationRequest.setRefuseReason (null);
        customizationRequestMapper.updateByPrimaryKeySelective(customizationRequest);
    }

    @Override
    public CommonResult<List<CustomizationRequest>> getCustomizationRequestByStatus(Integer status) {
        try {
            if (status == null) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "状态不能为空");
            }

            List<CustomizationRequest> customizationRequest = customizationRequestMapper.selectByStatus(status);
            return CommonResult.ok(customizationRequest);
        } catch (Exception e) {
            log.error("根据状态查询定制需求列表异常，状态: {}", status, e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "查询定制需求列表异常：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> updateCustomizationRequestStatus(Long id, Integer status) {
        try {
            if (id == null || status == null) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "ID和状态不能为空");
            }

            CustomizationRequest customizationRequest = new CustomizationRequest();
            customizationRequest.setId(id);
            customizationRequest.setStatus(status);
            
            // 根据状态设置相应的时间字段
            Date now = new Date();
            switch (status) {
                case 3: // 客户通过
                    customizationRequest.setAgreeTime(now);
                    break;
                case 4: // 客户不通过
                    customizationRequest.setRefuseTime(now);
                    break;
                case 5: // 关闭
                    customizationRequest.setCloseTime(now);
                    break;
            }

            int result = customizationRequestMapper.updateByPrimaryKeySelective(customizationRequest);
            if (result > 0) {
                log.info("定制需求状态更新成功，ID: {}, 新状态: {}", id, status);
                return CommonResult.ok(true);
            } else {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "更新定制需求状态失败");
            }
        } catch (Exception e) {
            log.error("更新定制需求状态异常，ID: {}, 状态: {}", id, status, e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "更新定制需求状态异常：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> updateDesignImage(Long id, String designImage) {
        try {
            if (id == null) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "ID不能为空");
            }

            CustomizationRequest customizationRequest = new CustomizationRequest();
            customizationRequest.setId(id);
            customizationRequest.setDesignImage(designImage);
            customizationRequest.setStatus(2); // 设计师发起确认

            int result = customizationRequestMapper.updateByPrimaryKeySelective(customizationRequest);
            if (result > 0) {
                log.info("设计效果图更新成功，ID: {}", id);
                return CommonResult.ok(true);
            } else {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "更新设计效果图失败");
            }
        } catch (Exception e) {
            log.error("更新设计效果图异常，ID: {}", id, e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "更新设计效果图异常：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> confirmDesign(Long id, Boolean approved, String refuseReason) {
        try {
            if (id == null || approved == null) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "ID和确认结果不能为空");
            }

            if (!approved && !StringUtils.hasText(refuseReason)) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "拒绝时必须填写拒绝原因");
            }

            CustomizationRequest customizationRequest = new CustomizationRequest();
            customizationRequest.setId(id);
            
            Date now = new Date();
            if (approved) {
                customizationRequest.setStatus(3); // 客户通过
                customizationRequest.setAgreeTime(now);
            } else {
                customizationRequest.setStatus(4); // 客户不通过
                customizationRequest.setRefuseTime(now);
                customizationRequest.setRefuseReason(refuseReason);
            }

            int result = customizationRequestMapper.updateByPrimaryKeySelective(customizationRequest);
            if (result > 0) {
                log.info("设计确认成功，ID: {}, 确认结果: {}", id, approved);
                return CommonResult.ok(true);
            } else {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "设计确认失败");
            }
        } catch (Exception e) {
            log.error("设计确认异常，ID: {}, 确认结果: {}", id, approved, e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "设计确认异常：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> addCommunicationNote(Long id, String communicationNote) {
        try {
            if (id == null || !StringUtils.hasText(communicationNote)) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "ID和沟通记录不能为空");
            }

            // 查询现有记录
            CustomizationRequest existingRequest = customizationRequestMapper.selectByPrimaryKey(id);
            if (existingRequest == null) {
                return CommonResult.fail(ResultStatusEnum.DATA_NOT_FOUND, "定制需求不存在");
            }

            // 构建新的沟通记录
            JSONObject newNote = new JSONObject();
            newNote.put("role", "customer"); // 可以根据实际情况设置角色
            newNote.put("msg", communicationNote);
            newNote.put("dateTime", new Date());

            // 处理现有沟通记录
            JSONArray communicationNotes;
            if (StringUtils.hasText(existingRequest.getCommunicationNotes())) {
                try {
                    communicationNotes = JSON.parseArray(existingRequest.getCommunicationNotes());
                } catch (Exception e) {
                    communicationNotes = new JSONArray();
                }
            } else {
                communicationNotes = new JSONArray();
            }

            // 添加新记录
            communicationNotes.add(newNote);

            // 更新数据库
            CustomizationRequest updateRequest = new CustomizationRequest();
            updateRequest.setId(id);
            updateRequest.setCommunicationNotes(communicationNotes.toJSONString());

            int result = customizationRequestMapper.updateByPrimaryKeySelective(updateRequest);
            if (result > 0) {
                log.info("沟通记录添加成功，ID: {}", id);
                return CommonResult.ok(true);
            } else {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "添加沟通记录失败");
            }
        } catch (Exception e) {
            log.error("添加沟通记录异常，ID: {}", id, e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "添加沟通记录异常：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deleteCustomizationRequest(Long id) {
        try {
            if (id == null) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "ID不能为空");
            }

            int result = customizationRequestMapper.deleteByPrimaryKey(id);
            if (result > 0) {
                log.info("定制需求删除成功，ID: {}", id);
                return CommonResult.ok(true);
            } else {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "删除定制需求失败");
            }
        } catch (Exception e) {
            log.error("删除定制需求异常，ID: {}", id, e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "删除定制需求异常：" + e.getMessage());
        }
    }
}
