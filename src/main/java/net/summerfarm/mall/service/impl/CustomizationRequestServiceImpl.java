package net.summerfarm.mall.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.input.CustomizationRequestEnabledInput;
import net.summerfarm.mall.model.input.CustomizationRequestInput;
import net.summerfarm.mall.model.vo.CustomizationRequestVO;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.service.CustomizationRequestService;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import net.summerfarm.mall.enums.CustomizationRequestStatusEnum;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 定制需求服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Slf4j
@Service
public class CustomizationRequestServiceImpl implements CustomizationRequestService {

    @Resource
    private CustomizationRequestMapper customizationRequestMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ProductInfoVO> querySkuAndSave(CustomizationRequestInput input, MerchantSubject merchantSubject) {


        return Collections.emptyList ();
    }


    @Override
    public CustomizationRequestVO getCustomizationRequestByOrderNo(String orderNo, MerchantSubject merchantSubject) {
        try {
            if (!StringUtils.hasText(orderNo)) {
                throw new BizException("订单号不能为空");
            }

            CustomizationRequest customizationRequest = customizationRequestMapper.selectByOrderNo(orderNo);
            if (customizationRequest == null) {
                throw new BizException("该订单号对应的定制需求不存在");
            }

            CustomizationRequestVO vo = new CustomizationRequestVO();
            BeanUtils.copyProperties(customizationRequest, vo);

            return vo;
        } catch (Exception e) {
            log.error("根据订单号查询定制需求异常，订单号: {}", orderNo, e);
            throw new BizException("查询定制需求异常：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refuse(CustomizationRequestEnabledInput input, MerchantSubject merchantSubject) {
        CustomizationRequest customizationRequest = customizationRequestMapper.selectByPrimaryKey(input.getId());
        if (customizationRequest == null) {
            throw new BizException("定制需求不存在");
        }
        if (!CustomizationRequestStatusEnum.canTransition(customizationRequest.getStatus(), CustomizationRequestStatusEnum.CUSTOMER_REJECTED.getCode())) {
            throw new BizException("定制需求现在是" + CustomizationRequestStatusEnum.getByCode(customizationRequest.getStatus()).getDescription() + "状态,不可拒绝");
        }

        customizationRequest.setStatus(CustomizationRequestStatusEnum.CUSTOMER_REJECTED.getCode());
        customizationRequest.setRefuseReason(input.getRefuseReason());
        customizationRequest.setRefuseTime(LocalDateTime.now ());

        // 添加沟通记录
        addCommunicationNote(customizationRequest, "customer", "客户拒绝设计，原因：" + input.getRefuseReason());

        customizationRequestMapper.updateByPrimaryKeySelective(customizationRequest);
        log.info("定制需求拒绝成功，ID: {}, 拒绝原因: {}", input.getId(), input.getRefuseReason());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirm(CustomizationRequestEnabledInput input, MerchantSubject merchantSubject) {
        CustomizationRequest customizationRequest = customizationRequestMapper.selectByPrimaryKey(input.getId());
        if (customizationRequest == null) {
            throw new BizException("定制需求不存在");
        }
        if (!CustomizationRequestStatusEnum.canTransition(customizationRequest.getStatus(), CustomizationRequestStatusEnum.CUSTOMER_APPROVED.getCode())) {
            throw new BizException("定制需求现在是" + CustomizationRequestStatusEnum.getByCode(customizationRequest.getStatus()).getDescription() + "状态,不可通过");
        }

        customizationRequest.setStatus(CustomizationRequestStatusEnum.CUSTOMER_APPROVED.getCode());
        customizationRequest.setRefuseReason("");
        customizationRequest.setAgreeTime(LocalDateTime.now ());

        // 添加沟通记录
        addCommunicationNote(customizationRequest, "customer", "客户确认通过设计");

        customizationRequestMapper.updateByPrimaryKeySelective(customizationRequest);
        log.info("定制需求确认成功，ID: {}", input.getId());
    }

    /**
     * 添加沟通记录
     * @param customizationRequest 定制需求对象
     * @param role 角色
     * @param message 消息内容
     */
    private void addCommunicationNote(CustomizationRequest customizationRequest, String role, String message) {
        try {
            // 构建新的沟通记录
            JSONObject newNote = new JSONObject();
            newNote.put("role", role);
            newNote.put("msg", message);
            newNote.put("dateTime", new Date());

            // 处理现有沟通记录
            JSONArray communicationNotes;
            if (StringUtils.hasText(customizationRequest.getCommunicationNotes())) {
                try {
                    communicationNotes = JSON.parseArray(customizationRequest.getCommunicationNotes());
                } catch (Exception e) {
                    communicationNotes = new JSONArray();
                }
            } else {
                communicationNotes = new JSONArray();
            }

            // 添加新记录
            communicationNotes.add(newNote);
            customizationRequest.setCommunicationNotes(communicationNotes.toJSONString());
        } catch (Exception e) {
            log.error("添加沟通记录异常,customizationRequest={}", customizationRequest.getId (),e);
        }
    }

}
