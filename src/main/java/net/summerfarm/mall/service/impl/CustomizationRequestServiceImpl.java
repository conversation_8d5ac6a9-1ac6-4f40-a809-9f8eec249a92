package net.summerfarm.mall.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.mapper.CustomizationRequestMapper;
import net.summerfarm.mall.model.domain.CustomizationRequests;
import net.summerfarm.mall.model.input.CustomizationRequestsInput;
import net.summerfarm.mall.service.CustomizationRequestService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 定制需求服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Slf4j
@Service
public class CustomizationRequestServiceImpl implements CustomizationRequestService {

    @Resource
    private CustomizationRequestMapper customizationRequestMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Long> saveCustomizationRequest(CustomizationRequestsInput input) {
        try {
            // 参数校验
            if (input == null) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "请求参数不能为空");
            }

            // 检查订单号是否已存在
            CustomizationRequests existingRequest = customizationRequestMapper.selectByOrderNo(input.getInitiateOrderNo());
            if (existingRequest != null) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "该订单号已存在定制需求");
            }

            // 构建定制需求对象
            CustomizationRequests customizationRequest = new CustomizationRequests();
            BeanUtils.copyProperties(input, customizationRequest);
            
            // 设置客户ID（从当前登录用户获取）
            customizationRequest.setMId(RequestHolder.getMId());
            
            // 设置默认值
            if (customizationRequest.getColorCount() == null) {
                customizationRequest.setColorCount(1);
            }
            if (customizationRequest.getStatus() == null) {
                customizationRequest.setStatus(0); // 默认状态：定制需求待生成
            }
            
            // 设置提交时间
            customizationRequest.setDesignSubmitTime(new Date());

            // 保存到数据库
            int result = customizationRequestMapper.insertSelective(customizationRequest);
            if (result > 0) {
                log.info("定制需求保存成功，ID: {}, 订单号: {}", customizationRequest.getId(), input.getInitiateOrderNo());
                return CommonResult.ok(customizationRequest.getId());
            } else {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "保存定制需求失败");
            }
        } catch (Exception e) {
            log.error("保存定制需求异常", e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "保存定制需求异常：" + e.getMessage());
        }
    }

    @Override
    public CommonResult<CustomizationRequests> getCustomizationRequestById(Long id) {
        try {
            if (id == null) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "ID不能为空");
            }

            CustomizationRequests customizationRequest = customizationRequestMapper.selectByPrimaryKey(id);
            if (customizationRequest == null) {
                return CommonResult.fail(ResultStatusEnum.DATA_NOT_FOUND, "定制需求不存在");
            }

            return CommonResult.ok(customizationRequest);
        } catch (Exception e) {
            log.error("查询定制需求异常，ID: {}", id, e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "查询定制需求异常：" + e.getMessage());
        }
    }

    @Override
    public CommonResult<CustomizationRequests> getCustomizationRequestByOrderNo(String orderNo) {
        try {
            if (!StringUtils.hasText(orderNo)) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "订单号不能为空");
            }

            CustomizationRequests customizationRequest = customizationRequestMapper.selectByOrderNo(orderNo);
            if (customizationRequest == null) {
                return CommonResult.fail(ResultStatusEnum.DATA_NOT_FOUND, "该订单号对应的定制需求不存在");
            }

            return CommonResult.ok(customizationRequest);
        } catch (Exception e) {
            log.error("根据订单号查询定制需求异常，订单号: {}", orderNo, e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "查询定制需求异常：" + e.getMessage());
        }
    }

    @Override
    public CommonResult<List<CustomizationRequests>> getCustomizationRequestsByMerchantId(Long mId) {
        try {
            if (mId == null) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "客户ID不能为空");
            }

            List<CustomizationRequests> customizationRequests = customizationRequestMapper.selectByMerchantId(mId);
            return CommonResult.ok(customizationRequests);
        } catch (Exception e) {
            log.error("根据客户ID查询定制需求列表异常，客户ID: {}", mId, e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "查询定制需求列表异常：" + e.getMessage());
        }
    }

    @Override
    public CommonResult<List<CustomizationRequests>> getCustomizationRequestsByStatus(Integer status) {
        try {
            if (status == null) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "状态不能为空");
            }

            List<CustomizationRequests> customizationRequests = customizationRequestMapper.selectByStatus(status);
            return CommonResult.ok(customizationRequests);
        } catch (Exception e) {
            log.error("根据状态查询定制需求列表异常，状态: {}", status, e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "查询定制需求列表异常：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> updateCustomizationRequestStatus(Long id, Integer status) {
        try {
            if (id == null || status == null) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "ID和状态不能为空");
            }

            CustomizationRequests customizationRequest = new CustomizationRequests();
            customizationRequest.setId(id);
            customizationRequest.setStatus(status);
            
            // 根据状态设置相应的时间字段
            Date now = new Date();
            switch (status) {
                case 3: // 客户通过
                    customizationRequest.setAgreeTime(now);
                    break;
                case 4: // 客户不通过
                    customizationRequest.setRefuseTime(now);
                    break;
                case 5: // 关闭
                    customizationRequest.setCloseTime(now);
                    break;
            }

            int result = customizationRequestMapper.updateByPrimaryKeySelective(customizationRequest);
            if (result > 0) {
                log.info("定制需求状态更新成功，ID: {}, 新状态: {}", id, status);
                return CommonResult.ok(true);
            } else {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "更新定制需求状态失败");
            }
        } catch (Exception e) {
            log.error("更新定制需求状态异常，ID: {}, 状态: {}", id, status, e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "更新定制需求状态异常：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> updateDesignImage(Long id, String designImage) {
        try {
            if (id == null) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "ID不能为空");
            }

            CustomizationRequests customizationRequest = new CustomizationRequests();
            customizationRequest.setId(id);
            customizationRequest.setDesignImage(designImage);
            customizationRequest.setStatus(2); // 设计师发起确认

            int result = customizationRequestMapper.updateByPrimaryKeySelective(customizationRequest);
            if (result > 0) {
                log.info("设计效果图更新成功，ID: {}", id);
                return CommonResult.ok(true);
            } else {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "更新设计效果图失败");
            }
        } catch (Exception e) {
            log.error("更新设计效果图异常，ID: {}", id, e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "更新设计效果图异常：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> confirmDesign(Long id, Boolean approved, String refuseReason) {
        try {
            if (id == null || approved == null) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "ID和确认结果不能为空");
            }

            if (!approved && !StringUtils.hasText(refuseReason)) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "拒绝时必须填写拒绝原因");
            }

            CustomizationRequests customizationRequest = new CustomizationRequests();
            customizationRequest.setId(id);
            
            Date now = new Date();
            if (approved) {
                customizationRequest.setStatus(3); // 客户通过
                customizationRequest.setAgreeTime(now);
            } else {
                customizationRequest.setStatus(4); // 客户不通过
                customizationRequest.setRefuseTime(now);
                customizationRequest.setRefuseReason(refuseReason);
            }

            int result = customizationRequestMapper.updateByPrimaryKeySelective(customizationRequest);
            if (result > 0) {
                log.info("设计确认成功，ID: {}, 确认结果: {}", id, approved);
                return CommonResult.ok(true);
            } else {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "设计确认失败");
            }
        } catch (Exception e) {
            log.error("设计确认异常，ID: {}, 确认结果: {}", id, approved, e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "设计确认异常：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> addCommunicationNote(Long id, String communicationNote) {
        try {
            if (id == null || !StringUtils.hasText(communicationNote)) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "ID和沟通记录不能为空");
            }

            // 查询现有记录
            CustomizationRequests existingRequest = customizationRequestMapper.selectByPrimaryKey(id);
            if (existingRequest == null) {
                return CommonResult.fail(ResultStatusEnum.DATA_NOT_FOUND, "定制需求不存在");
            }

            // 构建新的沟通记录
            JSONObject newNote = new JSONObject();
            newNote.put("role", "customer"); // 可以根据实际情况设置角色
            newNote.put("msg", communicationNote);
            newNote.put("dateTime", new Date());

            // 处理现有沟通记录
            JSONArray communicationNotes;
            if (StringUtils.hasText(existingRequest.getCommunicationNotes())) {
                try {
                    communicationNotes = JSON.parseArray(existingRequest.getCommunicationNotes());
                } catch (Exception e) {
                    communicationNotes = new JSONArray();
                }
            } else {
                communicationNotes = new JSONArray();
            }

            // 添加新记录
            communicationNotes.add(newNote);

            // 更新数据库
            CustomizationRequests updateRequest = new CustomizationRequests();
            updateRequest.setId(id);
            updateRequest.setCommunicationNotes(communicationNotes.toJSONString());

            int result = customizationRequestMapper.updateByPrimaryKeySelective(updateRequest);
            if (result > 0) {
                log.info("沟通记录添加成功，ID: {}", id);
                return CommonResult.ok(true);
            } else {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "添加沟通记录失败");
            }
        } catch (Exception e) {
            log.error("添加沟通记录异常，ID: {}", id, e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "添加沟通记录异常：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deleteCustomizationRequest(Long id) {
        try {
            if (id == null) {
                return CommonResult.fail(ResultStatusEnum.PARAM_ERROR, "ID不能为空");
            }

            int result = customizationRequestMapper.deleteByPrimaryKey(id);
            if (result > 0) {
                log.info("定制需求删除成功，ID: {}", id);
                return CommonResult.ok(true);
            } else {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "删除定制需求失败");
            }
        } catch (Exception e) {
            log.error("删除定制需求异常，ID: {}", id, e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "删除定制需求异常：" + e.getMessage());
        }
    }
}
