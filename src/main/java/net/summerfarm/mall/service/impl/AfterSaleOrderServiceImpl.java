package net.summerfarm.mall.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.common.util.JsonUtil;
import com.cosfo.message.client.resp.MsgSendLogResp;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.BeanCopyUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.SaleStockChangeTypeEnum;
import net.summerfarm.enums.*;
import net.summerfarm.mall.after.service.impl.NormalOrderAfterSolution;
import net.summerfarm.mall.after.service.impl.TimingOrderAfterSolution;
import net.summerfarm.mall.after.template.AfterSaleOrderAuditHandler;
import net.summerfarm.mall.after.template.AfterSaleOrderCreatHandler;
import net.summerfarm.mall.after.template.AfterSaleOrderHandleHandler;
import net.summerfarm.mall.after.template.impl.AuditAfterSolution;
import net.summerfarm.mall.after.template.impl.CreatAfterSolution;
import net.summerfarm.mall.after.template.impl.HandleAfterSolution;
import net.summerfarm.mall.common.delayqueue.AfterSaleProofItem;
import net.summerfarm.mall.common.sms.SMSSenderFactory;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.common.util.PageInfoHelper;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.AfterSaleOrderAction;
import net.summerfarm.mall.contexts.AfterSaleWorkflow;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.factory.AfterSaleCalculatorFactory;
import net.summerfarm.mall.factory.AfterSaleOrderFactory;
import net.summerfarm.mall.factory.AfterSaleSolutionOrderFactory;
import net.summerfarm.mall.factory.AfterSaleWorkflowFactory;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.SMS;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.tms.DistOrderDTO;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.model.vo.merchant.subaccount.MerchantSubAccountQuery;
import net.summerfarm.mall.payments.common.enums.BasePayTypeEnum;
import net.summerfarm.mall.payments.request.PaymentHandler;
import net.summerfarm.mall.repository.MerchantSubAccountRepository;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.facade.TmsDistOrderDetailFacade;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryReq;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryRes;
import net.summerfarm.mall.service.facade.dto.AreaStoreUnLockReq;
import net.summerfarm.mall.service.facade.dto.OrderUnLockSkuDetailReqDTO;
import net.summerfarm.mall.service.helper.AfterSaleOrderHelper;
import net.summerfarm.mall.service.helper.AfterSaleSpecialRuleHelper;
import net.summerfarm.mall.wechat.enums.AfterSaleCalculatorType;
import net.summerfarm.mall.wechat.service.WeChatPayService;
import net.summerfarm.mall.wechat.templatemessage.AfterSaleNoticeMag;
import net.summerfarm.mall.wechat.templatemessage.TemplateMsgSender;
import net.summerfarm.mall.wechat.templatemessage.TimingOrderRefundWarningMsg;
import net.summerfarm.mall.wechat.templatemessage.TimingRefundMsg;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.user.UserBase;
import org.apache.commons.lang3.math.NumberUtils;
import org.jdom.JDOMException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.NoTransactionException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static net.summerfarm.mall.contexts.Global.TIME_FRAME_FEE_SKU;
import static net.summerfarm.mall.contexts.Global.getPayTypeEnum;


/**
 * @Package: net.summerfarm.trade.service.impl
 * @Description: 售后业务
 * @author: <EMAIL>
 * @Date: 2017/7/4
 */
@Service
@Slf4j
public class AfterSaleOrderServiceImpl implements AfterSaleOrderService {

    public static final Pattern PATTERN = Pattern.compile(Global.CHECK_CHAR);
    private static final Logger logger = LoggerFactory.getLogger(AfterSaleOrderService.class);
    public static final int MAX_PAGE_SIZE = 20;
    public static final int ONE_DAY = 24;
    @Resource
    private AfterSaleSolutionOrderFactory afterSaleSolutionOrderFactory;
    @Resource
    private AfterSaleOrderHelper afterSaleOrderHelper;
    @Resource
    private NormalOrderAfterSolution normalOrderAfter;
    @Resource
    private TimingOrderAfterSolution timingOrderAfter;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private WeChatPayService weChatPayService;
    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private OrderService orderService;
    @Resource
    private MerchantMapper merchantMapper;
    
    // @Resource
    private AfterSaleOrderFactory afterSaleOrderFactory;
    // @Resource
    private AfterSaleWorkflowFactory afterSaleWorkflowFactory;

    @Resource
    private AfterSaleCalculatorFactory afterSaleCalculatorFactory;
    @Resource
    private PrepayInventoryService prepayInventoryService;
    @Resource
    private  PrepayInventoryRecordMapper prepayInventoryRecordMapper;
    @Resource
    private PaymentHandler paymentHandler;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    AfterSaleOrderAction afterSaleOrderAction;
    @Resource
    private ConfigService configService;

    @Resource
    private RechargeRecordService rechargeRecordService;

    @Resource
    private TemplateMsgSender templateMsgSender;
    @Resource
    private MerchantSubAccountMapper merchantSubAccountMapper;
    @Resource
    private TmsDistOrderDetailFacade tmsDistOrderDetailFacade;
    @Resource
    private TimingOrderRefundRecordMapper timingOrderRefundRecordMapper;
    @Resource
    private SMSSenderFactory smsSenderFactory;
    @Resource
    private TimingOrderRefundTimeMapper timingOrderRefundTimeMapper;
    @Resource
    private RefundHandleEventMapper refundHandleEventMapper;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private TimingRuleMapper timingRuleMapper;
    @Resource
    private TimingOrderMapper timingOrderMapper;

    @Resource
    private WmsAreaStoreFacade areaStoreFacade;
    @Resource
    private OrderRelationMapper orderRelationMapper;
    @Resource
    private OrderPreferentialService orderPreferentialService;
    @Resource
    private MerchantSubAccountRepository merchantSubAccountRepository;
    @Resource
    private AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;

    @Resource
    private AfterSaleSpecialRuleHelper afterSaleSpecialRuleHelper;

    @Override
    public AjaxResult findPage(int pageIndex, int pageSize) {
        if (pageSize > MAX_PAGE_SIZE) {
            AjaxResult.getOK();
        }

        AfterSaleOrder select = new AfterSaleOrder();
        select.setmId(RequestHolder.getMId());
        PageHelper.startPage(pageIndex, pageSize);
        List<AfterSaleOrderVO> distincts = selectAfterSaleOrderVO(select);
        if (CollectionUtils.isEmpty(distincts)) {
            return AjaxResult.getOK();
        }
        for (AfterSaleOrderVO afterSaleOrderVO : distincts) {
            if(afterSaleOrderVO.getType()==3){
                List<OrderItemVO> suitOrderItem = orderItemMapper.selectOrderItemSuitId(afterSaleOrderVO.getOrderNo(), null, null,afterSaleOrderVO.getProductType());
                afterSaleOrderVO.setSuitOrderItem(suitOrderItem);
            }else{
                if (afterSaleOrderVO.getSuitId() > 0 && afterSaleOrderVO.getDeliveryed() == 0) {
                    List<OrderItemVO> suitOrderItem = orderItemMapper.selectOrderItemSuitId(afterSaleOrderVO.getOrderNo(), null, afterSaleOrderVO.getSuitId(),null);
                    afterSaleOrderVO.setSuitOrderItem(suitOrderItem);
                } else {
                    List<OrderItemVO> suitOrderItem = orderItemMapper.selectOrderItemSuitId(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getSku(), afterSaleOrderVO.getSuitId(),afterSaleOrderVO.getProductType());
                    afterSaleOrderVO.setSuitOrderItem(suitOrderItem);
                }
            }

        }
        PageInfo pageInfo = PageInfoHelper.createPageInfo(distincts);

        distincts = distincts.stream().filter( e -> e.getSku() != null).collect(Collectors.toList());

        pageInfo.setList(distincts);
        return AjaxResult.getOK(pageInfo);
    }

    @Override
    public AjaxResult countView() {
        return AjaxResult.getOK(afterSaleOrderMapper.countView(RequestHolder.getMId(), RequestHolder.getAccountId()));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult findAfterSaleOrder(String orderNo, String sku, int suitId,Integer type) {
        AfterSaleOrder selectKeys = new AfterSaleOrder();
        selectKeys.setOrderNo(orderNo);
        if(type == null || type !=3){
            if (suitId == 0) {
                selectKeys.setSku(sku);
            }
            selectKeys.setmId(RequestHolder.getMId());
            selectKeys.setAccountId(RequestHolder.getAccountId());
            selectKeys.setSuitId(suitId);
        }
        List<AfterSaleOrderVO> afterSaleOrders = selectAfterSaleOrderVO(selectKeys);
        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        if (orders == null) {
            return AjaxResult.getError("订单不存在");
        }

        if (!CollectionUtils.isEmpty(afterSaleOrders)) {

            List<OrderItemVO> allOrderItem = orderItemMapper.selectOrderItemSuitId(orderNo, null, null,null);

            for (AfterSaleOrderVO afterSaleOrderVO : afterSaleOrders) {
                afterSaleOrderVO.setOrderTime(DateUtils.date2LocalDateTime(orders.getOrderTime()));
                afterSaleOrderVO.setTotalPrice(orders.getTotalPrice());

                afterSaleOrderVO.setOverTime(afterSaleOrderVO.getUpdatetime().plusHours(24));
                List<Refund> refunds = refundMapper.selectByAfterSaleOrderNoSuccess(afterSaleOrderVO.getAfterSaleOrderNo());
                if (refunds != null && refunds.size()>1){
                    afterSaleOrderVO.setIsTwoRefund(true);
                }
                if (afterSaleOrderVO.getSuitId() > 0 && afterSaleOrderVO.getDeliveryed() == AfterSaleOrder.DELIVERY_NOT_RECEIVED) {
                    List<OrderItemVO> suitOrderItem = orderItemMapper.selectOrderItemSuitId(afterSaleOrderVO.getOrderNo(), null, afterSaleOrderVO.getSuitId(),null);
                    if(type!=null && type==3){
                        suitOrderItem=allOrderItem;
                    }
                    afterSaleOrderVO.setSuitOrderItem(suitOrderItem);
                } else {
                    List<OrderItemVO> suitOrderItem = orderItemMapper.selectOrderItemSuitId(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getSku(), afterSaleOrderVO.getSuitId(),afterSaleOrderVO.getProductType());
                    if(type!=null && type==3){
                        suitOrderItem=allOrderItem;
                    }

                    afterSaleOrderVO.setSuitOrderItem(suitOrderItem);
                }


                afterSaleOrderVO.setAfterSaleProofList(afterSaleProofMapper.selectDesc(afterSaleOrderVO.getAfterSaleOrderNo()));

                if (afterSaleOrderVO.getView() == 0) { //消息置为已读
                    AfterSaleOrder update = new AfterSaleOrder();
                    update.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
                    update.setView(1);
                    afterSaleOrderMapper.update(update);
                }
            }

        }
        return AjaxResult.getOK(afterSaleOrders);
    }

    @Override
    @Deprecated
    public AjaxResult save(AfterSaleOrderVO afterSaleOrderVO){
        Orders orders = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        //走未到货流程
        String applyRemark = afterSaleOrderVO.getApplyRemark();
        if(!StringUtils.isEmpty(applyRemark)){

            Matcher m = PATTERN.matcher(applyRemark);
            if(!m.matches()){
                return AjaxResult.getErrorWithMsg("补充说明中含有特殊字符");
            }
        }
        if(TIME_FRAME_FEE_SKU.equals(afterSaleOrderVO.getSku())){
            return AjaxResult.getErrorWithMsg("精准送不支持售后");
        }

        if (afterSaleOrderVO.getDeliveryed() == AfterSaleOrder.DELIVERY_NOT_RECEIVED && Objects.equals(orders.getType(), 3)) {
            if (!(orders.getAdminId() != null && Objects.equals(orders.getDirect(), MajorDirectEnum.PERIOD.getType()))) {
                return AjaxResult.getErrorWithMsg("非账期大客户代下单不支持未到货售后");
            }
        }

        AfterSaleStrategy afterSaleStrategy = getAfterSaleStrategy(orders,afterSaleOrderVO);
        logger.info("getStrategy:{}", afterSaleStrategy.getClass());
        logger.info("afterSaleOrderVO:{}", afterSaleOrderVO);

        AjaxResult result = afterSaleStrategy.afterSaleOrder(afterSaleOrderVO);
        return result;


    }

    @Override
    public AjaxResult pre(String orderNo, String sku, Long mId,Integer deliveryId) {
        AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        Inventory inventory = inventoryMapper.selectBySku(sku);
        Products products = productsMapper.selectByPrimaryKey(inventory.getPdId());
        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        if (Objects.isNull(orders)) {
            return AjaxResult.getError("订单不存在！");
        }

        //普通订单售后校验时间
        if(Objects.equals(orders.getOrderSaleType(),0)
                && afterSaleOrderHelper.inAfterSaleTime(orderNo, products.getAfterSaleTime(),orders,deliveryId)){
            return AjaxResult.getError(ResultConstant.OUT_OF_AFTER_SALE_TIME);
        }
        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNoIntercept(orderNo);

        // 全品类水果的「可售后时间」 为配送日期-2天的截单时间 需求 by 薄荷
        if (afterSaleSpecialRuleHelper.judgeFruitFullCategoryAfterSale(inventory.getSubType(), products.getCategoryId(), deliveryPlanVOS, orders)) {
            return AjaxResult.getError(ResultConstant.OUT_OF_AFTER_SALE_TIME, "当前商品已过截单时间，暂不可发起售后，如有疑问可咨询客服。");
        }
        //设置订单配送状态。
        if(!CollectionUtils.isEmpty(deliveryPlanVOS)){
            DeliveryPlanVO deliveryPlanVO = deliveryPlanVOS.get(0);
            if (deliveryPlanVO.getInterceptFlag() == 1){
                afterSaleOrderVO.setDeliveryStatus(DeliveryStatusEnum.INTERCEPT.getStatus());
            }else {
                DistOrderDTO distOrderDTO = tmsDistOrderDetailFacade.queryDistOrderDetail(orderNo,deliveryPlanVO.getContactId(),deliveryPlanVO.getDeliveryTime(), TmsSourceEnum.getDistOrderSource(orders.getType()));
                if (distOrderDTO != null){
                    if (distOrderDTO.getStatus() == null){
                        afterSaleOrderVO.setDeliveryStatus(DeliveryStatusEnum.TO_BE_WIRED.getStatus());
                    }else {
                        afterSaleOrderVO.setDeliveryStatus(TmsDistOrderStatusEnum.getDistOrderStatus(distOrderDTO.getStatus()));
                    }
                }else {
                    afterSaleOrderVO.setDeliveryStatus(DeliveryStatusEnum.TO_BE_WIRED.getStatus());
                }
            }
        }else {
            afterSaleOrderVO.setDeliveryStatus(DeliveryStatusEnum.NOT_YET.getStatus());
        }

        afterSaleOrderVO.setAfterSaleQuantity(inventory.getAfterSaleQuantity());
        afterSaleOrderVO.setAfterSaleTime(products.getAfterSaleTime());
        afterSaleOrderVO.setAfterSaleType(products.getAfterSaleType());
        afterSaleOrderVO.setSaleUnit(inventory.getUnit());
        afterSaleOrderVO.setAfterSaleUnit(inventory.getAfterSaleUnit());
        afterSaleOrderVO.setCategoryId(products.getCategoryId());
        afterSaleOrderVO.setRefundType(products.getRefundType());

        return AjaxResult.getOK(afterSaleOrderVO);
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult cancel(String afterSaleOrderNo) {
        AfterSaleOrder afterSaleOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        if (AfterSaleOrderStatus.SUCCESS.getStatus() == afterSaleOrder.getStatus()) {  //处理成功的售后不能取消
            return AjaxResult.getError();
        }
        int rs = afterSaleOrderMapper.updateStatus(afterSaleOrderNo, AfterSaleOrderStatus.CANCEL.getStatus());
        AfterSaleProof afterSaleProof = new AfterSaleProof();
        afterSaleProof.setStatus(AfterSaleOrderStatus.CANCEL.getStatus());
        afterSaleProof.setId(afterSaleOrder.getId());
        afterSaleProofMapper.updateById(afterSaleProof);
        logger.info("取消售后{}结果：{}", afterSaleOrderNo, rs);
        if (rs == 1) {
            return AjaxResult.getOK();
        }
        return AjaxResult.getError();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult refund(String afterSaleOrderNo) throws CertificateException, UnrecoverableKeyException, NoSuchAlgorithmException, IOException, KeyManagementException, KeyStoreException, JDOMException {
        AjaxResult result = paymentHandler.refundByAfterSaleOrderNo(afterSaleOrderNo);
        if (!AjaxResult.isSuccess(result)) {
            throw new RuntimeException("退款单异常");
        }
        return AjaxResult.getOK();
    }


    @Override
    public AjaxResult updatePicProof(String afterSaleOrderNo, String proofPic, String applyRemark) {

        AfterSaleOrderVO afterSaleOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        if (afterSaleOrder.getStatus() != AfterSaleOrderStatus.RE_COMMIT.getStatus()) {
            return AjaxResult.getErrorWithMsg("售后订单异常");
        }

        if (LocalDateTime.now().isAfter(afterSaleOrder.getUpdatetime().plusHours(ONE_DAY))) {
            return AjaxResult.getErrorWithMsg("超过截止时间");
        }

        //状态改为待审核
        afterSaleOrderMapper.updateStatus(afterSaleOrderNo, AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        //新建一条证明数据
        AfterSaleProof insert = conventAfterSaleProof(afterSaleOrder);
        insert.setAfterSaleOrderNo(afterSaleOrderNo);
        insert.setProofPic(proofPic);
        insert.setApplyRemark(applyRemark);
        insert.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        afterSaleProofMapper.updateByAfterSale(insert);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult closeProof(AfterSaleProofItem afterSaleProofItem) {

        AfterSaleOrderVO afterSaleOrderVO = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleProofItem.getAfterSaleOrderNo());
        logger.info("关闭凭证更新售后单号:" + afterSaleOrderVO.getAfterSaleOrderNo());
        if (afterSaleOrderVO.getStatus() == AfterSaleOrderStatus.RE_COMMIT.getStatus()) {
            //否者 就失败售后单
            afterSaleOrderMapper.updateStatus(afterSaleProofItem.getAfterSaleOrderNo(), AfterSaleOrderStatus.FAIL.getStatus());
            AfterSaleProof update = new AfterSaleProof();
            update.setId(afterSaleOrderVO.getId());
            update.setStatus(AfterSaleOrderStatus.FAIL.getStatus());
            afterSaleProofMapper.updateById(update);
        }
        return AjaxResult.getOK();
    }

    @Override
    @Deprecated
    public AjaxResult calcAfterSaleCoupon(String orderNo, String sku, Integer expectQuantity, Integer suitId, Integer deliveryed,
                                                   Integer type,Integer deliveryId, Integer handleType,String afterSaleOrderNo) {

        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        Map<String, String> map = new HashMap();
        String key = "";

        if(deliveryed == AfterSaleOrder.DELIVERY_NOT_RECEIVED && (Objects.equals(orders.getType(),0))){
            key = AfterSaleCalculatorType.NOT_NEED.getType();
        }
        if(deliveryed == AfterSaleOrder.DELIVERY_NOT_RECEIVED && Objects.equals(orders.getType(),3)){
            if (orders.getAdminId() != null && Objects.equals(orders.getDirect(), MajorDirectEnum.PERIOD.getType())){
                key = AfterSaleCalculatorType.NOT_NEED.getType();
            } else {
                return AjaxResult.getErrorWithMsg("非账期大客户代下单不支持未到货售后");
            }
        }
        if(deliveryed == AfterSaleOrder.DELIVERY_NOT_RECEIVED &&  Objects.equals(orders.getType(),1)){
            key =AfterSaleCalculatorType.TIMING_NOT_NEED.getType();
        }
        if(deliveryed == AfterSaleOrder.DELIVERY_RECEIVED ){
            if(Objects.equals(orders.getType(),1)){
                key = AfterSaleCalculatorType.TIMING_BROKEN.getType();
            } else {
                key =  AfterSaleCalculatorType.BROKEN.getType();
            }
        }


        AfterSaleOrderVO afterSaleOrder = new AfterSaleOrderVO();
        afterSaleOrder.setOrderNo(orderNo);
        afterSaleOrder.setSku(sku);
        afterSaleOrder.setQuantity(expectQuantity);
        afterSaleOrder.setSuitId(suitId);
        afterSaleOrder.setDeliveryed(deliveryed);
        afterSaleOrder.setType(type);
        afterSaleOrder.setDeliveryId(deliveryId);
        afterSaleOrder.setHandleType(handleType);
        afterSaleOrder.setAfterSaleOrderNo(afterSaleOrderNo);
        AfterSaleCalculator afterSaleCalculator = afterSaleCalculatorFactory.getAfterSaleCalculator(key);
        AjaxResult result = afterSaleCalculator.calcRefund(afterSaleOrder);
        String code = result.getCode();
        if(Objects.equals("SUCCESS",code)){
            AfterSaleOrderCalculator calculator =  (AfterSaleOrderCalculator)result.getData();
            map.put("couponMoney",calculator.getCouponMoney().toString());
            map.put("skuType",String.valueOf(calculator.getSkuType()));
            if (StringUtils.isNotBlank(calculator.getCouponId())) {
                map.put("couponId", calculator.getCouponId());
            }
        }
        return AjaxResult.getOK(map);
    }


    public AfterSaleProof conventAfterSaleProof(AfterSaleOrderVO afterSaleOrderVO) {
        AfterSaleProof afterSaleProof = new AfterSaleProof();
        afterSaleProof.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        afterSaleProof.setProofPic(afterSaleOrderVO.getProofPic());
        afterSaleProof.setHandleType(afterSaleOrderVO.getHandleType());
        afterSaleProof.setHandleNum(afterSaleOrderVO.getHandleNum());
        afterSaleProof.setApplyRemark(afterSaleOrderVO.getApplyRemark());
        afterSaleProof.setQuantity(afterSaleOrderVO.getQuantity());
        afterSaleProof.setHandleRemark(afterSaleOrderVO.getHandleRemark());
        afterSaleProof.setExtraRemark(afterSaleOrderVO.getExtraRemark());
        afterSaleProof.setAfterSaleType(afterSaleOrderVO.getAfterSaleType());
        afterSaleProof.setRefundType(afterSaleOrderVO.getRefundType());
        afterSaleProof.setStatus(afterSaleOrderVO.getStatus());
        return afterSaleProof;
    }

    @Override
    public  AjaxResult afterSaleEntryBill(String afterSaleOrderNo){

        AfterSaleOrderVO  afterSaleOrderVO =  afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        int skuQuantity = afterSaleOrderVO.getQuantity();
        int totalQuantity = afterSaleOrderVO.getQuantity();
        HashMap selectKey = new HashMap();
        selectKey.put("mId", afterSaleOrderVO.getmId());
        String sku = afterSaleOrderVO.getSku();
        Merchant merchant = merchantMapper.selectOne(selectKey);
        Integer adminId = merchant.getAdminId();

        Orders selectByOrderNo = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        //未到货，拍多拍错不想要返库存
        if(!Objects.equals(afterSaleOrderVO.getRefundType(),"其他") && Objects.equals(afterSaleOrderVO.getDeliveryed(), AfterSaleOrder.DELIVERY_NOT_RECEIVED)) {

            //是否是预付商品
            List<PrepayInventoryRecord> prepayInventoryRecords = prepayInventoryRecordMapper.selectRecordList(adminId, afterSaleOrderVO.getOrderNo(), sku);

            if(!CollectionUtils.isEmpty(prepayInventoryRecords)){
                boolean increase = prepayInventoryService.increase(adminId, sku, afterSaleOrderVO.getOrderNo(), skuQuantity);
                if(!increase){
                    throw new DefaultServiceException("返还预付商品异常");
                }

            }
            //遍历订单项并返库存
            ArrayList<String> skuList = new ArrayList<>();
            if(!Objects.equals(afterSaleOrderVO.getRefundType(), AfterRefundTypeEnum.SWITCH_WAREHOUSE.getDescription())){
                //查询订单订单项
                List<OrderItem> orderItems = orderItemMapper.selectOrderItemByArea(afterSaleOrderVO.getOrderNo(), merchant.getAreaNo());
                //Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
                List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNoNoStatus(afterSaleOrderVO.getOrderNo());
                Long contactId = CollectionUtils.isEmpty(deliveryPlanVOS) ? null : deliveryPlanVOS.get(0).getContactId();

                //处理sku顺序、避免死锁
                orderItems.sort(Comparator.comparing(OrderItem::getSku));
                for (OrderItem orderItem : orderItems) {
                    if (Objects.equals(orderItem.getSku(), afterSaleOrderVO.getSku())) {
                        //orderService.updateStock(orderItem.getSku(), merchant.getAreaNo(), afterSaleOrderVO.getQuantity(), merchant.getMname(), SaleStockChangeTypeEnum.RETURN, afterSaleOrderVO.getOrderNo(), recordMap,true,contactId);
                        //库存释放 新模型
                        AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
                        areaStoreUnLockReq.setContactId(contactId);
                        areaStoreUnLockReq.setOrderType(SaleStockChangeTypeEnum.CANCEL_ORDER.getTypeName());
                        areaStoreUnLockReq.setOrderNo(afterSaleOrderVO.getOrderNo());
                        areaStoreUnLockReq.setIdempotentNo(afterSaleOrderNo);
                        areaStoreUnLockReq.setOperatorNo(afterSaleOrderNo);
                        List<OrderUnLockSkuDetailReqDTO> orderReleaseSkuDetailReqDTOS = new ArrayList<>();
                        OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
                        orderUnLockSkuDetailReqDTO.setSkuCode(orderItem.getSku());
                        orderUnLockSkuDetailReqDTO.setReleaseQuantity(afterSaleOrderVO.getQuantity());
                        orderReleaseSkuDetailReqDTOS.add(orderUnLockSkuDetailReqDTO);
                        areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(orderReleaseSkuDetailReqDTOS);
                        areaStoreUnLockReq.setMerchantId(afterSaleOrderVO.getmId());
                        areaStoreUnLockReq.setSource(DistOrderSourceEnum.getDistOrderSourceByOrderType(selectByOrderNo.getType()));
                        areaStoreUnLockReq.setOperatorName(merchant.getMname());
                        areaStoreFacade.storeUnLock(areaStoreUnLockReq);
                        skuList.add(orderItem.getSku());
                        break;
                    }
                }
            }
            //quantityChangeRecordService.insert(recordMap);

            if (!CollectionUtils.isEmpty(skuList)) {
                weChatPayService.asyncTaskStock(skuList);
            }

        }

        //售后数量等于订单项数量更改订单项状态
        OrderItem orderItem = orderItemMapper.selectOrderItemByAfter(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getSku(), null, afterSaleOrderVO.getProductType());
        AfterSaleOrder selectAfterOrder = new AfterSaleOrder();
        selectAfterOrder.setOrderNo(afterSaleOrderVO.getOrderNo());
        selectAfterOrder.setSuitId(orderItem.getSuitId());
        selectAfterOrder.setSku(orderItem.getSku());

        selectAfterOrder.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
        selectAfterOrder.setDeliveryed(AfterSaleOrder.DELIVERY_NOT_RECEIVED);
        List<AfterSaleOrderVO> queryList = selectAfterSaleOrderVO(selectAfterOrder);
        //列表里面包含处理过状态为2的东西
        if (!CollectionUtils.isEmpty(queryList)) {
            for (AfterSaleOrderVO saleOrderVO : queryList) {
                if(Objects.equals(saleOrderVO.getDeliveryed(),AfterSaleOrder.DELIVERY_NOT_RECEIVED)){
                    skuQuantity =  saleOrderVO.getQuantity() + skuQuantity;
                }
            }
        }
        //售后数量等于购买数量 更改订单项状态
        if(skuQuantity >= orderItem.getAmount()){
            orderItemMapper.updateStatusById(orderItem.getId(), OrderStatusEnum.DRAWBACK.getId());
        }

        //售后数量等于订单购买数量更改订单状态配送单
        //已经售后的数量
        Integer afterQuantity = afterSaleProofMapper.querySumQuantity(afterSaleOrderVO.getOrderNo(),null,afterSaleOrderVO.getAfterSaleOrderNo());
        totalQuantity = afterQuantity == null ? totalQuantity : afterQuantity + totalQuantity;
        //总件数
        Integer totalAmount =  orderItemMapper.selectOrderItemActingUsable(afterSaleOrderVO.getOrderNo(),null);

        if(totalQuantity >= totalAmount){
            Orders orders = new Orders();
            orders.setOrderNo(afterSaleOrderVO.getOrderNo());
            orders.setStatus(Short.valueOf("8"));
            ordersMapper.updateByOrderNoSelective(orders);
            DeliveryPlan deliveryPlan = new DeliveryPlan();
            deliveryPlan.setOrderNo(afterSaleOrderVO.getOrderNo());
            deliveryPlan.setStatus(OrderStatusEnum.DRAWBACK.getId());
            deliveryPlan.setUpdateTime(LocalDateTime.now());
            deliveryPlanMapper.updateStatus(deliveryPlan);
        }
        afterSaleOrderMapper.updateStatus(afterSaleOrderVO.getAfterSaleOrderNo(), AfterSaleOrderStatus.SUCCESS.getStatus());
        AfterSaleProof updateProof = new AfterSaleProof();
        updateProof.setId(afterSaleOrderVO.getId());
        updateProof.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());
        afterSaleProofMapper.updateById(updateProof);
        return AjaxResult.getOK();

    }
    @Override
    public AjaxResult afterOrderDeliveryPlan(String orderNo){

        LocalDateTime now = LocalDateTime.now();
        List<DeliveryPlanVO> queryDeliveryPlans = new ArrayList<>();

        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        if(orders == null || !Objects.equals(orders.getType(),1)){
            return AjaxResult.getError();
        }
        List<OrderItem> orderItems = orderItemMapper.selectOrderItem(orderNo);
        String sku = orderItems.get(0).getSku();
        Inventory inventory = inventoryMapper.selectBySku(sku);
        Long pdId = inventory.getPdId();
        Products products = productsMapper.selectByPrimaryKey(pdId);

        Integer afterSaleTime = products.getAfterSaleTime();

        if(afterSaleTime == null){
            return AjaxResult.getError("spu无售后时长");
        }


        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orderNo);

        for (DeliveryPlanVO deliveryPlanVO : deliveryPlanVOS) {
            LocalDate deliveryTime = deliveryPlanVO.getDeliveryTime();
            LocalTime of = LocalTime.of(0, 0);
            LocalDateTime deliveryDateTime = LocalDateTime.of(deliveryTime, of);
            //可以已到货售后的配送计划
            Duration between = Duration.between(deliveryDateTime,now);
            if(between.toHours() < afterSaleTime && between.toHours() > 0 ){
                queryDeliveryPlans.add(deliveryPlanVO);
            }

        }

        return AjaxResult.getOK(queryDeliveryPlans);


    }

    @Override
    public AjaxResult afterSaleOrder(){
        Long mId = RequestHolder.getMId();
//        ArrayList<OrderVO> resultOrderVO = new ArrayList<>();
        ArrayList<AfterSaleSimplifyOrderVO> afterSaleSimplifyOrderVOS = new ArrayList<>();
        // 根据登录人id查询所有可售后的订单编号
        List<String> resultOrderNo = queryOrderNos(mId);
        for (String  orderNo : resultOrderNo) {
            // 根据订单编号查询订单详情数据
            OrderVO orderVO = orderService.normalOrderDetail(orderNo);

            AfterSaleSimplifyOrderVO saleSimplifyOrderVO = new AfterSaleSimplifyOrderVO();
            BeanCopyUtil.copyProperties(orderVO,saleSimplifyOrderVO);
            List<AfterSaleSimplifyItemVO> saleSimplifyItemVOS = BeanCopyUtil.copyListProperties(orderVO.getOrderItems(), AfterSaleSimplifyItemVO::new);
            saleSimplifyOrderVO.setOrderItems(saleSimplifyItemVOS);
            afterSaleSimplifyOrderVOS.add(saleSimplifyOrderVO);
        }
        return AjaxResult.getOK(afterSaleSimplifyOrderVOS);

    }

    @Override
    public AjaxResult afterSaleOrderNumber(){
        Long mId = RequestHolder.getMId();
        List<String> orderNos = queryOrderNos(mId);
        if(CollectionUtils.isEmpty(orderNos)){
            return AjaxResult.getOK(false);
        }
        return AjaxResult.getOK(true);
    }

    @Override
    @Deprecated
    public AjaxResult handle(AfterSaleOrderVO afterSaleOrderVO) {
        throw new RuntimeException("这个方法已经不再使用了:" + afterSaleOrderVO);
        // //分发
        // String afterSaleOrderNo = afterSaleOrderVO.getAfterSaleOrderNo();
        // Integer handleType = afterSaleOrderVO.getHandleType();
        // AfterSaleOrderVO queryAfterOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        // AfterSaleWorkflow afterSaleworkflow;
        // if(Objects.equals(queryAfterOrder.getDeliveryed(), AfterSaleDeliveryedEnum.NOT_NEED.getType())){
        //     afterSaleworkflow = afterSaleWorkflowFactory.getWorkflow(AfterSaleWorkflowTypeEnum.NOT_NEED.getType());

        //     /** {@link ExchangeGoodsWorkflow} */
        // } else if(Objects.equals(queryAfterOrder.getDeliveryed(),AfterSaleDeliveryedEnum.BROKEN.getType()) &&
        //         Objects.equals(handleType,AfterSaleHandleType.DELIVERY_GOODS.getType())) {
        //     afterSaleworkflow = afterSaleWorkflowFactory.getWorkflow(AfterSaleWorkflowTypeEnum.EXCHANGE.getType());
        // } else {
        //     afterSaleworkflow = afterSaleWorkflowFactory.getWorkflow(AfterSaleWorkflowTypeEnum.BROKEN.getType());

        // }
        // //是否为拦截退单
        // if (Arrays.asList(new Integer[]{11,12}).contains(afterSaleOrderVO.getHandleType())){
        //     afterSaleworkflow = afterSaleWorkflowFactory.getWorkflow(AfterSaleWorkflowTypeEnum.INTERCEPT.getType());
        // }
        // logger.info("afterSaleWorkflowHandle:"+afterSaleworkflow.getClass());
        // AjaxResult workflow = afterSaleworkflow.workflow(AfterSaleWorkflowTypeEnum.NOT_NEED.getType(), afterSaleOrderVO);
        // return workflow;
    }

    @Override
    @Deprecated
    public AjaxResult audit(AfterSaleOrderVO afterSaleOrderVO) {
        //分发
        String afterSaleOrderNo = afterSaleOrderVO.getAfterSaleOrderNo();
        AfterSaleOrderVO queryAfterOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        afterSaleOrderVO.setHandleType(queryAfterOrder.getHandleType());
        afterSaleOrderVO.setType(queryAfterOrder.getType());
        OrderVO orderVO = ordersMapper.selectByOrderyNo(queryAfterOrder.getOrderNo());
        AfterSaleWorkflow afterSaleworkflow;
        if(queryAfterOrder.getDeliveryed() == AfterSaleOrder.DELIVERY_NOT_RECEIVED){
            afterSaleworkflow = afterSaleWorkflowFactory.getWorkflow(AfterSaleWorkflowTypeEnum.NOT_NEED.getType());
        } else {
            afterSaleworkflow = afterSaleWorkflowFactory.getWorkflow(AfterSaleWorkflowTypeEnum.BROKEN.getType());
        }
        if (Arrays.asList(new Integer[]{11,12}).contains(afterSaleOrderVO.getHandleType())){
            afterSaleworkflow = afterSaleWorkflowFactory.getWorkflow(AfterSaleWorkflowTypeEnum.INTERCEPT.getType());
        }
        logger.info("afterSaleworkflowAudit:"+afterSaleworkflow.getClass());
        AjaxResult workflow = afterSaleworkflow.workflow(AfterSaleWorkflowTypeEnum.BROKEN.getType(), afterSaleOrderVO);

        return workflow;
    }

    //查询在售后期内的订单号
    private List<String> queryOrderNos(Long mId){
        LocalDate now = LocalDate.now();
        LocalDate startDate = now.minusDays(1);
        LocalDate endDate = now.plusDays(1);
        List<String> resultOrderNo = new ArrayList<>();
        //在售后期内的普通订单
        List<String> orderNos = ordersMapper.selectByMid(mId, OrderTypeEnum.NORMAL.getId(), startDate, endDate);
        //在售后期内的省心送订单
        List<String> timingOrderNos = ordersMapper.selectByMid(mId, OrderTypeEnum.TIMING.getId(), startDate,now);
        //在售后期内的pop订单
        List<String> popOrderNos = ordersMapper.selectByMid(mId, OrderTypeEnum.POP.getId(), startDate, now);

        resultOrderNo.addAll(popOrderNos);
        resultOrderNo.addAll(orderNos);
        resultOrderNo.addAll(timingOrderNos);
        return resultOrderNo;
    }

    /**
    * 获取售后处理信息  afterSaleRemarkType
    */
    @Deprecated
    private AfterSaleStrategy getAfterSaleStrategy(Orders orders ,AfterSaleOrderVO afterSaleOrderVO){
        throw new RuntimeException("该方法已经废弃:" + afterSaleOrderVO);
        // AfterSaleStrategy afterSaleStrategy = null;
        // Integer orderSaleType = orders.getOrderSaleType();
        // //普通订单,秒杀订单 未到货拍多拍错
        // if ((orders.getType().intValue() == 0)
        //         && afterSaleOrderVO.getDeliveryed().intValue()== 0 &&
        //         (Objects.equals(orderSaleType,0))
        //         && "拍多/拍错/不想要".equalsIgnoreCase(afterSaleOrderVO.getRefundType())) {
        //     /** {@link NotNeedAfterSaleOrder} */
        //     afterSaleStrategy = afterSaleOrderFactory.getAfterSaleStrategy(0);
        // }
        // //未到货售后其他
        // if((afterSaleOrderVO.getDeliveryed().intValue() == 0)
        //         && !"拍多/拍错/不想要".equalsIgnoreCase(afterSaleOrderVO.getRefundType()) && Objects.equals(orderSaleType,0)){
        //     /** {@link OutOfDateAfterSaleOrder} */
        //     afterSaleStrategy = afterSaleOrderFactory.getAfterSaleStrategy(2);
        // }
        // Integer handleType = afterSaleOrderVO.getHandleType();
        // if (afterSaleOrderVO.getDeliveryed().intValue() == 1){
        //     if(Objects.equals(handleType, AfterSaleHandleType.REFUND_GOODS.getType())
        //             || Objects.equals(handleType, AfterSaleHandleType.REFUND_ENTRY_BILL.getType())){

        //         /** {@link RefundAfterSaleOrder} */
        //         afterSaleStrategy = afterSaleOrderFactory.getAfterSaleStrategy(3);
        //     } else if(Objects.equals(handleType, AfterSaleHandleType.EXCHANGE_GOODS.getType())
        //             || Objects.equals(handleType, AfterSaleHandleType.DELIVERY_GOODS.getType())){

        //         /** {@link ExchangeGoodsAfterSaleOrder} */
        //         afterSaleStrategy = afterSaleOrderFactory.getAfterSaleStrategy(4);
        //     } else {
        //         /** {@link BrokenAfterSaleOrder} */
        //         afterSaleStrategy = afterSaleOrderFactory.getAfterSaleStrategy(1);
        //     }
        // }

        // if (AfterSaleHandleType.REFUSE.getType().equals(afterSaleOrderVO.getHandleType())
        //         || AfterSaleHandleType.REFUSE_BILL.getType().equals(afterSaleOrderVO.getHandleType())
        // ) {
        //     afterSaleStrategy = afterSaleOrderFactory.getAfterSaleStrategy(7);
        // }

        // //判断整单拦截
        // if (AfterSaleHandleType.BLOCK_REFUNDS.getType().equals(afterSaleOrderVO.getHandleType()) ||
        //         AfterSaleHandleType.BLOCK_INCOMING_BILLS.getType().equals(afterSaleOrderVO.getHandleType())){
        //     afterSaleStrategy = afterSaleOrderFactory.getAfterSaleStrategy(8);
        // }


        // return afterSaleStrategy;
    }

    @Override
    public List<AfterSaleOrderVO> selectAfterSaleOrderVO(AfterSaleOrder afterSaleOrder){
        String orderNo = afterSaleOrder.getOrderNo();
        //根据订单号获取信息
        if(!StringUtils.isEmpty(orderNo)){
            return selectByOrderNo(afterSaleOrder);
            //根据mId 获取订单信息
        } else{
            return selectBymId(afterSaleOrder);
        }
    }
    //根据订单号获取售后信息
    private List<AfterSaleOrderVO> selectByOrderNo(AfterSaleOrder afterSaleOrder){
        //获取订单售后信息
        List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderMapper.selectNew(afterSaleOrder);
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.selectByOrderNo(afterSaleOrder);
        handleVO(afterSaleOrderVOS,afterSaleProofs);
        return afterSaleOrderVOS;
    }
    //根据mid获取订单信息
    private List<AfterSaleOrderVO> selectBymId(AfterSaleOrder afterSaleOrder){
        //获取订单售后信息
        List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderMapper.selectNew(afterSaleOrder);
        //遍历获取售后详情
        for (AfterSaleOrderVO afterSaleOrderVO : afterSaleOrderVOS) {
            List<AfterSaleProof> select = afterSaleProofMapper.select(afterSaleOrderVO.getAfterSaleOrderNo());
            AfterSaleProof afterSaleProof = handleAfterSaleProofs(select);
            afterSaleOrderVO.setHandleNum(afterSaleProof.getHandleNum());
            afterSaleOrderVO.setQuantity(afterSaleProof.getQuantity());
        }
        return afterSaleOrderVOS;
    }

    //根据mid获取订单信息
    @Override
    public List<AfterSaleOrderVO> speedAfterSaleOrder(LocalDateTime beginTime,  LocalDateTime endTime, Long mId,Integer status){
        List<AfterSaleOrderVO> afterSaleOrderVOS = new ArrayList<>();
        //获取订单售后信息
        List<AfterSaleOrderVO> afterSaleOrderNoList = afterSaleOrderMapper.selectSpeedAfterSaleOrder(beginTime,endTime,mId,status);
        //遍历获取售后详情
        for (AfterSaleOrderVO vo : afterSaleOrderNoList) {
            AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
            List<AfterSaleProof> select = afterSaleProofMapper.select(vo.getAfterSaleOrderNo());
            AfterSaleProof afterSaleProof = handleAfterSaleProofs(select);
            afterSaleOrderVO.setHandleNum(afterSaleProof.getHandleNum());
            afterSaleOrderVO.setQuantity(afterSaleProof.getQuantity());
            afterSaleOrderVO.setView(vo.getView());
            afterSaleOrderVO.setStatus(afterSaleProof.getStatus());
            afterSaleOrderVOS.add(afterSaleOrderVO);
        }
        return afterSaleOrderVOS;
    }

    @Override
    public List<AfterSaleOrderVO> selectAfterSaleByStatus(AfterSaleOrder aso) {
        //获取订单售后信息
        List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderMapper.selectAfterSale(aso);
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.selectByOrderNo(aso);
        handleVO(afterSaleOrderVOS,afterSaleProofs);
        return afterSaleOrderVOS;
    }

    @Override
    public List<AfterSaleOrderVO> selectSuccessAfterSaleByStatus(AfterSaleOrder aso) {
        //获取订单售后信息
        List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderMapper.selectSuccessAfterSale(aso);
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.selectByOrderNo(aso);
        handleVO(afterSaleOrderVOS,afterSaleProofs);
        return afterSaleOrderVOS;
    }

    @Override
    public Integer getAfterSaleSuccessQuanlity(String orderNo) {
        Orders orders = ordersMapper.selectByOrderyNo(orderNo);
        List<OrderItem> orderItems = orderItemMapper.selectOrderItem(orders.getOrderNo());
        OrderItem orderItem = orderItems.get(0);
        String sku = orderItem.getSku();
        Integer suitId = orderItem.getSuitId();

        //普通售后
        AfterSaleOrder select = new AfterSaleOrder();
        select.setOrderNo(orders.getOrderNo());
        select.setSku(sku);
        select.setSuitId(suitId);
        select.setType(0);
        List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderService.selectSuccessAfterSaleByStatus(select);

        Integer afterSaleQuantity = 0;
        if (!CollectionUtils.isEmpty(afterSaleOrderVOS) && Objects.equals(orders.getType(), OrderTypeEnum.TIMING.getId())) {
            for (AfterSaleOrderVO afterSale : afterSaleOrderVOS) {
                Integer selectHandleType = afterSale.getHandleType();
                // 审批，审核失败 补发 和 换货不计算在内
                if(Objects.equals(selectHandleType, AfterSaleHandleType.EXCHANGE_GOODS.getType())
                        || Objects.equals(selectHandleType, AfterSaleHandleType.DELIVERY_GOODS.getType())){
                    continue;
                }

                //未到货售后 或 售后类型为 退货退款 退货录入账单
                if (afterSale.getDeliveryed() == 0 ) {
                    afterSaleQuantity += afterSale.getQuantity();
                }
            }
        }
        return afterSaleQuantity;
    }

    private AfterSaleProof handleAfterSaleProofs(List<AfterSaleProof> afterSaleProofs){
        AfterSaleProof afterSaleProof = new AfterSaleProof();
        if(!CollectionUtils.isEmpty(afterSaleProofs)){
            if(afterSaleProofs.size() > 1){
                //获取补充凭证
                List<AfterSaleProof> collect = afterSaleProofs.stream().filter(x ->
                        Objects.equals(x.getStatus(), AfterSaleOrderStatus.RE_COMMIT.getStatus()))
                        .collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(collect)){
                    afterSaleProof = collect.get(NumberUtils.INTEGER_ZERO);
                } else {
                    afterSaleProof = afterSaleProofs.get(NumberUtils.INTEGER_ZERO);
                }
            } else {
                afterSaleProof = afterSaleProofs.get(NumberUtils.INTEGER_ZERO);
            }
        }
        return afterSaleProof;
    }
    /**
    * 数据处理
    */
    private void handleVO(List<AfterSaleOrderVO> afterSaleOrderVOS,List<AfterSaleProof> afterSaleProofs){

        if(!CollectionUtils.isEmpty(afterSaleOrderVOS) && !CollectionUtils.isEmpty(afterSaleProofs)) {
            Map<String, List<AfterSaleProof>> saleProofMap = afterSaleProofs.stream().
                    collect(Collectors.groupingBy(AfterSaleProof::getAfterSaleOrderNo));
            //遍历售后单信息填充 数量信息
            for (AfterSaleOrderVO afterSaleOrderVO : afterSaleOrderVOS) {
                List<AfterSaleProof> afterSaleProofList = saleProofMap.get(afterSaleOrderVO.getAfterSaleOrderNo());
                if (!CollectionUtils.isEmpty(afterSaleProofList)) {
                    AfterSaleProof afterSaleProof;
                    //如果当前售后单有 补充凭证 的售后状态 那同一个售后单号有两个售后详情信息
                    if (afterSaleProofList.size() > NumberUtils.INTEGER_ONE) {
                        List<AfterSaleProof> lastProof = afterSaleProofList.stream()
                                .filter(x -> !Objects.equals(x.getStatus(), AfterSaleOrderStatus.RE_COMMIT.getStatus()))
                                .collect(Collectors.toList());
                        if(!CollectionUtils.isEmpty(lastProof)){
                            afterSaleProof = lastProof.get(NumberUtils.INTEGER_ZERO);
                        } else {
                            afterSaleProof = afterSaleProofs.get(NumberUtils.INTEGER_ZERO);
                        }
                    } else {
                        afterSaleProof = afterSaleProofList.get(NumberUtils.INTEGER_ZERO);
                    }
                    afterSaleOrderVO.setQuantity(afterSaleProof.getQuantity());
                    afterSaleOrderVO.setHandleNum(afterSaleProof.getHandleNum());
                    afterSaleOrderVO.setHandleType(afterSaleProof.getHandleType());
                    afterSaleOrderVO.setProofPic(afterSaleProof.getProofPic());
                    afterSaleOrderVO.setUpdatetime(afterSaleProof.getUpdatetime());
                    afterSaleOrderVO.setStatus(afterSaleProof.getStatus());
                }
            }
        }
        return;
    }

    @Override
    public AjaxResult checkStock(String orderNo, String sku, Integer deliveryId, Integer quantity, Long mId) {
        //配送地址id
        Long contactId = afterSaleOrderAction.getContactId(orderNo, deliveryId);

        /*Integer storeQuantity = queryStoreQuantity(sku, contactId);
        if (storeQuantity < quantity) {
            return AjaxResult.getErrorWithMsg("库存不足,下单失败");
        }*/
        Merchant merchant = merchantMapper.selectOneByMid(mId);
        if(mId == null){
            return AjaxResult.getErrorWithMsg("获取用户信息失败");
        }

        //改用新模型获取库存信息
        List<String> skuList = Collections.singletonList(sku);
        AreaStoreQueryReq areaStoreQueryReq = new AreaStoreQueryReq();
        areaStoreQueryReq.setContactId(contactId);
        areaStoreQueryReq.setSkuCodeList(skuList);
        areaStoreQueryReq.setMId(mId);
        areaStoreQueryReq.setSource(DistOrderSourceEnum.getDistOrderAfterSaleSource(merchant.getBusinessLine()));
        Map<String, AreaStoreQueryRes> storeQueryResMap = areaStoreFacade.getInfo(areaStoreQueryReq);
        if (CollectionUtils.isEmpty(storeQueryResMap)) {
            throw new BizException("库存信息获取异常或此地区不支持配送");
        }
        AreaStoreQueryRes queryStore = storeQueryResMap.get(sku);
        if(Objects.isNull(queryStore)){
            return AjaxResult.getErrorWithMsg(sku + "无库存信息");
        }
        if(queryStore.getOnlineQuantity() - quantity < 0){
            return AjaxResult.getErrorWithMsg(sku + "库存不足,下单失败");
        }
        return AjaxResult.getOK(queryStore);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult newSave(AfterSaleOrderVO afterSaleOrderVO) {
        logger.info("新发起售后数据"+JSON.toJSONString(afterSaleOrderVO));
        //先将订单查出来，感觉后面一直都在用，避免重复查询
        Orders orders = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        //通过工厂进行分发拼装
        CreatAfterSolution solution = afterSaleSolutionOrderFactory.getCreatSolution(afterSaleOrderVO,orders);
        AfterSaleOrderCreatHandler afterSaleOrderCreatHandler = solution.getAfterSaleOrderCreatHandler();
        //执行创建售后单
        return afterSaleOrderCreatHandler.creatAfterSaleOrder(solution,afterSaleOrderVO,orders);
    }

    @Override
    public AjaxResult newHandle(AfterSaleOrderVO afterSaleOrderVO) {
        logger.info("AfterSaleOrderServiceImpl[]newHandle[]afterSaleOrderVO:"+JSON.toJSONString(afterSaleOrderVO));

        //先将订单查出来，感觉后面一直都在用，避免重复查询
        Orders orders = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        //数据拼装
        AfterSaleOrderVO query = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());

        //订单拦截审核金额修复
        if (afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.BLOCK_REFUNDS.getType())
        || afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.BLOCK_INCOMING_BILLS.getType())){
            //省心送订单拦截，直接金额为0
            if (query.getDeliveryId() == null){
                query.setRecoveryNum(afterSaleOrderVO.getRecoveryNum());
                query.setHandleNum(afterSaleOrderVO.getHandleNum().subtract(query.getRecoveryNum()));
            }
        }else {
            if (afterSaleOrderVO.getHandleNum()!=null && afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO) != 0){
                if (afterSaleOrderVO.getHandleNum().compareTo(query.getHandleNum())!= 0 ){
                    query.setHandleNum(afterSaleOrderVO.getHandleNum().subtract(query.getRecoveryNum()));
                }
            }
        }
        if (afterSaleOrderVO.getQuantity()!=null && !afterSaleOrderVO.getQuantity().equals(0)){
            query.setQuantity(afterSaleOrderVO.getQuantity());
        }
        //只允许返券改程退款售后服务类型。
        if (query.getHandleType().equals(AfterSaleHandleType.COUPON.getType()) && afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.REFUND.getType())){
            query.setHandleType(afterSaleOrderVO.getHandleType());
        }
        if (afterSaleOrderVO.getHandleType() == -1){
            //退货退款、退款类型审核不通过发送公众号消息提醒
            try {
                if (query.getHandleType().equals(AfterSaleHandleType.REFUND.getType()) || query.getHandleType().equals(AfterSaleHandleType.REFUND_GOODS.getType()) ||
                        query.getHandleType().equals(AfterSaleHandleType.COUPON.getType())) {
                    Merchant merchant = merchantMapper.selectOneByMid(orders.getmId() == null ? afterSaleOrderVO.getmId() : Long.valueOf(orders.getmId()));
                    if (MerchantSizeEnum.SINGLE_SHOP.getValue().equals(merchant.getSize())) {
                        InventoryVO inventory = inventoryMapper.selectInventoryVOBySku(query.getSku());
                        query.setPdName(inventory.getPdName());
                        String message = AfterSaleNoticeMag.templateMessage(merchant.getOpenid(), query, AfterSaleOrderStatus.FAIL.getStatus(), query.getAddTime());
                        templateMsgSender.sendTemplateMsg(message);
                    } else {
                        log.info("大客户类型，不发送售后单公众号消息提醒！");
                    }
                }
            } catch (Exception e) {
                log.warn("审核不通过发送售后单公众号消息提醒失败！afterSaleOrderVO:{}, cause:{}", JSON.toJSONString(afterSaleOrderVO), e);
            }
            query.setHandleType(afterSaleOrderVO.getHandleType());
        }
        query.setHandleSecondaryRemark(afterSaleOrderVO.getHandleSecondaryRemark());
        query.setHandleRemark(afterSaleOrderVO.getHandleRemark());
        query.setExtraRemark(afterSaleOrderVO.getExtraRemark());
        query.setHandler(afterSaleOrderVO.getHandler());
        query.setSnapshot(afterSaleOrderVO.getSnapshot());

        //假如是退货退款、退货录入账单需要组装回收商品参数
        getExchangeGoods(query);
        HandleAfterSolution solution = afterSaleSolutionOrderFactory.getHandleSolution(query,orders);
        AfterSaleOrderHandleHandler afterSaleOrderHandleHandler = solution.getAfterSaleOrderHandleHandler();
        return afterSaleOrderHandleHandler.handleAfterSaleOrder(solution,query,orders);
    }

    @Override
    public AjaxResult newAudit(AfterSaleOrderVO afterSaleOrderVO){
        logger.info("新审批时数据"+JSON.toJSONString(afterSaleOrderVO));
        AfterSaleOrderVO query = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        query.setStatus(afterSaleOrderVO.getStatus());
        query.setAuditer(afterSaleOrderVO.getAuditer());
        query.setAuditeRemark(afterSaleOrderVO.getAuditeRemark());

        //假如是退货退款、退货录入账单需要组装回收商品参数
        getExchangeGoods(query);
        Orders orders = ordersMapper.selectByOrderNo(query.getOrderNo());
        //采用多例，防止属性覆盖
        AuditAfterSolution solution = afterSaleSolutionOrderFactory.getAuditSolution(query,orders);
        AfterSaleOrderAuditHandler afterSaleOrderAuditHandler = solution.getAfterSaleOrderAuditHandler();
        return afterSaleOrderAuditHandler.auditAfterSaleOrder(solution,query, orders);
    }

    /**
     * 获取可发起已到货/未到货售后
     * @param orderNo
     * @param isManage
     * @return
     */
    @Override
    public AjaxResult deliveryStatus(String orderNo,Boolean isManage ,Integer deliveryId, String sku){
        log.info("获取售后已到货/未到货类型参数：orderNo >>> {}, isManage >>> {}, deliveryId >>> {}, sku >>> {}", orderNo, isManage, deliveryId, sku);
        Integer adminId = null;
        if (RequestHolder.getMerchantSubject() != null) {
            adminId = RequestHolder.getAdminId();
        }
        Boolean isMall = !isManage;
        return afterSaleOrderHelper.getDeliveryStatus(orderNo, isMall, adminId, deliveryId, sku);
    }

    /**
     * 获取可售后服务类型
     * @param orderNo
     * @param sku
     * @param deliveryed
     * @param isManage
     * @return
     */
    @Override
    public AjaxResult getHandleType(String orderNo,String sku,Integer deliveryed,Boolean isManage){
        String param = "orderNo="+orderNo+"&sku="+sku+"&deliveryed="+deliveryed+"&isManage="+isManage;
        log.info("获取售后服务类型参数："+param);
        Boolean isMall = !isManage;
        return afterSaleOrderHelper.getHandleType(orderNo,sku,deliveryed,isMall);
    }


    /**
     * 计算可售后数量
     * @param afterSaleOrderVO
     * @return
     */
    @Override
    public AjaxResult getMaxQuantity(AfterSaleOrderVO afterSaleOrderVO) {
        Orders orders = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        Inventory inventory = inventoryMapper.selectBySku(afterSaleOrderVO.getSku());
        if (ObjectUtils.isEmpty(inventory)){
            return AjaxResult.getErrorWithMsg("计算可售后数量商品不存在");
        }
        AjaxResult result;
        if (orders.getType().equals(OrderTypeEnum.TIMING.getId())){
            result = timingOrderAfter.getMayAfterSaleQuantity(afterSaleOrderVO, inventory.getAfterSaleQuantity(), orders);
        }else {
            result = normalOrderAfter.getMayAfterSaleQuantity(afterSaleOrderVO, inventory.getAfterSaleQuantity(), orders);
        }
        if (!AjaxResult.isSuccess(result)){
            return result;
        }
        if ((int)result.getData() <= 0){
            result = AjaxResult.getErrorWithMsg("可售后数量不足");
        }
        return result;
    }

    @Override
    public AjaxResult getAfterSaleMoney(AfterSaleOrderVO afterSaleOrderVO) {

        AjaxResult ajaxResult = afterSaleOrderHelper.getCouponMoney(afterSaleOrderVO);
        if (!AjaxResult.isSuccess(ajaxResult)){
            return ajaxResult;
        }
        AjaxResult moneyResult = hasPrecisionDelivery(afterSaleOrderVO, ajaxResult);
        return moneyResult;
    }

    private AjaxResult hasPrecisionDelivery(AfterSaleOrderVO afterSaleOrderVO, AjaxResult ajaxResult) {
        if (afterSaleOrderVO.getDeliveryed().equals(AfterSaleDeliveryedEnum.NOT_NEED.getType())){
            //判断精准送，最后sku退款金额包含精准送金额
            if (afterSaleOrderHelper.isLastSku(afterSaleOrderVO)){
                List<OrderRelation> orderRelations = orderRelationMapper.selectByOrderNoBatch(Collections.singletonList(afterSaleOrderVO.getOrderNo()));
                if (!CollectionUtils.isEmpty(orderRelations) && null != orderRelations.get(0).getPrecisionDeliveryFee() && orderRelations.get(0).getPrecisionDeliveryFee().compareTo(BigDecimal.ZERO) > 0){
                    //看有没有用精准送运费券
                    List<OrderPreferential>  orderPreferentials = orderPreferentialService.selectByOrderNoAndType(afterSaleOrderVO.getOrderNo(), OrderPreferentialTypeEnum.ACCURATE_COUPON);
                    if (CollectionUtils.isEmpty(orderPreferentials)){
                        BigDecimal money = (BigDecimal)ajaxResult.getData();
                        return AjaxResult.getOK(money.add(BigDecimal.valueOf(30)));
                    }
                }
            }
        }
        return ajaxResult;
    }

    @Override
    public CommonResult checkDeliveryFee(AfterSaleOrderVO afterSaleOrderVO) {
        return CommonResult.ok(afterSaleOrderHelper.checkDeliveryFee(afterSaleOrderVO));
    }

    @Override
    public AjaxResult getAfterSaleMoneyForAfterSale(AfterSaleOrderVO afterSaleOrderVO) {
        AjaxResult ajaxResult = afterSaleOrderHelper.getCouponMoneyforAfterSale(afterSaleOrderVO);
        if (!AjaxResult.isSuccess(ajaxResult)){
            return ajaxResult;
        }
        AjaxResult moneyResult = hasPrecisionDelivery(afterSaleOrderVO, ajaxResult);
        return moneyResult;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult batchSave(List<AfterSaleOrderVO> afterSaleOrderVOS) {
        if(!CollectionUtils.isEmpty(afterSaleOrderVOS)){
            try {
                for (AfterSaleOrderVO afterSaleOrderVO : afterSaleOrderVOS) {
                    AjaxResult save = newSave(afterSaleOrderVO);
                    if (!AjaxResult.isSuccess(save)){
                        //手动回滚
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return save;
                    }
                }
            } catch (NoTransactionException e) {
                log.error("批量生成售后单异常：{}",e);
                //手动回滚
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return AjaxResult.getErrorWithMsg("生成订单时异常");
            }
        }
        return AjaxResult.getOK();
    }

    @Override
    public Integer getUnfilledAfterSaleOrderByMid(Long mId) {
        return afterSaleOrderMapper.getUnfilledAfterSaleOrderByMid(mId);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void timingAutoRefund(String orderNo) {

            //初始化售后退款数据
            AfterSaleOrderVO afterSaleOrderVO = this.initAfterSaleOrderVO(orderNo);
            if(afterSaleOrderVO.getOrders().getStatus() == OrderStatusEnum.CANCEL.getId() || afterSaleOrderVO.getOrders().getStatus() == OrderStatusEnum.DRAWBACK.getId()){
                log.info("timingAutoRefund-省心送订单状态不可退款,orderNo:{},status:{}",orderNo,afterSaleOrderVO.getOrders().getStatus());
                return;
            }
            //获取最大售后数量
            this.getMayAfterSaleQuantity(afterSaleOrderVO);
            if (!ObjectUtils.isEmpty(afterSaleOrderVO) && afterSaleOrderVO.getQuantity() <= 0){
                log.info("省心送自动售后数量不足,orderNo:{}",orderNo);
                return;
            }
            //获取最大售后金额
            afterSaleOrderHelper.getTimingAfterSaleMoney(afterSaleOrderVO);
            //生成对应售后单
            afterSaleOrderVO.setHandleRemark("省心送订单超过90天自动原路退回");
            afterSaleOrderVO.setAuditeRemark("省心送订单超过90天自动原路退回");
            this.initAfterSaleOrder(afterSaleOrderVO);
            //处理退款
            Boolean refundXianMuTag = getRefundTag(orderNo,afterSaleOrderVO.getOrders());
            if (refundXianMuTag){
                //超出退款时间退至鲜沐卡
                this.timingRefundXianMuPay(afterSaleOrderVO);
            }else {
                //原路退回
                this.timingRefund(afterSaleOrderVO);
            }
            //更新订单状态
            handleTimingOrderStatus(afterSaleOrderVO);
            //用户触达：短信、公众号
            sendTimingAfterSaleWxMsg(afterSaleOrderVO);
    }

    private Boolean getRefundTag(String orderNo,Orders orders) {
        Payment query = new Payment();
        query.setOrderNo(orderNo);
        Payment payment = paymentMapper.selectOne(query);
        BasePayTypeEnum typeEnum = getPayTypeEnum(payment.getPayType());
        LocalDateTime orderTime = orders.getOrderTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        switch (typeEnum.ordinal()) {
            case 0:
                //微信判断订单时间是否超过一年
                if (isMoreThanOneYearAgo(orderTime,LocalDateTime.now())){
                    return Boolean.TRUE;
                }else {
                    return Boolean.FALSE;
                }
            case 4:
                //招行判断订单时间是否超过180天
                if (isMoreThan180DaysAgo(orderTime,LocalDateTime.now())){
                    return Boolean.TRUE;
                }else {
                    return Boolean.FALSE;
                }
            default:
                return Boolean.FALSE;
        }
    }

    private static boolean isMoreThanOneYearAgo(LocalDateTime pastTime, LocalDateTime currentTime) {
        // 使用ChronoUnit计算两个时间的差距
        long yearsBetween = ChronoUnit.YEARS.between(pastTime, currentTime);

        // 判断是否超过1年
        return yearsBetween > 1;
    }

    private static boolean isMoreThan180DaysAgo(LocalDateTime pastTime, LocalDateTime currentTime) {
        // 使用ChronoUnit计算两个时间的差距
        long daysBetween = ChronoUnit.DAYS.between(pastTime, currentTime);

        // 判断是否超过180天
        return daysBetween > 180;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public CommonResult timingRefundView() {
        List<String> orderNoList = getNeedReturnTimingOrderNoForView();
        List<TimingRefundViewVO>  timingRefundViewVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderNoList)){
            return  CommonResult.ok(timingRefundViewVOList);
        }
        List<String> refundOrderNos = new ArrayList<>();
        Map<String,Integer> orderRefundQuantity = new HashMap<>();
        for (String orderNo : orderNoList){
            //初始化售后退款数据
            AfterSaleOrderVO afterSaleOrderVO = this.initAfterSaleOrderVO(orderNo);
            if(afterSaleOrderVO.getOrders().getStatus() == OrderStatusEnum.CANCEL.getId() || afterSaleOrderVO.getOrders().getStatus() == OrderStatusEnum.DRAWBACK.getId()){
                log.info("弹窗timingRefundView-省心送订单状态不可退款,orderNo:{},status:{}",orderNo,afterSaleOrderVO.getOrders().getStatus());
                continue;
            }
            //获取最大售后数量
            this.getMayAfterSaleQuantity(afterSaleOrderVO);
            if (!ObjectUtils.isEmpty(afterSaleOrderVO) && afterSaleOrderVO.getQuantity() <= 0){
                log.info("弹窗timingRefundView-省心送自动售后数量不足,orderNo:{}",orderNo);
                continue;
            }
            refundOrderNos.add(orderNo);
            orderRefundQuantity.put(orderNo,afterSaleOrderVO.getQuantity());
        }
        if (!CollectionUtils.isEmpty(refundOrderNos)){
            List<String> OrderNos = timingOrderRefundRecordMapper.selectByOrderNoList(refundOrderNos,TimingRefundRecordTypeEnum.ORDER_REFUND_VIEW.getType());
            if (!CollectionUtils.isEmpty(OrderNos)){
                refundOrderNos.removeAll(OrderNos);
            }
        }

        if (CollectionUtils.isEmpty(refundOrderNos)){
            return  CommonResult.ok(timingRefundViewVOList);
        }
        //根据数量封装弹窗内容
        buildTimingRefundView(timingRefundViewVOList,refundOrderNos,orderRefundQuantity);
        //保存已弹窗订单
        List<TimingOrderRefundRecord> refundRecords = new ArrayList<>();
        refundOrderNos.forEach(e ->{
            TimingOrderRefundRecord timingOrderRefundRecord = new TimingOrderRefundRecord();
            timingOrderRefundRecord.setOrderNo(e);
            timingOrderRefundRecord.setCreateTime(LocalDateTime.now());
            timingOrderRefundRecord.setType(TimingRefundRecordTypeEnum.ORDER_REFUND_VIEW.getType());
            refundRecords.add(timingOrderRefundRecord);
        });
        timingOrderRefundRecordMapper.insertBatch(refundRecords);
        return CommonResult.ok(timingRefundViewVOList.stream().sorted(Comparator.comparing(TimingRefundViewVO::getOrderTime)).collect(Collectors.toList()));
    }

    @Override
    public CommonResult timingNotPlanView() {
        List<String> orderNoList = getTimingNotPlanOrderNoForView();
        List<TimingRefundViewVO>  timingRefundViewVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderNoList)){
            return  CommonResult.ok(timingRefundViewVOList);
        }
        List<String> refundOrderNos = new ArrayList<>();
        Map<String,Integer> orderRefundQuantity = new HashMap<>();
        for (String orderNo : orderNoList){
            //初始化售后退款数据
            AfterSaleOrderVO afterSaleOrderVO = this.initAfterSaleOrderVO(orderNo);
            if(afterSaleOrderVO.getOrders().getStatus() == OrderStatusEnum.CANCEL.getId() || afterSaleOrderVO.getOrders().getStatus() == OrderStatusEnum.DRAWBACK.getId()){
                log.info("弹窗timingNotPlanView-省心送订单状态不可退款,orderNo:{},status:{}",orderNo,afterSaleOrderVO.getOrders().getStatus());
                continue;
            }
            //获取最大售后数量
            this.getMayAfterSaleQuantity(afterSaleOrderVO);
            if (!ObjectUtils.isEmpty(afterSaleOrderVO) && afterSaleOrderVO.getQuantity() <= 0){
                log.info("弹窗timingNotPlanView-省心送自动售后数量不足,orderNo:{}",orderNo);
                continue;
            }
            refundOrderNos.add(orderNo);
            orderRefundQuantity.put(orderNo,afterSaleOrderVO.getQuantity());
        }
        if (!CollectionUtils.isEmpty(refundOrderNos)){
            List<String> OrderNos = timingOrderRefundRecordMapper.selectByOrderNoList(refundOrderNos,TimingRefundRecordTypeEnum.HISTORICAL.getType());
            if (!CollectionUtils.isEmpty(OrderNos)){
                refundOrderNos.removeAll(OrderNos);
            }
        }

        if (CollectionUtils.isEmpty(refundOrderNos)){
            return  CommonResult.ok(timingRefundViewVOList);
        }
        //根据数量封装弹窗内容
        buildTimingRefundView(timingRefundViewVOList,refundOrderNos,orderRefundQuantity);
        //保存已弹窗订单
        List<TimingOrderRefundRecord> refundRecords = new ArrayList<>();
        refundOrderNos.forEach(e ->{
            TimingOrderRefundRecord timingOrderRefundRecord = new TimingOrderRefundRecord();
            timingOrderRefundRecord.setOrderNo(e);
            timingOrderRefundRecord.setCreateTime(LocalDateTime.now());
            timingOrderRefundRecord.setType(TimingRefundRecordTypeEnum.HISTORICAL.getType());
            refundRecords.add(timingOrderRefundRecord);
        });
        timingOrderRefundRecordMapper.insertBatch(refundRecords);
        return CommonResult.ok(timingRefundViewVOList.stream().sorted(Comparator.comparing(TimingRefundViewVO::getOrderTime)).collect(Collectors.toList()));
    }

    @Override
    public CommonResult timingOldOrderView() {
        List<String> orderNoList = getTimingOldOrderNoForView();
        List<TimingRefundViewVO>  timingRefundViewVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderNoList)){
            return  CommonResult.ok(timingRefundViewVOList);
        }
        List<String> refundOrderNos = new ArrayList<>();
        Map<String,Integer> orderRefundQuantity = new HashMap<>();
        for (String orderNo : orderNoList){
            //初始化售后退款数据
            AfterSaleOrderVO afterSaleOrderVO = this.initAfterSaleOrderVO(orderNo);
            if(afterSaleOrderVO.getOrders().getStatus() == OrderStatusEnum.CANCEL.getId() || afterSaleOrderVO.getOrders().getStatus() == OrderStatusEnum.DRAWBACK.getId()){
                log.info("弹窗timingOldOrderView-省心送订单状态不可退款,orderNo:{},status:{}",orderNo,afterSaleOrderVO.getOrders().getStatus());
                continue;
            }
            //获取最大售后数量
            this.getMayAfterSaleQuantity(afterSaleOrderVO);
            if (!ObjectUtils.isEmpty(afterSaleOrderVO) && afterSaleOrderVO.getQuantity() <= 0){
                log.info("弹窗timingOldOrderView-省心送自动售后数量不足,orderNo:{}",orderNo);
                continue;
            }
            refundOrderNos.add(orderNo);
            orderRefundQuantity.put(orderNo,afterSaleOrderVO.getQuantity());
        }
        if (!CollectionUtils.isEmpty(refundOrderNos)){
            List<String> OrderNos = timingOrderRefundRecordMapper.selectByOrderNoList(refundOrderNos,TimingRefundRecordTypeEnum.HISTORICAL.getType());
            if (!CollectionUtils.isEmpty(OrderNos)){
                refundOrderNos.removeAll(OrderNos);
            }
        }

        if (CollectionUtils.isEmpty(refundOrderNos)){
            return  CommonResult.ok(timingRefundViewVOList);
        }
        //根据数量封装弹窗内容
        buildTimingRefundView(timingRefundViewVOList,refundOrderNos,orderRefundQuantity);
        //保存已弹窗订单
        List<TimingOrderRefundRecord> refundRecords = new ArrayList<>();
        refundOrderNos.forEach(e ->{
            TimingOrderRefundRecord timingOrderRefundRecord = new TimingOrderRefundRecord();
            timingOrderRefundRecord.setOrderNo(e);
            timingOrderRefundRecord.setCreateTime(LocalDateTime.now());
            timingOrderRefundRecord.setType(TimingRefundRecordTypeEnum.HISTORICAL.getType());
            refundRecords.add(timingOrderRefundRecord);
        });
        timingOrderRefundRecordMapper.insertBatch(refundRecords);
        return CommonResult.ok(timingRefundViewVOList.stream().sorted(Comparator.comparing(TimingRefundViewVO::getOrderTime)).collect(Collectors.toList()));
    }

    private void buildTimingRefundView(List<TimingRefundViewVO> timingRefundViewVOList,List<String> orderNos,Map<String,Integer> orderRefundQuantity){
        List<TimingOrder> timingOrders = timingOrderMapper.selectByOrderNoList(orderNos);
        Map<String,TimingOrder> timingOrderMap = timingOrders.stream().collect(Collectors.toMap(TimingOrder::getOrderNo, e -> e));
        for (String orderNo : orderNos){
            List<OrderItemVO> orderItems =  orderItemMapper.selectByOrderNoNewPro(orderNo);
            TimingRefundViewVO timingRefundViewVO = new TimingRefundViewVO();
            timingRefundViewVO.setOrderNo(orderNo);
            timingRefundViewVO.setUndeliveredQuantity(orderRefundQuantity.get(orderNo));
            timingRefundViewVO.setOrderQuantity(orderNos.size());
            if (!CollectionUtils.isEmpty(orderItems)){
                timingRefundViewVO.setPicture(orderItems.get(0).getSkuPic());
                timingRefundViewVO.setPdName(orderItems.get(0).getPdName());
                timingRefundViewVO.setQuantity(orderItems.get(0).getAmount());
                timingRefundViewVO.setOrderTime(DateUtils.date2LocalDateTime(orderItems.get(0).getAddTime()));
            }

            if (!CollectionUtils.isEmpty(timingOrderMap) && timingOrderMap.get(orderNo)!= null && timingOrderMap.get(orderNo).getDeliveryEndTime()!= null ){
                timingRefundViewVO.setDeadlineDate(DateUtils.date2LocalDate(timingOrderMap.get(orderNo).getDeliveryEndTime()));
            }

            timingRefundViewVOList.add(timingRefundViewVO);
        }
    }

    private List<String> getNeedReturnTimingOrderNoForView(){
        Long mId = RequestHolder.getMId();
        //查询当前用户新数据
        LocalDate returnEndTime = LocalDate.now().plusDays(Global.TIMING_ORDER_AFTER_VIEW_DAY);
        LocalDate queryStartTime = LocalDate.now();
        log.info("弹窗查询开始时间：{}，结束时间：{}",queryStartTime,returnEndTime);
        List<String> newOrderNos = timingOrderRefundTimeMapper.selectOrderNoForView(queryStartTime,returnEndTime,mId);
        if (CollectionUtils.isEmpty(newOrderNos)){
            return new ArrayList<>();
        }
        List<String> viewOrderList = new ArrayList<>(newOrderNos);
        return viewOrderList.stream().distinct().collect(Collectors.toList());
    }

    private List<String> getTimingOldOrderNoForView(){
        Long mId = RequestHolder.getMId();
        //查询当前用户老订单数据
        List<String> oldOrderNos = ordersMapper.getNeedReturnTimingOrderForView(Global.TIMING_ORDER_OLD_TIME,mId);
        if (CollectionUtils.isEmpty(oldOrderNos)){
            return new ArrayList<>();
        }
        List<String> viewOrderList = new ArrayList<>(oldOrderNos);
        return viewOrderList.stream().distinct().collect(Collectors.toList());
    }

    private List<String> getTimingNotPlanOrderNoForView(){
        Long mId = RequestHolder.getMId();
        //查询当前用户下单省心送后，超过48小时未设置配送计划
        List<String> notPlanOrderNos = ordersMapper.selectUnscheduledOrders(mId);
        if (CollectionUtils.isEmpty(notPlanOrderNos)){
            return new ArrayList<>();
        }
        List<String> viewOrderList = new ArrayList<>(notPlanOrderNos);
        return viewOrderList.stream().distinct().collect(Collectors.toList());
    }

    private void timingRefund(AfterSaleOrderVO afterSaleOrderVO){
        //查询当前退款信息是否是预付商品
        List<PrepayInventoryRecord> records = prepayInventoryRecordMapper.selectByOrderNoAndSku(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getSku());
        Inventory inventory = inventoryMapper.selectBySku(afterSaleOrderVO.getSku());
        if (CollectionUtils.isEmpty(records) && (afterSaleOrderVO.getTimingAfterSaleMoney() == null || afterSaleOrderVO.getTimingAfterSaleMoney().compareTo(BigDecimal.ZERO) <= 0) && Objects.equals(inventory.getType(),0)) {
            throw new DefaultServiceException(0,"售后金额不能小于零");
        }

        //修改售后记录
        //查询历史退款记录
        OrderVO orderVO = ordersMapper.selectByOrderyNo(afterSaleOrderVO.getOrderNo());
        BigDecimal totalFen = orderVO.getTotalPrice().multiply(BigDecimal.valueOf(100));
        BigDecimal refundFen = afterSaleOrderVO.getTimingAfterSaleMoney().multiply(BigDecimal.valueOf(100));
        logger.info("refundFen={}",refundFen);
        // List<Refund> refunds = refundMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        String refundNo = Global.createAfterSaleOrderNo(afterSaleOrderVO.getOrderNo());//afterSaleOrderVO.getOrderNo() + (refunds.size() + 1);
        Refund refund = new Refund(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getAfterSaleOrderNo(), refundNo, totalFen, refundFen);
        if (inventory!=null){
            if(Objects.equals(inventory.getType(),1) || !CollectionUtils.isEmpty(records)){
                refund.setRefundChannel("鲜沐卡");
            }
        }
        refund.setCouponId(afterSaleOrderVO.getCouponId());
        refund.setStatus(Integer.valueOf(RefundStatusEnum.IN_HANLING.ordinal()).byteValue());
        refundMapper.insertSelective(refund);
        // 插入事件表 等待执行第三方退款
        RefundHandleEvent handleEvent = new RefundHandleEvent();
        handleEvent.setStatus(HandleEventStatus.NEW.ordinal());
        handleEvent.setRefundNo(refund.getRefundNo());
        handleEvent.setRetryCount(0);
        refundHandleEventMapper.insert(handleEvent);
    }

    private void timingRefundXianMuPay(AfterSaleOrderVO afterSaleOrderVO){

        OrderVO orderVO = ordersMapper.selectByOrderyNo(afterSaleOrderVO.getOrderNo());
        BigDecimal totalFen = orderVO.getTotalPrice().multiply(BigDecimal.valueOf(100));
        BigDecimal refundFen = afterSaleOrderVO.getTimingAfterSaleMoney().multiply(BigDecimal.valueOf(100));
        logger.info("refundFen={}",refundFen);
        // List<Refund> refunds = refundMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        String refundNo = Global.createAfterSaleOrderNo(afterSaleOrderVO.getOrderNo()); // afterSaleOrderVO.getOrderNo() + (refunds.size() + 1);
        Refund refund = new Refund(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getAfterSaleOrderNo(), refundNo, totalFen, refundFen);
        refund.setRefundChannel("鲜沐卡");
        refund.setCouponId(afterSaleOrderVO.getCouponId());
        refund.setStatus((byte) RefundStatusEnum.SUCCESS.ordinal());
        refund.setRefundDesc("其他");
        refund.setOnlineRefundEndTime(new Date());
        refund.setCashFee(totalFen.divide(new BigDecimal(100)));
        refund.setCashRefundFee(refundFen.divide(new BigDecimal(100)));
        refundMapper.insertSelective(refund);
        //新增余额
        rechargeRecordService.insert(afterSaleOrderVO.getmId(), afterSaleOrderVO.getAccountId(), RechargeRecordType.REFUND.getId(), refund.getRefundNo(), afterSaleOrderVO.getTimingAfterSaleMoney());

    }

    @Override
    public void handleTimingOrderStatus(AfterSaleOrderVO afterSaleOrderVO) {
        Orders orders = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        Integer afterSaleQuantity = afterSaleOrderService.getAfterSaleSuccessQuanlity(afterSaleOrderVO.getOrderNo());

        BigDecimal totalRefund = BigDecimal.ZERO;
        List<Refund> refunds =  refundMapper.selectLikeOrderNo(afterSaleOrderVO.getOrderNo());
        refunds =  refunds.stream().filter(e -> e.getStatus().intValue() == RefundStatusEnum.SUCCESS.ordinal()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(refunds)){
            totalRefund = refunds.stream().map(Refund::getRefundFee).reduce(BigDecimal.ZERO,BigDecimal::add);
        }
        totalRefund = totalRefund.add(afterSaleOrderVO.getTimingAfterSaleMoney().multiply(BigDecimal.valueOf(100)));
        List<DeliveryPlanVO> plans = deliveryPlanMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        int deliveredQuantity = plans.stream().filter(deliveryPlanVO -> !deliveryPlanVO.getDeliveryTime().isAfter(LocalDate.now())).mapToInt(dp -> dp.getQuantity()).sum();
        Integer quantity = afterSaleOrderVO.getOrderItemAmount();
        log.info("orderNo:{}, deliveredQuanlity:{}, afterSaleQuanlity:{},quantity:{}", afterSaleOrderVO.getOrderNo(), deliveredQuantity, afterSaleQuantity, quantity);

        if (totalRefund.divide(new BigDecimal(100)).compareTo(orders.getTotalPrice()) == 0) {
            ordersMapper.updateStatus(afterSaleOrderVO.getOrderNo(), OrderStatusEnum.DRAWBACK.getId(), orders.getStatus());
            afterSaleOrderVO.getOrders().setStatus((short) OrderStatusEnum.DRAWBACK.getId());
            DeliveryPlan deliveryPlan = new DeliveryPlan();
            deliveryPlan.setOrderNo(afterSaleOrderVO.getOrderNo());
            deliveryPlan.setStatus(8);
            deliveryPlan.setUpdateTime(LocalDateTime.now());
            deliveryPlanMapper.updateStatus(deliveryPlan);
        } else if(deliveredQuantity + afterSaleQuantity == quantity){
            orderItemMapper.updateStatusByOrderNo(afterSaleOrderVO.getOrderNo(), OrderStatusEnum.RECEIVED.getId());
            ordersMapper.updateStatus(afterSaleOrderVO.getOrderNo(), OrderStatusEnum.RECEIVED.getId(), orders.getStatus());
            afterSaleOrderVO.getOrders().setStatus((short) OrderStatusEnum.RECEIVED.getId());
            orderService.updateScore(afterSaleOrderVO.getOrderNo(), orders.getmId());

        }

    }

    @Override
    public void timingRefundSms() {
        LocalDate orderTimeSmsFirst = LocalDateTime.now().plusDays(Global.TIMING_ORDER_AFTER_SMS_FIRST).toLocalDate();
        LocalDate orderTimeSmsSecond = LocalDateTime.now().plusDays(Global.TIMING_ORDER_AFTER_SMS_SECOND).toLocalDate();
        LocalDate orderTimeSmsThird = LocalDateTime.now().plusDays(Global.TIMING_ORDER_AFTER_SMS_THIRD).toLocalDate();
        LocalDate orderTimeSmsFourth = LocalDateTime.now().plusDays(Global.TIMING_ORDER_AFTER_SMS_FOURTH).toLocalDate();
        LocalDate orderTimeSmsFifth = LocalDateTime.now().plusDays(Global.TIMING_ORDER_AFTER_SMS_FIFTH).toLocalDate();
        List<String> firstOrderNos =  timingOrderRefundTimeMapper.selectOrderNoByRefundDate(orderTimeSmsFirst);
        List<String> secondOrderNos = timingOrderRefundTimeMapper.selectOrderNoByRefundDate(orderTimeSmsSecond);
        List<String> thirdOrderNos = timingOrderRefundTimeMapper.selectOrderNoByRefundDate(orderTimeSmsThird);
        List<String> fourthOrderNos = timingOrderRefundTimeMapper.selectOrderNoByRefundDate(orderTimeSmsFourth);
        List<String> fifthOrderNos = timingOrderRefundTimeMapper.selectOrderNoByRefundDate(orderTimeSmsFifth);
        //发送短信通知和公众号消息推送
        sendSmsForOrderList(firstOrderNos,Global.TIMING_ORDER_AFTER_SMS_FIRST,orderTimeSmsFirst);
        sendSmsForOrderList(secondOrderNos,Global.TIMING_ORDER_AFTER_SMS_SECOND,orderTimeSmsSecond);
        sendSmsForOrderList(thirdOrderNos,Global.TIMING_ORDER_AFTER_SMS_THIRD,orderTimeSmsThird);
        sendSmsForOrderList(fourthOrderNos,Global.TIMING_ORDER_AFTER_SMS_FOURTH,orderTimeSmsFourth);
        sendSmsForOrderList(fifthOrderNos,Global.TIMING_ORDER_AFTER_SMS_FIFTH,orderTimeSmsFifth);

    }

    @Override
    public void closeOrderGenerateAfterSaleOrder(List<OrderItemVO> itemList, String orderNo, UserBase user) {
        if (CollectionUtils.isEmpty(itemList)){
            throw new BizException("当前订单项信息不存在！");
        }
        for (OrderItemVO orderItemVO : itemList){
            //初始化售后退款数据
            AfterSaleOrderVO afterSaleOrderVO = this.initAfterSaleOrderVOBySku(orderNo,orderItemVO);
            if(afterSaleOrderVO.getOrders().getStatus() == OrderStatusEnum.CANCEL.getId() || afterSaleOrderVO.getOrders().getStatus() == OrderStatusEnum.DRAWBACK.getId()){
                log.info("closeOrderGenerateAfterSaleOrder-订单状态异常,orderNo:{},status:{}",orderNo,afterSaleOrderVO.getOrders().getStatus());
                return;
            }
            //获取最大售后数量
            this.getMayAfterSaleQuantity(afterSaleOrderVO);
            if (!ObjectUtils.isEmpty(afterSaleOrderVO) && afterSaleOrderVO.getQuantity() <= 0){
                log.info("closeOrderGenerateAfterSaleOrder售后数量不足,orderNo:{}",orderNo);
                return;
            }
            //获取最大售后金额
            afterSaleOrderHelper.getTimingAfterSaleMoney(afterSaleOrderVO);
            //生成对应售后单
            afterSaleOrderVO.setHandleRemark("关单自动生成售后单，手动关单后请及时联系财务线下打款");
            afterSaleOrderVO.setAuditeRemark("关单自动生成售后单，手动关单后请及时联系财务线下打款");
            afterSaleOrderVO.setAuditer(Objects.isNull(user) ? "系统处理" : user.getNickname());
            afterSaleOrderVO.setProductType(orderItemVO.getProductType());
            this.initAfterSaleOrder(afterSaleOrderVO);
        }

    }

    /**
     * 发送微信模板消息
     *
     * @param afterSaleOrderVO
     * @param day
     */
    private void sendWxMsg(AfterSaleOrderVO afterSaleOrderVO,Integer day) {
        try {
            //通过子账号查询对应的openId
            MerchantSubAccount merchantSubAccount = new MerchantSubAccount();
            merchantSubAccount.setAccountId(afterSaleOrderVO.getAccountId());
            merchantSubAccount.setDeleteFlag(CommonStatus.YES.getCode());
            MerchantSubAccount subAccount = merchantSubAccountMapper.selectByEntity(
                    merchantSubAccount);
            if (subAccount == null || org.apache.commons.lang3.StringUtils.isBlank(subAccount.getOpenid())) {
                log.warn("获取openId失败,timingDeliveryVO:{}", JSON.toJSONString(afterSaleOrderVO));
                return;
            }
            String subMsg = TimingOrderRefundWarningMsg.templateMessage(subAccount.getOpenid(),
                    afterSaleOrderVO,day);
            //发送微信消息
            log.info("省心送退款发送公众号通知，订单号:{},剩余天数:{}",afterSaleOrderVO.getOrders().getOrderNo(),day);
            MsgSendLogResp sendResult = templateMsgSender.sendTemplateMsg(subMsg);
            log.info("【省心送退款预警】发送微信消息,返回结果:{},timingDeliveryVO:{}", JSON.toJSONString(sendResult),
                    afterSaleOrderVO);
        } catch (Exception e) {
            log.error("【省心送退款预警】发送微信消息失败,cause:{}", Throwables.getStackTraceAsString(e));
        }

    }

    private void sendSmsForOrderList(List<String> orderNos,Integer day,LocalDate refundTime) {
        if (CollectionUtils.isEmpty(orderNos)){
            return;
        }
        List<AfterSaleOrderVO> refundOrderNos = new ArrayList<>();
        for (String orderNo : orderNos){
            //初始化售后退款数据
            AfterSaleOrderVO afterSaleOrderVO = this.initAfterSaleOrderVO(orderNo);
            if(afterSaleOrderVO.getOrders().getStatus() == OrderStatusEnum.CANCEL.getId() || afterSaleOrderVO.getOrders().getStatus() == OrderStatusEnum.DRAWBACK.getId()){
                log.info("sendSmsForOrderList-短信-省心送自动售后不可退款,orderNo:{},status:{}",orderNo,afterSaleOrderVO.getOrders().getStatus());
                continue;
            }
            //获取最大售后数量
            this.getMayAfterSaleQuantity(afterSaleOrderVO);
            if (!ObjectUtils.isEmpty(afterSaleOrderVO) && afterSaleOrderVO.getQuantity() <= 0){
                log.info("短信-省心送自动售后数量不足,orderNo:{}",orderNo);
                continue;
            }
            refundOrderNos.add(afterSaleOrderVO);
        }

        if (!CollectionUtils.isEmpty(refundOrderNos)){
            refundOrderNos.forEach(e -> {
                //发送短信通知
                timingRefundSendSms(e.getOrderNo(),refundTime);
                //发送公众号消息推送
                sendWxMsg(e,day);
            });
        }
    }


    /**
     * 省心送自动退款短信触达
     * @param orderNo orderNo
     * @param refundTime refundTime
     */
    private void timingRefundSendSms(String orderNo,LocalDate refundTime){
        SMS sms = new SMS();
        OrderVO orderVO = ordersMapper.selectByOrderyNo(orderNo);
        if (ObjectUtils.isEmpty(orderVO) || null == orderVO.getMphone()){
            return;
        }
        sms.setPhone(orderVO.getMphone());
        sms.setSceneId(18L);
        sms.setArgs(Arrays.asList(orderNo,refundTime.format(DateTimeFormatter.ISO_DATE).replace("-","."),refundTime.format(DateTimeFormatter.ISO_DATE).replace("-",".")));
        log.info("省心送退款发送短信通知，订单号:{},退款时间:{}",orderNo,refundTime);
        smsSenderFactory.getSMSSender().sendSMS(sms);
    }

    private AfterSaleOrderVO initAfterSaleOrderVO (String orderNo){
        AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        List<OrderItem> orderItem = orderItemMapper.selectTimingItem(orderNo);
        if (CollectionUtils.isEmpty(orderItem)){
            return afterSaleOrderVO;
        }
        Inventory inventory = inventoryMapper.selectBySku(orderItem.get(0).getSku());
        afterSaleOrderVO.setmId(orders.getmId());
        afterSaleOrderVO.setAccountId(orders.getAccountId());
        afterSaleOrderVO.setOrderNo(orderNo);
        afterSaleOrderVO.setHandleType(AfterSaleHandleType.REFUND.getType());
        afterSaleOrderVO.setDeliveryed(AfterSaleOrder.DELIVERY_NOT_RECEIVED);
        afterSaleOrderVO.setSku(orderItem.get(0).getSku());
        afterSaleOrderVO.setSuitId(orderItem.get(0).getSuitId());
        afterSaleOrderVO.setProductType(orderItem.get(0).getProductType());
        afterSaleOrderVO.setAfterSaleQuantity(inventory.getAfterSaleQuantity());
        afterSaleOrderVO.setOrderItemAmount(orderItem.get(0).getAmount());
        afterSaleOrderVO.setPdName(orderItem.get(0).getPdName());
        afterSaleOrderVO.setAfterSaleUnit(inventory.getAfterSaleUnit());
        afterSaleOrderVO.setType(0);
        afterSaleOrderVO.setOrders(orders);
        return afterSaleOrderVO;
    }

    private void initAfterSaleOrder (AfterSaleOrderVO afterSaleOrderVO){
        //进入前拼装好 需要的信息

        AfterSaleOrder updateAfterSaleOrder = new AfterSaleOrder();
        AfterSaleProof updateAfterSaleProof = new AfterSaleProof();
        String afterSaleOrderNo = afterSaleOrderAction.createAfterSaleOrderNo(afterSaleOrderVO.getmId(), afterSaleOrderVO.getAccountId());
        afterSaleOrderVO.setAfterSaleOrderNo(afterSaleOrderNo);
        afterSaleOrderAction.assembleTimingAfterSaleOrder(afterSaleOrderVO,updateAfterSaleOrder);
        afterSaleOrderAction.assembleTimingAfterSalePoof(afterSaleOrderVO,updateAfterSaleProof);
    }



    private void getMayAfterSaleQuantity(AfterSaleOrderVO afterSaleOrderVO) {
            if (null == afterSaleOrderVO.getOrderNo()){
                log.info("省心送售后订单查询为空");
                afterSaleOrderVO.setQuantity(0);
                return;
            }
            Integer amount = afterSaleOrderVO.getOrderItemAmount();
            //已配送数量
            Integer integer = deliveryPlanMapper.selectDeliveredQuantity4Follow(afterSaleOrderVO.getOrderNo());
            //查询省心送定单已经未到货退款的数量
            Integer notDeliveryQuantity = 0;
            List<AfterSaleOrderVO> selectList = afterSaleOrderMapper.selectAfterQuantityByOrderNo(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getSku(),afterSaleOrderVO.getDeliveryId(),afterSaleOrderVO.getSuitId(),afterSaleOrderVO.getAfterSaleOrderNo(),afterSaleOrderVO.getProductType());
            if (!org.springframework.util.CollectionUtils.isEmpty(selectList)) {
                for (AfterSaleOrderVO afterSale : selectList) {
                    if (afterSale.getDeliveryed() == 0 ) {
                        notDeliveryQuantity = notDeliveryQuantity + afterSale.getQuantity();
                    }
                }
            }
            amount = amount - integer - notDeliveryQuantity;
            if (amount <= 0){
                afterSaleOrderVO.setQuantity(0);
                return;
            }
        afterSaleOrderVO.setQuantity(amount);
    }

    @Override
    public List<String> getNeedReturnTimingOrder(String startTime){
        LocalDate refundDate = LocalDate.now();
        log.info("省心送自动退款查询日期：{}",refundDate);
        List<TimingOrderRefundTime>  orderList = timingOrderRefundTimeMapper.selectByRefundDate(refundDate);
        List<String> orderNoList = null;
        if (!CollectionUtils.isEmpty(orderList)){
            orderNoList = orderList.stream().map(TimingOrderRefundTime::getOrderNo).collect(Collectors.toList());
        }
        return orderNoList;
    }

    @Override
    public List<String> getAfterSaleTimeOutNo() {
        return afterSaleOrderMapper.selectTimeOutProof();
    }

    /**
     * 发送微信模板消息
     *
     * @param afterSaleOrderVO
     */
    private void sendTimingAfterSaleWxMsg(AfterSaleOrderVO afterSaleOrderVO) {
        try {
            //通过子账号查询对应的openId
            MerchantSubAccountQuery merchantSubAccountQuery = new MerchantSubAccountQuery();
            merchantSubAccountQuery.setAccountId(afterSaleOrderVO.getAccountId());
            MerchantSubAccount subAccount = merchantSubAccountRepository.selectOne(merchantSubAccountQuery);
            if (subAccount == null || org.apache.commons.lang3.StringUtils.isBlank(subAccount.getOpenid())) {
                log.warn("获取openId失败,timingDeliveryVO:{}", JSON.toJSONString(afterSaleOrderVO));
                return;
            }
            log.info("售后发送微信模板消息,发送内容子账号信息:{}", JsonUtil.toJson(subAccount));

            String subMsg = TimingRefundMsg.templateMessage(subAccount.getOpenid(),afterSaleOrderVO);
            //发送微信消息
            log.info("发送微信消息,发送内容:{}", subMsg);
            MsgSendLogResp sendResult = templateMsgSender.sendTemplateMsg(subMsg);
            log.info("发送微信消息,返回结果:{}", JSON.toJSONString(sendResult));

            //短信触达
            SMS sms = new SMS();
            OrderVO orderVO = ordersMapper.selectByOrderyNo(afterSaleOrderVO.getOrderNo());
            if (ObjectUtils.isEmpty(orderVO) || null == orderVO.getMphone()){
                return;
            }
            sms.setPhone(orderVO.getMphone());
            sms.setSceneId(19L);
            sms.setArgs(Collections.singletonList(afterSaleOrderVO.getOrderNo()));
            log.info("省心送退款成功发送短信通知，订单号:{}",afterSaleOrderVO.getOrderNo());
            smsSenderFactory.getSMSSender().sendSMS(sms);

        } catch (Exception e) {
            log.error("发送微信消息失败,cause:{}", Throwables.getStackTraceAsString(e));
        }
    }

    private AfterSaleOrderVO initAfterSaleOrderVOBySku(String orderNo,OrderItemVO orderItemVO){
        AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        Inventory inventory = inventoryMapper.selectBySku(orderItemVO.getSku());
        afterSaleOrderVO.setmId(orders.getmId());
        afterSaleOrderVO.setAccountId(orders.getAccountId());
        afterSaleOrderVO.setOrderNo(orderNo);
        Integer handleType = Objects.equals(orders.getDirect(), MajorDirectEnum.PERIOD.getType())
                ? AfterSaleHandleType.ENTRY_BILL.getType()
                : AfterSaleHandleType.REFUND.getType();
        afterSaleOrderVO.setHandleType(handleType);
        afterSaleOrderVO.setDeliveryed(AfterSaleOrder.DELIVERY_NOT_RECEIVED);
        afterSaleOrderVO.setSku(orderItemVO.getSku());
        afterSaleOrderVO.setSuitId(orderItemVO.getSuitId());
        afterSaleOrderVO.setProductType(orderItemVO.getProductType());
        afterSaleOrderVO.setAfterSaleQuantity(inventory.getAfterSaleQuantity());
        afterSaleOrderVO.setOrderItemAmount(orderItemVO.getAmount());
        afterSaleOrderVO.setPdName(orderItemVO.getPdName());
        afterSaleOrderVO.setAfterSaleUnit(inventory.getAfterSaleUnit());
        afterSaleOrderVO.setType(0);
        afterSaleOrderVO.setOrders(orders);
        return afterSaleOrderVO;
    }

    private void getExchangeGoods(AfterSaleOrderVO query) {
        if (Objects.equals(query.getHandleType(), AfterSaleHandleType.REFUND_GOODS.getType()) ||
                Objects.equals(query.getHandleType(), AfterSaleHandleType.REFUND_ENTRY_BILL.getType())) {
            List<ExchangeGoods> exchangeGoods = afterSaleDeliveryPathMapper.selectExchangeGoods(query.getAfterSaleOrderNo());
            if (!CollectionUtils.isEmpty(exchangeGoods)) {
                query.setExchangeGoodList(exchangeGoods);
            }
        }
    }
}
