package net.summerfarm.mall.service.impl;

import com.alibaba.fastjson.JSON;
import net.summerfarm.enums.SkuTypeEnum;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.mapper.DistributionRuleMapper;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.vo.DistributionFreeVO;
import net.summerfarm.mall.model.vo.DistributionRuleVO;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.model.vo.OrderItemVO;
import net.summerfarm.mall.service.DistributionRuleService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * <AUTHOR> ct
 * create at:  2019/7/9  11:51 AM
 */
@Service("distributionRuleService")
public class DistributionRuleServiceImpl implements DistributionRuleService {
    @Resource
    private DistributionRuleMapper distributionRuleMapper;

    @Override
    public DistributionRuleVO queryByAdminId() {
        //获取用户信息
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        Integer adminId = merchantSubject.getAdminId();
        Area area = merchantSubject.getArea();
        Integer areaNo = area.getAreaNo();
        //查询配送费是否是适用全部城市  areaNo 0代表全部
        DistributionRule distributionRule = distributionRuleMapper.queryAreaNO(adminId);
        if (distributionRule != null) {
            areaNo = 0;
        }
        DistributionRuleVO distributionRuleVO = distributionRuleMapper.queryByAdminId(adminId, areaNo);
        return distributionRuleVO;
    }

    @Override
    public BigDecimal customDeliveryFee(Integer adminId, Integer areaNo, List<OrderItemCalcDTO> dtoList) {
        //先查具体城市，没有就查全部
        DistributionRule distributionRule = distributionRuleMapper.selectValid(adminId, areaNo);
        if (distributionRule == null) {
            distributionRule = distributionRuleMapper.selectValid(adminId, 0);
        }
        //未配置定制运费，不计算
        if (distributionRule == null) {
            return null;
        }

        List<RuleDistributionFree> validRule = distributionRuleMapper.selectValidRule(distributionRule.getId());
        if (CollectionUtils.isEmpty(validRule)) {
            return distributionRule.getDeliveryFee();
        }

        DistributionRuleVO ruleVO = distributionRuleMapper.queryByAdminId(adminId, areaNo);
        if (ruleVO == null) {
            ruleVO = distributionRuleMapper.queryByAdminId(adminId, 0);
            if (ruleVO == null) {
                return null;
            }
        }

        List<DistributionFreeRule> distributionFreeRules = ruleVO.getDistributionFreeRules();
        //未配置免邮逻辑，配送费直接生效
        if (CollectionUtils.isEmpty(distributionFreeRules)) {
            return ruleVO.getDeliveryFee();
        }

        //判断是否满足免邮
        for (DistributionFreeRule freeRule : distributionFreeRules) {
            boolean satisfied = satisfiedFreeRule(freeRule.getRule(), dtoList);
            if (satisfied) {
                return BigDecimal.ZERO;
            }
        }

        return ruleVO.getDeliveryFee();
    }

    @Override
    public BigDecimal customDeliveryFeeForAfter(Integer adminId, Integer areaNo, List<OrderItemVO> orderItems) {
        //先查具体城市，没有就查全部
        DistributionRule distributionRule = distributionRuleMapper.selectValid(adminId, areaNo);
        if (distributionRule == null) {
            distributionRule = distributionRuleMapper.selectValid(adminId, 0);
        }
        //未配置定制运费，不计算
        if (distributionRule == null) {
            return null;
        }

        List<RuleDistributionFree> validRule = distributionRuleMapper.selectValidRule(distributionRule.getId());
        if (CollectionUtils.isEmpty(validRule)) {
            return distributionRule.getDeliveryFee();
        }

        DistributionRuleVO ruleVO = distributionRuleMapper.queryByAdminId(adminId, areaNo);
        if (ruleVO == null) {
            ruleVO = distributionRuleMapper.queryByAdminId(adminId, 0);
            if (ruleVO == null) {
                return null;
            }
        }

        List<DistributionFreeRule> distributionFreeRules = ruleVO.getDistributionFreeRules();
        //未配置免邮逻辑，配送费直接生效
        if (CollectionUtils.isEmpty(distributionFreeRules)) {
            return ruleVO.getDeliveryFee();
        }

        //判断是否满足免邮
        for (DistributionFreeRule freeRule : distributionFreeRules) {
            boolean satisfied = satisfiedFreeRuleForAfterSale(freeRule.getRule(), orderItems);
            if (satisfied) {
                return BigDecimal.ZERO;
            }
        }

        return ruleVO.getDeliveryFee();
    }

    /**
     * 判断是否满足免邮规则
     *
     * @param freeRule 免邮规则
     * @param dtoList  订单项
     * @return 是否
     */
    private boolean satisfiedFreeRule(String freeRule, List<OrderItemCalcDTO> dtoList) {
        if (StringUtils.isEmpty(freeRule)) {
            return false;
        }
        List<DistributionFreeVO> voList = JSON.parseArray(freeRule, DistributionFreeVO.class);
        if (CollectionUtils.isEmpty(voList)) {
            return false;
        }

        Map<Integer, Integer> amountMap = orderMap(dtoList, OrderItem::getAmount, (map, item) -> map == null ? item : map + item);
        Map<Integer, BigDecimal> priceMap = orderMap(dtoList, OrderItemCalcDTO::getCalcPartDeliveryFee, (map, item) -> map == null ? item : map.add(item));

        //所有子条件都满足才免邮
        for (DistributionFreeVO vo : voList) {
            Integer category = vo.getCategory();
            //判断金额是否满足免邮
            if (Objects.equals(vo.getType(), 1)) {
                BigDecimal price = priceMap.getOrDefault(category, BigDecimal.ZERO);
                if (price.compareTo(vo.getAmount()) < 0) {
                    return false;
                }

                continue;
            }

            //判断件数是否免邮
            Integer amount = amountMap.getOrDefault(category, 0);
            if (amount.compareTo(vo.getNumber()) < 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * 售后根据订单项判断是否满足免邮规则
     *
     * @param freeRule 免邮规则
     * @param orderItems  订单项
     * @return 是否
     */
    private boolean satisfiedFreeRuleForAfterSale(String freeRule, List<OrderItemVO> orderItems) {
        if (StringUtils.isEmpty(freeRule)) {
            return false;
        }
        List<DistributionFreeVO> voList = JSON.parseArray(freeRule, DistributionFreeVO.class);
        if (CollectionUtils.isEmpty(voList)) {
            return false;
        }

        Map<Integer, Integer> amountMap = orderMapForItem(orderItems, OrderItem::getAmount, (map, item) -> map == null ? item : map + item);
        Map<Integer, BigDecimal> priceMap = orderMapForItem(orderItems, OrderItemVO::getActualTotalPrice, (map, item) -> map == null ? item : map.add(item));

        //所有子条件都满足才免邮
        for (DistributionFreeVO vo : voList) {
            Integer category = vo.getCategory();
            //判断金额是否满足免邮
            if (Objects.equals(vo.getType(), 1)) {
                BigDecimal price = priceMap.getOrDefault(category, BigDecimal.ZERO);
                if (price.compareTo(vo.getAmount()) < 0) {
                    return false;
                }

                continue;
            }

            //判断件数是否免邮
            Integer amount = amountMap.getOrDefault(category, 0);
            if (amount.compareTo(vo.getNumber()) < 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * 生成各个类型统计map
     *
     * @param dtoList 订单项
     * @param getFun  取值函数
     * @param addFun  计算函数
     * @param <T>     金额 or 数量
     * @return map
     */
    private <T> Map<Integer, T> orderMap(List<OrderItemCalcDTO> dtoList, Function<OrderItemCalcDTO, T> getFun, BiFunction<T, T, T> addFun) {
        Map<Integer, T> result = new HashMap<>(8);
        for (OrderItemCalcDTO dto : dtoList) {
            //全部
            T allApply = addFun.apply(result.get(0), getFun.apply(dto));
            result.put(0, allApply);

            //代仓、自营
            if (SkuTypeEnum.AGENT.ordinal() == dto.getSkuType()) {
                T agentApply = addFun.apply(result.get(1), getFun.apply(dto));
                result.put(1, agentApply);
            } else {
                T agentApply = addFun.apply(result.get(2), getFun.apply(dto));
                result.put(2, agentApply);
            }

            //乳制品、非乳制品
            if (Objects.equals(dto.getCategoryType(), 2)) {
                T agentApply = addFun.apply(result.get(3), getFun.apply(dto));
                result.put(3, agentApply);
            } else {
                T agentApply = addFun.apply(result.get(4), getFun.apply(dto));
                result.put(4, agentApply);
            }
        }

        return result;
    }

    /**
     * 生成各个类型统计map
     *
     * @param orderItems 订单项
     * @param getFun  取值函数
     * @param addFun  计算函数
     * @param <T>     金额 or 数量
     * @return map
     */
    private <T> Map<Integer, T> orderMapForItem(List<OrderItemVO> orderItems, Function<OrderItemVO, T> getFun, BiFunction<T, T, T> addFun) {
        Map<Integer, T> result = new HashMap<>(8);
        for (OrderItemVO dto : orderItems) {
            //全部
            T allApply = addFun.apply(result.get(0), getFun.apply(dto));
            result.put(0, allApply);

            //代仓、自营
            if (SkuTypeEnum.AGENT.ordinal() == dto.getSkuType()) {
                T agentApply = addFun.apply(result.get(1), getFun.apply(dto));
                result.put(1, agentApply);
            } else {
                T agentApply = addFun.apply(result.get(2), getFun.apply(dto));
                result.put(2, agentApply);
            }

            //乳制品、非乳制品
            if (Objects.equals(dto.getType(), 2)) {
                T agentApply = addFun.apply(result.get(3), getFun.apply(dto));
                result.put(3, agentApply);
            } else {
                T agentApply = addFun.apply(result.get(4), getFun.apply(dto));
                result.put(4, agentApply);
            }
        }

        return result;
    }

}
