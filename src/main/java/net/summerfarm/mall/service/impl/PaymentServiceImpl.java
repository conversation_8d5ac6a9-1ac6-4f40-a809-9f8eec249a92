package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.crm.client.dto.WechatUserInfoDTO;
import net.summerfarm.crm.client.provider.WechatUserQueryProvider;
import net.summerfarm.enums.OrderSaleType;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.common.util.MailUtil;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.DistOrderSourceEnum;
import net.summerfarm.mall.enums.OrderStatusEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.model.vo.FenceVO;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.facade.WncDeliveryRuleQueryFacade;
import net.summerfarm.mall.service.facade.dto.PayAfterDeliveryDateReq;
import net.summerfarm.mall.service.helper.OrderServiceHelper;
import net.summerfarm.mall.task.AsyncTaskService;
import net.summerfarm.mall.wechat.templatemessage.OrderPaySuccessMsg;
import net.summerfarm.mall.wechat.templatemessage.TemplateMsgSender;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static net.summerfarm.mall.common.util.DateUtils.LONG_DATE_FORMAT;

/**
 * @Package: net.summerfarm.wechat.service.impl
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/10/8
 */
@Slf4j
@Service
public class PaymentServiceImpl implements PaymentService {

    private static final Logger logger = LoggerFactory.getLogger(PaymentService.class);

    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private TemplateMsgSender templateMsgSender;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    @Lazy
    private OrderService orderService;
    @Resource
    @Lazy
    private AsyncTaskService asyncTaskService;
    @Resource
    private MailUtil mailUtil;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private CardMapper cardMapper;
    @Resource
    private CardRuleMapper cardRuleMapper;
    @Resource
    private MerchantCardMapper merchantCardMapper;
    @Resource
    @Lazy
    private DiscountCardService discountCardService;

    @Resource
    private RechargeService rechargeService;
    @Resource
    private OrderServiceHelper orderServiceHelper;
    @Resource
    private ExpandActivityService expandActivityService;
    @Resource
    private WncDeliveryRuleQueryFacade deliveryRuleQueryFacade;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private FenceService fenceService;
    @DubboReference
    private WechatUserQueryProvider wechatUserQueryProvider;
    @Resource
    private MerchantSubAccountMapper subAccountMapper;
    @NacosValue(value = "${paySucMsgTemp}",autoRefreshed = true)
    private String paySucMsgTemp;
    @NacosValue(value = "${paySucMsgGrayUnionId}",autoRefreshed = true)
    private List<String> paySucMsgGrayUnionId;

    @Autowired
    MqProducer mqProducer;

    @Resource
    private OrderCancelAutoRefundService orderCancelAutoRefundService;

    /**
     * 回调结果为成功后的处理
     *
     * @param payment
     * @param order
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void notifySuccess(Payment payment, Orders order) {
        int rs = paymentMapper.countSuccessByOrderNo(payment.getOrderNo());
        log.info(payment.getOrderNo() + "回调情况" + rs);
        if ("0".equals(rs + "")) {
            // 当支付信息没有重复，对支付成功信息更新
            //黄金卡和商品订单
            if (OrderTypeEnum.VIRTUAL_GOODS.getId().equals(order.getType())) {
                if (OrderSaleType.NORMAL_OR_CARD.ordinal() == order.getOrderSaleType()) {
                    discountCardPaySuccess(order, payment);
                } else if (OrderSaleType.ADVANCE_OR_RECHARGE.ordinal() == order.getOrderSaleType()) {
                    rechargePaySuccess(order, payment);
                }
            } else {
                paymentSuccess(order, payment);
            }
        } else {
            //支付成功信息查重
            log.warn("支付结果通知重复!" + payment.getOrderNo());
        }
    }

    @Override
    public void insertPayment(String transacationNumber, String payOrderNo, String payType, Integer companyAccountId, String scanCode, String bocPayType) {
        Payment query = new Payment();
        query.setOrderNo(payOrderNo);
        Payment old = paymentMapper.selectOne(query);
        if (old == null) {
            Payment payment = new Payment();
            payment.setTransactionNumber(transacationNumber);
            payment.setPayType(payType);
            payment.setOrderNo(payOrderNo);
            payment.setCompanyAccountId(companyAccountId);
            payment.setStatus(0);
            payment.setScanCode(scanCode);
            payment.setBocPayType(bocPayType);
            paymentMapper.insertSelective(payment);
        } else {
            if (!Objects.equals(0, old.getStatus())) {
                throw new DefaultServiceException(0, "付款已完成，请勿重复支付");
            }

            old.setTransactionNumber(transacationNumber);
            old.setPayType(payType);
            old.setOrderNo(payOrderNo);
            old.setCompanyAccountId(companyAccountId);
            old.setStatus(0);
            old.setScanCode(scanCode);
            old.setBocPayType(bocPayType);
            paymentMapper.updateByPrimaryKeySelective(old);
        }
    }

    @Override
    public void insertPayment(Payment payment) {
        insertPayment(payment.getTransactionNumber(), payment.getOrderNo(), payment.getPayType(), payment.getCompanyAccountId(), payment.getScanCode(), payment.getBocPayType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusByPaymentNo(String paymentNo, Integer status) {
        Payment payment = new Payment();
        payment.setOrderNo(paymentNo);
        payment.setStatus(status);
        paymentMapper.updateByOrderNo(payment);
    }

    /**
     * 支付成功后操作
     */
    public void paymentSuccess(Orders orders, Payment payment) {
        //2.判断支付金额是否一致
        String out_trade_no = orders.getOrderNo();
        BigDecimal totalPrice = orders.getTotalPrice();
        BigDecimal total_fee = payment.getMoney();
        if (totalPrice.compareTo(total_fee) != 0) {
            log.warn("支付金额({})与订单金额({})不匹配", total_fee, totalPrice);
            throw new DefaultServiceException(1, "微信返回订单金额与商户侧的订单金额不一致");
        }

        // 更新支付单信息
        log.info("更新支付单信息:{}", payment.getOrderNo());
        paymentMapper.updateByOrderNo(payment);

        // 如订单被取消 则创建退款单
        orderCancelAutoRefundService.executeOrderCancelAutoGenerateRefund(payment, orders);

        //3.判断订单状态
        if (!(orders.getStatus() == OrderStatusEnum.NO_PAYMENT.getId())) {
            log.error("{}支付成功回调时订单状态异常，当前状态为：{}", out_trade_no, orders.getStatus());
            // 通知售后处理后续
            String title = "【待处理事项】支付订单状态异常";
            String content = out_trade_no + "支付成功回调时订单状态异常，当前状态为：" + orders.getStatus();
            String[] to = new String[]{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"};
            mailUtil.sendMail(title, content, to, null);
//            throw new DefaultServiceException(1, ResultConstant.ORDER_STATUS_ERROR);
            return;
        }

        //计算配送日期
        Long mId = orders.getmId();
        Long accountId = orders.getAccountId();
        String orderNo = orders.getOrderNo();
        Area area = areaMapper.selectByMId(mId);
        log.info("支付回调成功orderNo：" + orderNo + "个人区域信息：" + JSON.toJSONString(area));
        int outTime = 0;

        //5.修改配送计划状态 //省心送或者预售
        DeliveryPlan updateDeliveryPlan = new DeliveryPlan();
        updateDeliveryPlan.setOrderNo(orderNo);
        //代销不入仓订单不更新配送时间「代销不入仓不加单、以订单生成时间为准」
        //POP订单也以订单生成时间为准 不更新配送时间
        /*boolean notWarehouseIntoOrder = orderService.isNotWarehouseIntoOrder(orderNo);
        if (!notWarehouseIntoOrder && !Objects.equals(orders.getType(), OrderTypeEnum.POP.getId())){
            updateDeliveryPlan.setDeliveryTime(Global.getStartTimeByDate(orders.getOrderTime()).toLocalDate().plusDays(2));
        }*/
            // 省心送送 下单状态都为1   更新配送计划状态 在冻结范围内的状态变为3 以后的状态变为2
        if (Objects.equals(orders.getType(), OrderTypeEnum.TIMING.getId())) {
                /*List<OrderItem> orderItems = orderItemMapper.selectOrderItem(orderNo);
                String sku = orderItems.get(0).getSku();
                List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orderNo);
                if(!CollectionUtils.isEmpty(deliveryPlanVOS)) {
                    //遍历设置时间
                    deliveryPlanVOS.forEach(plan -> {
                        Integer storeNo = plan.getOrderStoreNo();
                        LocalTime closeTimingTime = logisticsService.selectCloseTime(storeNo);
                        // 省心送送 下单状态都为1   更新配送计划状态 在冻结范围内的状态变为3 以后的状态变为2
                        LocalDate updateTimingDate = Global.getStartTime(closeTimingTime).plusDays(2).toLocalDate();
                        if (Objects.equals(orders.getType(), 1)) {
                            updateTimingDate = deliveryService.queryTomorrow(sku, storeNo);
                            updateDeliveryPlan.setDeliveryTime(updateTimingDate);
                            updateDeliveryPlan.setId(plan.getId());
                            Integer status = updateTimingDate.isAfter(plan.getDeliveryTime()) || Objects.equals(updateTimingDate, plan.getDeliveryTime()) ? 3 : 2;
                            updateDeliveryPlan.setStatus(status);
                            deliveryPlanMapper.updateStatusById(updateDeliveryPlan);
                        }

                    });
                }*/
        } else {
                List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNoNoStatus(orderNo);
                if(CollectionUtils.isEmpty(deliveryPlanVOS)){
                    deliveryPlanVOS = deliveryPlanMapper.selectByMasterOrderNoVOByNoStatus(orderNo);
                }

                Long contactId = deliveryPlanVOS.get(0).getContactId();
                Contact contact = contactMapper.selectByPrimaryKey(contactId);

                //普通订单修改（有随单送同时修改随单配送计划）
                updateDeliveryPlan.setStatus(OrderStatusEnum.DELIVERING.getId());
                updateDeliveryPlan.setMasterOrderNo(orderNo);
                List<OrderItem> orderItems = orderItemMapper.selectOrderItem(orderNo);
                List<String> skuList = orderItems.stream().map(OrderItem::getSku).collect(Collectors.toList());

                PayAfterDeliveryDateReq deliveryDateReq = new PayAfterDeliveryDateReq();
                deliveryDateReq.setContactId(contactId);
                deliveryDateReq.setMerchantId(orders.getmId());
                deliveryDateReq.setOrderTime(DateUtils.date2LocalDateTime(orders.getOrderTime()));
                deliveryDateReq.setPayTime(DateUtils.date2LocalDateTime(payment.getEndTime()));
                deliveryDateReq.setSkus(skuList);
                deliveryDateReq.setCity(contact.getCity());
                deliveryDateReq.setArea(contact.getArea());
                deliveryDateReq.setSource(DistOrderSourceEnum.getSourceEnumByOrderType(orders.getType()));
                LocalDate deliveryDate= deliveryRuleQueryFacade.queryPayAfterDeliveryDate(deliveryDateReq);
                updateDeliveryPlan.setDeliveryTime(deliveryDate);


                updateDeliveryPlan.setContactId(contactId);
                if (orders.getOutTimes().equals(1)) { //判断是否超时加单
                    //普通订单根据订单配送计划的地址的围栏区域编号获取该区域是否可以加单(之前逻辑是根据商户的默认地址获取区域是否可以加单)
                    FenceVO fenceVO = fenceService.selectFenceByCityArea(contact.getArea(), contact.getCity());
                    if (Objects.nonNull(fenceVO)) {
                        area = areaMapper.selectByAreaNo(fenceVO.getAreaNo());
                        log.info("PaymentServiceImpl[]paymentSuccess[]orderNo:{}area:{}", orderNo, JSON.toJSONString(area));
                    }
                    //超时加单逻辑 判断能不能超时加单
                    Boolean addOrderTag = orderServiceHelper.supportOverTimeAddOrder(DateUtils.date2LocalDateTime(payment.getEndTime()), area, contact, orders);
                    if (addOrderTag) {
                        List<LocalDate> deliveryDateList = deliveryRuleQueryFacade.getDeliveryRuleList(DateUtils.date2LocalDateTime(payment.getEndTime()).minusMinutes(30)
                                ,orders.getmId(),updateDeliveryPlan.getContactId(),
                                null,null, DistOrderSourceEnum.getSourceEnumByOrderType(orders.getType()));
                        updateDeliveryPlan.setDeliveryTime(deliveryDateList.get(0));
                        outTime = 2;
                    } else {
                        outTime = 3;
                    }
                }
                deliveryPlanMapper.updateStatus(updateDeliveryPlan);

        }

        //6.更新订单信息
        Orders record = new Orders();
        record.setOutTimes(outTime);
        record.setOrderNo(orderNo);
        record.setStatus((short) OrderStatusEnum.DELIVERING.getId());
        ordersMapper.updateByOrderNoSelective(record);

        //7.支付成功更新订单项状态
        orderItemMapper.updateStatusByOrderNo(orderNo, 3);

        //8.支付成功回调，查询触发拓展购买活动
        expandActivityService.payCallBack(orders.getAreaNo(),orders.getOrderNo(),orders.getmId());

        Map selectKeys = new HashMap();
        selectKeys.put("mId", mId);
        Merchant merchant = merchantMapper.selectOne(selectKeys);

        //9.更新最近下单时间
        Merchant merchantUpdateKeys = new Merchant();
        merchantUpdateKeys.setmId(mId);
        merchantUpdateKeys.setLastOrderTime(orders.getOrderTime());
        merchantMapper.updateByPrimaryKeySelective(merchantUpdateKeys);

        if (orders.getCardRuleId() != null) {
            CardRule cardRule = cardRuleMapper.selectByPrimaryKey(orders.getCardRuleId());
            if (cardRule == null) {
                log.error("优惠卡规则{}未查询到", orders.getCardRuleId());
            } else {
                Card card = cardMapper.selectByPrimaryKey(cardRule.getCardId());
                MerchantCard merchantCard = new MerchantCard();
                merchantCard.setMId(mId);
                merchantCard.setCardId(card.getId());
                merchantCard.setCardRuleId(orders.getCardRuleId());

                if (card.getType() == 0) { //固定时间间隔到期
                    merchantCard.setVaildDate(LocalDateTime.now().plusDays(card.getVaildTime()));
                } else {
                    merchantCard.setVaildDate(card.getVaildDate());
                }
                merchantCard.setSender("系统");
                merchantCard.setAddTime(LocalDateTime.now());
                merchantCardMapper.insert(merchantCard);
            }
        }

        orderService.addFruitSales(orderNo);
        //推送茶百道安佳淡奶油订单数据
        //pushOrderToCBD(orderNo);

        //发送支付成功微信消息
        String msg = null;

        try {
            // 如果客户没有添加企微账户推送企微二维码
            Boolean sendWechatMsg = false;
            MerchantSubAccount subAccount = subAccountMapper.selectMangerByMId(mId);
            if (subAccount != null) {
                // 企微模板变更，暂时指定客户发送
                if (CollectionUtil.isEmpty(paySucMsgGrayUnionId)) {
                    DubboResponse<WechatUserInfoDTO> userRes = wechatUserQueryProvider.selectOfficialUserByUnionId(subAccount.getUnionid());
                    if (userRes.isSuccess() && userRes.getData() == null) {
                        sendWechatMsg = true;
                    }
                } else if (paySucMsgGrayUnionId.contains(subAccount.getUnionid())) {
                    sendWechatMsg = true;
                }
            }
            //支付成功提醒 普通订单
            if (Objects.equals(orders.getOrderSaleType(), 0) && Objects.equals(orders.getType(), 0)) {
                msg = OrderPaySuccessMsg.templateMessage(merchant.getOpenid(), out_trade_no, DateUtils.date2String(payment.getEndTime(), LONG_DATE_FORMAT),
                        payment.getMoney().toString(), updateDeliveryPlan.getDeliveryTime(),sendWechatMsg,paySucMsgTemp);
                //省心送订单
            } else if (Objects.equals(orders.getOrderSaleType(), 0) && Objects.equals(orders.getType(), 1)) {
                msg = OrderPaySuccessMsg.templateMessage(merchant.getOpenid(), out_trade_no, DateUtils.date2String(payment.getEndTime(), LONG_DATE_FORMAT), payment.getMoney().toString(), null,sendWechatMsg,paySucMsgTemp);
            }
            logger.info("订单回调日志: {}",msg);
            templateMsgSender.sendTemplateMsg(msg);
        } catch (Exception e) {
            log.info("消息提醒有错误 orderNO={}", orderNo);
        }

        // 订单后续处理
        Integer orderSaleType = orders.getOrderSaleType();
        log.info("支付回调处理-订单号:{},onSaleType:{}", orderNo, orderSaleType);
        log.info("订单" + out_trade_no + "支付成功，支付结果正确!");
    }

    private void discountCardPaySuccess(Orders orders, Payment payment) {
        String out_trade_no = orders.getOrderNo();
        //1.判断支付金额是否一致
        BigDecimal total_fee = payment.getMoney();
        if (orders.getTotalPrice().compareTo(total_fee) != 0) {
            log.warn("支付金额({})与订单金额({})不匹配", total_fee, orders.getTotalPrice());
            throw new DefaultServiceException(1, "微信返回订单金额与商户侧的订单金额不一致");
        }

        //2.判断订单状态
        if (orders.getStatus() != OrderStatusEnum.NO_PAYMENT.getId()) {
            log.error("{}支付成功回调时订单状态异常，当前状态为：{}", out_trade_no, orders.getStatus());
            // 通知售后处理后续
            String title = "【待处理事项】支付订单状态异常";
            String content = out_trade_no + "支付成功回调时订单状态异常，当前状态为：" + orders.getStatus();
            String[] to = new String[]{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"};
            mailUtil.sendMail(title, content, to, null);
            throw new DefaultServiceException(1, ResultConstant.ORDER_TYPE_ERROR);
        }

        //3.支付表信息更新
        log.info("更新订单：" + out_trade_no + "支付数据");
        paymentMapper.updateByOrderNo(payment);

        //4.更新订单信息
        Orders record = new Orders();
        record.setOrderNo(orders.getOrderNo());
        record.setStatus((short) OrderStatusEnum.RECEIVED.getId());
        ordersMapper.updateByOrderNoSelective(record);

        //5.发放优惠卡
        discountCardService.sendCardToMerchant(orders.getDiscountCardId(), orders.getmId());

        //6.发送支付成功微信消息
        Map selectKeys = new HashMap();
        selectKeys.put("mId", orders.getmId());
        Merchant merchant = merchantMapper.selectOne(selectKeys);
        String msg = OrderPaySuccessMsg.discountCardMessage(merchant.getOpenid(), out_trade_no, DateUtils.date2String(payment.getEndTime(), LONG_DATE_FORMAT), payment.getMoney().toString());
        templateMsgSender.sendTemplateMsg(msg);
        log.info("订单" + out_trade_no + "支付成功，支付结果正确!");
    }

    private void rechargePaySuccess(Orders orders, Payment payment) {
        String out_trade_no = orders.getOrderNo();
        //1.判断支付金额是否一致
        BigDecimal total_fee = payment.getMoney();
        if (orders.getTotalPrice().compareTo(total_fee) != 0) {
            log.warn("支付金额({})与订单金额({})不匹配", total_fee, orders.getTotalPrice());
            throw new DefaultServiceException(1, "微信返回订单金额与商户侧的订单金额不一致");
        }

        //2.判断订单状态
        if (orders.getStatus() != OrderStatusEnum.NO_PAYMENT.getId()) {
            log.error("{}支付成功回调时订单状态异常，当前状态为：{}", out_trade_no, orders.getStatus());
            // 通知售后处理后续
            String title = "【待处理事项】支付订单状态异常";
            String content = out_trade_no + "支付成功回调时订单状态异常，当前状态为：" + orders.getStatus();
            String[] to = new String[]{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"};
            mailUtil.sendMail(title, content, to, null);
            throw new DefaultServiceException(1, ResultConstant.ORDER_TYPE_ERROR);
        }

        //3.支付表信息更新
        log.info("更新订单：" + out_trade_no + "支付数据");
        paymentMapper.updateByOrderNo(payment);

        //4.更新订单信息
        Orders record = new Orders();
        record.setOrderNo(orders.getOrderNo());
        record.setStatus((short) OrderStatusEnum.RECEIVED.getId());
        int updateCount = ordersMapper.updateStatus(orders.getOrderNo(), (short) OrderStatusEnum.RECEIVED.getId(), (short) OrderStatusEnum.NO_PAYMENT.getId());
        if (updateCount != 1) {
            throw new DefaultServiceException("订单状态更新失败，orderNo：" +  orders.getOrderNo() + "，目标状态：" + OrderStatusEnum.RECEIVED.getId() + "，原始状态：" + OrderStatusEnum.NO_PAYMENT.getId());
        }

        //5.处理充值
        rechargeService.paySuccess(orders);

        //6.支付成功回调，查询触发拓展购买活动
        //expandActivityService.payCallBack(orders.getOrderNo());

        //6.发送支付成功微信消息
        Map selectKeys = new HashMap();
        selectKeys.put("mId", orders.getmId());
        Merchant merchant = merchantMapper.selectOne(selectKeys);
        String msg = OrderPaySuccessMsg.discountCardMessage(merchant.getOpenid(), out_trade_no, DateUtils.date2String(payment.getEndTime(), LONG_DATE_FORMAT), payment.getMoney().toString());
        templateMsgSender.sendTemplateMsg(msg);
        log.info("订单" + out_trade_no + "支付成功，支付结果正确!");
    }
}
