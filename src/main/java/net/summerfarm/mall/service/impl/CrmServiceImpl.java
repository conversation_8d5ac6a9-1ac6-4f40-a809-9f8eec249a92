package net.summerfarm.mall.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.client.dto.WechatUserInfoDTO;
import net.summerfarm.crm.client.enums.BdQrCodeQueryChannelEnum;
import net.summerfarm.enums.SubAccountType;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.PopupTypeEnum;
import net.summerfarm.mall.facade.crm.WechatUserQueryFacade;
import net.summerfarm.mall.mapper.MerchantPopupRecordMapper;
import net.summerfarm.mall.mapper.PopupTypeMapper;
import net.summerfarm.mall.model.domain.MerchantPopupRecord;
import net.summerfarm.mall.model.domain.MerchantSubAccount;
import net.summerfarm.mall.model.domain.PopupType;
import net.summerfarm.mall.model.dto.login.LoginMerchantCacheDTO;
import net.summerfarm.mall.model.input.BdQrCodeQueryInput;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.service.CrmService;
import net.summerfarm.mall.service.MerchantSubAccountService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class CrmServiceImpl implements CrmService {

    @Resource
    private WechatUserQueryFacade wechatUserQueryFacade;
    @Resource
    private MerchantSubAccountService subAccountService;
    @Resource
    private MerchantPopupRecordMapper merchantPopupRecordMapper;
    @Resource
    private PopupTypeMapper popupTypeMapper;


    @Override
    public String queryBdQr(BdQrCodeQueryInput input) {
        String unionid = getUnionId();
        if (unionid == null) {
            log.info("找不到unionId");
            return null;
        }
        MerchantSubAccount account = subAccountService.selectOneNew("unionid", unionid);
        if (account == null) {
            log.info("找不到sub account, unionId: {}", unionid);
            return null;
        }
        Long mId = account.getmId();

        // 首页弹窗需判断是否超过一个自然月未弹出
        // 如果一个自然月内弹出过一次, 返回Null
        if (BdQrCodeQueryChannelEnum.HOME_PAGE_POP_UP.equals(input.getChannel())) {

            // 非店长不弹
            if (account.getType() == null || SubAccountType.MANAGER.ordinal() != account.getType()) {
                log.info("不是店长, accountId: {}", account.getAccountId());
                return null;
            }

            // 店长如果加过企微了,就不弹了.
            if (hasEverAddWeCom(account)) {
                log.info("加过企微了, accountId: {}", account.getAccountId());
                return null;
            }

            PopupType popupType = popupTypeMapper.selectOneByTypeName(PopupTypeEnum.HOME_PAGE_BD_QR_CODE.name());
            MerchantPopupRecord popupRecord = merchantPopupRecordMapper.selectOneByMIdAndTypeId(mId, popupType.getId());

            // 如果需要弹窗, 更新弹窗记录
            if (shouldHomePagePopup(popupRecord, popupType)) {
                insertOrUpdatePopupRecord(popupRecord, popupType, mId);
            } else {
                log.info("已弹过弹窗, mId: {}, popupType: {}, last_pop_time: {}", mId, popupType, popupRecord.getLastPopupTime());
                return null;
            }
        }

        try {
            return wechatUserQueryFacade.queryBdQrCodeByMidAndChannel(mId, input.getChannel());
        } catch (Exception e) {
            log.info("获取二维码失败, mId: {}, channel: {}, {}", mId, input.getChannel(), e.getMessage(), e);
            return null;
        }
    }

    private String getUnionId() {
        LoginMerchantCacheDTO cache = RequestHolder.getLoginMerchantCache();
        if (cache == null) {
            log.info("未能获取cache信息");
            return null;
        }

        // 尝试从 LoginMerchantCacheDTO 获取 unionId
        String unionId = cache.getUnionid();
        if (StringUtils.isNotBlank(unionId)) {
            return unionId;
        }

        // 如果 LoginMerchantCacheDTO 的 unionId 为空，尝试从 MerchantSubject 获取
        MerchantSubject subject = cache.getMerchantSubject();
        if (subject == null) {
            log.info("未能获取MerchantSubject信息");
            return null;
        }

        return subject.getUnionid();
    }

    private boolean hasEverAddWeCom(MerchantSubAccount account) {
        if (account.getUnionid() == null) {
            return false;
        }
        List<WechatUserInfoDTO> wechatUserInfoDTOS = wechatUserQueryFacade.selectWechatUserInfoByUnionId(account.getUnionid());
        return CollectionUtils.isNotEmpty(wechatUserInfoDTOS);
    }

    private boolean shouldHomePagePopup(MerchantPopupRecord popupRecord, PopupType popupType) {

        // 没记录说明没弹过
        return popupRecord == null || LocalDateTime.now().isAfter(popupRecord.getLastPopupTime().plusHours(popupType.getPopupInterval()));
    }

    private void insertOrUpdatePopupRecord(MerchantPopupRecord popupRecord, PopupType popupType, Long mId) {
        if (popupRecord == null) {
            popupRecord = MerchantPopupRecord.builder().mId(mId).typeId(popupType.getId()).build();
            merchantPopupRecordMapper.insertSelective(popupRecord);
        } else {
            merchantPopupRecordMapper.updateCountAndLastPopupTimeById(popupRecord.getCount() + 1, LocalDateTime.now(), popupRecord.getId());
        }

    }
}
