package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.ProductsRecommendEnum;
import net.summerfarm.mall.model.domain.MarketPriceControlProducts;
import net.summerfarm.mall.model.domain.SortCombinationTab;
import net.summerfarm.mall.model.vo.CombinationSortVO;
import net.summerfarm.mall.model.vo.PageVo;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.convert.TabSortResultConverter;
import net.summerfarm.mall.service.facade.MarketItemFacade;
import net.summerfarm.mall.service.facade.TabSortQueryFacade;
import net.summerfarm.mall.service.facade.dto.CombinationSortDTO;
import net.summerfarm.mall.service.facade.dto.MarketItemDetailDTO;
import net.summerfarm.mall.service.strategy.recommend.Cat2AreaHotSellingRecommend;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-09-14
 * @description
 */
@Slf4j
@Service
public class ProductRecommendServiceImpl implements ProductRecommendService {
    @Resource
    private InventoryService inventoryService;
    @Resource
    private SortCombinationService sortCombinationService;

    @Resource
    @Qualifier("redisTemplate")
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private SalePriceTransService salePriceTransService;
    @Resource
    private Cat2AreaHotSellingRecommend cat2AreaHotSellingRecommend;

    @Resource
    private TabSortQueryFacade tabSortQueryFacade;

    @Resource
    private MarketPriceControlProductsService marketPriceControlProductsService;

    @Resource
    private MarketItemFacade marketItemFacade;

    /**
     * 商品推荐AB实验ID
     */
    private static String RECOMMEND_AB_EXPERIMENT_ID = "empty-recommend-ab-testing";
    /**
     * 商品推荐AB实验分组
     */
    private static String[] RECOMMEND_AB_EXPERIMENT_VARIANT = new String[]{"v3","v4"};

    @Override
    public PageInfo<ProductInfoVO> recommendV2(int pageIndex, int pageSize, String skuStr, Integer type, Integer tabId) {
        //大客户不推荐
        if (RequestHolder.isMajor()) {
            return new PageInfo<>();
        }

        SortCombinationTab tab = sortCombinationService.getTabOrDefault(tabId);

        // 1.当tab为固定位类型时，只有固定位数据返回
        // 2.当tab为null时，无固定位数据，推荐数据置顶
        // 3.当tab不为null且tab为推荐类型时，固定位数据置顶，推荐数据其次，剩余数据最后
        // 以上3种情况，当商品售罄时，直接沉底
        List<String> fixSkus = new ArrayList<>();
        Map<String, Object> esSkuSortMap = new HashMap<>();
        if (tab == null || Objects.equals(0, tab.getType())) {
            List<String> recommentSkus = recommendSku(skuStr);

            //购物车不处理置顶商品
            if (!Objects.equals(type, 2) && tab != null) {
                fixSkus = tabSortQueryFacade.listTabSkuByTabId(tabId);
                if (CollectionUtils.isNotEmpty(recommentSkus) && CollectionUtil.isNotEmpty(fixSkus)) {
                    recommentSkus.removeAll(fixSkus);
                }
            }

            // 详情页V2推荐
            if (type.equals(ProductsRecommendEnum.PRODUCT_INFO_V2.getType())) {
                log.info("详情页V2推荐, skuStr:{}", skuStr);
                recommentSkus = cat2AreaHotSellingRecommend.recommendSkus(skuStr);
            }

            esSkuSortMap = buildEsSkuSortMap(fixSkus, recommentSkus);
            fixSkus = null;
        } else if (Objects.equals(1, tab.getType())) {
            fixSkus = tabSortQueryFacade.listTabSkuByTabId(tabId);
            //无sku、返回空
            if (CollectionUtils.isEmpty(fixSkus)) {
                return new PageInfo<>();
            }
            esSkuSortMap = buildEsSkuSortMap(fixSkus, null);
        }

        PageInfo<ProductInfoVO> pageInfo = salePriceTransService.selectRecommendProductVOV3(RequestHolder.getMerchantAreaNo(), esSkuSortMap, fixSkus,
                    new PageVo(pageIndex, pageSize));
        
        // 过滤定制包材商品 - 直接过滤返回的pageInfo
        filterCustomPackagingMaterials(pageInfo);

        if (pageInfo != null && CollectionUtil.isNotEmpty(pageInfo.getList())) {
            List<ProductInfoVO> homeProductVOList = pageInfo.getList();
            inventoryService.checkoutSku(homeProductVOList, RequestHolder.getMerchantSubject());

            //获取全部控价品信息
            Map<String, MarketPriceControlProducts> controlProductsMap = marketPriceControlProductsService.selectAllControlProductsByCache();
            homeProductVOList.forEach(productInfoVO -> {
                if (CollectionUtil.isEmpty(controlProductsMap) || !controlProductsMap.containsKey(productInfoVO.getSku())) {
                    return;
                }
                MarketPriceControlProducts marketPriceControlProducts = controlProductsMap.get(productInfoVO.getSku());
                if (marketPriceControlProducts != null) {
                    productInfoVO.setPriceHide(marketPriceControlProducts.getPriceHide());
                    productInfoVO.setFacePriceHide(marketPriceControlProducts.getFacePriceHide());
                }
            });
        }
        return pageInfo;
    }

    private Map<String, Object> buildEsSkuSortMap(List<String> fixSkus, List<String> recommentSkus) {
        int sortValue = 1000000;
        Map<String, Integer> esSkuSortMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(fixSkus)) {
            for (int i = 0; i < fixSkus.size(); i++) {
                sortValue = sortValue - i;
                String sku = fixSkus.get(i);
                esSkuSortMap.put(sku, sortValue);
            }
        }

        if (CollectionUtils.isNotEmpty(recommentSkus)) {
            for (int i = 0; i < recommentSkus.size(); i++) {
                sortValue = sortValue - i;
                String sku = recommentSkus.get(i);
                esSkuSortMap.put(sku, sortValue);
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("sortSkus", esSkuSortMap);
        return map;
    }

    @Override
    public List<CombinationSortVO> listAllTab() {
        List<CombinationSortDTO> allTabSort = tabSortQueryFacade.listAllTabSort(RequestHolder.getMerchantAreaNo(), RequestHolder.getMId());
        return TabSortResultConverter.toCombinationSortVOList(allTabSort);
    }

    private List<String> recommendSku(String skuStr) {
        log.info("商品推荐数据：开始推荐:{}", RequestHolder.getMId());
        String abExpVariant = RequestHolder.getAbExpVariant(RECOMMEND_AB_EXPERIMENT_ID);
        if (StringUtils.isNotBlank(abExpVariant) && Arrays.asList(RECOMMEND_AB_EXPERIMENT_VARIANT).contains(abExpVariant)) {
            List<String> skuList = new ArrayList<>();
            try {
                Object recommendSkuObj = redisTemplate.opsForHash().get(ProductsRecommendEnum.HOME_V2.getRedisKey(), RequestHolder.getMerchantId().toString());
                if (recommendSkuObj != null) {
                    skuList = new ArrayList<>(Arrays.asList(String.valueOf(recommendSkuObj).split(",")));
                }
            } catch (Exception e) {
                log.error("获取首页推荐v2数据异常", e);
            }

           log.info("命中首页推荐AB实验, mid:{}, variantId:{}, size:{}, skuList:{}",
                    RequestHolder.getMId(), abExpVariant, RequestHolder.getSize(), skuList);
            return skuList;
        }

        // 首页或购物车为空推荐
        if (StringUtils.isBlank(skuStr)) {
            List<String> recommendList = new ArrayList<>();
            try {
                Object o = redisTemplate.opsForHash()
                        .get(ProductsRecommendEnum.HOME.getRedisKey(), RequestHolder.getMId().toString());
                recommendList = new ArrayList<>(Arrays.asList(String.valueOf(o).split(",")));
            } catch (Exception e) {
                log.error("获取首页推荐数据异常", e);
            }
            log.info("用户:{} 的推荐SKU:{}", RequestHolder.getMId(), recommendList);
            return recommendList;
        } // 购物车不为空推荐
        else {
            String[] skuArr = skuStr.split(",");
            //按顺序取sku，并过滤查询sku
            int maxSize = 0;
            List<List<String>> listList = new ArrayList<>();
            for (String sku : skuArr) {
                List<String> list = new ArrayList<>();
                try {
                    Object o = redisTemplate.opsForHash().get(ProductsRecommendEnum.TROLLEY.getRedisKey(), sku);
                    list = new ArrayList<>(Arrays.asList(String.valueOf(o).split(",")));
                } catch (Exception e) {
                    log.error("获取购物车推荐数据异常", e);
                }
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }

                listList.add(list);
                if (list.size() > maxSize) {
                    maxSize = list.size();
                }
            }

            if (CollectionUtils.isEmpty(listList)) {
                return null;
            }

            Set<String> result = new HashSet<>();
            for (int i = 0; i < maxSize; i++) {
                for (List<String> skuList : listList) {
                    if (skuList.size() > i) {
                        String reSku = skuList.get(i);
                        if (Arrays.stream(skuArr).noneMatch(el -> Objects.equals(el, reSku))) {
                            result.add(reSku);
                        }
                    }
                }
            }
            return new ArrayList<>(result);
        }
    }

    @Override
    public List<String> getHomeRecommendSku() {
        try {
            Object o = redisTemplate.opsForHash()
                    .get(ProductsRecommendEnum.HOME.getRedisKey(), RequestHolder.getMId().toString());
            return new ArrayList<>(Arrays.asList(String.valueOf(o).split(",")));
        } catch (Exception e) {
            log.error("获取首页推荐数据异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 过滤定制包材商品
     * 
     * @param pageInfo 包含商品信息的分页对象
     */
    private void filterCustomPackagingMaterials(PageInfo<ProductInfoVO> pageInfo) {
        try {
            if (pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList())) {
                return;
            }

            // 收集所有需要检查的SKU
            List<ProductInfoVO> productList = pageInfo.getList();
            Set<String> allSkus = productList.stream()
                    .map(ProductInfoVO::getSku)
                    .collect(Collectors.toSet());

            if (allSkus.isEmpty()) {
                return;
            }

            log.info("开始过滤定制包材商品，待检查SKU数量: {}", allSkus.size());

            // 调用MarketItemFacade获取商品标签信息
            List<MarketItemDetailDTO> itemDetails = marketItemFacade.queryMarketItemLabelByItemCodes(allSkus);

            // 找出包含"定制包材"标签的SKU
            Set<String> customPackagingSkus = itemDetails.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getItemLabel()) &&
                            item.getItemLabel().contains("定制包材"))
                    .map(MarketItemDetailDTO::getItemCode)
                    .collect(Collectors.toSet());

            if (customPackagingSkus.isEmpty()) {
                return;
            }

            log.info("发现定制包材商品，数量: {}，SKU列表: {}", customPackagingSkus.size(), customPackagingSkus);

            // 过滤掉定制包材商品
            List<ProductInfoVO> filteredList = productList.stream()
                    .filter(product -> !customPackagingSkus.contains(product.getSku()))
                    .collect(Collectors.toList());

            pageInfo.setList(filteredList);

            log.info("定制包材商品过滤完成，过滤前商品数量: {}，过滤后商品数量: {}", productList.size(), filteredList.size());
        } catch (Exception e) {
            log.info("过滤定制包材商品时发生异常", e);
        }
    }
}
