package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.PageInfoHelper;
import net.summerfarm.mall.common.util.RedisCacheUtil;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.constant.Constants;
import net.summerfarm.mall.engine.client.req.EsHomeProductQueryConditionReq;
import net.summerfarm.mall.engine.client.req.EsHomeProductQueryReq;
import net.summerfarm.mall.enums.AreaSkuExtTypeEnum;
import net.summerfarm.mall.enums.CommodityTopEnum;
import net.summerfarm.mall.enums.SearchConditionEnum;
import net.summerfarm.mall.facade.engine.model.EsMarketItemInfoDTO;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.model.domain.Fence;
import net.summerfarm.mall.model.domain.MarketPriceControlProducts;
import net.summerfarm.mall.model.dto.product.MallProductQueryDTO;
import net.summerfarm.mall.model.input.product.ProductQueryInput;
import net.summerfarm.mall.model.input.product.SearchConditionInput;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.model.vo.product.HomeProductQueryVo;
import net.summerfarm.mall.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.keyvalue.repository.KeyValueRepository;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SkuQueryServiceImpl implements SkuQueryService {


    @Resource
    private MajorPriceMapper majorPriceMapper;
    @Resource
    private ProductSearchRerankService productSearchRerankService;
    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Resource
    private AreaService areaService;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private InventoryService inventoryService;
    @Resource
    private MerchantService merchantService;
    @Resource
    private CategoryService categoryService;
    @Resource
    private FenceService fenceService;
    @Resource
    private AreaSkuService areaSkuService;
    @Resource
    private FrontCategoryService frontCategoryService;
    @Resource
    private FrontCategoryMapper frontCategoryMapper;
    @Resource
    private MarketPriceControlProductsService marketPriceControlProductsService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private ProductsPropertyValueMapper productsPropertyValueMapper;
    @Resource
    private TimingRuleService timingRuleService;
    @Resource
    private FrequentSkuPoolService frequentSkuPoolService;

    @Override
    public PageInfo<ProductInfoVO> selectHomeProductVo(ProductQueryInput input) {

        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        Area area = areaService.selectAreaWithCache((RequestHolder.getMerchantArea().getAreaNo()));
        if (merchantSubject == null) {
            merchantSubject = new MerchantSubject();
            merchantSubject.setArea(area);
        }

        Integer areaNo = Optional.ofNullable(area.getAreaNo()).orElse(RequestHolder.getMerchantAreaNo());
        Fence fence = fenceService.selectCacheOneByAreaNo(areaNo);
        if (fence == null || fence.getStoreNo() == null) {
            return new PageInfo<>();
        }

        EsHomeProductQueryReq esHomeProductQueryReq = new EsHomeProductQueryReq();
        EsHomeProductQueryConditionReq conditionReq = new EsHomeProductQueryConditionReq();
        boolean b = fillEsCondition (esHomeProductQueryReq, conditionReq, merchantSubject, input);
        if(!b){
            return new PageInfo<>();
        }
        PageVo pageVo = new PageVo(input.getPageIndex (), input.getPageSize ());

        PageInfo<ProductInfoVO> productInfoVO = selectHomeProduct(esHomeProductQueryReq, conditionReq,merchantSubject, pageVo);
        if (productInfoVO == null) {
            return new PageInfo<>(new ArrayList<>());
        }
        inventoryService.newMajorPrice(productInfoVO.getList(), merchantSubject);
        inventoryService.checkoutSku(productInfoVO.getList(), merchantSubject);
        fillTiming (productInfoVO.getList (), input.getConditionInputs (),area.getAreaNo ());
        return productInfoVO;
    }

    private void fillTiming(List<ProductInfoVO> list, List<SearchConditionInput> conditionInputs, Integer areaNo) {
        if(CollectionUtil.isEmpty (conditionInputs)){
            return;
        }

        Map<Integer, SearchConditionInput> conditionInputMap = conditionInputs.stream ().collect (Collectors.toMap (SearchConditionInput::getType, x -> x));
        SearchConditionInput searchConditionInput = conditionInputMap.get (SearchConditionEnum.SearchConditionTypeEnum.PRODUCT_FEATURE.getType ());
        if(searchConditionInput != null && searchConditionInput.getKey ().contains (String.valueOf (SearchConditionEnum.ProductFeatureEnum.TIMING.getKey ()))) {
            List<TimingRuleVO> areaTimingRules = timingRuleService.getTimingInfoByAreaNoCache(areaNo);
            if (CollectionUtils.isEmpty(areaTimingRules)) {
                log.info("运营服务区:{}并没有配置任何省心送:{}", areaNo);
                return;
            }
            Set<String> skuSet = list.stream ().map (ProductInfoVO::getSku).collect (Collectors.toSet ());
            Map<String, TimingRuleVO> skuTimingRules = areaTimingRules.stream()
                    .filter(timingRuleVO -> skuSet.contains(timingRuleVO.getTimingSku()))
                    .collect(Collectors.toMap(TimingRuleVO::getTimingSku, Function.identity(),
                            (o1, o2) -> {
                                // 确保返回的是最新的
                                return o1.getId() > o2.getId() ? o1 : o2;
                            }));
            list.forEach (productInfoVO -> {
                TimingRuleVO timingRule = skuTimingRules.get (productInfoVO.getSku ());
                if(timingRule != null) {
                    productInfoVO.setTimingRuleId (timingRule.getId ());
                }
            });
        }
    }
    @Override
    public void fillCondition(EsHomeProductQueryReq esHomeProductQueryReq,List<EsMarketItemInfoDTO> all) {
        String pdName = esHomeProductQueryReq.getPdName ();
        Integer areaNo = esHomeProductQueryReq.getAreaNo ();
        String key = areaNo + pdName;
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject ();
        if(merchantSubject!=null){
            Integer adminId = merchantSubject.getAdminId ();
            key  = adminId + key;
        }
        List<SearchConditionVO> queryCondition = getQueryCondition (key, areaNo, all);
        redisTemplate.opsForValue ().set (key, JSON.toJSONString (queryCondition), 2, TimeUnit.MINUTES);
    }
    private PageInfo<ProductInfoVO> selectHomeProduct(EsHomeProductQueryReq esHomeProductQueryReq,
                                                      EsHomeProductQueryConditionReq conditionReq,
                                                      MerchantSubject merchantSubject,
                                                      PageVo pageVo) {
        Integer adminId = null;
        if ("大客户".equals(merchantSubject.getSize())) {
            adminId = merchantSubject.getAdminId ();
        }
        try {
            // 增加搜索AB实验的支持：
            PageInfo<EsMarketItemInfoDTO> esMarketItemPageInfo = productSearchRerankService.searchProducts(esHomeProductQueryReq, conditionReq, null, true);
            if (Objects.isNull(esMarketItemPageInfo) || CollectionUtils.isEmpty(esMarketItemPageInfo.getList())) {
                return PageInfoHelper.createPageInfo(Collections.emptyList());
            }
            try (Page<ProductInfoVO> productInfoVOS = new Page<>(pageVo.getPageIndex(), pageVo.getPageSize())) {
                for (EsMarketItemInfoDTO esMarketItemInfoDTO : esMarketItemPageInfo.getList()) {
                    ProductInfoVO productInfoVO = EsMarketItemInfoDTO.convertToProductInfoVO(esMarketItemInfoDTO, conditionReq.isCoreCustomer ());
                    productInfoVOS.add(productInfoVO);
                }
                if (CollectionUtils.isEmpty(productInfoVOS)) {
                    return PageInfoHelper.createPageInfo(Collections.emptyList());
                }
                productInfoVOS.setTotal(esMarketItemPageInfo.getTotal());
                List<String> skus = productInfoVOS.stream().map(ProductInfoVO::getSku).collect(Collectors.toList());
                // 查询价格信息
                List<ProductInfoVO> areaSkus = areaSkuMapper.selectByAreaNoAndSkus(esHomeProductQueryReq.getAreaNo (), skus, adminId, merchantSubject.getDirect());
                for (ProductInfoVO productInfoVO : productInfoVOS) {
                    //优先取SKU名称，如果为空，则兜底为SPU名称+SKU规格信息
                    productInfoVO.resetSkuNameAndSkuPic();
                    areaSkus.stream().filter(item -> item.getSku().equals(productInfoVO.getSku()))
                            .findFirst()
                            .ifPresent(item -> {
                                String ladderPrice = item.getLadderPrice();
                                productInfoVO.setLadderPrice(ladderPrice);
                                if (org.apache.commons.lang3.StringUtils.isNotBlank(ladderPrice) || ObjectUtil.notEqual("[]", ladderPrice)) {
                                    List<LadderPriceVO> ladderPriceObjs = JSONObject.parseArray(ladderPrice, LadderPriceVO.class);
                                    productInfoVO.setLadderPrices(ladderPriceObjs);
                                }
                                productInfoVO.setSalePrice(item.getSalePrice());
                                productInfoVO.setOriginalPrice(item.getOriginalPrice());
                                productInfoVO.setType(item.getType());
                                productInfoVO.setCateType(item.getCateType());
                                productInfoVO.setExtType(item.getExtType());
                                productInfoVO.setMallShow(item.getMallShow());
                                productInfoVO.setDirect(item.getDirect());
                                productInfoVO.setActivityOriginPrice(item.getActivityOriginPrice());
                                productInfoVO.setPrice(item.getPrice());
                                productInfoVO.setQuoteType(item.getQuoteType());
                            });
                    //设置默认库存，防止库存泄露，库存从其他接口获取
                    if (productInfoVO.getBaseSaleQuantity() * productInfoVO.getBaseSaleUnit() > productInfoVO.getQuantity()) {
                        productInfoVO.setOnlineQuantity(Constants.AreaStoreConstant.defaultOnlineQuantity);
                        productInfoVO.setQuantity(Constants.AreaStoreConstant.defaultOnlineQuantity);
                    }else {
                        productInfoVO.setOnlineQuantity(Constants.ONLINE_QUANTITY);
                        productInfoVO.setQuantity(Constants.ONLINE_QUANTITY);
                    }
                    fillAvgInfo(productInfoVO);
                }

                //获取全部控价品信息
                Map<String, MarketPriceControlProducts> controlProductsMap = marketPriceControlProductsService.selectAllControlProductsByCache();
                productInfoVOS.forEach(productInfoVO -> {
                    if (CollectionUtil.isEmpty(controlProductsMap) || !controlProductsMap.containsKey(productInfoVO.getSku())) {
                        return;
                    }
                    MarketPriceControlProducts marketPriceControlProducts = controlProductsMap.get(productInfoVO.getSku());
                    if (marketPriceControlProducts != null) {
                        productInfoVO.setPriceHide(marketPriceControlProducts.getPriceHide());
                        productInfoVO.setFacePriceHide(marketPriceControlProducts.getFacePriceHide());
                    }
                });
                // 查询是否在常购清单中
                Map<String, Boolean> skuInFrequentSkuPoolInfoMap = frequentSkuPoolService.checkSkuInFrequentSkuPool(RequestHolder.getMId(), skus);
                productInfoVOS.forEach(productInfoVO -> {
                    productInfoVO.setInMerchantFrequentSkuPool(skuInFrequentSkuPoolInfoMap.getOrDefault(productInfoVO.getSku(), false));
                });
                //新增标签
                return productInfoVOS.toPageInfo();
            }
        } catch (Exception e) {
            log.error("es商品列表查询异常", e);
            return PageInfoHelper.createPageInfo(Collections.emptyList());
        }

    }
    private void fillAvgInfo(ProductInfoVO productInfoVO) {
        Set<Integer> xxsg = categoryService.getChildCategoryIdsByParentName ("新鲜水果");
        if(xxsg.contains (productInfoVO.getCategoryId ())) {
            AvgInfoVO avgInfoVO = inventoryService.getAvgInfo(productInfoVO.getCategoryId (),productInfoVO.getWeight ());
            productInfoVO.setAvgNumerator(avgInfoVO.getAvgNumerator ());
            productInfoVO.setAvgUnit(avgInfoVO.getAvgUnit ());
        }
        fillAvgWeight(productInfoVO);
    }
    private void fillAvgWeight(ProductInfoVO productInfoVO){
        if(ObjectUtil.isNotNull (productInfoVO.getWeightNum ()) && Constants.kgList.contains (productInfoVO.getNetWeightUnit ())){
            productInfoVO.setWeightNum (productInfoVO.getWeightNum ().multiply (new BigDecimal (2)));
            if(productInfoVO.getWeightNum ().compareTo (new BigDecimal (1))<0){
                productInfoVO.setWeightNum (null);
            }
        }
        if(ObjectUtil.isNotNull (productInfoVO.getNetWeightNum ()) && Constants.kgList.contains (productInfoVO.getNetWeightUnit ())){
            productInfoVO.setNetWeightNum (productInfoVO.getNetWeightNum ().multiply (new BigDecimal (2)));
            if(productInfoVO.getNetWeightNum ().compareTo (new BigDecimal (1))<0){
                productInfoVO.setNetWeightNum (null);
            }
        }
    }

    @Override
    public List<SearchConditionVO> getQueryCondition(String keyword, Integer areaNo,List<EsMarketItemInfoDTO> all) {
        if(CollectionUtil.isEmpty (all)){
            return Collections.emptyList ();
        }
        Set<String> skus = all.stream ().map (EsMarketItemInfoDTO::getItemCode).collect (Collectors.toSet ());

        Set<Long> pdIds = inventoryService.selectPdIdsSetBySkus (skus);

        BigDecimal maxPrice = all.stream ().filter (x -> ObjectUtil.isNotNull (x.getPrice ())).map (EsMarketItemInfoDTO::getPrice).max (Comparator.comparing (x -> x)).orElse (new BigDecimal (5000));
        BigDecimal minPrice = all.stream ().filter (x -> ObjectUtil.isNotNull (x.getPrice ())).map (EsMarketItemInfoDTO::getPrice).min (Comparator.comparing (x -> x)).orElse (BigDecimal.ZERO);


        List<String> brandNames = all.stream()
                .filter(ee -> !StringUtils.isEmpty(ee.getBrandName()))
                .map(EsMarketItemInfoDTO::getBrandName)
                .collect(Collectors.groupingBy(brandName -> brandName, Collectors.counting()))
                .entrySet()
                .stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        Set<Integer> categoryIds = all.stream ().filter (ee-> ObjectUtil.isNotNull (ee.getCategoryId ())).map (ee-> ee.getCategoryId ().intValue ()).collect (Collectors.toSet ());
        Map<Integer, Long> categoryIdCountMap = all.stream()
                .filter(ee -> ObjectUtil.isNotNull(ee.getCategoryId()))  // 过滤掉 null 的 CategoryId
                .map(ee -> ee.getCategoryId().intValue())  // 获取 CategoryId 的整数值
                .collect(Collectors.groupingBy(
                        Function.identity(),  // 按照 CategoryId 分组
                        Collectors.counting()  // 计算每个 CategoryId 的数量
                ));
        List<FrontCategoryCountVO> secondFrontCategoryVOS = frontCategoryMapper.selectByCategoryIdsByCategoryIds (new ArrayList<> (categoryIds), areaNo);
        List<FrontCategoryVO> firstFrontCategoryVOS = new ArrayList<> ();
        if(CollectionUtil.isNotEmpty (secondFrontCategoryVOS)) {
            secondFrontCategoryVOS.forEach (e -> {
                Integer counts = e.getCounts () == null ? 0 : e.getCounts ();
                if (CollectionUtil.isNotEmpty (categoryIdCountMap) && categoryIdCountMap.containsKey (e.getCategoryId ())) {
                    e.setCounts (categoryIdCountMap.get (e.getCategoryId ()).intValue () + counts);
                }
            });
            log.info ("categoryIdCountMap:{},secondFrontCategoryVOS={}", JSON.toJSONString (categoryIdCountMap), JSON.toJSONString (secondFrontCategoryVOS));

            // 进行排序
            secondFrontCategoryVOS = secondFrontCategoryVOS.stream ()
                    .collect (Collectors.toMap (
                            FrontCategoryCountVO::getName,  // 使用 name 作为键
                            category -> category,            // 使用对象本身作为值
                            (existing, replacement) -> existing // 如果键重复，保留第一个
                    )).values ().stream ().collect (Collectors.toList ()).stream ().sorted (Comparator.comparingInt (FrontCategoryCountVO::getCounts).reversed ()).collect (Collectors.toList ());
            log.info ("categoryIdCountMap:{},secondFrontCategoryVOS after sort and distinct={}", JSON.toJSONString (categoryIdCountMap), JSON.toJSONString (secondFrontCategoryVOS));

            Map<Integer, Integer> firstCategoryCountMap = secondFrontCategoryVOS.stream ()
                    .collect (Collectors.groupingBy (FrontCategoryCountVO::getParentId,
                            Collectors.summingInt (FrontCategoryCountVO::getCounts)));

            // 根据值排序并提取键
            List<Integer> parentIds = firstCategoryCountMap.entrySet ().stream ()
                    .sorted (Map.Entry.comparingByValue (Comparator.reverseOrder ())) // 按值从大到小排序
                    .map (Map.Entry::getKey) // 提取键
                    .collect (Collectors.toList ()); // 收集到列表中

            firstFrontCategoryVOS = frontCategoryMapper.selectByIds (new ArrayList<> (secondFrontCategoryVOS.stream ().map (FrontCategoryCountVO::getParentId).collect (Collectors.toSet ())), areaNo);
            // 进行去重
            firstFrontCategoryVOS = firstFrontCategoryVOS.stream ()
                    .filter (category -> parentIds.contains (category.getId ())) // 过滤出在 parentIds 中的 ID
                    .collect (Collectors.toMap (
                            FrontCategoryVO::getName,  // 使用 name 作为键
                            category -> category,            // 使用对象本身作为值
                            (existing, replacement) -> existing // 如果键重复，保留第一个
                    ))
                    .values ()
                    .stream ()
                    .collect (Collectors.toList ());
            log.info ("firstFrontCategoryVOS parentIds={},distinct={}", JSON.toJSONString (parentIds), JSON.toJSONString (firstFrontCategoryVOS));

            // 创建一个 ID 到 FrontCategoryVO 的映射
            Map<Integer, FrontCategoryVO> voMap = firstFrontCategoryVOS.stream()
                    .collect(Collectors.toMap(FrontCategoryVO::getId, vo -> vo));

            // 根据 ids 的顺序重新排序 firstFrontCategoryVOS
            firstFrontCategoryVOS = parentIds.stream()
                    .map(voMap::get)
                    .filter(Objects::nonNull) // 过滤掉可能不存在的 ID
                    .collect(Collectors.toList());
            log.info ("firstFrontCategoryVOS parentIds={},sort={}",JSON.toJSONString (parentIds),JSON.toJSONString (firstFrontCategoryVOS));
        }

        ArrayList<SearchConditionVO> result = new ArrayList<> ();
        for (SearchConditionEnum.SearchConditionTypeEnum type : SearchConditionEnum.SearchConditionTypeEnum.values ()) {
            SearchConditionVO searchConditionVO = new SearchConditionVO ();
            searchConditionVO.setTitle(type.getDescription ());
            searchConditionVO.setType(type.getType ());
            switch (type){
                case PRODUCT_FEATURE:
                    if(!RequestHolder.isMajor()) {
                        List<SearchConditionBodyVO> collect = Arrays.stream (SearchConditionEnum.ProductFeatureEnum.values ()).map (searchConditionEnum -> {
                            SearchConditionBodyVO searchConditionBodyVO = new SearchConditionBodyVO ();
                            searchConditionBodyVO.setKey (String.valueOf (searchConditionEnum.getKey ()));
                            searchConditionBodyVO.setName (searchConditionEnum.getName ());
                            return searchConditionBodyVO;
                        }).collect (Collectors.toList ());
                        searchConditionVO.setConditionList(collect);
                    }
                    break;
                case FIRST_FRONT_CATEGORY:
                    if(CollectionUtil.isNotEmpty (firstFrontCategoryVOS)){
                        searchConditionVO.setConditionList(firstFrontCategoryVOS.stream ().map (frontCategoryVO -> {
                            SearchConditionBodyVO searchConditionBodyVO = new SearchConditionBodyVO ();
                            searchConditionBodyVO.setKey(frontCategoryVO.getId ().toString ());
                            searchConditionBodyVO.setName(frontCategoryVO.getName ());
                            return searchConditionBodyVO;
                        }).collect(Collectors.toList()));
                    }
                    break;
                case SECOND_FRONT_CATEGORY:
                    if(CollectionUtil.isNotEmpty (secondFrontCategoryVOS)) {
                        searchConditionVO.setConditionList (secondFrontCategoryVOS.stream ().map (frontCategoryVO -> {
                            SearchConditionBodyVO searchConditionBodyVO = new SearchConditionBodyVO ();
                            searchConditionBodyVO.setKey (frontCategoryVO.getId ().toString ());
                            searchConditionBodyVO.setName (frontCategoryVO.getName ());
                            return searchConditionBodyVO;
                        }).collect (Collectors.toList ()));
                    }
                    break;
//                case BRAND:
//                    if(CollectionUtil.isNotEmpty (brandNames)) {
//                        searchConditionVO.setConditionList (brandNames.stream ().map (brandName -> {
//                            SearchConditionBodyVO searchConditionBodyVO = new SearchConditionBodyVO ();
//                            searchConditionBodyVO.setKey (brandName);
//                            searchConditionBodyVO.setName (brandName);
//                            return searchConditionBodyVO;
//                        }).collect (Collectors.toList ()));
//                    }
//                    break;
//                case PRICE_RANGE:
//                    SearchConditionBodyVO vo = new SearchConditionBodyVO ();
//                    vo.setMinPrice (minPrice);
//                    vo.setMaxPrice (maxPrice);
//                    searchConditionVO.setConditionList(Collections.singletonList (vo));
//                    break;
                case FRUIT_GRADE:
                    List<Map<String, Object>> list1 = productsPropertyValueMapper.selectProductsPropertyValueNameBySkusAndProductsPropertyName  (skus, "级别");
                    if(CollectionUtil.isNotEmpty (list1)) {
                        Map<String, Long> jbs = new HashMap<> ();
                        for (Map<String, Object> w : list1) {
                            String key = (String) w.get("products_property_value"); // 确保这是字符串
                            Long count =(Long) w.get("count");
                            jbs.put(key, count);
                        }
                        searchConditionVO.setConditionList (jbs.entrySet().stream()
                                .sorted(Map.Entry.comparingByValue()) // 按照值进行排序
                                .map(jb -> {
                            SearchConditionBodyVO searchConditionBodyVO = new SearchConditionBodyVO ();
                            searchConditionBodyVO.setKey (jb.getKey ());
                            searchConditionBodyVO.setName (jb.getKey ());
                            return searchConditionBodyVO;
                        }).collect (Collectors.toList ()));
                    }
                    break;
                case FRUIT_VARIETY:
                    List<Map<String, Object>> list = productsPropertyValueMapper.selectProductsPropertyValueNameByPdIdsAndProductsPropertyName (pdIds, "品种");
                    if(CollectionUtil.isNotEmpty (list)) {
                        Map<String, Long> pzs = new HashMap<> ();
                        for (Map<String, Object> w : list) {
                            String key = (String) w.get("products_property_value"); // 确保这是字符串
                            Long count = (Long) w.get("count");
                            pzs.put(key, count);
                        }
                        searchConditionVO.setConditionList (pzs.entrySet().stream()
                                .sorted(Map.Entry.comparingByValue()) // 按照值进行排序
                                .map(pz -> {
                            SearchConditionBodyVO searchConditionBodyVO = new SearchConditionBodyVO ();
                            searchConditionBodyVO.setKey (pz.getKey ());
                            searchConditionBodyVO.setName (pz.getKey ());
                            return searchConditionBodyVO;
                        }).collect (Collectors.toList ()));
                    }
                    break;
            }
            result.add (searchConditionVO);
        }
        return result;
    }
    @Override
    public List<SearchConditionVO> selectQueryCondition(String key) {
        String s = redisTemplate.opsForValue ().get (key);
        if(StringUtils.isNotBlank (s)){
            return JSON.parseArray (s,SearchConditionVO.class);
        }else{
            return Collections.emptyList ();
        }
    }
    private boolean fillEsCondition(EsHomeProductQueryReq esHomeProductQueryReq, EsHomeProductQueryConditionReq conditionReq, MerchantSubject merchantSubject,ProductQueryInput input) {
        // mallShow：报价单门店可见该商品，0-是，1-否；代下单不限制
        Integer mallShow = 0;

        Integer adminId;
        List<String> skuList = new ArrayList<> ();
        Set<String> notShowSkuList = null;
        if ("大客户".equals(merchantSubject.getSize())) {
            adminId = merchantSubject.getAdminId();
            //查询生效的报价单sku
            skuList = majorPriceMapper.selectSKu(adminId, merchantSubject.getArea().getAreaNo(), merchantSubject.getDirect(), mallShow);

            if (Objects.equals(merchantSubject.getSkuShow(), 1)) {
                //获取类目sku信息,已排除sku报价单展示的spu和不展示的sku
                List<String> majorSkuList = inventoryService.getBigMerchantMajorCategory(adminId, merchantSubject.getArea().getAreaNo(), merchantSubject.getDirect(), mallShow);
                skuList.addAll(majorSkuList);
                skuList = skuList.stream().distinct().collect(Collectors.toList());

                if (CollectionUtils.isEmpty(skuList)) {
                    return false;
                }
            } else if (Objects.equals(merchantSubject.getSkuShow(), 2)) {
//                查询需要隐藏的sku
                notShowSkuList = inventoryService.mallShowHideSkuList(adminId, merchantSubject.getArea().getAreaNo(), merchantSubject.getDirect());
            }
        }
        if (CollectionUtils.isEmpty(skuList)) {
            skuList = null;
        }

        if (CollectionUtils.isEmpty(notShowSkuList)) {
            notShowSkuList = null;
        }
        // 支持从接口传入areaNo，实现接口控制搜索某一特定区域的商品；
        Integer areaNo = merchantSubject.getArea().getAreaNo ();

        esHomeProductQueryReq.setAreaNo(areaNo);
        //小程序过滤部分类目
        conditionReq.setMiniProgramLogin(RequestHolder.isMiniProgramLogin());
        //小程序过滤部分类目
        if (RequestHolder.isMiniProgramLogin()) {
            Set<Integer> unShowCategoryId = categoryService.getUnShowCategoryId();
            if (CollectionUtil.isNotEmpty(unShowCategoryId)) {
                esHomeProductQueryReq.setMiniProgramCategoryIds(unShowCategoryId);
            }
            //大客户过滤代仓商品
            if (RequestHolder.isMajor()) {
                List<Integer> unShowAdminId = Lists.newArrayList(450, 744, 1110, 1175, 1227, 1525);
                if (unShowAdminId.contains(RequestHolder.getAdminId())) {
                    Set<String> unShowSku = areaSkuService.selectAgentSku(RequestHolder.getAdminId(), RequestHolder.getMerchantAreaNo());
                    if (CollectionUtil.isNotEmpty(unShowSku)) {
                        esHomeProductQueryReq.setMiniProgramUnShowSkus(unShowSku);
                    }
                }
            }
        }
//        esHomeProductQueryReq.setMaxPrice (input.getMaxPrice ());
//        esHomeProductQueryReq.setMinPrice (input.getMinPrice ());
        esHomeProductQueryReq.setSortBy(input.getSortBy ());
        esHomeProductQueryReq.setSortDirection(input.getSortDirection());
        esHomeProductQueryReq.setSaleOut (input.getSaleOut ());

        //筛选项
        List<SearchConditionInput> conditionInputs = input.getConditionInputs ();
        if(CollectionUtil.isNotEmpty (conditionInputs)){
            List<String> KeyPropertyList = new ArrayList<> ();
            Set<Integer> secondsCategoryIds = new HashSet<> ();
            Set<Integer> firstCategoryIds = new HashSet<> ();
            conditionInputs.forEach (searchConditionInput -> {
                SearchConditionEnum.SearchConditionTypeEnum of = SearchConditionEnum.SearchConditionTypeEnum.of (searchConditionInput.getType ());
                switch (of){
                    case PRODUCT_FEATURE:
                        searchConditionInput.getKey ().forEach (key -> {
                            SearchConditionEnum.ProductFeatureEnum productFeatureEnum = SearchConditionEnum.ProductFeatureEnum.of (Integer.valueOf (key));
                            switch (productFeatureEnum){
                                case TIMING:
                                    esHomeProductQueryReq.setTiming(1);
                                    break;
                                case NEW_PRODUCTS:
                                    esHomeProductQueryReq.setLabel (Lists.newArrayList ("新品"));
                                    break;
                                case NEAR_EXPIRATION:
                                    esHomeProductQueryReq.setExtTypeList (Lists.newArrayList (AreaSkuExtTypeEnum.EXT_TYPE_EXPIRED.getExtType ()));
                                    break;
                            }
                        });
                        break;
                    case FIRST_FRONT_CATEGORY:
                        List<Integer> firstFrontCategory = searchConditionInput.getKey ().stream().map (Integer::valueOf).collect(Collectors.toList());
                        List<Integer> fCategoryIdList = frontCategoryMapper.selectCategoryByParentIds (firstFrontCategory, areaNo);
                        if (!CollectionUtils.isEmpty(fCategoryIdList)) {
                            firstCategoryIds.addAll (fCategoryIdList);
                        }
                        break;
                    case SECOND_FRONT_CATEGORY:
                        searchConditionInput.getKey ().forEach (frontCategoryId->{
                            List<Integer> sCategoryIdList = frontCategoryService.selectCategoryId(Integer.valueOf (frontCategoryId));
                            if (!CollectionUtils.isEmpty(sCategoryIdList)) {
                                secondsCategoryIds.addAll (sCategoryIdList);
                            }
                        });
                        break;
//                    case BRAND:
//                        esHomeProductQueryReq.setBrandNameList (searchConditionInput.getKey ());
//                        break;
//                    case PRICE_RANGE:
//                        break;
                    case FRUIT_GRADE:
                        esHomeProductQueryReq.setSalePropertyList(searchConditionInput.getKey ());
                        break;
                    case FRUIT_VARIETY:
//                    case FAT_CONTENT:
//                    case STORAGE_AREA:
//                    case ORIGIN:
                        KeyPropertyList.addAll (searchConditionInput.getKey ());
                        break;
                }
            });
            if(CollectionUtil.isNotEmpty (KeyPropertyList)){
                esHomeProductQueryReq.setKeyPropertyList (KeyPropertyList);
            }
            if (!secondsCategoryIds.isEmpty() && !firstCategoryIds.isEmpty()) {
                // 两个列表都不为空，取交集
                Set<Integer> intersection = new HashSet<>(secondsCategoryIds);
                intersection.retainAll(firstCategoryIds);
                esHomeProductQueryReq.setCategoryIds(new ArrayList<> (intersection));
            } else if (!secondsCategoryIds.isEmpty()) {
                // 只有 secondsCategoryIds 不为空
                esHomeProductQueryReq.setCategoryIds(new ArrayList<> (secondsCategoryIds));
            } else if (!firstCategoryIds.isEmpty()) {
                // 只有 firstCategoryIds 不为空
                esHomeProductQueryReq.setCategoryIds(new ArrayList<> (firstCategoryIds));
            }
        }
        if (merchantSubject.getSkuShow() == null && !org.apache.commons.collections4.CollectionUtils.isEmpty(skuList)) {
            //terms
            esHomeProductQueryReq.setSkuList(skuList);
        }
        conditionReq.setMajor(RequestHolder.isMajor());
        conditionReq.setSkuShow(merchantSubject.getSkuShow());
        if (RequestHolder.isMajor()) {
            if (!org.apache.commons.collections4.CollectionUtils.isEmpty(skuList)) {
                //报价单sku
                esHomeProductQueryReq.setMajorSkuList(skuList);
            }
            //全量
            if (Objects.equals(merchantSubject.getSkuShow(), 2)) {
                esHomeProductQueryReq.setUnShowSkus (notShowSkuList);
                esHomeProductQueryReq.setMType(0);
            }
        } else {
            //单店查询非大客户专享
            esHomeProductQueryReq.setMType(0);
        }

        if (StringUtils.isNotEmpty(input.getTitleSuggest ())) {
            esHomeProductQueryReq.setPdName(input.getTitleSuggest());
        }
        boolean coreCustomer = merchantService.checkCoreCustomers(merchantSubject.getMerchantId());
        conditionReq.setCoreCustomer(coreCustomer);
        esHomeProductQueryReq.setPageIndex(input.getPageIndex());
        esHomeProductQueryReq.setPageSize(input.getPageSize());
        return true;
    }
}
