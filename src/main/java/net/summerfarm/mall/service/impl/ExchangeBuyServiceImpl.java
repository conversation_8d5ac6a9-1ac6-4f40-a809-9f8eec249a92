package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.market.exchange.ExchangeBuyDTO;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.service.ExchangeBuyService;
import net.summerfarm.mall.service.MerchantPoolService;
import net.summerfarm.mall.service.helper.ExchangeBuyServiceHelper;
import net.xianmu.common.cache.InMemoryCache;
import net.xianmu.common.result.CommonResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @create: 2022/9/15
 */
@Slf4j
@Service
public class ExchangeBuyServiceImpl implements ExchangeBuyService {

    @Resource
    private ExchangeBaseInfoMapper exchangeBaseInfoMapper;

    @Resource
    private ExchangeScopeConfigMapper exchangeScopeConfigMapper;

    @Resource
    private ExchangeItemConfigMapper exchangeItemConfigMapper;

    @Resource
    private ExchangeBuyServiceHelper exchangeBuyServiceHelper;

    @Resource
    private OrdersMapper ordersMapper;

    @Resource
    private MerchantVisitMapper merchantVisitMapper;

    @Resource
    private MerchantPoolService merchantPoolService;

    @Resource
    private ExchangeBuyService mySelfService;

    public CommonResult<ExchangeBuyDTO> getExchangeBuyInfo() {
        ExchangeBuyDTO buyDTO = new ExchangeBuyDTO();
        if (RequestHolder.isMajor()) { //NOSONAR
            log.info("大客户不能获取换购活动信息");
            return CommonResult.ok(buyDTO);
        }

        List<ExchangeBaseInfo> baseInfos = mySelfService.listAllValid();
        if (CollectionUtil.isEmpty(baseInfos)) {
            log.info("没有换购活动信息");
            return CommonResult.ok(buyDTO);
        }
        Map<Long, ExchangeBaseInfo> baseInfoMap = baseInfos.stream()
                .collect(Collectors.toMap(ExchangeBaseInfo::getId, Function.identity()));
        List<Long> baseInfoIds = Lists.newArrayList(baseInfoMap.keySet());
        //获取人群包id
        List<ExchangeScopeConfig> scopeConfigs = exchangeScopeConfigMapper.listByBaseInfoIds(
                baseInfoIds);
        if (CollectionUtil.isEmpty(scopeConfigs)) {
            log.info("换购活动的活动范围为空");
            return CommonResult.ok(buyDTO);
        }

        //过滤人群包id,取人群包最新版本的详情
        //获取当前用户所有人群包ID
        Long mId = RequestHolder.getMId();
        List<MerchantPoolDetail> merchantPoolDetails = merchantPoolService.getDetailByMIdWithCache(mId);
        if (CollectionUtil.isEmpty(merchantPoolDetails)) {
            log.info("用户没有人群包");
            return CommonResult.ok(buyDTO);
        }
        List<Long> filerPoolInfoIds = merchantPoolDetails.stream().map(MerchantPoolDetail::getPoolInfoId)
                .collect(Collectors.toList());

        //理论上不应该在多个换购活动范围中，如果有多个，取范围创建时间最晚的
        List<ExchangeScopeConfig> resultScopeConfigs = scopeConfigs.stream()
                .filter(x -> filerPoolInfoIds.contains(x.getScopeId())).sorted((o1, o2) -> {
                    if (o1.getCreateTime().before(o2.getCreateTime())) {
                        return 1;
                    }
                    return -1;
                }).limit(1).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(resultScopeConfigs)) {
            log.info("用户无可见活动");
            return CommonResult.ok(buyDTO);
        }
        ExchangeScopeConfig scopeConfig = resultScopeConfigs.get(0);
        buyDTO.setExchangeScopeConfig(scopeConfig);
        buyDTO.setExchangeBaseInfo(baseInfoMap.get(scopeConfig.getBaseInfoId()));
        return CommonResult.ok(buyDTO);
    }


    @Override
    public CommonResult<List<ProductInfoVO>> listAllItems() {
        List<ProductInfoVO> list = Lists.newArrayList();
        if (RequestHolder.isMajor()) { //NOSONAR
            log.info("大客户不能获取换购活动商品信息");
            return CommonResult.ok(list);
        }
        CommonResult<ExchangeBuyDTO> buyInfo = getExchangeBuyInfo();
        ExchangeBuyDTO exchangeBuyDTO = buyInfo.getData();
        if (exchangeBuyDTO == null) {
            return CommonResult.ok(list);
        }
        //先判断是否有换购活动 以及 当前用户是否在活动中
        /*Long mId = RequestHolder.getMId();
        DataSynchronizationInformation dataSyncInfo = informationMapper.selectByTableName(
                EXCHANGE_ITEM);*/
        ExchangeBaseInfo baseInfo = exchangeBuyDTO.getExchangeBaseInfo();
        ExchangeScopeConfig scopeConfig = exchangeBuyDTO.getExchangeScopeConfig();
        if (baseInfo == null || scopeConfig == null) {
            return CommonResult.ok(list);
        }
        //先从配置表获取商品，再从离线表获取商品， 计算价格
        List<ExchangeItemConfig> itemConfigs = exchangeItemConfigMapper.selectByScopeId(
                scopeConfig.getId());
        if (CollectionUtil.isNotEmpty(itemConfigs)) {
            List<ProductInfoVO> configItemList = exchangeBuyServiceHelper.buildProductInfos(
                    itemConfigs, baseInfo, true);
            list.addAll(configItemList);
        }

        //2024年07月23号 产品要求去掉获取离线数据逻辑（飞书沟通） -- by薄荷
        /*List<String> configSkus = itemConfigs.stream().map(ExchangeItemConfig::getSku)
                .collect(Collectors.toList());
        //计算价格，获取活动配置的优惠限制
        List<ExchangeItem> exchangeItems = exchangeItemMapper.selectByMid(mId,
                dataSyncInfo.getDateFlag());
        //处理活动配置的商品，总数最多40个，第一种算法的数量如果不足30个，则从第二种补充
        if (CollectionUtil.isEmpty(exchangeItems)) {
            //只返回配置的商品
            return CommonResult.ok(list);
        }
        Map<Integer, List<ExchangeItem>> listMap = exchangeItems.stream()
                .collect(Collectors.groupingBy(ExchangeItem::getType));
        if (listMap == null) {
            return CommonResult.ok(list);
        }

        // 获取购物车中的商品，需要将这部分sku过滤掉
        Long accountId = RequestHolder.getAccountId();
        List<String> trolleySkus = trolleyMapper.listAllSku(mId, accountId);
        configSkus.addAll(trolleySkus);
        Set<ExchangeItem> showItems = Sets.newLinkedHashSet();
        Set<String> algorithmSkus = Sets.newLinkedHashSet();
        int maxSize = 30;
        for (ExchangeItemTypeEnum typeEnum : ExchangeItemTypeEnum.values()) {
            List<ExchangeItem> itemList = listMap.get(typeEnum.getCode());
            if (CollectionUtil.isEmpty(itemList)) {
                continue;
            }
            //过滤掉配置的sku
            Set<ExchangeItem> items = itemList.stream()
                    .filter(x -> !configSkus.contains(x.getSku()))
                    .sorted(Comparator.comparing(ExchangeItem::getPriority)).limit(maxSize).collect(
                            Collectors.toCollection(LinkedHashSet::new));
            showItems.addAll(items);
            LinkedHashSet<String> skuSet = items.stream().map(x -> x.getSku())
                    .collect(Collectors.toCollection(LinkedHashSet::new));
            algorithmSkus.addAll(skuSet);
            //避免两种算法中有重复
            maxSize = 40 - skuSet.size();
        }
        if (CollectionUtil.isEmpty(showItems)) {
            return CommonResult.ok(list);
        }
        //获取离线表的商品，计算价格
        List<ProductInfoVO> algorithmList = exchangeBuyServiceHelper.buildProductInfos(showItems,
                baseInfo);
        //经过ES搜索后数据，再去重
        Set<String> finalAlgorithmSkus = algorithmSkus;
        List<ProductInfoVO> filterAlgorithmList = algorithmList.stream()
                .filter(x -> finalAlgorithmSkus.contains(x.getSku())).collect(Collectors.toList());

        list.addAll(filterAlgorithmList);*/
        return CommonResult.ok(list);
    }

    @Override
    public CommonResult<Boolean> guideShow() {
        Long mId = RequestHolder.getMId();
        //查询商家是否购买过换购商品，购买过则不需要蒙层引导
        int count = ordersMapper.countOrdersByType(mId);
        if (count >= 1) {
            return CommonResult.ok(false);
        }
        //判断是否弹窗过
        MerchantVisit merchantVisit = merchantVisitMapper.getByMidAndType(mId, 0);
        if (merchantVisit == null || merchantVisit.getCount() <= 0) {
            //需要弹窗并记录已访问
            MerchantVisit visit = new MerchantVisit();
            visit.setCount(1);
            visit.setType(0);
            visit.setMId(mId);
            merchantVisitMapper.insertSelective(visit);
            return CommonResult.ok(true);
        }
        return CommonResult.ok(false);
    }

    @Override
    @InMemoryCache
    public List<ExchangeBaseInfo> listAllValid() {
        return exchangeBaseInfoMapper.listAllValid();
    }
}
