package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.engine.client.req.EsHomeProductQueryConditionReq;
import net.summerfarm.mall.engine.client.req.EsHomeProductQueryReq;
import net.summerfarm.mall.facade.engine.MarketQueryProviderFacade;
import net.summerfarm.mall.facade.engine.model.EsMarketItemInfoDTO;
import net.summerfarm.mall.model.domain.Contact;
import net.summerfarm.mall.model.dto.product.MallProductQueryDTO;
import net.summerfarm.mall.service.ContactService;
import net.summerfarm.mall.service.ProductSearchRerankService;
import net.summerfarm.mall.service.SkuQueryService;
import net.summerfarm.mall.service.facade.WncDeliveryRuleQueryFacade;
import net.summerfarm.mall.service.search.CategoryPredictionCacheService;
import net.summerfarm.mall.service.search.ProductSearchBatchDisperseService;
import net.summerfarm.recommend.strategy.model.UserExperimentVariantDTO;
import net.summerfarm.recommend.strategy.service.UserExperimentingService;
import net.summerfarm.wnc.client.enums.SourceEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品搜索重排序服务实现类。
 * 该服务处理基于用户实验和类目预测的商品搜索结果重排序逻辑。
 */
@Slf4j
@Service
public class ProductSearchRerankServiceImpl implements ProductSearchRerankService {

    @Autowired
    private MarketQueryProviderFacade marketQueryProviderFacade;

    @Autowired
    private UserExperimentingService userExperimentingService;

    @Autowired
    private HttpServletResponse response;

    @Autowired
    private SkuQueryService skuQueryService;

    @Autowired
    private CategoryPredictionCacheService categoryPredictionCacheService;

    @Autowired
    private ContactService contactService;
    @Autowired
    private WncDeliveryRuleQueryFacade deliveryRuleQueryFacade;

    @Autowired
    private ProductSearchBatchDisperseService productSearchBatchDisperseService;

    private static final String EXPERIMENT_ID = "product_search_disperse";

    private static final String EXPERIMENT_HEADER_NAME = "x-rerank-variant";

    private static final String EXPERIMENT_VARIANT_RESULT_HEADER_NAME = "xm-ab-exp";

    @NacosValue(value = "${rerank.by.stock.batch.size:20}", autoRefreshed = true)
    private int rerankByStockBatchSize;

    @NacosValue(value = "${rerank.max.sku.fetch.count:400}", autoRefreshed = true)
    private int maxSkuFetchCount;

    @NacosValue(value = "${search.maxScore.percentage:80}", autoRefreshed = true)
    private Integer percentage;
    // "xm-ab-exp":"[{\"experimentId\":\"new-home\",\"experimentPlace\":\"place-of:new-home\",\"variantId\":\"V3\"}]"

    /**
     * 获取实验分组。
     * 优先从HTTP Header 'xm-ab-exp' 中获取，如果Header中不存在，则从用户实验服务中获取。
     *
     * @return 实验分组
     */
    private String getExperimentVariant() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        if (requestAttributes != null) {
            HttpServletRequest request = requestAttributes.getRequest();
            String xmAbExp = request.getHeader(EXPERIMENT_VARIANT_RESULT_HEADER_NAME);
            if (xmAbExp != null && !xmAbExp.isEmpty()) {
                try {
                    // 解析json
                    List<UserExperimentVariantDTO> userExperimentVariantDTOList = com.alibaba.fastjson.JSON
                            .parseArray(xmAbExp, UserExperimentVariantDTO.class);
                    if (!CollectionUtils.isEmpty(userExperimentVariantDTOList)) {
                        for (UserExperimentVariantDTO userExperimentVariantDTO : userExperimentVariantDTOList) {
                            if (EXPERIMENT_ID.equalsIgnoreCase(userExperimentVariantDTO.getExperimentId())) {
                                return userExperimentVariantDTO.getVariantId();
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("解析xm-ab-exp header失败,xmAbExp:{}", xmAbExp, e);
                }
            }
        }
        List<UserExperimentVariantDTO> experimentVariantList = userExperimentingService
                .getExperimentVariant(RequestHolder.getMId(), RequestHolder.getMerchantAreaNo());
        String experimentVariant = "V0";
        if (!CollectionUtils.isEmpty(experimentVariantList)) {
            experimentVariant = Optional
                    .ofNullable(experimentVariantList.stream()
                            .filter(experiment -> EXPERIMENT_ID.equalsIgnoreCase(experiment.getExperimentId())) // 匹配实验ID
                            .findFirst().orElse(null)) // 如果没有找到，则默认值
                    .map(UserExperimentVariantDTO::getVariantId).orElse(experimentVariant); // 默认为V0
        }
        return experimentVariant;
    }

    /**
     * 根据用户实验分组和类目预测对商品进行重排序。
     *
     * @param esHomeProductQueryReq 商品查询请求，包含查询关键字、分页信息等。
     * @param conditionReq          查询条件，包含过滤条件等。
     * @return 重排序后的商品分页信息，包含商品列表和分页信息。
     */
    @Override
    public PageInfo<EsMarketItemInfoDTO> searchProducts(EsHomeProductQueryReq esHomeProductQueryReq,
                                                        EsHomeProductQueryConditionReq conditionReq,
                                                        MallProductQueryDTO mallProductQuery,
                                                        boolean withScore) {
        String query = esHomeProductQueryReq.getPdName();
        String experimentVariant = getExperimentVariant();
        boolean isForIndividualUser = CollectionUtils.isEmpty(esHomeProductQueryReq.getMajorSkuList());
        boolean needPageInMemory = mallProductQuery != null && mallProductQuery.getDeliveryTime() != null;
        if (needPageInMemory) {
            log.info("需要内存分页，直接走内存分页逻辑。query:{}, deliveryTime:{}", query, mallProductQuery.getDeliveryTime());
            response.setHeader(EXPERIMENT_HEADER_NAME, experimentVariant + ":no_change:no_query");
            return this.homeProductPageQueryInMemory(esHomeProductQueryReq, conditionReq, mallProductQuery);
        }
        if (StringUtils.isEmpty(query) || !isForIndividualUser) {
            log.info("pdName是空的,或者是大客户，大客户暂时不做重排序，什么也不做。query:{}, isForIndividualUser:{}", query, isForIndividualUser);
            response.setHeader(EXPERIMENT_HEADER_NAME,
                    experimentVariant + (isForIndividualUser ? ":no_change:no_query" : ":no_change:major_client"));
            if (withScore) {
                return marketQueryProviderFacade.homeProductQueryWithScore(esHomeProductQueryReq, conditionReq);
            } else {
                return marketQueryProviderFacade.homeProductQuery(esHomeProductQueryReq, conditionReq);
            }
        }
        Long mId = RequestHolder.getMId();
        log.info("(类目重排序)已经全量了，此处全部都走类目重排:{}", mId);
        Map<Long, Double> categoryPercentile = categoryPredictionCacheService.getQueryCategoryPercentile(query);
        PageInfo<EsMarketItemInfoDTO> rerankedProducts = rerankProducts(esHomeProductQueryReq, conditionReq, query, categoryPercentile, experimentVariant, withScore);
        return rerankedProducts;
    }

    private PageInfo<EsMarketItemInfoDTO> rerankProducts(EsHomeProductQueryReq esHomeProductQueryReq,
                                                         EsHomeProductQueryConditionReq conditionReq, String pdName,
                                                         Map<Long, Double> categoryPercentile, String experimentVariant, boolean withScore) {
        PageInfo<EsMarketItemInfoDTO> esPageInfo = new PageInfo<>();
        esPageInfo.setList(Collections.emptyList());
        int pageSize = esHomeProductQueryReq.getPageSize();
        int pageIndex = esHomeProductQueryReq.getPageIndex();
        if (pageSize * pageIndex > maxSkuFetchCount) {
            log.warn("query:{} 查询页数过大，未能重排序,pageSize:{}, pageIndex:{}", pdName, pageSize, pageIndex);
            response.setHeader(EXPERIMENT_HEADER_NAME, experimentVariant + ":no_change");
            return esPageInfo;
        }
        if (categoryPercentile == null) {
            categoryPercentile = Collections.EMPTY_MAP;
        }
        esHomeProductQueryReq.setPageSize(maxSkuFetchCount);// 先查询200条+pageSize
        esHomeProductQueryReq.setPageIndex(1); // 固定从page 1开始

        if (withScore) {
            esPageInfo = marketQueryProviderFacade.homeProductQueryWithCoreRpc(esHomeProductQueryReq, conditionReq);
        } else {
            esPageInfo = marketQueryProviderFacade.homeProductQuery(esHomeProductQueryReq, conditionReq);
        }
        if (null == esPageInfo || org.apache.commons.collections.CollectionUtils.isEmpty(esPageInfo.getList())) {
            return esPageInfo;
        }

        List<EsMarketItemInfoDTO> esMarketItemInfoDTOs = esPageInfo.getList();
        if (withScore) {
            EsMarketItemInfoDTO esMarketItemInfoDTO = esMarketItemInfoDTOs.get(0);
            float maxScore = esMarketItemInfoDTO.getMaxScore() * percentage / 100;
            esMarketItemInfoDTOs = esPageInfo.getList().stream().filter(o -> o.getScore() > maxScore).collect(Collectors.toList());
            skuQueryService.fillCondition(esHomeProductQueryReq, esMarketItemInfoDTOs);
        }

        if (CollectionUtils.isEmpty(esMarketItemInfoDTOs)) {
            log.warn("重排序前，query:{}，过滤最低分数后商品数为空", pdName);
            esPageInfo.setList(Collections.EMPTY_LIST);
            return esPageInfo;
        }
        int filteredCount = esMarketItemInfoDTOs.size();
        log.info("重排序前，query:{}, categoryPercentile:{}, filteredCount:{}", pdName, categoryPercentile, filteredCount);
        if (filteredCount < pageSize * (pageIndex - 1)) {
            log.warn("重排序前，query:{}，过滤最低分数后商品数量不足，未能重排序, filteredCount:{}, pageSize:{}, pageIndex:{}", pdName, filteredCount, pageSize, pageIndex);
            esPageInfo.setList(Collections.EMPTY_LIST);
            return esPageInfo;
        }

        // Precompute the index map using itemCode as the key
        Map<String, Double> indexMap = new HashMap<>();
        for (int i = 0, totalLength = esMarketItemInfoDTOs.size(); i < totalLength; i++) {
            indexMap.put(esMarketItemInfoDTOs.get(i).getItemCode(), (double) (totalLength + 2000 - i));
        }
        Map<Long, Double> finalCategoryPercentile = categoryPercentile;
        esMarketItemInfoDTOs.sort((o1, o2) -> {
            double categoryScore1 = finalCategoryPercentile.getOrDefault(o1.getCategoryId(), 0.1);
            double categoryScore2 = finalCategoryPercentile.getOrDefault(o2.getCategoryId(), 0.1);
            double score1 = indexMap.getOrDefault(o1.getItemCode(), 1.00);
            double score2 = indexMap.getOrDefault(o2.getItemCode(), 1.00);
            double finalScore1 = score1 * categoryScore1;
            double finalScore2 = score2 * categoryScore2;
            return Double.compare(finalScore2, finalScore1);
        });
        log.info("重排序完成，query:{}, categoryPercentile:{}", pdName, categoryPercentile);

        int rerankBatchSize = Math.min(rerankByStockBatchSize, esMarketItemInfoDTOs.size());
        log.info("开始按照库存和销量进行批量分散重排序，SKU总数: {}", esMarketItemInfoDTOs.size());
        esMarketItemInfoDTOs = productSearchBatchDisperseService.batchDisperseSkusWithDefaultStock(
                esMarketItemInfoDTOs, rerankBatchSize);

        esPageInfo.setTotal(esMarketItemInfoDTOs.size());

        // 对排序完的esMarketItemInfoDTOs根据pageSize，pageIndex做分页
        int start = Math.max(0, (pageIndex - 1) * pageSize);
        if (start >= esMarketItemInfoDTOs.size()) {
            esPageInfo.setList(Collections.EMPTY_LIST);
            return esPageInfo;
        }
        int end = Math.min(start + pageSize, esMarketItemInfoDTOs.size());
        esPageInfo.setList(esMarketItemInfoDTOs.subList(start, end));
        return esPageInfo;
    }

    /**
     * 在内存里进行分页
     *
     * @param esHomeProductQueryReq es请求参数
     * @param conditionReq          es请求参数
     * @param mallProductQuery      内存分页请求参数
     * @return 分页结果
     */
    public PageInfo<EsMarketItemInfoDTO> homeProductPageQueryInMemory(EsHomeProductQueryReq esHomeProductQueryReq,
                                                                      EsHomeProductQueryConditionReq conditionReq,
                                                                      MallProductQueryDTO mallProductQuery) {
        // 在内存里分页
        int pageSize = esHomeProductQueryReq.getPageSize();
        int pageIndex = esHomeProductQueryReq.getPageIndex();
        try {
            esHomeProductQueryReq.setPageSize(200 + pageSize * pageIndex);// 先查询200条+pageSize
            esHomeProductQueryReq.setPageIndex(1); // 固定从page 1开始
            PageInfo<EsMarketItemInfoDTO> esPageInfo = marketQueryProviderFacade.homeProductQuery(esHomeProductQueryReq, conditionReq);
            List<EsMarketItemInfoDTO> all = esPageInfo.getList();
            if (CollectionUtil.isEmpty(all)) {
                return PageInfo.emptyPageInfo();
            }
            // 再次筛选符合条件的（指定配送日期）商品
            List<EsMarketItemInfoDTO> esMarketItemInfoDTOs = filterByDeliveryTime(mallProductQuery, esPageInfo.getList());

            // 对排序完的esMarketItemInfoDTOs根据pageSize，pageIndex做分页
            int start = Math.max(0, (pageIndex - 1) * pageSize);
            if (start >= esMarketItemInfoDTOs.size()) {
                log.warn("分页参数错误，start:{}, size:{}", start, esMarketItemInfoDTOs.size());
                return PageInfo.emptyPageInfo();
            }
            int end = Math.min(start + pageSize, esMarketItemInfoDTOs.size());
            esPageInfo.setList(esMarketItemInfoDTOs.subList(start, end));
            esPageInfo.setTotal(esMarketItemInfoDTOs.size());
            if (CollectionUtils.isEmpty(esMarketItemInfoDTOs)) {
                esPageInfo.setIsLastPage(true);
            }
            return esPageInfo;
        } catch (Exception e) {
            log.error("homeProductQuery error:{}", e.getMessage(), e);
            return PageInfo.emptyPageInfo();
        }
    }

    /**
     * 过滤出符合条件的商品列表
     *
     * @param mallProductQuery     商城查询参数
     * @param esMarketItemInfoDTOs 商品列表
     * @return 过滤后的商品列表
     */
    private List<EsMarketItemInfoDTO> filterByDeliveryTime(MallProductQueryDTO mallProductQuery, List<EsMarketItemInfoDTO> esMarketItemInfoDTOs) {
        if (null == mallProductQuery || CollectionUtils.isEmpty(esMarketItemInfoDTOs)) {
            return esMarketItemInfoDTOs;
        }
        // 指定的配送日期
        LocalDate assignDeliveryTime = mallProductQuery.getDeliveryTime();
        if (null == mallProductQuery.getMId() || null == assignDeliveryTime) {
            return esMarketItemInfoDTOs;
        }
        Contact contacts = contactService.getMerchantDefaultContactCache(mallProductQuery.getMId());
        if (contacts == null) {
            log.info("当前门店的默认contacts is null! mId >>> {}", mallProductQuery.getMId());
            return Collections.emptyList();
        }
        List<String> skuCodeList = esMarketItemInfoDTOs.stream().map(EsMarketItemInfoDTO::getItemCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuCodeList)) {
            return esMarketItemInfoDTOs;
        }
        Map<String, LocalDate> deliveryDateMap = deliveryRuleQueryFacade.getDeliveryRuleListAddSkuMap(
                LocalDateTime.now(),
                mallProductQuery.getMId(),
                contacts.getContactId(),
                null, null,
                SourceEnum.POP_MALL,
                skuCodeList);
        return esMarketItemInfoDTOs.stream().filter(esMarketItemInfoDTO -> assignDeliveryTime.equals(deliveryDateMap.get(esMarketItemInfoDTO.getItemCode()))).collect(Collectors.toList());
    }
}
