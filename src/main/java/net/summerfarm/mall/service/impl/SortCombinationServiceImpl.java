package net.summerfarm.mall.service.impl;

import net.summerfarm.common.util.StringUtils;
import net.summerfarm.enums.SeriesTypeEnum;
import net.summerfarm.mall.common.util.RedisCacheUtil;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.mapper.SortCombinationMapper;
import net.summerfarm.mall.mapper.SortCombinationTabMapper;
import net.summerfarm.mall.model.domain.SortCombination;
import net.summerfarm.mall.model.domain.SortCombinationTab;
import net.summerfarm.mall.service.SortCombinationService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-04-08
 * @description
 */
@Service
public class SortCombinationServiceImpl implements SortCombinationService {
    @Resource
    private SortCombinationMapper combinationMapper;
    @Resource
    private SortCombinationTabMapper tabMapper;
    @Resource
    private RedisCacheUtil redisCacheUtil;

    @Override
    public List<SortCombinationTab> queryTabByAreaNo() {
        SortCombination combination = combinationMapper.selectByAreaNo(SeriesTypeEnum.SORT.getType(), RequestHolder.getMerchantAreaNo());
        if(combination == null){
            return null;
        }

        return tabMapper.selectByCombinationId(combination.getId());
    }

    @Override
    public SortCombinationTab tabDetail(Integer id) {
        return tabMapper.selectByPrimaryKey(id);
    }

    @Override
    public SortCombinationTab selectSortCombinationTabWithCache(Integer pdId) {
        if (StringUtils.isBlank(pdId)) {
            return null;
        }
        String key = Global.CACHE_SORT_COMBINATION_TAB + pdId;

        return redisCacheUtil.getDataWithCache(key,  300, SortCombinationTab.class, () -> tabMapper.selectByPrimaryKey(pdId));
    }

    @Override
    public SortCombinationTab getTabOrDefault(Integer id) {
        SortCombinationTab tab = null;
        if(id != null){
            tab = selectSortCombinationTabWithCache(id);
        } else {
            List<SortCombinationTab> tabs = queryTabByAreaNo();
            if(!CollectionUtils.isEmpty(tabs)){
                tab = tabs.get(0);
            }
        }

        return tab;
    }
}
