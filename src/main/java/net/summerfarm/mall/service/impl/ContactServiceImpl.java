package net.summerfarm.mall.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.common.util.JsonEqualityChecker;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.constant.Constants;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.facade.wnc.ContactDeliveryRuleQueryFacade;
import net.summerfarm.mall.facade.wnc.FenceFacade;
import net.summerfarm.mall.facade.wnc.WncStoreQueryFacade;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.FenceVO;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.model.vo.merchant.contact.ContactAddressRemark;
import net.summerfarm.mall.service.ContactService;
import net.summerfarm.mall.service.FenceService;
import net.summerfarm.mall.service.MerchantService;
import net.summerfarm.mall.service.facade.WncDeliveryAlertQueryFacade;
import net.summerfarm.mall.service.facade.WncDeliveryFenceQueryFacade;
import net.summerfarm.mall.service.facade.WncDeliveryRuleQueryFacade;
import net.summerfarm.mall.service.facade.dto.*;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.xianmu.common.cache.InMemoryCache;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Package: net.summerfarm.customer.service.impl
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/10/8
 */
@Service
public class ContactServiceImpl implements ContactService {
    private static String EMOJI_REG="[^\\u0000-\\uFFFF]";

    @Resource
    private ContactMapper contactMapper;
    @Resource
    private MqProducer mqProducer;

    private static final Logger logger = LoggerFactory.getLogger(ContactServiceImpl.class);

    @Resource
    private FenceService fenceService;
    @Resource
    WarehouseLogisticsService logisticsService;
    @Resource
    private MchEnterpriseAddressRelationMapper mchEnterpriseAddressRelationMapper;
    @Resource
    private WncDeliveryRuleQueryFacade deliveryRuleQueryFacade;
    @Resource
    private AreaMapper areaMapper;

    @Resource
    ContactOperateLogMapper contactOperateLogMapper;
    @Resource
    private WncDeliveryFenceQueryFacade wncDeliveryFenceQueryFacade;
    @Resource
    private FenceFacade fenceFacade;
    @Resource
    private WncDeliveryAlertQueryFacade wncDeliveryAlertQueryFacade;

    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private MerchantService merchantService;
    @Resource
    private ContactDeliveryRuleQueryFacade contactDeliveryRuleQueryFacade;

    private static final int MALL_SOURCE = 1;

    @Resource
    private OrdersMapper ordersMapper;

    @Resource
    private WncStoreQueryFacade wncStoreQueryFacade;


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult updateContact(Contact contact) {
        logger.info("用户发起修改地址请求.req-contact:{}", JSON.toJSONString(contact));
        if (contact.getContactId() == null || contactMapper.selectByPrimaryKey(contact.getContactId()) == null) {
            return AjaxResult.getError(ResultConstant.DEFAULT_FAILED, "不正确地址");
        }
        AjaxResult ajaxResult = checkContactRemark(contact);
        if (ajaxResult != null){
            logger.warn("地址备注校验异常!");
            return ajaxResult;
        }
        Boolean isDefault = selectIsDefault(contact.getContactId());

        //设置为默认地址
        if (contact.getIsDefault() != null) {
            logger.info("设置默认地址.req-contact:{}", JSON.toJSONString(contact));
            //其他可用地址设置为非默认
            Contact record = new Contact();
            record.setmId(contact.getmId());
            record.setIsDefault(0);
            record.setHouseNumber(contact.getHouseNumber());
            contactMapper.updateByMid(record);
            //设置当前地址为默认
            record.setIsDefault(1);
            record.setContactId(contact.getContactId());
            contactMapper.updateByPrimaryKeySelective(record);
            //merchantService.pushMerchantInfo(contact.getmId());
            addOperLog(Objects.requireNonNull(RequestHolder.getMerchantSubject()),
                    "设置为默地址", ContactLogEnum.OperateType.UPDATE.ordinal(), record.getContactId());

            return AjaxResult.getOK();
        }
        //删除地址
        if (contact.getStatus() != null) {
            logger.info("删除地址.req-contact:{}", JSON.toJSONString(contact));
            if(contact.getStatus()!=4){
                List<Contact> list = contactMapper.selectByMid(contact.getmId(), ContactStatusEnum.PASS.code);
                if (CollectionUtils.isEmpty(list) || list.size() < 2) {
                    return AjaxResult.getError(ResultConstant.DEFAULT_FAILED, "当前仅有1个地址，不可删除，请添加新地址");
                }
            }
            Contact record = new Contact();
            record.setContactId(contact.getContactId());
            record.setStatus(2);
            record.setHouseNumber(contact.getHouseNumber());
            contactMapper.updateByPrimaryKeySelective(record);
            //删除地址之后，判断是否商城中有和企业信息关联，如有则删除关联
            MchEnterpriseAddressRelation mchEnterpriseAddressRelation = mchEnterpriseAddressRelationMapper.selectById(contact.getContactId());
            if (!ObjectUtils.isEmpty(mchEnterpriseAddressRelation)) {
                //如果存在关联关系则使其失效
                mchEnterpriseAddressRelationMapper.updateByKey(mchEnterpriseAddressRelation.getEnterpriseInformationId());
            }
            List<Contact> list = contactMapper.selectByMid(contact.getmId(), ContactStatusEnum.PASS.code);
            if(list.size() == 1){
                record = new Contact();
                record.setContactId(list.get(0).getContactId());
                record.setIsDefault(1);
                contactMapper.updateByPrimaryKeySelective(record);
            }
            addOperLog(Objects.requireNonNull(RequestHolder.getMerchantSubject()),
                    "删除地址", ContactLogEnum.OperateType.DELETE.ordinal(), record.getContactId());
            return AjaxResult.getOK();
        }
        //单纯修改联系人 不修改地址
        if (contact.getContactId() != null) {
            logger.info("单纯修改联系人 不修改地址.req-contact:{}", JSON.toJSONString(contact));
            List<Contact> list = contactMapper.selectByMid(contact.getmId(), ContactStatusEnum.PASS.code);
            if (CollectionUtils.isEmpty(list) || list.size() < 2) {
                return AjaxResult.getError(ResultConstant.DEFAULT_FAILED, "为保证您的正常使用，请直接新增地址");
            }
            Contact con = contactMapper.selectByPrimaryKey(contact.getContactId());
            if (!con.getmId().equals(contact.getmId())) {
                return AjaxResult.getError(ResultConstant.DEFAULT_FAILED, "没有修改权限");
            }
            if (Objects.equals(con.getContact(), contact.getContact())
                    && Objects.equals(con.getPhone(), contact.getPhone())
                    && Objects.equals(con.getHouseNumber(), contact.getHouseNumber())
                    && JsonEqualityChecker.areJsonObjectsEqual(con.getAddressRemark(), contact.getAddressRemark())
            ){
                logger.info("修改地址时未修改任何内容 >>> req-contact:{}", JSON.toJSONString(contact));
                return AjaxResult.getError(ResultConstant.DEFAULT_FAILED, "未修改任何内容");
            }
            final Merchant merchant = merchantMapper.selectByPrimaryKey(con.getmId());
            if(merchant == null) {
                logger.info("门店不存在!.req-contact:{}", JSON.toJSONString(contact));
                return AjaxResult.getError(ResultConstant.DEFAULT_FAILED, "门店信息不存在");
            }
            // 是否pop
            boolean popMerchant = merchantService.isPopMerchantV2(merchant.getBusinessLine());

            //先删除 再插入
            Contact record = new Contact();
            record.setContactId(contact.getContactId());
            record.setStatus(2);
            contactMapper.updateByPrimaryKeySelective(record);

            con.setPhone(contact.getPhone());
            con.setContact(contact.getContact());
            con.setContactId(null);
            con.setHouseNumber(contact.getHouseNumber());
            con.setStoreNo(con.getStoreNo());
            if (StringUtils.isNotBlank(contact.getAddressRemark())){
                con.setAddressRemark(contact.getAddressRemark());
            }
            contactMapper.insertSelective(con);
            //如果和用户企业信息有关联，则修改新的关联
            MchEnterpriseAddressRelation mchEnterpriseAddressRelation = mchEnterpriseAddressRelationMapper.selectById(contact.getContactId());
            if (!ObjectUtils.isEmpty(mchEnterpriseAddressRelation)) {
                //如果存在关联关系则更新
                MchEnterpriseAddressRelation m = new MchEnterpriseAddressRelation();
                m.setId(mchEnterpriseAddressRelation.getId());
                m.setContactId(con.getContactId());
                mchEnterpriseAddressRelationMapper.updateByPrimaryKeySelective(m);
            }
            if(popMerchant) {
                logger.info("pop客户重新设置城配仓和配送周期.contact:{}", JSON.toJSONString(con));
                contactDeliveryRuleQueryFacade.popDeliveryRuleConfigSaveOrUpdate(con.getContactId(), con.getStoreNo());
            }

            addOperLog(Objects.requireNonNull(RequestHolder.getMerchantSubject()),
                    "修改地址", ContactLogEnum.OperateType.UPDATE.ordinal(), record.getContactId());
            return AjaxResult.getOK();

        }
        return AjaxResult.getError(ResultConstant.DEFAULT_FAILED);

    }

    private Boolean selectIsDefault(Long contactId) {
        Contact contact = contactMapper.selectIsDefault(contactId);
        return contact != null;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult insertContact(Contact contact) {
        logger.info("接受到新增地址请求：contact：{}", JSON.toJSONString(contact));
        boolean isPopMerchant = RequestHolder.isPopMerchant();
        //检查地址备注特殊字符
        AjaxResult ajaxResult = checkContactRemark(contact);
        if (ajaxResult != null){
            return ajaxResult;
        }

        boolean repeatFlag = contactMapper.isRepeat(contact);
        if (repeatFlag) {
            return AjaxResult.getErrorWithMsg("收货地址信息重复，请修改后提交");
        }
        // 校验城配仓信息
        this.getStoreNo(isPopMerchant, contact);
        //新增地址
        if (contact.getContactId() == null) {
            Long insert = insert(contact);
            addOperLog(Objects.requireNonNull(RequestHolder.getMerchantSubject()),"新增地址", ContactLogEnum.OperateType.ADD.ordinal(), insert);
        } else {
            if (contactMapper.selectByPrimaryKey(contact.getContactId()) == null) {
                AjaxResult.getError(ResultConstant.DEFAULT_FAILED, "不正确地址");
            }
            List<Contact> list = contactMapper.selectByMid(contact.getmId(), 1);
            if (CollectionUtils.isEmpty(list) || list.size() < 2) {
                AjaxResult.getError(ResultConstant.DEFAULT_FAILED, "当前只有一个可用,请新增地址再调整");
            }
            Contact record = new Contact();
            record.setContactId(contact.getContactId());
            record.setStatus(2);
            contactMapper.updateByPrimaryKeySelective(record);
            Long insert = insert(contact);
            addOperLog(Objects.requireNonNull(RequestHolder.getMerchantSubject()),"修改地址", ContactLogEnum.OperateType.UPDATE.ordinal(), insert);

        }
        return AjaxResult.getOK();
    }

    @Override
    public CommonResult<Void> completeAddress(Contact contact) {
        logger.info("开始地址补全：contact:{}, merchant:{}", JSON.toJSONString(contact), JSON.toJSONString(RequestHolder.getLoginMerchantCache()));
        boolean isPopMerchant = RequestHolder.isPopMerchant();

        if (StringUtils.isBlank(contact.getContact())) {
            logger.error("联系人信息缺失。contact:{}", JSON.toJSONString(contact));
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "未填写联系人！", ResultConstant.DEFAULT_FAILED);
        }

        Contact oldContact = contactMapper.selectByPrimaryKey(contact.getContactId());
        if (oldContact == null) {
            logger.error("待补全的地址不存在。contact:{}", JSON.toJSONString(contact));
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "不正确地址", ResultConstant.DEFAULT_FAILED);
        }

        if (AddressCompletionStatusEnum.COMPLETED.getCode().equals(oldContact.getAddressCompletionFlag())) {
            logger.error("地址已补全。oldContact:{}", JSON.toJSONString(oldContact));
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "地址已补全,请刷新试试~", ResultConstant.DEFAULT_FAILED);
        }

        //检查地址备注特殊字符
        AjaxResult ajaxResult = checkContactRemark(contact);
        if (ajaxResult != null){
            logger.error("检查地址备注特殊字符不通过，msg：{}", JSON.toJSONString(ajaxResult));
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ajaxResult.getMsg(), ResultConstant.DEFAULT_FAILED);
        }

        //地址截单区域是否和店铺截单区域相同
        Integer areaNo = RequestHolder.getMerchantAreaNo();
        AjaxResult ajaxResult1 = fenceService.checkCityAreaFence(contact);
        if(!ajaxResult1.isSuccess()){
            logger.error("ajaxResult:{},contact:{}", JSON.toJSONString(ajaxResult1), JSON.toJSONString(contact));
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "不在配送范围内", Constants.INCONSISTENCY_IN_AREA_NO);
        }
        Fence fence = (Fence) ajaxResult1.getData();
        if(!Objects.equals(areaNo, fence.getAreaNo())) {
            logger.error("完善地址时不允许跨运营区域，merchantArea：{}, newContactFence:{}", areaNo, JSON.toJSONString(fence));
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "新地址和门店注册地址不在同一个运营区域，请新增地址", Constants.INCONSISTENCY_IN_AREA_NO);
        }

        // 校验城配仓信息
        this.getStoreNo(isPopMerchant, contact);

        // 补全地址信息
        contact.setAddressCompletionFlag(AddressCompletionStatusEnum.COMPLETED.getCode());
        contactMapper.updateByPrimaryKeySelective(contact);
        addOperLog(Objects.requireNonNull(RequestHolder.getMerchantSubject()), "地址补全", ContactLogEnum.OperateType.UPDATE.ordinal(), contact.getContactId());

        logger.info("地址补全结束，新地址:{}", JSON.toJSONString(contact));
        return CommonResult.ok();
    }

    private Long insert(Contact contact) {
        Area merchantArea = RequestHolder.getMerchantArea();
        boolean isPopMerchant = RequestHolder.isPopMerchant();
        Contact record = new Contact();
        record.setmId(contact.getmId());
        record.setContact(contact.getContact());
        record.setPhone(contact.getPhone());
        record.setProvince(contact.getProvince());
        record.setCity(contact.getCity());
        record.setArea(contact.getArea());
        record.setAddress(contact.getAddress());
        record.setPoiNote(contact.getPoiNote());
        record.setStatus(3);
        record.setIsDefault(0);
        record.setHouseNumber(contact.getHouseNumber());
        if (contact.getContactAddressRemark() != null) {
            contact.setAddressRemark(JSONUtil.toJsonStr(contact.getContactAddressRemark()));
        }
        if (StringUtils.isNotBlank(contact.getAddressRemark())){
            record.setAddressRemark(contact.getAddressRemark());
        }
        Integer areaNo = merchantArea.getAreaNo();
        contactMapper.insertSelective(record);


        //如果和用户企业信息有关联，则修改新的关联
        MchEnterpriseAddressRelation mchEnterpriseAddressRelation = mchEnterpriseAddressRelationMapper.selectById(contact.getContactId());
        if (!ObjectUtils.isEmpty(mchEnterpriseAddressRelation)) {
            //如果存在关联关系则更新
            MchEnterpriseAddressRelation m = new MchEnterpriseAddressRelation();
            m.setId(mchEnterpriseAddressRelation.getId());
            m.setContactId(record.getContactId());
            mchEnterpriseAddressRelationMapper.updateByPrimaryKeySelective(m);
        }

        // 处理门店地址自动审核
        boolean autoAuditFlag = false;
        Integer fenceAreaNo = fenceFacade.getAreaNoByAddress(record.getCity(), record.getArea(), isPopMerchant);
        autoAuditFlag = Objects.equals(areaNo, fenceAreaNo) && !StringUtils.isEmpty(record.getPoiNote());
        logger.info("地址自动审标记：autoAuditFlag：{}， merchantAreaNo:{}, fenceAreaNo:{}, newContact:{}", autoAuditFlag, areaNo, fenceAreaNo, JSON.toJSONString(record));

        //审核通过 poi不为空且相同
        if(autoAuditFlag){
            MQData mqData = new MQData();
            mqData.setType(MType.CONTACT_REGISTER_AUDIT.name());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id",record.getContactId());
            jsonObject.put("status",1);
            jsonObject.put("address",contact.getAddress());
            jsonObject.put("poiNote",contact.getPoiNote());
            mqData.setData(jsonObject);
            mqProducer.send(RocketMqMessageConstant.MALL_LIST, null, JSONObject.toJSONString(mqData));
        }
        return record.getContactId();
    }

    private Integer getStoreNo(boolean isPopMerchant, Contact contact){
        Integer storeNo = null;
        if(isPopMerchant) {
            logger.info("pop商城获取城配仓信息, contact:{}", JSON.toJSONString(contact));
            storeNo = fenceFacade.getPopStoreNo(contact.getCity(), contact.getArea(), contact.getPoiNote(), contact.getmId());
        } else {
            logger.info("鲜沐商城获取城配仓信息, contact:{}", JSON.toJSONString(contact));
            Fence fence = fenceService.getStoreNo(contact);
            if(Objects.isNull(fence)){
                throw new DefaultServiceException("不在配送范围内");
            }
            storeNo = fence.getStoreNo();
        }
        if(Objects.isNull(storeNo)){
            logger.error("contact :{} 。该地址不在运营服务范围内", JSON.toJSONString(contact));
            throw new DefaultServiceException("该地址不在运营服务范围内");
        }
        return storeNo;
    }

    /**
     * 创建门店时，初始化一个默认地址
     * @param merchant
     * @return
     */
    @Override
    public Contact addContactForRegister(Merchant merchant){
        if(StringUtils.isBlank(merchant.getProvince()) || StringUtils.isBlank(merchant.getCity())) {
            logger.warn("省市区地址为空，暂不初始化地址。merchant:{}", JSON.toJSONString(merchant));
            return null;
        }
        Contact contact = new Contact();
        contact.setContact(merchant.getMcontact());
        contact.setmId(merchant.getmId());
        contact.setPhone(merchant.getPhone());
        contact.setProvince(merchant.getProvince());
        contact.setCity(merchant.getCity());
        contact.setArea(merchant.getArea());
        contact.setHouseNumber(merchant.getHouseNumber());
        contact.setAddress(merchant.getAddress());
        contact.setStatus(1);
        contact.setIsDefault(1);
        contact.setPoiNote(merchant.getPoiNote());
        contact.setAddressCompletionFlag(merchant.getAddressCompletionFlag());

        // 校验城配仓
        this.getStoreNo(merchant.isPopMerchant(), contact);
        contactMapper.insertSelective(contact);
        return contact;
    }


    /**
     * 预注册激活
     * @param merchant
     */
    public void addContactForPreRegister(Merchant merchant){
        Area area = areaMapper.selectByAreaNo(merchant.getAreaNo());
        //新增contact 联系人信息 审核通过
        Contact contact = new Contact();
        contact.setContact(merchant.getMcontact());
        contact.setmId(merchant.getmId());
        contact.setPhone(merchant.getPhone());
        contact.setProvince(merchant.getProvince());
        contact.setCity(merchant.getCity());
        contact.setArea(merchant.getArea());
        contact.setHouseNumber(merchant.getHouseNumber());
        //修改注册地址
        contact.setAddress(merchant.getAddress());
        contact.setStatus(1);
        contact.setIsDefault(1);
        contact.setPoiNote(merchant.getPoiNote());
        //上海仓的处理
        Fence fence = fenceService.getStoreNo(contact);
        if(Objects.isNull(fence)){
            throw new DefaultServiceException("不在配送范围内");
        }
        //计算距离
        WarehouseLogisticsCenter logisticsCenter = logisticsService.selectByStoreNo(fence.getStoreNo());
        BigDecimal distance = GaoDeUtil.calculDistance(logisticsCenter.getPoiNote(), merchant.getPoiNote(), 2);
        contact.setDistance(distance);
        contact.setStoreNo(fence.getStoreNo());
        contactMapper.insertSelective(contact);
    }

    @Override
    public AjaxResult getContact(Integer status, Integer defaultFlag, Long contactId) {
        //是否大客户
        boolean isMajorCustomer = RequestHolder.isMajor();
        Long mId = RequestHolder.getMId();

        //是否值获取默认地址信息--用于首页页面
        if (Objects.nonNull(defaultFlag) && Objects.equals(defaultFlag, CommonStatus.YES.getCode())) {
            Contact contact = contactMapper.selectIsDefaultByMid(mId);
            if (Objects.isNull(contact)) {
                return AjaxResult.getError(Constants.NO_CONTACT,"不存在收货地址,添加收货地址后进行操作");
            }
            List<Contact> contacts = Collections.singletonList(contact);
            getContactDetail(CommonStatus.YES.getCode(), isMajorCustomer, mId, contacts);
            return AjaxResult.getOK(contacts);
        } else if (Objects.nonNull(contactId)) {
            //根据地址ID获取地址信息--用于下单页面
            Contact contact = contactMapper.selectByPrimaryKey(contactId);
            if (Objects.isNull(contact)) {
                return AjaxResult.getError(Constants.NO_CONTACT,"不存在收货地址,添加收货地址后进行操作");
            }
            List<Contact> contacts = Collections.singletonList(contact);
            getContactDetail(CommonStatus.YES.getCode(), isMajorCustomer, mId, contacts);
            return AjaxResult.getOK(contacts);
        }

        //根据状态获取所有地址信息--用于地址管理页面
        List<Contact> contacts = contactMapper.selectByMid(mId, status);
        getContactDetail(CommonStatus.NO.getCode(), isMajorCustomer, mId, contacts);
        return AjaxResult.getOK(contacts);
    }

    @Override
    public AjaxResult getDeliveryTime(Long contactId) {
        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        /*Integer storeNo = contact.getStoreNo();
        LocalTime closeTime = logisticsService.selectCloseTime(storeNo);*/

        FenceCloseTimeReq req = new FenceCloseTimeReq();
        req.setSource(DistOrderSourceEnum.getSourceEnum(RequestHolder.getBusinessLine()));
        req.setContactId(contact.getContactId());
        req.setArea(contact.getArea());
        req.setCity(contact.getCity());
        LocalTime closeTime = wncDeliveryFenceQueryFacade.queryCloseTime(req);
        return AjaxResult.getOK(closeTime);
    }

    @Override
    public Contact getDefaultContact(Long mid, Integer status) {
        List<Contact> contacts = contactMapper.selectByMidOrderByDefault(mid, status);
        Contact contact = CollectionUtils.isEmpty(contacts) ? null : contacts.get(NumberUtils.INTEGER_ZERO);
        return contact;
    }

    @Override
    public Contact selectByPrimaryKey(Long contactId){
        return contactMapper.selectByPrimaryKey(contactId);
    }

    /**
     * 获取商户默认地址，可能为空，本方法不会抛异常（未注册的mid没有contact）。
     * @param mId 商户id
     * @return  默认地址
     */
    @Override
    @InMemoryCache(expiryTimeInSeconds = 120)
    @Nullable
    public Contact getMerchantDefaultContact(Long mId) {
        // 获取默认地址
        Contact contact = contactMapper.selectIsDefaultByMid(mId);
        if (Objects.isNull(contact)) {
            logger.error("不存在收货地址, mid:{}", mId);
            return null;
        }

        //获取地址详细信息
        List<Contact> contacts = Collections.singletonList(contact);
        getContactDetail(CommonStatus.YES.getCode(), RequestHolder.isMajor(), mId, contacts);
        return contacts.get(0);
    }

    /**
     * 获取商户默认地址，可能为空，本方法不会抛异常（未注册的mid没有contact）。
     * 本方法就是单纯的将 contactMapper.selectIsDefaultByMid(mId) 方法的结果缓存起来（内存缓存）
     * @param mId 商户id
     * @return  默认地址
     */
    @Override
    @InMemoryCache(expiryTimeInSeconds = 120)
    @Nullable
    public Contact getMerchantDefaultContactCache(Long mId) {
        // 获取默认地址
        Contact contact = contactMapper.selectIsDefaultByMid(mId);
        if (Objects.isNull(contact)) {
            logger.error("未找到mid的默认收货地址:{}", mId);
            return null;
        }
        return contact;
    }

    @Override
    public List<Contact> queryMerchantContactByOrderTime(Long mId, LocalDateTime startOrderTime, LocalDateTime endOrderTime) {
        if (null == mId || null == startOrderTime || null == endOrderTime){
            throw new ParamsException("查询历史订单地址失败，参数为空");
        }
        if (startOrderTime.isAfter(endOrderTime)){
            throw new ParamsException("下单开始时间不能在结束时间之后");
        }
        // startOrderTime 和 endOrderTime 不能超过一年
        if (startOrderTime.plusYears(1L).isBefore(endOrderTime)){
            throw new ParamsException("查询时间不能超过一年");
        }
        List<Long> contactIdList = ordersMapper.selectContactIdListByOrderTimeAndMId(mId, startOrderTime, endOrderTime);
        if (CollectionUtils.isEmpty(contactIdList)){
            return Collections.emptyList();
        }
        return contactMapper.selectByContactIdIn(contactIdList);
    }

    private void addOperLog(MerchantSubject merchantSubject, String text, Integer operate, Long conatctId){
        ContactOperateLog contactOperateLog = new ContactOperateLog();
        contactOperateLog.setMId(merchantSubject.getMerchantId());
        contactOperateLog.setContactId(conatctId);
        contactOperateLog.setCreateTime(LocalDateTime.now());
        contactOperateLog.setUpdateTime(LocalDateTime.now());
        contactOperateLog.setContext(text);
        contactOperateLog.setOperateType(operate);
        contactOperateLog.setOperateName(merchantSubject.getAccount().getContact());
        contactOperateLog.setOperateSource(MALL_SOURCE);
        contactOperateLogMapper.insertSelective(contactOperateLog);
    }


    private AjaxResult checkContactRemark(Contact contact){
        if (contact == null){
            return null;
        }
        Pattern pattern = Pattern.compile(EMOJI_REG);
        if (StringUtils.isNotBlank(contact.getAddressRemark())){
            ContactAddressRemark contactAddressRemark = JSONUtil.toBean(contact.getAddressRemark(), ContactAddressRemark.class);
            if (contactAddressRemark !=null && !StringUtils.isEmpty(contactAddressRemark.getCustomRemark())){
                Matcher matcher = pattern.matcher(contactAddressRemark.getCustomRemark());
                if (matcher.find()) {
                    return AjaxResult.getError(ResultConstant.DEFAULT_FAILED, "地址备注不允许输入特殊字符");
                }
            }
        }
        ContactAddressRemark contactAddressRemark = contact.getContactAddressRemark();
        if (contactAddressRemark == null){
           return  null;
       }
        String customRemark = contactAddressRemark.getCustomRemark();
        if (StringUtils.isEmpty(customRemark)){
            return null;
        }
        // 判断是否包含表情字符
        Matcher matcher = pattern.matcher(customRemark);
        if (matcher.find()) {
            return AjaxResult.getError(ResultConstant.DEFAULT_FAILED, "地址备注不允许输入特殊字符");
        }
        return null;
    }

    /**
     * @description: 获取地址的详细信息 -- 是否加单 截单 下一个配送日期等
     * @author: lzh
     * @date: 2024/1/4 11:03
     * @param: [status, major, mId, contacts]
     * @return: void
     **/
    private void getContactDetail(Integer status, boolean major, Long mId, List<Contact> contacts) {
        // 先查询城配仓的履约类型，0：城配履约，1：快递履约
        Map<Integer, Integer> fulfillmentTypeMap = wncStoreQueryFacade.getFulfillmentTypeByStoreNoList(
                contacts.stream().map(Contact::getStoreNo).filter(Objects::nonNull).collect(Collectors.toList())
        );

        for (Contact contact : contacts) {
            FenceVO fenceVO = fenceService.selectFenceByCityArea(contact.getArea(), contact.getCity());
            contact.setEffectFlag(Objects.isNull(fenceVO) ? NumberUtils.INTEGER_ZERO : NumberUtils.INTEGER_ONE);

            //根据围栏区域编号获取该区域是否可以加单
            if (Objects.isNull(fenceVO) || Objects.isNull(fenceVO.getAreaNo())) {
                continue;
            }
            Area area = areaMapper.selectByAreaNo(fenceVO.getAreaNo());

            //大客户不允许加单
            if (major) {
                contact.setSupportAddOrder(CommonStatus.YES.getCode());
            } else if (Objects.isNull(area)) {
                //为空获取默认的区域编号
                area = areaMapper.selectByMId(mId);
                contact.setSupportAddOrder(area.getSupportAddOrder());
            } else {
                contact.setSupportAddOrder(area.getSupportAddOrder());
            }

            //只有首页或者下单的时候才获取配送规则
            if (Objects.equals(status, CommonStatus.YES.getCode())) {
                //无效地址不获取配送规则
                DeliveryRuleDTO ruleListByCache = deliveryRuleQueryFacade.getDeliveryRuleListByCache(LocalDateTime.now(), mId, contact.getContactId(),
                        null, null, DistOrderSourceEnum.getSourceEnum(RequestHolder.getBusinessLine()));
                if (ruleListByCache != null) {
                    contact.setDeliveryFrequentNew(ruleListByCache.getDeliveryTimes());
                    contact.setCutOffTime(ruleListByCache.getDeliveryCloseTime() == null ? null :
                            DateUtils.localDateTimeToLong(ruleListByCache.getDeliveryCloseTime().minusDays(1)));
                } else {
                    contact.setDeliveryFrequentNew(Collections.emptyList());
                }

                //获取截单时间
                /*LocalDateTime cutOffTime = deliveryService.getCutOffTime(area, mId, contact);
                if (cutOffTime != null) {
                    contact.setCutOffTime(DateUtils.localDateTimeToLong(cutOffTime));
                }*/

                //获取预计送达时间
                DeliveryAlertReq deliveryAlertReq = new DeliveryAlertReq();
                deliveryAlertReq.setContactId(contact.getContactId());
                deliveryAlertReq.setCity(contact.getCity());
                deliveryAlertReq.setArea(contact.getArea());
                deliveryAlertReq.setStoreNo(contact.getStoreNo());
                deliveryAlertReq.setMerchantId(contact.getmId().toString());
                if (major) {
                    deliveryAlertReq.setAdminId(RequestHolder.getAdminId() == null ? null : RequestHolder.getAdminId().toString());
                }
                DeliveryAlertRes deliveryAlertRes = wncDeliveryAlertQueryFacade.queryDeliveryAlert(deliveryAlertReq);
                contact.setLastDeliveryTime(deliveryAlertRes.getLastDeliveryTime());
            }

            // 城配仓的履约类型，0：城配履约，1：快递履约
            contact.setFulfillmentType(fulfillmentTypeMap.get(contact.getStoreNo()));
            contact.initAddrRemark();
        }

        boolean popMerchant = RequestHolder.isPopMerchant();
        if(popMerchant) {
            // pop商城围栏默认开启
            contacts.forEach(contact -> contact.setFenceStatus(FenceStatusEnum.OPEN.getCode()));
        } else {
            //批量获取围栏信息
            ContactBelongFenceReq contactBelongFenceReq = new ContactBelongFenceReq();
            contactBelongFenceReq.setContacts(contacts);
            contactBelongFenceReq.setFenceChannelType(FenceChannelTypeEnum.getChannelCode());
            Map<Long, ContactBelongFenceRes> map = wncDeliveryFenceQueryFacade.batchQueryContactAddressBelongFence(contactBelongFenceReq);
            if (!CollectionUtils.isEmpty(map)) {
                contacts.stream().forEach(contact -> {
                    ContactBelongFenceRes contactBelongFenceRes = map.get(contact.getContactId());
                    if (Objects.nonNull(contactBelongFenceRes)) {
                        contact.setFenceStatus(contactBelongFenceRes.getStatus());
                    } else {
                        contact.setFenceStatus(FenceStatusEnum.OPEN.getCode());
                    }
                });
            } else {
                //没有返回就默认为开放状态
                contacts.stream().forEach(contact -> contact.setFenceStatus(FenceStatusEnum.OPEN.getCode()));
            }
        }
    }
}
