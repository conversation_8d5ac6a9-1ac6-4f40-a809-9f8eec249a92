package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.constant.MarketConstant;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.RegConstant;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.InventoryAdjustPriceTypeEnum;
import net.summerfarm.enums.MajorPriceMallShowEnum;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.common.redis.KeyConstant;
import net.summerfarm.mall.common.util.*;
import net.summerfarm.mall.constant.Constants;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.enums.TimingRuleEnum.type;
import net.summerfarm.mall.enums.market.timing.DeliveryStartTypeEnum;
import net.summerfarm.mall.enums.order.AddOnEnum;
import net.summerfarm.mall.facade.market.ProductsSaleRuleFacade;
import net.summerfarm.mall.facade.market.dto.ProductsSaleRuleDto;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.inventory.MarketItemDTO;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDTO;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDetailDTO;
import net.summerfarm.mall.model.dto.market.activity.TimingSkuActivityDTO;
import net.summerfarm.mall.model.dto.product.MarketProviderQueryDTO;
import net.summerfarm.mall.model.dto.product.ProductInventoryInfoDTO;
import net.summerfarm.mall.model.input.TimingDeliveryInput;
import net.summerfarm.mall.model.input.product.CommodityInventoryInput;
import net.summerfarm.mall.model.input.product.HotListSearchInput;
import net.summerfarm.mall.model.input.product.ProductSearchInput;
import net.summerfarm.mall.model.input.product.ProductSpecificationQueryInput;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.model.vo.product.CommodityInventoryVO;
import net.summerfarm.mall.model.vo.product.HomeProductQueryVo;
import net.summerfarm.mall.repository.CycleInventoryCostRepository;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.cache.AreaSkuCache;
import net.summerfarm.mall.service.facade.MarketItemFacade;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.converter.MarketItemConverter;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryReq;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryRes;
import net.summerfarm.warehouse.mapper.WarehouseInventoryMappingMapper;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.xianmu.common.cache.GenericInMemoryCacheProcessor;
import net.xianmu.common.cache.InMemoryCache;
import net.xianmu.common.cache.NamedFunction;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.commons.collections.MapUtils;
import org.elasticsearch.common.util.set.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import net.summerfarm.mall.enums.MajorPriceTypeEnum;

/**
 * @Package: net.summerfarm.service.impl
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/10/8
 */
@Service
@Slf4j
public class InventoryServiceImpl implements InventoryService {

    private static final Logger logger = LoggerFactory.getLogger(InventoryServiceImpl.class);

    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private ProductsPropertyValueService productsPropertyValueService;
    @Resource
    private CycleInventoryCostRepository cycleInventoryCostRepository;
    @Resource
    private TimingRuleMapper timingRuleMapper;
    @Resource
    private TimingRuleService timingRuleService;
    @Resource
    private MajorPriceMapper majorPriceMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private MajorRebateMapper majorRebateMapper;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private ArrivalNoticeMapper arrivalNoticeMapper;
    @Resource
    private MerchantSubAccountMapper merchantSubAccountMapper;
    @Resource
    private MajorCategoryMapper majorCategoryMapper;
    @Resource
    private TemporaryActivityConfigMapper temporaryActivityConfigMapper;
    @Resource
    private PrepayInventoryService prepayInventoryService;
    @Resource
    private WarehouseInventoryMappingMapper inventoryMappingMapper;
    @Resource
    private SalePriceTransService salePriceTransService;
    @Resource
    private MerchantService merchantService;
    @Resource
    private PriceStrategyService priceStrategyService;
    @Resource
    private DeliveryService deliveryService;
    @Resource
    private AreaService areaService;
    @Resource
    private ActivityService activityService;
    @Resource
    private MerchantPoolService merchantPoolService;
    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Resource
    private FenceService fenceService;
    @Resource
    private CouponBlackAndWhiteMapper couponBlackAndWhiteMapper;
    @Lazy
    @Resource
    private CategoryService categoryService;
    @Resource
    private ConfigService configService;
    @Resource
    private CouponMapper couponMapper;
    @Resource
    private BrandMapper brandMapper;
    @Resource
    private ContactService contactService;

    @Resource
    private MarketItemFacade marketItemFacade;
    @Resource
    private WmsAreaStoreFacade wmsAreaStoreFacade;
    @Resource
    private MajorPriceService majorPriceService;
    @Resource
    private MarketPriceControlProductsService marketPriceControlProductsService;
    @Resource
    private AreaSkuCache areaSkuCache;
    @Resource
    private MarketRuleService marketRuleService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private FrequentSkuPoolService frequentSkuPoolService;

    private static final String MAJOR_CUSTOMER_SIZE_LABEL = "大客户";

    private Map<String, AreaStoreQueryRes> getAreaStoreMap(Long mId, List<String> skuList) {
        Map<String, AreaStoreQueryRes> areaStoreMap = new HashMap<>();
        if (mId != null) {
            Contact contact = contactService.getMerchantDefaultContactCache(mId);
            AreaStoreQueryReq queryReq = new AreaStoreQueryReq();
            queryReq.setContactId(Optional.ofNullable(contact).map(Contact::getContactId).orElse(null));
            queryReq.setSkuCodeList(skuList);
            queryReq.setMId(mId);
            queryReq.setSource(DistOrderSourceEnum.getDistOrderSource(RequestHolder.getBusinessLine()));
            areaStoreMap = wmsAreaStoreFacade.getInfo(queryReq);
        }
        return areaStoreMap;
    }

    /**
     * 释放卡劵-可以给安佳类产品使用
     */
    public static final String releaseCard = "RELEASE_CARD";

    /**
     * 屏蔽sku
     */
    public static final String aolLimitPriceSku = "AOL_LIMIT_PRICE_SKU";


    @Resource
    private FrontCategoryToCategoryMapper frontCategoryToCategoryMapper;

    @Resource
    private ProductsSaleRuleFacade productsSaleRuleFacade;

    private static final Pattern PATTERN = Pattern.compile("\\d{1,5}\\.{0,1}\\d{0,2}");

    private static final Pattern PATTERN_1 = Pattern.compile("[a-zA-Z\\u4e00-\\u9fa5]{1,2}");

    private static final Pattern PATTERN_2 = Pattern.compile("[\\u4e00-\\u9fa5]");

    @Override
    public PageInfo<ProductInfoVO> selectHomeProductVoV2(int pageIndex, int pageSize, ProductSearchInput input) {
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        HomeProductQueryVo homeProductQueryVo = buildHomeProductVo(merchantSubject, input);
        if (homeProductQueryVo == null) {
            return new PageInfo<>(new ArrayList<>());
        }
        // 代下单查询逻辑
        if (!StringUtils.isEmpty(input.getQueryStr())) {
            log.info("代下单查询逻辑,似乎没有迁移到ES...{}", input);
            PageInfo<ProductInfoVO> pageInfo = getHelpOrderProductList(pageIndex, pageSize, input.getMId(), input.getQueryStr(), null, null, null, null, null);
            return pageInfo;
        }
        PageVo pageVo = new PageVo(pageIndex, pageSize);
        PageInfo<ProductInfoVO> productInfoVOPageInfo = salePriceTransService.selectHomeProductVO3(homeProductQueryVo, pageVo);
        if (productInfoVOPageInfo == null) {
            return new PageInfo<>(new ArrayList<>());
        }
        //待优化 大客户价格展示不同
        merchantSubject = homeProductQueryVo.getMerchantSubject();
        newMajorPrice(productInfoVOPageInfo.getList(), merchantSubject);
        checkoutSku(productInfoVOPageInfo.getList(), merchantSubject);
        return productInfoVOPageInfo;
    }

    private HomeProductQueryVo buildHomeProductVo(MerchantSubject merchantSubject, ProductSearchInput input) {
        Integer show = 1;
        // mallShow：报价单门店可见该商品，0-是，1-否；代下单不限制
        Integer mallShow = 0;
        Long mId = input.getMId();

        //代下单  赋值构造
        if (mId != null) {
            show = null;
            mallShow = null;
            HashMap<String, Object> map = new HashMap<>();
            map.put("mId", mId);
            merchantSubject = new MerchantSubject();
            Merchant merchant = redisCacheUtil.getDataWithCache(Global.CACHE_MERCHANT_INFO + mId, 300, Merchant.class, () -> merchantMapper.selectOneByMid(mId));
            merchantSubject.setSize(merchant.getSize());
            merchantSubject.setDirect(merchant.getDirect());
            merchantSubject.setSkuShow(merchant.getSkuShow());
            merchantSubject.setAdminId(merchant.getAdminId());
            merchantSubject.setArea(new Area(merchant.getAreaNo()));
            merchantSubject.setMerchantId(mId);
            MerchantSubAccount mangeAccount = redisCacheUtil.getDataWithCache(Global.CACHE_MERCHANT_SUB_ACCOUNT + mId, 300, MerchantSubAccount.class, () -> merchantSubAccountMapper.selectMangerByMId(mId));
            merchantSubject.setAccount(mangeAccount);
            //dubbo接口无法获取request 对象
            try {
                RequestHolder.getRequest().setAttribute(Global.MERCHANT_SUBJECT, merchantSubject);
            } catch (Exception e) {
                logger.warn("非http请求或者无法获取 request");
            }
        }

        if (merchantSubject == null) {
            merchantSubject = new MerchantSubject();
            Area area = areaService.selectAreaWithCache((RequestHolder.getMerchantArea().getAreaNo()));
            merchantSubject.setArea(area);
        }

        Integer adminId = null;
        List<String> skuList = input.getSkus();
        Set<String> notShowSkuList = null;
        if ("大客户".equals(merchantSubject.getSize())) {
            adminId = merchantSubject.getAdminId();
            //查询生效的报价单sku
            skuList = majorPriceMapper.selectSKu(adminId, merchantSubject.getArea().getAreaNo(), merchantSubject.getDirect(), mallShow);

            if (Objects.equals(merchantSubject.getSkuShow(), 1)) {
                //获取类目sku信息,已排除sku报价单展示的spu和不展示的sku
                List<String> majorSkuList = getBigMerchantMajorCategory(adminId, merchantSubject.getArea().getAreaNo(), merchantSubject.getDirect(), mallShow);
                skuList.addAll(majorSkuList);
                skuList = skuList.stream().distinct().collect(Collectors.toList());

                if (CollectionUtils.isEmpty(skuList)) {
                    return null;
                }
            } else if (Objects.equals(merchantSubject.getSkuShow(), 2)) {
//                查询需要隐藏的sku
                notShowSkuList = mallShowHideSkuList(adminId, merchantSubject.getArea().getAreaNo(), merchantSubject.getDirect());
            }
        }
        if (CollectionUtils.isEmpty(skuList)) {
            skuList = null;
        }

        if (CollectionUtils.isEmpty(notShowSkuList)) {
            notShowSkuList = null;
        }

        HomeProductQueryVo homeProductQueryVo = new HomeProductQueryVo();
        homeProductQueryVo.setMerchantSubject(merchantSubject);
        homeProductQueryVo.setSkuList(skuList);
        homeProductQueryVo.setSkuQueryList(input.getSkuQueryList());
        homeProductQueryVo.setFrontCategoryId(input.getFrontCategoryId());
        homeProductQueryVo.setQueryStr(input.getQueryStr());
        homeProductQueryVo.setShow(show);
        homeProductQueryVo.setPdName(input.getPdName());
        homeProductQueryVo.setAdminId(adminId);
        homeProductQueryVo.setDirect(merchantSubject.getDirect());
        homeProductQueryVo.setSubTypeList(AddOnEnum.getSubTypeList(input.getAddOn()));
        homeProductQueryVo.setNotShowSkuList(notShowSkuList);

        // 支持传入areaNo：
        homeProductQueryVo.setAreaNo(input.getAreaNo());

        // 支持传入deliveryTime
        homeProductQueryVo.setDeliveryTime(input.getDeliveryTime());

        return homeProductQueryVo;
    }
    @Override
    public AjaxResult<List<ProductInfoVO>> selectProductInfo(long pdId) {
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        Integer areaNo = RequestHolder.getMerchantAreaNo();
        //普通客户
        List<ProductInfoVO> homeProductVOList = Collections.emptyList();

        //关键属性值
        Map<Long, List<ProductsPropertyValueVO>> keyPropertyMap = productsPropertyValueService.getProductsPropertyValueCache(Collections.singletonList(pdId));

        //待优化 大客户价格展示不同
        if (merchantSubject != null && RequestHolder.isMajor()) {
            List<String> skuList = null;
            Integer adminId = merchantSubject.getAdminId();
            logger.info("查询大客户生效中的报价单商品, adminId:{}, areaNo:{}, 合作模式(1:账期,2:现结):{}", adminId, areaNo, merchantSubject.getDirect());
            //获取生效的sku报价单
            skuList = majorPriceMapper.selectSKu(adminId, areaNo, merchantSubject.getDirect(), MajorPriceMallShowEnum.YES.ordinal());
            if (merchantSubject.getSkuShow().intValue() == 1) {
                //定量
                skuList = getSKuByPdIdMsg(pdId, adminId, areaNo, merchantSubject.getDirect());
            }
            logger.info("大客户生效中的报价单SKU列表:{}", skuList);
            //直营
            if (merchantSubject.getSkuShow() == 1 && CollectionUtils.isEmpty(skuList)) {
                return AjaxResult.getOK(Collections.emptyList());
            }
            if (CollectionUtils.isEmpty(skuList)) {
                skuList = null;
            }

            homeProductVOList = salePriceTransService.selectProductInfoByPdIdV2(pdId, areaNo, merchantSubject.getAdminId(), skuList, merchantSubject.getSkuShow(), merchantSubject.getDirect());

            newMajorPrice(homeProductVOList, merchantSubject);
        } else {
            homeProductVOList = salePriceTransService.selectProductInfoByPdIdV2(pdId, areaNo, null, null, null, null);
            homeProductVOList.forEach(productInfoVO -> {
                //获取sku加购上限，为空则不限制
                Integer purchaseCeiling = this.getPurchaseCeiling(productInfoVO.getSku(), productInfoVO.getQuantity(), RequestHolder.getMId());
                if (null != purchaseCeiling) {
                    productInfoVO.setPurchaseCeiling(purchaseCeiling);
                }
            });
        }
        Long mId = Optional.ofNullable(merchantSubject).map(MerchantSubject::getMerchantId).orElse(0L);
        Contact contact = contactService.getMerchantDefaultContactCache(mId);
        //到货提醒， 登录才有
        if (merchantSubject != null) {
            handleArrivalNotice (homeProductVOList, mId, Optional.ofNullable (contact).map (Contact::getStoreNo).orElse (null));
        }
        handleAvgPriceAndKeyValueList(homeProductVOList, keyPropertyMap);
        //不是大客户 并且登陆
        if (merchantSubject != null && (!RequestHolder.isMajor())) {
            handleAreaMarketRule (homeProductVOList, merchantSubject);
        }
        handleSkuTimingRules(homeProductVOList, merchantSubject);

        // 获取pdId的起购信息
        Map<Long, ProductsSaleRuleDto> ruleDtoMap = productsSaleRuleFacade.queryProductsSaleRuleMapByPdIds(Collections.singletonList((long) pdId));
        ProductsSaleRuleDto saleRuleDto = ruleDtoMap.get(pdId);
        if (null != saleRuleDto) {
            homeProductVOList.forEach(productInfoVO -> {
                productInfoVO.setSpuBaseSaleQuantity(saleRuleDto.getBaseSaleQuantity());
            });
        }

        //获取全部控价品信息
        Map<String, MarketPriceControlProducts> controlProductsMap = marketPriceControlProductsService.selectAllControlProductsByCache();
        homeProductVOList.forEach(productInfoVO -> {
            if (CollectionUtils.isEmpty(controlProductsMap) || !controlProductsMap.containsKey(productInfoVO.getSku())) {
                return;
            }
            MarketPriceControlProducts marketPriceControlProducts = controlProductsMap.get(productInfoVO.getSku());
            if (marketPriceControlProducts != null) {
                productInfoVO.setPriceHide(marketPriceControlProducts.getPriceHide());
                productInfoVO.setFacePriceHide(marketPriceControlProducts.getFacePriceHide());
                productInfoVO.setPriceControlLine(MarketControlPriceHideEnum.HIDE.getCode().equals(marketPriceControlProducts.getFacePriceHide()) ?
                        marketPriceControlProducts.getPriceControlLine() : null);
            }
        });

        // 查询是否在常购清单中
        Map<String, Boolean> skuInFrequentSkuPoolInfoMap = frequentSkuPoolService.checkSkuInFrequentSkuPool(mId, homeProductVOList.stream().map(ProductInfoVO::getSku).collect(Collectors.toList()));
        homeProductVOList.forEach(productInfoVO -> {
            productInfoVO.setInMerchantFrequentSkuPool(skuInFrequentSkuPoolInfoMap.getOrDefault(productInfoVO.getSku(), false));
        });

        return AjaxResult.getOK(homeProductVOList);
    }

    private void fillTimingRule(TimingRule timingRule, ProductInfoVO productInfoVO, LocalDate deliveryDate) {
        productInfoVO.setTimingRuleId(timingRule.getId());
        productInfoVO.setThreshold(timingRule.getThreshold());
        productInfoVO.setDeliveryPeriod(timingRule.getDeliveryPeriod());
        productInfoVO.setDeliveryUnit(timingRule.getDeliveryUnit());

        if(deliveryDate!=null) {
            productInfoVO.setDeliveryStart (DateUtils.localDate2Date (deliveryDate));
        }
        //配送截止时间最晚是下单后的第90天
        LocalDate deliveryEnd = LocalDate.now().plusDays(timingRule.getDeliveryPeriod());

        //当为指定日期的时候 最晚配送时间 = 指定日期 + 配送周期
        if (Objects.equals(timingRule.getDeliveryStartType(), DeliveryStartTypeEnum.APPOINT.getCode()) && deliveryDate != null) {
            deliveryEnd = deliveryDate.plusDays(timingRule.getDeliveryPeriod());
        }

        productInfoVO.setDeliveryEnd(DateUtils.localDate2Date(deliveryEnd));

        productInfoVO.setDeliveryUpperLimit(timingRule.getDeliveryUpperLimit());
        productInfoVO.setAutoCalculate(timingRule.getAutoCalculate());
    }

    @Override
    public AjaxResult selectByCouponId(int pageIndex, int pageSize, String pdName, String categoryIds, String skus, Long couponId) {
        //获取券信息, 获取相关配置,
        List<ProductInfoVO> homeProductVOList = null;
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();

        Integer adminId = null;
        //大客户报价单sku
        List<String> skuList = null;
        //用户查询sku
        List<String> allSkuList = null;
        List<Integer> categorys = null;

        List<String> querySku = null;
        //获取登录信息为空
        if (merchantSubject == null) {
            throw new DefaultServiceException(0, ResultConstant.LOGIN_FIRST);
        }
        if ("大客户".equals(merchantSubject.getSize())) {
            adminId = merchantSubject.getAdminId();
            skuList = majorPriceMapper.selectSKu(adminId, merchantSubject.getArea().getAreaNo(), merchantSubject.getDirect(), MajorPriceMallShowEnum.YES.ordinal());
            if (CollectionUtils.isEmpty(skuList) && merchantSubject.getSkuShow() == 1) {
                return AjaxResult.getOK(PageInfoHelper.createPageInfo(homeProductVOList));
            }
        }
        /*if (!StringUtils.isEmpty(categoryIds)) {
            categorys = new ArrayList<>();
            String[] categoryList = categoryIds.split(",");
            for (String s : categoryList) {
                categorys.add(Integer.valueOf(s));
            }

            //获取子级类目ID
            categorys = categoryService.getCacheSublevelCategoryIds(couponId, categorys);
        }
        if (!StringUtils.isEmpty(skus)) {
            allSkuList = new ArrayList<>();
            List<String> sku = Arrays.asList(skus.split(","));
            allSkuList.addAll(sku);
        }*/

        //过滤黑名单商品数据
        List<String> blackSkus = new ArrayList<>();
        if (Objects.nonNull(couponId)) {
            CouponBlackAndWhite couponBlackAndWhite = new CouponBlackAndWhite();
            couponBlackAndWhite.setCouponId(couponId);
            //couponBlackAndWhite.setType(BlackAndWhiteTypeEnum.BLACK.getCode());
            List<CouponBlackAndWhite> couponBlackAndWhites = couponBlackAndWhiteMapper.getAllByEntity(couponBlackAndWhite);
            Map<Long, List<CouponBlackAndWhite>> blackMap = null;
            Map<Long, List<CouponBlackAndWhite>> whiteMap = null;
            if (!CollectionUtils.isEmpty(couponBlackAndWhites)) {
                Map<Integer, List<CouponBlackAndWhite>> collectCouponBlackAndWhite = couponBlackAndWhites.stream()
                        .collect(Collectors.groupingBy(CouponBlackAndWhite::getType));
                if (!CollectionUtils.isEmpty(collectCouponBlackAndWhite.get(BlackAndWhiteTypeEnum.BLACK.getCode()))) {
                    blackMap = collectCouponBlackAndWhite.get(BlackAndWhiteTypeEnum.BLACK.getCode()).stream()
                            .collect(Collectors.groupingBy(CouponBlackAndWhite::getCouponId));
                }
                if (!CollectionUtils.isEmpty(collectCouponBlackAndWhite.get(BlackAndWhiteTypeEnum.WHITE.getCode()))) {
                    whiteMap = collectCouponBlackAndWhite.get(BlackAndWhiteTypeEnum.WHITE.getCode()).stream()
                            .collect(Collectors.groupingBy(CouponBlackAndWhite::getCouponId));
                }
            }

            if (!CollectionUtils.isEmpty(blackMap) && !CollectionUtils.isEmpty(blackMap.get(couponId))) {
                blackSkus.addAll(blackMap.get(couponId).stream().map(CouponBlackAndWhite::getSku).collect(Collectors.toList()));
            }

            Coupon coupon = couponMapper.selectByPrimaryKey(couponId.intValue());

            //运费劵不做过滤
            if (!Objects.equals(coupon.getAgioType(), CouponEnum.CouponTypeEnum.DELIVERY.getCode())) {

                //优惠券释放：售后补偿券、销售囤货券、销售现货券、销售品类券这四类分组的优惠券支持安佳可用
                List<String> releaseCardList = configService.getValuesByLocalCache(releaseCard);
                List<Integer> releaseCards = releaseCardList.stream().map(e -> Integer.valueOf(e)).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(releaseCards) || !releaseCards.contains(coupon.getGrouping())) {

                    //将安佳相关品添加到黑名单
                    List<String> limitPriceSku = configService.getValuesWithCache(aolLimitPriceSku, 1800L);
                    if (!CollectionUtils.isEmpty(limitPriceSku)) {
                        blackSkus.addAll(limitPriceSku);
                    }
                }
            }

            //搜索指定商品
            if (!StringUtils.isEmpty(coupon.getSku()) && !Objects.equals("{}", coupon.getSku())) {
                allSkuList = new ArrayList<>();
                Set<String> keySet = ((Map<String, String>) JSON.parse(coupon.getSku())).keySet();
                allSkuList.addAll(keySet);
            } else if (!CollectionUtils.isEmpty(whiteMap) && !CollectionUtils.isEmpty(whiteMap.get(couponId))) {
                allSkuList = new ArrayList<>();
                allSkuList.addAll(whiteMap.get(couponId).stream().map(CouponBlackAndWhite::getSku).collect(Collectors.toList()));
            }

            //搜索指定类目
            if (!StringUtils.isEmpty(coupon.getCategoryId()) && !Objects.equals("{}", coupon.getCategoryId())) {
                Set<String> cateStr = ((Map<String, String>) JSON.parse(coupon.getCategoryId())).keySet();
                List<Integer> categoryIdList = cateStr.stream().map(Integer::valueOf).collect(Collectors.toList());

                //获取子级类目ID
                categorys = categoryService.getCacheSublevelCategoryIds(couponId, categoryIdList);
            }
        }

        //大客户定量展示要筛选sku
        if ("大客户".equals(merchantSubject.getSize()) && merchantSubject.getSkuShow() == 1 && !CollectionUtils.isEmpty(allSkuList)) {
            querySku = new ArrayList<>();
            for (String sku : allSkuList) {

                for (String s : skuList) {
                    if (Objects.equals(sku, s)) {
                        querySku.add(s);
                    }
                }
            }
            //没有交集返回空
            if (CollectionUtils.isEmpty(querySku)) {
                return AjaxResult.getOK(PageInfoHelper.createPageInfo(homeProductVOList));
            }
            //大客户全量，要从报价单和非大客户专享的商品中筛选, 定量展示没有sku筛选条件
        } else if (("大客户".equals(merchantSubject.getSize()) && merchantSubject.getSkuShow() == 2) || (CollectionUtils.isEmpty(allSkuList) && !CollectionUtils.isEmpty(skuList))) {
            querySku = skuList;

            //普通用户
        } else {
            querySku = allSkuList;
        }

        Long merchantId = RequestHolder.getNewMerchantId();
        boolean coreCustomers = merchantService.checkCoreCustomers(merchantId);
        PageVo pageVo = new PageVo(pageIndex, pageSize);
        PageInfo<ProductInfoVO> homeProductVOPage = salePriceTransService.selectCouponProductVOV3(categorys, pdName,
                adminId, querySku, coreCustomers, blackSkus, pageVo);
        List<ProductInfoVO> homeProductPageList = homeProductVOPage.getList();
        majorPrice(homeProductPageList, merchantSubject);
        checkoutSku(homeProductPageList, merchantSubject);

        return AjaxResult.getOK(homeProductVOPage);
    }

    @Override
    public void newMajorPrice(List<ProductInfoVO> homeProductVOList, MerchantSubject merchantSubject) {

        if (!CollectionUtils.isEmpty(homeProductVOList) && "大客户".equals(merchantSubject.getSize())) {

            List<String> skuList = homeProductVOList.stream().map(ProductInfoVO::getSku).collect(Collectors.toList());
            Map<String, MajorPrice> majorPriceMap = new HashMap<>();
            Map<String, BigDecimal> majorMallPriceMap = new HashMap<>();
            Area area = merchantSubject.getArea();

            Map<String, Integer> prepayAmountMap = prepayInventoryService.queryUsableMap(merchantSubject.getAdminId(), null);

            // 获取sku对应的报价单
            if(CollUtil.isNotEmpty(skuList) && area != null) {
                List<MajorPrice> majorPrices = majorPriceMapper.selectSkusMajorPriceList(merchantSubject.getAdminId(), merchantSubject.getDirect(), area.getAreaNo(), skuList);
                if(CollectionUtil.isNotEmpty (majorPrices)){
                    majorPriceMap = majorPrices.stream().collect(Collectors.toMap(MajorPrice::getSku, Function.identity(), (k1, k2) -> k2));
                    List<String> skus = majorPrices.stream ().filter (majorPrice -> MajorPriceTypeEnum.MALL_RELATED.contains (majorPrice.getPriceType ())).map (MajorPrice::getSku).collect (Collectors.toList ());
                    majorMallPriceMap = majorPriceService.getMajorMallPriceBatch (area.getAreaNo (), skus, merchantSubject.getAdminId (), RequestHolder.getMId (), false,majorPriceMap);
                }
            }

            for (ProductInfoVO infoVO : homeProductVOList) {
                infoVO.setLadderPrice(null);
                infoVO.setLadderPrices(null);
                if (infoVO.getPrice() != null) { // 先取报价单的价格
                    infoVO.setSalePrice(infoVO.getPrice());
                } else if (infoVO.getActivityOriginPrice() != null) {
                    infoVO.setSalePrice(infoVO.getActivityOriginPrice().doubleValue());
                }

                // 特殊处理大客户商城价
                MajorPrice majorPrice = majorPriceMap.get(infoVO.getSku());
                log.info("开始处理大客户商城价：majorPrice：{}", JSON.toJSONString(majorPrice));
                if (majorPrice != null && MajorPriceTypeEnum.MALL_RELATED.contains (majorPrice.getPriceType ())) {
                    BigDecimal mallPrice = majorMallPriceMap.get (infoVO.getSku());
                    if(null != mallPrice) {
                        infoVO.setSalePrice(mallPrice.doubleValue());
                    }
                }
                //现结的有价格策略
                String sku = infoVO.getSku();

                //获取合同信息
                boolean isPrepaySku = false;
                if (!CollectionUtils.isEmpty(prepayAmountMap) && prepayAmountMap.get(sku) != null) {
                    infoVO.setPrePayAmount(prepayAmountMap.get(sku));
                    isPrepaySku = true;
                }
                //账期 预付 0
                if (isPrepaySku && merchantSubject.getDirect().intValue() == 1) {
                    infoVO.setSalePrice(0.0);
                }
            }
        }
    }


    public void majorPrice(List<ProductInfoVO> homeProductVOList, MerchantSubject merchantSubject) {

        if (!CollectionUtils.isEmpty(homeProductVOList) && "大客户".equals(merchantSubject.getSize())) {

            for (ProductInfoVO infoVO : homeProductVOList) {

                infoVO.setLadderPrice(null);
                infoVO.setLadderPrices(null);
                if (infoVO.getPrice() != null) { // 先取报价单的价格
                    infoVO.setSalePrice(infoVO.getPrice());
                } else if (infoVO.getActivityOriginPrice() != null) {
                    infoVO.setSalePrice(infoVO.getActivityOriginPrice().doubleValue());
                }
                //现结的有价格策略
                String sku = infoVO.getSku();

                //获取合同信息
                boolean isPrepaySku = false;
                Map<String, Integer> prepayAmountMap = prepayInventoryService.queryUsableMap(merchantSubject.getAdminId(), null);
                if (!CollectionUtils.isEmpty(prepayAmountMap) && prepayAmountMap.get(sku) != null) {
                    infoVO.setPrePayAmount(prepayAmountMap.get(sku));
                    isPrepaySku = true;
                }
                //账期 预付 0
                if (isPrepaySku && merchantSubject.getDirect().intValue() == 1) {
                    infoVO.setSalePrice(0.0);
                }
                if (merchantSubject.getDirect() == 2) {
                    //先判断 sku
                    MajorRebate select = new MajorRebate();
                    select.setSku(infoVO.getSku());
                    select.setAreaNo(RequestHolder.getMerchantAreaNo());
                    select.setAdminId(merchantSubject.getAdminId());
                    select.setCate(2);
                    MajorRebate majorRebate = majorRebateMapper.selectOne(select);
                    if (majorRebate == null) {
                        select.setCate(1);
                        //先结无返点 0
                        if (isPrepaySku) {
                            infoVO.setSalePrice(0.0);
                        }
                        //预付商品没有返点 价格为 0
                        Inventory inventoryVO = inventoryMapper.selectBySku(infoVO.getSku());
                        if (Objects.nonNull(inventoryVO)) {
                            Products products = productsMapper.selectByPrimaryKey(inventoryVO.getPdId());
                            select.setSku(String.valueOf(products.getCategoryId()));
                            majorRebate = majorRebateMapper.selectOne(select);
                        }
                    }
                    if (majorRebate != null) {
                        infoVO.setRebateNumber(majorRebate.getNumber());
                        infoVO.setRebateType(majorRebate.getType());
                        infoVO.setMPrice(infoVO.getSalePrice());

                        //定额钱
                        if (majorRebate.getType() == 1) {
                            BigDecimal price = new BigDecimal(infoVO.getMPrice() + infoVO.getRebateNumber());
                            infoVO.setSalePrice(price.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                        } else if (majorRebate.getType() == 2) {
                            //比例
                            /*Double calprice = Math.ceil(infoVO.getmPrice() * (1 + infoVO.getRebateNumber() / 100) * 10) / 10;
                            infoVO.setSalePrice(calprice);*/
                            Double calPrice = infoVO.getMPrice() * (1 + infoVO.getRebateNumber() / 100);
                            infoVO.setSalePrice(new BigDecimal(calPrice).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                        }
                        //存在预付商品
                        if (isPrepaySku) {
                            Double rebatePrice = infoVO.getRebateNumber();
                            if (majorRebate.getType().intValue() == 2) {
                                rebatePrice = Math.ceil(infoVO.getMPrice() * (infoVO.getRebateNumber() / 100) * 10) / 10;
                            }
                            infoVO.setSalePrice(rebatePrice);
                        }

                    }
                }

            }
        }
    }

    /**
     * 根据规格计算平均价(带单位)
     *
     * @param price  售卖价
     * @param weight 规格
     * @return
     */
    public String calculateAvgPrice(BigDecimal price, String weight) {
        //去除规格说明部分 (特殊说明)
        if (weight.contains("(")) {
            weight = weight.substring(0, weight.indexOf("("));
        }

        //去除其他销售属性（复合型已经排序到weight字段前面）
        String[] arr = weight.split("/");
        if (arr.length > 1) {
            weight = arr[0];
        }

        if (weight.matches(RegConstant.WEIGHT_REG1) || weight.matches(RegConstant.WEIGHT_REG2)) {
            BigDecimal value1 = BigDecimal.valueOf(1);
            BigDecimal value2 = BigDecimal.valueOf(1);
            Matcher matcher = PATTERN.matcher(weight);
            StringBuffer result = new StringBuffer();
            if (matcher.find()) {
                value1 = BigDecimal.valueOf(Double.valueOf(matcher.group()));
            }
            if (matcher.find()) {
                value2 = BigDecimal.valueOf(Double.valueOf(matcher.group()));
            }
            if (weight.matches("\\d{1,5}\\.{0,1}\\d{0,2}[\\w\\u4e00-\\u9fa5]{1,2}\\*\\d{1,5}[\\u4e00-\\u9fa5]{1}")) {
                String[] weigts = weight.split("\\*");
                if (value2.compareTo(BigDecimal.valueOf(1)) <= 0) {
                    result.append(price.divide(value1, 2, BigDecimal.ROUND_HALF_DOWN)).append("/");
                    Matcher matcher1 = PATTERN_1.matcher(weigts[0]);
                    if (matcher1.find()) {
                        result.append(matcher1.group()); //容量单位
                    }
                } else {
                    result.append(price.divide(value2, 2, BigDecimal.ROUND_HALF_DOWN)).append("/");
                    Matcher matcher1 = PATTERN_2.matcher(weigts[1]);
                    if (matcher1.find()) {
                        result.append(matcher1.group()); //数量单位
                    }
                }
            } else {
                //去除'毛重'字段
                weight = weight.substring(2);
                result.append(price.divide(value2, 2, BigDecimal.ROUND_HALF_DOWN)).append("/");
                Matcher matcher1 = PATTERN_1.matcher(weight);
                if (matcher1.find()) {
                    result.append(matcher1.group()); //区间单位
                }
            }
            return result.toString();
        }
        return "暂无";
    }

    @Override
    public CommonResult<PageInfo<TimingProductVO>> selectTimingSkuGray(TimingDeliveryInput timingDeliveryInput) {
        return selectTimingSkuV2(timingDeliveryInput);
    }

    @Override
    public CommonResult<PageInfo<TimingProductVO>> selectTimingSkuV2(TimingDeliveryInput timingDeliveryInput) {
        if (RequestHolder.isMajor()) {
            return CommonResult.fail();
        }
        if (Objects.isNull(timingDeliveryInput)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ResultConstant.PARAM_FAULT);
        }
        //如果包含特殊字符，则直接返回空
        if (StringUtil.containsMb4Char(timingDeliveryInput.getProductName())) {
            return CommonResult.ok(PageInfoHelper.createPageInfo(Lists.newArrayList()));
        }
        Integer areaNo = RequestHolder.getMerchantAreaNo();
        Long newMerchantId = RequestHolder.getNewMerchantId();
        // 获取当前用户信息 是否是核心客户 大客户
        boolean isCoreCustomer = merchantService.checkCoreCustomers(newMerchantId);
        timingDeliveryInput.setAreaNo(areaNo);
        Integer frontCategoryId = timingDeliveryInput.getFrontCategoryId();
        //通过前台类目查询后台类目
        if (frontCategoryId != null) {
            //此处前端传的是前台1级类目，先查2级前台类目，再查2级前台类目映射的后台类目
            List<Integer> categoryIds = frontCategoryToCategoryMapper.listBySecondFrontId(frontCategoryId);
            if (CollectionUtils.isEmpty(categoryIds)) {
                return CommonResult.ok(PageInfo.emptyPageInfo());
            }
            timingDeliveryInput.setCategoryIds(categoryIds);
        }


        PageInfo<TimingProductVO> timingProductVOs = selectTimingSkuInfo(timingDeliveryInput);
        List<TimingProductVO> timingProductVOsList = timingProductVOs.getList();
        List<ActivitySkuDTO> activitySkuDTOS = new ArrayList<>(timingProductVOsList.size());
        timingProductVOsList.forEach(timingProductVO -> {
            ActivitySkuDTO activitySkuDTO = new ActivitySkuDTO();
            activitySkuDTO.setSku(timingProductVO.getSku());
            activitySkuDTO.setQuantity(timingProductVO.getThreshold());
            activitySkuDTOS.add(activitySkuDTO);
        });
        Map<String, ActivitySkuDetailDTO> activitySkuMap = activityService.listByActivitySku(activitySkuDTOS, areaNo, newMerchantId);

        //获取全部控价品信息
        Map<String, MarketPriceControlProducts> controlProductsMap = marketPriceControlProductsService.selectAllControlProductsByCache();

        timingProductVOs.getList().forEach(timingProductVO -> {
                    String sku = timingProductVO.getSku();
                    //非核心客户库存展示
                    if (isCoreCustomer) {
                        timingProductVO.setQuantity(timingSingle(timingProductVO));
                    }
                    ActivitySkuDetailDTO activitySku = activitySkuMap.get(sku);

                    MarketPriceControlProducts priceControlProducts = null;

                    //返回隐藏实付价
                    if (!CollectionUtils.isEmpty(controlProductsMap) && controlProductsMap.containsKey(timingProductVO.getSku())) {
                        priceControlProducts = controlProductsMap.get(timingProductVO.getSku());
                        if (priceControlProducts != null) {
                            timingProductVO.setPriceHide(priceControlProducts.getPriceHide());
                            timingProductVO.setPriceControlLine(MarketControlPriceHideEnum.HIDE.getCode().equals(priceControlProducts.getFacePriceHide()) ?
                                    priceControlProducts.getPriceControlLine() : null);
                            timingProductVO.setFacePriceHide(priceControlProducts.getFacePriceHide());
                        }
                    }

                    //有活动价取活动价，且不展示阶梯价；没有则展示阶梯价
                    BigDecimal salePrice = timingProductVO.getSalePrice();
                    if (activitySku != null && Objects.equals(activitySku.getIsSupportTiming(), 1)) {
                        //活动为人群包的话需要实时计算阶梯价格
                        if (Objects.equals(activitySku.getIsCrowdPack(), CommonStatus.YES.getCode())) {
                            activitySku.getLadderPrices().forEach(ladderPriceVO -> {
                                //需要单独计算活动价格
                                PriceStrategy priceStrategy = new PriceStrategy();
                                priceStrategy.setAmount(ladderPriceVO.getAmount());
                                priceStrategy.setAdjustType(ladderPriceVO.getAdjustType());
                                priceStrategy.setRoundingMode(ladderPriceVO.getRoundingMode());
                                BigDecimal costPrice = null;
                                if (Objects.equals(InventoryAdjustPriceTypeEnum.GROSS_PROFIT_PERCENTAGE.getType(), priceStrategy.getAdjustType())) {
                                    costPrice = this.selectCostPrice(sku, areaNo);
                                }
                                BigDecimal activityPrice = priceStrategyService.calcStrategyPrice(priceStrategy, costPrice, salePrice);
                                ladderPriceVO.setPrice(activityPrice);
                            });
                        }

                        //破价品不展示活动价
                        if (this.checkControlProducts(priceControlProducts)) {
                            //获取最高阶梯的数量--省心送展示最高阶梯的价格信息
                            int maxQuantity;
                            BigDecimal maxPrice = null;
                            if (!CollectionUtils.isEmpty(activitySku.getLadderPrices())) {
                                LadderPriceVO ladderPriceVO = activitySku.getLadderPrices().get(0);
                                maxQuantity = ladderPriceVO.getUnit() > timingProductVO.getThreshold() ? ladderPriceVO.getUnit() : timingProductVO.getThreshold();
                                maxPrice = ladderPriceVO.getPrice();
                            } else {
                                maxQuantity = timingProductVO.getThreshold();
                            }
                            TimingSkuActivityDTO timingSkuActivityDTO = activityService.getTimingSkuActivityPrice(activitySku, true, maxQuantity,
                                    sku, maxPrice, timingProductVO.getSalePrice());
                            if (timingSkuActivityDTO != null && timingSkuActivityDTO.getTotalPriceAfterDiscount().compareTo(BigDecimal.ZERO) > 0) {
                                BigDecimal activityPrice = timingSkuActivityDTO.getTotalPriceAfterDiscount().divide(BigDecimal.valueOf(maxQuantity), 2, RoundingMode.HALF_UP);

                                //开启了隐藏面价且隐藏面价低于活动价则不展示活动价
                                timingProductVO.setSalePrice(activityPrice);
                                if (priceControlProducts != null && Objects.equals(priceControlProducts.getFacePriceHide(), MarketControlPriceHideEnum.HIDE.getCode())
                                        && priceControlProducts.getPriceControlLine() != null && activityPrice.compareTo(priceControlProducts.getPriceControlLine()) < 0) {
                                    log.info("省心送列表开启控价-屏蔽活动价，activityPrice:{}, priceControlProducts:{}", activityPrice, JSON.toJSONString(priceControlProducts));
                                    timingProductVO.setSalePrice(salePrice);
                                }
                                timingProductVO.setTotalPriceAfterDiscount(timingSkuActivityDTO.getTotalPriceAfterDiscount());
                            }
                        }
                        timingProductVO.setIsSupportTiming(activitySku.getIsSupportTiming());
                        timingProductVO.setActivityLadderPrices(activitySku.getLadderPrices());
                        timingProductVO.setActivityQuantity(activitySku.getActualQuantity());
                    }
                    //设置默认库存，防止库存泄露，库存从其他接口获取
                    if (timingProductVO.getBaseSaleQuantity() * timingProductVO.getBaseSaleUnit() > timingProductVO.getQuantity()) {
                        timingProductVO.setOnlineQuantity(Constants.AreaStoreConstant.defaultOnlineQuantity);
                        timingProductVO.setQuantity(Constants.AreaStoreConstant.defaultOnlineQuantity);
                    } else {
                        timingProductVO.setOnlineQuantity(Constants.ONLINE_QUANTITY);
                        timingProductVO.setQuantity(Constants.ONLINE_QUANTITY);
                    }
                }
        );

        return CommonResult.ok(timingProductVOs);
    }

    /**
     * 原inventoryMapper.selectTimingSku
     * 拆分多表关联查询
     *
     * @param input
     * @return
     */
    private PageInfo<TimingProductVO> selectTimingSkuInfo(TimingDeliveryInput input) {

        // 查询 timing_rule
        List<TimingRule> timingRules = timingRuleMapper.listBySkusPriority(input.getAreaNo(), input.getSkus(), input.getType());
        if (CollectionUtils.isEmpty(timingRules)) {
            return PageInfo.emptyPageInfo();
        }
        Map<String, TimingRule> timingRuleMap = timingRules.stream().collect(Collectors.toMap(TimingRule::getTimingSku, Function.identity(), (k1, k2) -> k1));

        List<String> timingSkuList = timingRules.stream().map(TimingRule::getTimingSku).collect(Collectors.toList());

        // 查询SKU (inventory、products、area_sku)
        input.setSkus(timingSkuList);
        MarketProviderQueryDTO queryDto = MarketItemConverter.convertProviderQueryDto(input);
        queryDto.setSortDescList(Collections.singletonList(MarketConstant.ORDERBY_ITEM_CODE_LIST));
        queryDto.setAdminShow(1);
        PageInfo<MarketItemDTO> marketItemDTOS = marketItemFacade.listMarketItem(queryDto);
        if (CollectionUtils.isEmpty(marketItemDTOS.getList())) {
            return PageInfo.emptyPageInfo();
        }

        List<String> skuList = marketItemDTOS.getList().stream().map(MarketItemDTO::getItemCode).collect(Collectors.toList());
        // 查询area_store
        Long mId = RequestHolder.getMId();
        Map<String, AreaStoreQueryRes> areaStoreMap = getAreaStoreMap(mId, skuList);

        // 查询inventory中剩余字段
        List<ProductInventoryInfoDTO> inventoryVOS = inventoryMapper.selectProductInvInfo(timingSkuList, input.getAreaNo());
        Map<String, ProductInventoryInfoDTO> inventoryVOMap = inventoryVOS.stream().collect(Collectors.toMap(ProductInventoryInfoDTO::getSku, Function.identity(), (k1, k2) -> k1));

        Map<String, AreaStoreQueryRes> finalAreaStoreMap = areaStoreMap;
        List<TimingProductVO> timingProductVOS = marketItemDTOS.getList().stream()
                .map(marketItemDTO -> {
                    return buildTimingProductVO(timingRuleMap, finalAreaStoreMap, inventoryVOMap, marketItemDTO);
                })
                .sorted((item1, item2) -> {
                    return item1.getSku().compareTo(item2.getSku()); // Compare sku values
                })
                .collect(Collectors.toList());

        return PageInfoHelper.copyPageInfo(marketItemDTOS, timingProductVOS);
    }

    private TimingProductVO buildTimingProductVO(Map<String, TimingRule> timingRuleMap,
                                                 Map<String, AreaStoreQueryRes> areaStoreMap,
                                                 Map<String, ProductInventoryInfoDTO> inventoryDtoMap,
                                                 MarketItemDTO i) {
        AreaStoreQueryRes areaStore = areaStoreMap.getOrDefault(i.getItemCode(), new AreaStoreQueryRes());
        ProductInventoryInfoDTO inventoryDto = inventoryDtoMap.getOrDefault(i.getItemCode(), new ProductInventoryInfoDTO());
        TimingRule timingRule = timingRuleMap.getOrDefault(i.getItemCode(), new TimingRule());

        /**
         * 已废弃字段：
         * area_sku.info
         * inventory.introduction
         */
        TimingProductVO timingProductVO = MarketItemConverter.convert2TimingProductVO(i);
        timingProductVO.setOnlineQuantity(Optional.ofNullable(areaStore.getOnlineQuantity()).orElse(0));
        timingProductVO.setQuantity(Optional.ofNullable(areaStore.getOnlineQuantity()).orElse(0));
        timingProductVO.setReserveUseQuantity(Optional.ofNullable(areaStore.getReserveUseQuantity()).orElse(0));
        timingProductVO.setReserveMaxQuantity(Optional.ofNullable(areaStore.getReserveMaxQuantity()).orElse(0));
        timingProductVO.setReserveMinQuantity(Optional.ofNullable(areaStore.getReserveMinQuantity()).orElse(0));
        if (StringUtils.isBlank(timingProductVO.getPicture_path())) {
            timingProductVO.setPicture_path(i.getMarketMainPicture());
        }
        Optional.ofNullable(areaStore.getCostPrice()).ifPresent(p -> {
            timingProductVO.setCostPrice(p.doubleValue());
        });

        timingProductVO.setPack(inventoryDto.getPack());
        timingProductVO.setMaturity(inventoryDto.getMaturity());
        timingProductVO.setOn_sale(inventoryDto.getOnSale());
        timingProductVO.setMaturity(inventoryDto.getMaturity());
        Optional.ofNullable(inventoryDto.getSalePrice()).ifPresent(p -> {
            timingProductVO.setSalePrice(BigDecimal.valueOf(p));
        });
        timingProductVO.setSlogan(inventoryDto.getSlogan());
        timingProductVO.setOtherSlogan(inventoryDto.getOtherSlogan());
        timingProductVO.setSalesMode(inventoryDto.getSalesMode());
        timingProductVO.setLimitedQuantity(inventoryDto.getLimitedQuantity());
        timingProductVO.setLadderPriceStr(inventoryDto.getLadderPriceStr());
        // timingSkuDetail
        timingProductVO.setStorageMethod(inventoryDto.getStorageMethod());


        timingProductVO.setRuleId(timingRule.getId());
        timingProductVO.setRuleName(timingRule.getName());
        timingProductVO.setStartTime(timingRule.getStartTime());
        timingProductVO.setEndTime(timingRule.getEndTime());
        timingProductVO.setDeliveryStart(timingRule.getDeliveryStart());
        timingProductVO.setDeliveryEnd(timingRule.getDeliveryEnd());
        timingProductVO.setThreshold(timingRule.getThreshold());
        timingProductVO.setRuleInformation(timingRule.getRuleInformation());
        timingProductVO.setDeliveryPeriod(timingRule.getDeliveryPeriod());
        // timingSkuDetail
        timingProductVO.setDeliveryStartType(timingRule.getDeliveryStartType());
        timingProductVO.setDeliveryUnit(timingRule.getDeliveryUnit());
        timingProductVO.setDeliveryUpperLimit(timingRule.getDeliveryUpperLimit());
        timingProductVO.setType(timingRule.getType());
        timingProductVO.setAutoCalculate(timingRule.getAutoCalculate());
        timingProductVO.setPlusDay(timingRule.getPlusDay());

        return timingProductVO;
    }


    @Override
    public AjaxResult selectTimingSkuDetail(String sku, Integer quantity, Integer ruleId) {
        if (RequestHolder.isMajor()) {
            return AjaxResult.getError();
        }
        //ruleId没传的话，先通过sku查询到ruleId
        Integer areaNO = RequestHolder.getMerchantAreaNo();
        if (ruleId == null) {
            TimingRule timingRule = timingRuleMapper.selectTimingRuleBySku(areaNO, sku, type.TIMING.ordinal());
            ruleId = timingRule.getId();
        }

        TimingProductVO timingProductVO = selectTimingSkuDetailInfo(sku, ruleId, areaNO);

        if (null == timingProductVO) {
            return AjaxResult.getError(ResultConstant.TIMING_OUT_OF_TIME);
        }
        boolean isCoreCustomer = merchantService.checkCoreCustomers(RequestHolder.getNewMerchantId());
        //非核心客户
        if (!isCoreCustomer) {
            Integer singleQuantity = timingSingle(timingProductVO);
            timingProductVO.setQuantity(singleQuantity);
        }

        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        Long merchantId = merchantSubject.getMerchantId();
        Area area = merchantSubject.getArea();

        if (timingProductVO.getOn_sale() != 1) {
            return AjaxResult.getErrorWithMsg("此商品已下架，请购买其他商品！");
        }

        if (quantity == null || quantity <= 0) {
            quantity = timingProductVO.getThreshold();
        }
        ActivitySkuDetailDTO activitySku = activityService.getActivitySku(sku, quantity, areaNO, merchantId);
        //有活动价取活动价，且不展示阶梯价；没有则展示阶梯价
        if (activitySku != null && Objects.equals(activitySku.getIsSupportTiming(), 1)) {
            log.info("省心送商品使用活动信息：activitySku:{}", JSON.toJSONString(activitySku));
            if (activitySku.getActivityPrice() != null) {
                TimingSkuActivityDTO timingSkuActivityDTO = activityService.getTimingSkuActivityPrice(activitySku, false, quantity,
                        sku, null, timingProductVO.getSalePrice());
                if (timingSkuActivityDTO != null && timingSkuActivityDTO.getTotalPriceAfterDiscount().compareTo(BigDecimal.ZERO) > 0) {
                    timingProductVO.setActivityPrice(timingSkuActivityDTO.getTotalPriceAfterDiscount().divide(BigDecimal.valueOf(quantity),
                            2, RoundingMode.HALF_UP));
                    timingProductVO.setTotalPriceAfterDiscount(timingSkuActivityDTO.getTotalPriceAfterDiscount());

                    //设置了限购，remainQuantity肯定是取到限购数量
                    if (activitySku.getLimitQuantity() > 0) {
                        Integer limitQuantity = activitySku.getLimitQuantity();
                        Integer remainingQuantity = limitQuantity > timingSkuActivityDTO.getQuantityPurchased() ? limitQuantity - timingSkuActivityDTO.getQuantityPurchased() : 0;
                        timingProductVO.setRemainingQuantity(remainingQuantity);
                    }
                } else {
                    timingProductVO.setRemainingQuantity(0);
                }
            } else {
                Integer limitQuantity = activityService.getActivityLimitQuantity(activitySku);
                if (activitySku.getLimitQuantity() > 0) {
                    timingProductVO.setRemainingQuantity(limitQuantity);
                }
            }
            timingProductVO.setActivityLimitedQuantity(activitySku.getLimitQuantity());
            timingProductVO.setActivityQuantity(activitySku.getActualQuantity());
            timingProductVO.setIsSupportTiming(activitySku.getIsSupportTiming());

            //活动为人群包的话需要实时计算阶梯价格
            if (Objects.equals(activitySku.getIsCrowdPack(), CommonStatus.YES.getCode())) {
                BigDecimal salePrice = timingProductVO.getSalePrice();
                activitySku.getLadderPrices().forEach(ladderPriceVO -> {
                    //需要单独计算活动价格
                    PriceStrategy priceStrategy = new PriceStrategy();
                    priceStrategy.setAmount(ladderPriceVO.getAmount());
                    priceStrategy.setAdjustType(ladderPriceVO.getAdjustType());
                    priceStrategy.setRoundingMode(ladderPriceVO.getRoundingMode());
                    BigDecimal costPrice = null;
                    if (Objects.equals(InventoryAdjustPriceTypeEnum.GROSS_PROFIT_PERCENTAGE.getType(), priceStrategy.getAdjustType())) {
                        costPrice = this.selectCostPrice(sku, RequestHolder.getMerchantAreaNo());
                    }
                    BigDecimal activityPrice = priceStrategyService.calcStrategyPrice(priceStrategy, costPrice, salePrice);
                    ladderPriceVO.setPrice(activityPrice);
                });
            }
            timingProductVO.setActivityLadderPrices(activitySku.getLadderPrices());
        }

        //配送时间
        TimingRule timingRule = new TimingRule();
        timingRule.setDeliveryPeriod(timingProductVO.getDeliveryPeriod());
        timingRule.setDeliveryStart(timingProductVO.getDeliveryStart());
        timingRule.setDeliveryStartType(timingProductVO.getDeliveryStartType());
        timingRule.setPlusDay(timingProductVO.getPlusDay());
        timingRule.setTimingSku(sku);
        LocalDate deliveryDate = deliveryService.calDeliveryStart(merchantId, area, timingRule);
        if (null != deliveryDate) {
            timingProductVO.setDeliveryStart(DateUtils.localDate2Date(deliveryDate));
            //配送截止时间最晚是下单后的第90天
            LocalDate deliveryEnd = LocalDate.now().plusDays(timingProductVO.getDeliveryPeriod());

            //当为指定日期的时候 最晚配送时间 = 指定日期 + 配送周期
            if (Objects.equals(timingRule.getDeliveryStartType(), DeliveryStartTypeEnum.APPOINT.getCode())) {
                deliveryEnd = deliveryDate.plusDays(timingRule.getDeliveryPeriod());
            }
            timingProductVO.setDeliveryEnd(DateUtils.localDate2Date(deliveryEnd));
            List<LocalDate> deliveryDateList = deliveryService.getDeliveryDateList(merchantId, area, timingRule);
            LocalDate finalDeliveryEnd = deliveryEnd;
            List<LocalDate> filterDataList = deliveryDateList.stream().filter(x -> finalDeliveryEnd.compareTo(x) >= 0)
                .sorted().collect(Collectors.toList());
            timingProductVO.setDeliveryFrequentNew(filterDataList);
        }

        //关键属性值
        long pdId = timingProductVO.getPdId().longValue();
        Map<Long, List<ProductsPropertyValueVO>> productKeyValueMap = productsPropertyValueService.getProductsPropertyValueCache(Collections.singletonList(pdId));
        List<ProductsPropertyValueVO> keyValueList = Optional.ofNullable(productKeyValueMap).map(m -> m.get(pdId)).orElse(Collections.emptyList());
        timingProductVO.setKeyValueList(keyValueList);

        //获取全部控价品信息
        Map<String, MarketPriceControlProducts> controlProductsMap = marketPriceControlProductsService.selectAllControlProductsByCache();
        if (!CollectionUtils.isEmpty(controlProductsMap) && controlProductsMap.containsKey(sku)) {
            MarketPriceControlProducts priceControlProducts = controlProductsMap.get(sku);
            if (priceControlProducts != null) {
                timingProductVO.setPriceHide(priceControlProducts.getPriceHide());
                timingProductVO.setFacePriceHide(priceControlProducts.getFacePriceHide());
                timingProductVO.setPriceControlLine(MarketControlPriceHideEnum.HIDE.getCode().equals(priceControlProducts.getFacePriceHide()) ?
                        priceControlProducts.getPriceControlLine() : null);
            }
        }

        return AjaxResult.getOK(timingProductVO);

    }


    /**
     * 原 inventoryMapper.selectTimingSkuDetail
     *
     * @param sku
     * @param ruleId
     * @param areaNo
     * @return
     */
    private TimingProductVO selectTimingSkuDetailInfo(String sku, Integer ruleId, Integer areaNo) {
        // 查询 timing_rule
        TimingRule timingRule = timingRuleMapper.selectByPrimaryKey(ruleId);
        if (Objects.isNull(timingRule)) {
            return null;
        }
        if (!sku.equals(timingRule.getTimingSku()) || !CommonStatus.YES.getCode().equals(timingRule.getDisplay())) {
            return null;
        }
        Map<String, TimingRule> timingRuleMap = new HashMap<>();
        timingRuleMap.put(sku, timingRule);

        PageInfo<MarketItemDTO> marketItemDTOPageInfo = marketItemFacade.listMarketItem(MarketProviderQueryDTO.builder()
                .skus(Collections.singletonList(sku))
                .areaNo(areaNo)
                .build());
        List<MarketItemDTO> marketItemList = marketItemDTOPageInfo.getList();
        if (CollectionUtils.isEmpty(marketItemList)) {
            return null;
        }

        // 查询area_store
        Long mId = RequestHolder.getMId();
        Map<String, AreaStoreQueryRes> areaStoreMap = getAreaStoreMap(mId, Collections.singletonList(sku));

        // 查询inventory中剩余字段
        List<ProductInventoryInfoDTO> inventoryVOS = inventoryMapper.selectProductInvInfo(Collections.singletonList(sku), areaNo);
        Map<String, ProductInventoryInfoDTO> inventoryVOMap = inventoryVOS.stream().collect(Collectors.toMap(ProductInventoryInfoDTO::getSku, Function.identity(), (k1, k2) -> k1));

        // 构建返回参数
        TimingProductVO timingProductVO = buildTimingProductVO(timingRuleMap, areaStoreMap, inventoryVOMap, marketItemList.get(0));

        // 查询品牌名称
        Integer brandId = inventoryVOS.get(0).getBrandId();
        if (Objects.nonNull(brandId)) {
            Brand brand = brandMapper.selectByPrimaryKey(brandId);
            Optional.ofNullable(brand).ifPresent(b -> {
                timingProductVO.setName(b.getName());
            });
        }

        return timingProductVO;
    }

    @Override
    public AjaxResult<PageInfo<ProductInfoVO>> selectSkuList(int pageIndex, int pageSize, String tableName) {
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        String size = merchantSubject.getSize();
        if (Objects.equals(size, Global.BIG_MERCHANT)) {
            return AjaxResult.getOK();
        }
        TemporaryActivityConfig temporaryActivityConfig = temporaryActivityConfigMapper.selectByTableName(tableName);
        if (temporaryActivityConfig == null || StringUtils.isEmpty(temporaryActivityConfig.getSkus())) {
            return AjaxResult.getOK();
        }
        String skus = temporaryActivityConfig.getSkus();
        List<String> skuList = Arrays.asList(skus.split(","));
        List<ProductInfoVO> productInfoVOS = salePriceTransService.selectGroupBuyProductVO(merchantSubject.getArea().getAreaNo(), skuList);
        PageInfo<ProductInfoVO> pageInfo = PageInfoHelper.manualPage(pageIndex, pageSize, productInfoVOS);
        //待优化 大客户价格展示不同
        majorPrice(pageInfo.getList(), merchantSubject);

        //营销活动处理
        checkoutSku(pageInfo.getList(), merchantSubject);

        return AjaxResult.getOK(pageInfo);
    }

    @Override
    public void checkoutSku(List<ProductInfoVO> homeProductVOList, MerchantSubject merchantSubject) {
        if (CollectionUtil.isEmpty(homeProductVOList)) {
            logger.warn("homeProductVOList 数据为空，跳过处理！");
            return;
        }
        // 批量处理到货提醒
        Contact contact = contactService.getMerchantDefaultContactCache(merchantSubject.getMerchantId());
        handleArrivalNotice(homeProductVOList, merchantSubject.getMerchantId(),
                Optional.ofNullable(contact).map(Contact::getStoreNo).orElse(null));
        // 获取商品属性
        List<Long> pdIds = homeProductVOList.stream().map(ProductInfoVO::getPdId).collect(Collectors.toList());
        Map<Long, List<ProductsPropertyValueVO>> productPropertyValueMap = productsPropertyValueService
                .getProductsPropertyValueCache(pdIds);
        homeProductVOList.forEach(productInfo -> {
            if (CollectionUtils.isEmpty(productInfo.getKeyValueList())) {
                // 由于pdId的属性是SKU粒度的，这里需要根据SKU过滤
                List<ProductsPropertyValueVO> keyValueListOfPdId = Optional
                        .ofNullable(productPropertyValueMap.get(productInfo.getPdId()))
                        .orElse(Collections.emptyList());
                productInfo.setKeyValueList(keyValueListOfPdId);
            }
        });


        Map<String, ActivitySkuDetailDTO> activitySkuDetailMap = Maps.newHashMap();
        if (!"大客户".equals(merchantSubject.getSize())) {
            // 批量查活动sku
            List<ActivitySkuDTO> activitySkuDTOS = homeProductVOList.stream().map(productInfoVO -> {
                ActivitySkuDTO activitySkuDTO = new ActivitySkuDTO();
                activitySkuDTO.setSku(productInfoVO.getSku());
                return activitySkuDTO;
            }).collect(Collectors.toList());
            activitySkuDetailMap = activityService.listByActivitySku(
                    activitySkuDTOS, merchantSubject.getArea().getAreaNo(), merchantSubject.getMerchantId());
            //批量查询满减满返规则
            Integer areaNo = merchantSubject.getArea().getAreaNo();
            List<String> skuListForMarketRule = homeProductVOList.stream().map(ProductInfoVO::getSku)
                    .collect(Collectors.toList());

            // 这里每个marketRuleDetail和MarketRule是一对多的关系
            Map<String, List<MarketRule>> marketRuleMap = marketRuleService.selectSkuMarketRulesMapByAreaNo(areaNo, skuListForMarketRule);
            for (ProductInfoVO productInfoVO : homeProductVOList) {
                salePriceTransService.handleSalePrice(activitySkuDetailMap, productInfoVO);
                if (CollectionUtil.isEmpty(productInfoVO.getMarketRuleList())) {
                    // 满减满返规则展示
                    productInfoVO.setMarketRuleList(Optional.ofNullable(marketRuleMap.get(productInfoVO.getSku()))
                            .orElse(Collections.emptyList()));
                }
            }
        }

        //设置均价信息、关键属性等等
        for (ProductInfoVO productInfoVO : homeProductVOList) {
            processProductInfo(productInfoVO);
        }
    }

    private void processProductInfo(ProductInfoVO productInfoVO) {
        //设置均价信息
        AvgInfoVO avgInfoVO = getAvgInfo(productInfoVO.getCategoryId(), productInfoVO.getWeight());
        productInfoVO.setAvgNumerator(avgInfoVO.getAvgNumerator());
        productInfoVO.setAvgUnit(avgInfoVO.getAvgUnit());
        // 价格信息
        productInfoVO.resetSkuNameAndSkuPic();
        //处理关键属性
        productInfoVO.sortKeyValueList();
    }


    private void handleArrivalNotice(List<ProductInfoVO> productInfoVOList, Long mId, Integer storeNo) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(productInfoVOList)
                || mId == null
                || storeNo == null) {
            log.info("不需要关注到货信息,mId:{}, storeNo:{}", mId, storeNo);
            return;
        }
        List<String> skuList = productInfoVOList.stream().map(ProductInfoVO::getSku).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(skuList)) {
            List<ArrivalNotice> arrivalNotices = arrivalNoticeMapper.selectBySkuListAndMidAndStoreNo(
                    skuList, mId, storeNo);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(arrivalNotices)) {
                log.warn("用户未关注任何到货通知:{},skuList:{}", mId, skuList);
                return;
            }
            log.info("用户关注的到货通知:{},arrivalNotices:{}", mId, JSON.toJSONString(arrivalNotices));
            Map<String, ArrivalNotice> arrivalNoticeMap = arrivalNotices.stream()
                    .collect(Collectors.toMap(ArrivalNotice::getSku, Function.identity()));
            for (ProductInfoVO productInfoVO : productInfoVOList) {
                if (arrivalNoticeMap.containsKey(productInfoVO.getSku())) {
                    productInfoVO.setArrivalNotice(1);
                }
            }
        }
    }

    /**
     * 处理商品信息列表中每个商品的平均价格、显示平均价格和属性值列表。
     *
     * @param productInfoVOList 商品信息列表
     * @param keyValueListMap   商品属性值列表映射，key为商品ID，value为属性值列表
     */
    private void handleAvgPriceAndKeyValueList(List<ProductInfoVO> productInfoVOList, Map<Long, List<ProductsPropertyValueVO>> keyValueListMap) {
        if (CollectionUtils.isEmpty(productInfoVOList)) {
            return;
        }
        productInfoVOList.forEach(productInfoVO -> {
            productInfoVO.setAvgPrice(calculateAvgPrice(BigDecimal.valueOf(productInfoVO.getSalePrice()), productInfoVO.getWeight()));
            productInfoVO.setShowAvg(getShowAvg(productInfoVO));
            // 如果没有属性，则填入空的List
            productInfoVO.setKeyValueList(Optional.ofNullable(keyValueListMap.get(productInfoVO.getPdId())).orElse(Collections.emptyList()));
            productInfoVO.sortKeyValueList();
        });
    }

    private void handleSkuTimingRules(List<ProductInfoVO> productInfoVOList, MerchantSubject merchantSubject) {
        if (CollectionUtils.isEmpty(productInfoVOList) || merchantSubject == null || merchantSubject.getIslock() != 0) {
            log.warn("商品列表为空，或者客户还未审核通过(isLock==0才代表审核通过)，不需要处理省心送信息:{}", merchantSubject);
            return;
        }
        // 先全部都设置为非省心送
        productInfoVOList.forEach(productInfoVO -> productInfoVO.setTimingRuleSku(false));
        Integer areaNo = Optional.ofNullable(merchantSubject.getArea()).map(Area::getAreaNo).orElse(0);
        List<TimingRuleVO> areaTimingRules = timingRuleService.getTimingInfoByAreaNoCache(areaNo);
        if (CollectionUtils.isEmpty(areaTimingRules)) {
            log.warn("运营服务区:{}并没有配置任何省心送:{}", areaNo);
            return;
        }
        Set<String> skuSet = productInfoVOList.stream().map(ProductInfoVO::getSku).collect(Collectors.toSet());
        Map<String, TimingRuleVO> skuTimingRules = areaTimingRules.stream()
                .filter(timingRuleVO -> skuSet.contains(timingRuleVO.getTimingSku()))
                .collect(Collectors.toMap(TimingRuleVO::getTimingSku, Function.identity(),
                        (o1, o2) -> {
                            // 确保返回的是最新的
                            return o1.getId() > o2.getId() ? o1 : o2;
                        }));
        if (MapUtils.isEmpty(skuTimingRules)) {
            log.info("这批SKU没有省心送配置:{}", skuSet);
            return;
        }
        Map<String, LocalDate> calDeliveryStartMap = deliveryService.calDeliveryStartBatch(merchantSubject.getMerchantId(), skuTimingRules.values());
        // 给每个SKU设置省心送信息
        productInfoVOList.forEach(productInfoVO -> {
            TimingRule timingRule = skuTimingRules.get(productInfoVO.getSku());
            if (null != timingRule) {
                productInfoVO.setTimingRuleSku(true);
                this.fillTimingRule(timingRule, productInfoVO, calDeliveryStartMap.getOrDefault(productInfoVO.getSku(), null));
            }
        });
    }

    private void handleAreaMarketRule(List<ProductInfoVO> productInfoVOList, MerchantSubject merchantSubject){
        if (CollectionUtils.isEmpty(productInfoVOList)
                || merchantSubject == null
                || MAJOR_CUSTOMER_SIZE_LABEL.equalsIgnoreCase(merchantSubject.getSize())) {
            log.warn("商品列表为空，或者客户并不是单店客户，不需要处理区域market rule.merchantSubject:{}", merchantSubject);
            return;
        }
        // 先全部都设置为空List
        productInfoVOList.forEach(productInfoVO -> productInfoVO.setMarketRuleList(Collections.emptyList()));
        Integer areaNo = Optional.ofNullable(merchantSubject.getArea()).map(Area::getAreaNo).orElse(0);
        List<String> skuList = productInfoVOList.stream().map(ProductInfoVO::getSku).collect(Collectors.toList());
        Map<String, List<MarketRule>> skuMarketRulesMap = marketRuleService.selectSkuMarketRulesMapByAreaNo(areaNo, skuList);
        if (MapUtils.isEmpty(skuMarketRulesMap)) {
            log.info("这批SKU:{} 在areaNO:{}没有找到有效的满减或者满返规则", skuList, areaNo);
            return;
        }
        log.info("处理SKU的market rule:{}, skuMarketRulesMap:{}", skuList, skuMarketRulesMap);
        productInfoVOList.forEach(productInfoVO ->
                productInfoVO.setMarketRuleList(Optional.ofNullable(skuMarketRulesMap.get(productInfoVO.getSku())).orElse(Collections.emptyList())));
    }

    /**
     * 获取大客户类目报价单 可展示的spu
     * 类目下的所有非大客户专享sku都展示，价格取商城售价
     * 而类目下有报价单的sku，也展示，价格取报价单
     */
    @Override
    public List<String> getBigMerchantMajorCategory(Integer adminId, Integer areaNo, Integer direct, Integer mallShow) {

        //查询类目报价单获取spu信息
        List<String> categorySpuList = majorCategoryMapper.selectSpu(adminId, areaNo, direct, mallShow, null);
        if (CollectionUtils.isEmpty(categorySpuList)) {
            return Lists.newArrayList();
        }

        List<String> skuList = getSkuListByCategoryList(categorySpuList, areaNo);
        //不展示的sku
        List<String> notMallShow = majorPriceMapper.selectSkuNotMallShow(adminId, areaNo, direct, mallShow);
        skuList = skuList.stream().distinct().filter(x -> !notMallShow.contains(x)).collect(Collectors.toList());
        return skuList;
    }

    @Override
    public Set<String> mallShowHideSkuList(Integer adminId, Integer areaNo, Integer direct) {
//        可以适当增加缓存   alculateMallShowMap   、 queryMallShowMap 都可以调用
        //查询类目报价单获取spu信息
        List<String> categorySpuList = majorCategoryMapper.selectSpu(adminId, areaNo, direct, net.summerfarm.mall.enums.MajorPriceMallShowEnum.HIDE.getCode(), null);
        Set<String> skus = new HashSet<>();
        if (CollectionUtil.isNotEmpty(categorySpuList)) {
            List<String> skuList = getSkuListByCategoryList(categorySpuList, areaNo);
            if (CollectionUtil.isNotEmpty(skuList)) {
                //大客户 sku 报价单
                List<MajorPrice> majorPrices = majorPriceMapper.selectSkusMajorPriceList(adminId, direct, areaNo, skuList);
                Set<String> priceSkuShowIds = majorPrices.stream().filter(e -> net.summerfarm.mall.enums.MajorPriceMallShowEnum.SHOW.getCode().equals(e.getMallShow())).map(MajorPrice::getSku).collect(Collectors.toSet());
                if (CollectionUtil.isNotEmpty(priceSkuShowIds)) {
                    skuList.removeAll(priceSkuShowIds);
                }
                skus.addAll(skuList);
            }
        }
        //不展示的sku
        List<String> notMallShow = majorPriceMapper.selectSkuNotMallShow(adminId, areaNo, direct, net.summerfarm.mall.enums.MajorPriceMallShowEnum.HIDE.getCode());
        if (CollectionUtil.isNotEmpty(notMallShow)) {
            skus.addAll(notMallShow);
        }
        return skus;
    }

    private List<String> getSkuListByCategoryList(List<String> categorySpuList, Integer areaNo) {
        List<String> spuList = new ArrayList<>();
        spuList.addAll(categorySpuList);
        List<String> skuList = Lists.newArrayList();
        int total = spuList.size();
        int size = 50;
        int count = 0;
        //获取sku，spuList过大会导致慢查询，超过50个分批查询
        while (total > size) {
            total -= size;
            List<String> spuSubList = spuList.subList(count * size, (count + 1) * size);
            List<String> skuSubList = inventoryMapper.selectBySpuList(spuSubList, areaNo);
            skuList.addAll(skuSubList);
            count++;
        }
        List<String> spuSubList = spuList.subList(count * size, spuList.size());
        List<String> skuSubList = inventoryMapper.selectBySpuList(spuSubList, areaNo);
        skuList.addAll(skuSubList);
        return skuList;
    }

    /**
     * 获取pdId 商城展示sku
     */
    private List<String> getSKuByPdIdMsg(Long pdId, Integer adminId, Integer areaNo, Integer direct) {
        //大客户定量 --类目下的所有非大客户专享sku都展示，价格取商城售价
        //而类目下有报价单的sku，也展示，价格取报价单
        List<String> skus = Lists.newArrayList();

        //看是否有类目报价单
        List<String> spus = majorCategoryMapper.selectSpu(adminId, areaNo, direct, 0, pdId);
        if (!CollectionUtils.isEmpty(spus)) {
            //查询类目报价单下的 非大客户skuId
            List<String> skuList = inventoryMapper.selectBySpuList(spus, areaNo);
            if (!CollectionUtils.isEmpty(skuList)) {
                skus.addAll(skuList);
            }
        }
        //在报价单中的商品
        List<Inventory> inventoryList = majorPriceMapper.selectSKuByAdminMsg(adminId, areaNo, direct, pdId, MajorPriceMallShowEnum.YES.ordinal());
        if (!CollectionUtils.isEmpty(inventoryList)) {
            skus.addAll(inventoryList.stream().map(Inventory::getSku).distinct().collect(Collectors.toList()));
        }
        return skus;
    }

    @Override
    public BigDecimal selectCostPrice(String sku, Integer areaNo) {
        Fence fence = fenceService.selectCacheOneByAreaNo(areaNo);
        if (null == fence) {
            logger.info("未获取到对应围栏信息{}", areaNo);
            return BigDecimal.ZERO;
        }
        Integer storeNo = fence.getStoreNo();
        WarehouseInventoryMapping warehouseInventoryMapping = redisCacheUtil.getDataWithCache(Global.CACHE_WAREHOUSE_INVENTORY_MAPPING + ":" + storeNo + ":" + sku, 300, WarehouseInventoryMapping.class, () -> inventoryMappingMapper.selectByUniqueIndex(storeNo, sku));
        if (warehouseInventoryMapping == null) {
            logger.info("sku{},不在库存总仓下{}", sku, storeNo);
            return BigDecimal.ZERO;
        }
        BigDecimal costPrice = cycleInventoryCostRepository.selectCycleCost(sku, warehouseInventoryMapping.getWarehouseNo());
        if (costPrice == null) {
            logger.info("未查询到成本价,sku{},warehouseNo:{}", sku, warehouseInventoryMapping.getWarehouseNo());
            return BigDecimal.ZERO;
        }
        return costPrice;
    }

    @Override
    public ProductVO selectTimingSkuOne(String sku, Integer areaNo) {
        ProductVO timingProduct = inventoryMapper.selectTimingSkuOne(sku, areaNo);
        if (Objects.isNull(timingProduct)) {
            log.warn("未查询到省心送sku:{},areaNo:{}", sku, areaNo);
            return null;
        }
        Long mId = RequestHolder.getNewMerchantId();
        boolean coreCustomers = merchantService.checkCoreCustomers(mId);
        if (!coreCustomers) {
            Integer quantity = timingSingle(timingProduct);
            timingProduct.setQuantity(quantity);
        }
        return timingProduct;
    }

    private Integer timingSingle(ProductVO timingProductVO) {
        ProductInfoVO productInfoVO = new ProductInfoVO();
        productInfoVO.setQuantity(timingProductVO.getQuantity());
        productInfoVO.setReserveMaxQuantity(timingProductVO.getReserveMaxQuantity());
        productInfoVO.setReserveMinQuantity(timingProductVO.getReserveMinQuantity());
        productInfoVO.setReserveUseQuantity(timingProductVO.getReserveUseQuantity());
        productInfoVO.setOnlineQuantity(timingProductVO.getOnlineQuantity());
        return getSingleQuantity(productInfoVO);
    }

    @Override
    public Integer getSingleQuantity(ProductInfoVO productInfoVO) {

        Integer reserveMaxQuantity = productInfoVO.getReserveMaxQuantity();
        Integer reserveMinQuantity = productInfoVO.getReserveMinQuantity();
        Integer reserveUseQuantity = productInfoVO.getReserveUseQuantity();
        Integer onlineQuantity = productInfoVO.getOnlineQuantity();
        Integer quantity;
        if (reserveMaxQuantity > reserveUseQuantity + reserveMinQuantity) {
            quantity = onlineQuantity + reserveUseQuantity > reserveMaxQuantity ? onlineQuantity - reserveMaxQuantity - reserveUseQuantity : 0;
        } else {
            quantity = onlineQuantity > reserveMinQuantity ? onlineQuantity - reserveMinQuantity : 0;
        }
        return quantity;
    }

    @Override
    public List<EsProductVO> querySuggestV2(String searchContent) {
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        ProductSearchInput searchInput = new ProductSearchInput();
        searchInput.setPdName(searchContent);
        HomeProductQueryVo homeProductQueryVo = buildHomeProductVo(merchantSubject, searchInput);
        if (homeProductQueryVo == null) {
            return new ArrayList<>();
        }
        return salePriceTransService.querySuggestWordV3(homeProductQueryVo);
    }

    /**
     * 获取商品采购数量上限
     *
     * @param sku 商品SKU编码
     * @param quantity 待校验的采购数量
     * @param merchantId 商户ID
     * @return 采购上限数量,如果返回null则表示不限制采购数量
     *         如果quantity<=0返回null
     *         如果是POP商户返回null
     *         如果商户在白名单内返回null
     *         如果商户不在白名单则返回限制后的数量
     */
    @Override
    public Integer getPurchaseCeiling(String sku, Integer quantity, Long merchantId) {
        if (null == quantity || quantity <= 0) {
            return null;
        }

        //pop客户不校验采购上限
        if (RequestHolder.isPopMerchant()) {
            return null;
        }
        String config = configService.getValue(Global.ADD_PURCHASE_POOL_INFO_ID);
        Long poolInfoId = Long.valueOf(config);
        MerchantPoolInfo merchantPoolInfo = merchantPoolService.getMerchantPoolInfoById(poolInfoId);

        // 如果没有找到人群包，则说明全都开放了
        boolean isInWhitelist = (null == merchantPoolInfo);
        if (!isInWhitelist && merchantId != null) {
            // 获取当前店铺是否在白名单人群包中
            List<MerchantPoolDetail> merchantPoolDetails = merchantPoolService.getDetailByMIdWithCache(merchantId);
            if (CollectionUtils.isEmpty(merchantPoolDetails)) {
                isInWhitelist = false;
            } else {
                isInWhitelist = merchantPoolDetails.stream().anyMatch(detail -> detail.getPoolInfoId().equals(poolInfoId));
            }
        }
        if (isInWhitelist) {
            log.info("当前店铺在白名单中，不限制购买数量, sku:{}, quantity:{}", sku, quantity);
            return null;
        }
        Integer limitedQuantity = getPurchaseLimit(quantity);
        log.warn("当前门店不在白名单，被限制加购数量了, merchantId:{}, sku:{}, quantity:{}, limitedQuantity:{}", merchantId, sku, quantity, limitedQuantity);
        return limitedQuantity;
    }

    // 这个工具支持批量获取数据库的数据并且缓存。默认10分钟过期。缓存的Key-Value分别是SKU-Inventory
    private final GenericInMemoryCacheProcessor<String, Inventory> inventoryCache = new GenericInMemoryCacheProcessor<String, Inventory>(60 * 10, 3000);

    @Override
    public Inventory selectInventoryWithCache(String sku) {
        Map<String, Inventory> cacheInventory = selectInventoryWithCacheBatch(Sets.newHashSet(sku));
        if (org.apache.commons.collections.MapUtils.isEmpty(cacheInventory) || !cacheInventory.containsKey(sku)) {
            log.error("未找到SKU:{}", sku);
            return null;
        }
        return cacheInventory.get(sku);
    }

    @Override
    public Map<String, Inventory> selectInventoryWithCacheBatch(Set<String> skuList) {
        // Use GenericInMemoryCacheProcessor to cache and process inventory data
        NamedFunction<String, Inventory> cacheValueComputer = new NamedFunction<String, Inventory>("selectInventoryWithCache",
                skus -> {
                    return inventoryMapper.listBySkus(skus);
                });

        List<Inventory> cacheInventoryList = inventoryCache.process(skuList, cacheValueComputer, Inventory::getSku);

        if (org.apache.commons.collections.CollectionUtils.isEmpty(cacheInventoryList)) {
            return Collections.emptyMap();
        }

        // Convert the list to map with sku as key and inventory as value
        return cacheInventoryList.stream()
                .collect(Collectors.toMap(
                        Inventory::getSku,
                        Function.identity(),
                        (existing, replacement) -> replacement));
    }

    @Override
    public PageInfo<ProductInfoVO> getHelpOrderProductList(int pageIndex, int pageSize, Long mId, String queryStr, Integer areaNo,
                                                           Integer type, Boolean activityPrice, Integer helpOrderFlag, Integer orderType) {
        ProductSearchInput searchInput = new ProductSearchInput();
        searchInput.setMId(mId);
        searchInput.setQueryStr(queryStr);

        HomeProductQueryVo homeProductQueryVo = buildHomeProductVo(null, searchInput);
        if (homeProductQueryVo == null) {
            return PageInfoHelper.createPageInfo(new ArrayList<>());
        }
        MerchantSubject merchantSubject = homeProductQueryVo.getMerchantSubject();
        Long merchantId = merchantSubject.getMerchantId();
        boolean coreCustomer = merchantService.checkCoreCustomers(merchantId);
        Integer adminId = homeProductQueryVo.getAdminId();
        Integer show = homeProductQueryVo.getShow();
        List<String> skuList = homeProductQueryVo.getSkuList();
        if (Objects.isNull(areaNo)) {
            areaNo = merchantSubject.getArea().getAreaNo();
        }
        PageVo pageVo = new PageVo(pageIndex, pageSize);
        PageInfo<ProductInfoVO> homeProductVOPage = salePriceTransService.selectHomeProductVO2(null, areaNo, null, adminId,
                skuList, merchantSubject.getSkuShow(), show, merchantSubject.getDirect(), merchantSubject.getSize(), queryStr, coreCustomer, pageVo, type, helpOrderFlag);
        //待优化 大客户价格展示不同
        majorPrice(homeProductVOPage.getList(), merchantSubject);
        // 根据条件判断是否获取特价
        if (activityPrice == null || activityPrice) {
            checkoutSku(homeProductVOPage.getList(), merchantSubject);
        }

        //crm获取省心送订单赋值阶梯价
        if (orderType != null && Objects.equals(orderType, OrderTypeEnum.TIMING.getId())) {
            if (!"大客户".equals(merchantSubject.getSize()) && CollectionUtil.isNotEmpty(homeProductVOPage.getList())) {
                //批量查活动sku
                List<ProductInfoVO> homeProductVOList = homeProductVOPage.getList();
                List<ActivitySkuDTO> activitySkuDTOS = new ArrayList<>(homeProductVOList.size());
                homeProductVOList.forEach(productInfoVO -> {
                    ActivitySkuDTO activitySkuDTO = new ActivitySkuDTO();
                    activitySkuDTO.setSku(productInfoVO.getSku());
                    activitySkuDTOS.add(activitySkuDTO);
                });
                Map<String, ActivitySkuDetailDTO> activitySkuDetailMap = activityService.listByActivitySku(
                        activitySkuDTOS, merchantSubject.getArea().getAreaNo(), merchantSubject.getMerchantId());

                if (!CollectionUtils.isEmpty(activitySkuDetailMap)) {
                    for (ProductInfoVO productInfoVO : homeProductVOList) {
                        ActivitySkuDetailDTO activitySku = activitySkuDetailMap.get(productInfoVO.getSku());
                        if (activitySku != null && Objects.equals(activitySku.getIsSupportTiming(), 1)) {

                            //人群包价格是实时计算出来
                            if (Objects.equals(activitySku.getIsCrowdPack(), CommonStatus.YES.getCode())) {
                                BigDecimal salePrice = BigDecimal.valueOf(productInfoVO.getSalePrice());
                                activitySku.getLadderPrices().forEach(ladderPriceVO -> {
                                    //需要单独计算活动价格
                                    PriceStrategy priceStrategy = new PriceStrategy();
                                    priceStrategy.setAmount(ladderPriceVO.getAmount());
                                    priceStrategy.setAdjustType(ladderPriceVO.getAdjustType());
                                    priceStrategy.setRoundingMode(ladderPriceVO.getRoundingMode());
                                    BigDecimal costPrice = null;
                                    if (Objects.equals(InventoryAdjustPriceTypeEnum.GROSS_PROFIT_PERCENTAGE.getType(), priceStrategy.getAdjustType())) {
                                        costPrice = this.selectCostPrice(productInfoVO.getSku(), merchantSubject.getArea().getAreaNo());
                                    }
                                    BigDecimal price = priceStrategyService.calcStrategyPrice(priceStrategy, costPrice, salePrice);
                                    ladderPriceVO.setPrice(price);
                                });
                            }
                            productInfoVO.setLadderPrice(null);
                            productInfoVO.setActivityLadderPrices(activitySku.getLadderPrices());
                        }
                    }
                }
            }
        }

        return homeProductVOPage;
    }

    @Override
    public List<CommodityInventoryVO> getCommodityInventory(CommodityInventoryInput commodityInventoryInput) {
        // 查询area_store
        List<CommodityInventoryVO> commodityInventoryVOList = new ArrayList<>(commodityInventoryInput.getSkuList().size());
        Long mId = RequestHolder.getMId();
        Set<String> skuSet = Sets.newHashSet(commodityInventoryInput.getSkuList());
        if (CollectionUtils.isEmpty(skuSet)) {
            return commodityInventoryVOList;
        }
        Map<String, AreaStoreQueryRes> areaStoreMap = getAreaStoreMap(mId, commodityInventoryInput.getSkuList());
        Map<String, Inventory> inventoryMap = this.selectInventoryWithCacheBatch(skuSet);
        if (CollectionUtils.isEmpty(inventoryMap)) {
            log.error("inventoryMap is empty!:{}", skuSet);
            return commodityInventoryVOList;
        }

        // 这里为了保证返回的顺序和输入的顺序是一致的
        for (String sku : commodityInventoryInput.getSkuList()) {
            Inventory inventory = inventoryMap.get(sku);
            if (inventory == null) {
                log.error("inventory is null!:{}", sku);
                continue;
            }
            Long pdId = inventory.getPdId();
            Integer onlineQuantity = Optional.ofNullable(areaStoreMap.get(sku))
                    .map(AreaStoreQueryRes::getOnlineQuantity).orElse(0);
            CommodityInventoryVO commodityInventoryVO = new CommodityInventoryVO();
            commodityInventoryVO.setOnlineQuantity(BaseUtil.cryptoInventory(onlineQuantity, pdId));
            commodityInventoryVO.setSku(sku);
            commodityInventoryVOList.add(commodityInventoryVO);
        }
        return commodityInventoryVOList;
    }

    @Override
    public AvgInfoVO getAvgInfo(Integer categoryId, String weight) {
        BigDecimal avgNumerator = null;
        String avgUnit = null;

        try {
            //        鲜果才返均价
            if (StringUtils.isNotBlank(weight) && categoryService.getChildCategoryIdsByParentName("新鲜水果").contains(categoryId)) {
                String result = weight.split(Global.SLASH)[0];
                //如果包含中划线 eg:毛重7-8斤
                if (result.contains(Global.STRIKETHROUGH)) {
                    //如果包含中划线 eg:毛重7-8斤
                    String regex = "(\\d+(?:\\.\\d+)?)-(\\d+(?:\\.\\d+)?)((?:[A-Za-z]+)|[\\u4e00-\\u9fa5]+)";

                    Pattern pattern = Pattern.compile(regex);
                    Matcher matcher = pattern.matcher(result);
                    if (matcher.find()) {
                        String unit = matcher.group(3);
                        if (Global.avgUnitList.contains(unit)) {
                            avgUnit = "斤";
                            avgNumerator = new BigDecimal(matcher.group(1)).add(new BigDecimal(matcher.group(2))).divide(new BigDecimal(2));
                            if (!"斤".equals(unit)) {
                                avgNumerator = avgNumerator.multiply(new BigDecimal(2));
                            }

                        }
                    }
                    //如果包含* eg:5斤*1包
                } else if (result.contains(Global.TIMES_SIGN)) {

                    String regex = "(\\d+(?:\\.\\d+)?)([A-Za-z\\u4e00-\\u9fa5]+)\\*(\\d+(?:\\.\\d+)?)([A-Za-z\\u4e00-\\u9fa5]+)";

                    Pattern pattern = Pattern.compile(regex);
                    Matcher matcher = pattern.matcher(result);

                    if (matcher.find()) {
                        BigDecimal number1 = new BigDecimal(matcher.group(1));
                        String unit1 = matcher.group(2);
                        BigDecimal number2 = new BigDecimal(matcher.group(3));
                        String unit2 = matcher.group(4);

                        if (Global.avgUnitList.contains(unit1) && (number1.compareTo(BigDecimal.ONE) > 0 || number2.compareTo(BigDecimal.ONE) > 0)) {
                            avgUnit = "斤";
                            avgNumerator = number1.multiply(number2);
                            if (!"斤".equals(unit1)) {
                                avgNumerator = avgNumerator.multiply(new BigDecimal(2));
                            }
                        } else if (number2.compareTo(BigDecimal.ONE) > 0 && number1.compareTo(BigDecimal.ONE) != 0) {
                            avgNumerator = number2;
                            avgUnit = unit2;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.info("计算判断均价相关信息异常，categoryId={}, weight={}", categoryId, weight, e);
        }
        AvgInfoVO avgInfoVO = new AvgInfoVO();
        avgInfoVO.setAvgNumerator(avgNumerator);
        avgInfoVO.setAvgUnit(avgUnit);
        return avgInfoVO;
    }

    @Override
    public Map<String, Integer> getMinSaleQuantity(List<String> skus) {
        if (CollectionUtils.isEmpty(skus)) {
            return Maps.newHashMap();
        }

        List<Inventory> inventories = inventoryMapper.listBySkus(skus);
        if (CollectionUtils.isEmpty(inventories)) {
            return Maps.newHashMap();
        }

        Map<String, Integer> map = new HashMap<>();
        for (Inventory inventory : inventories) {
            if (inventory.getBaseSaleQuantity() == null || inventory.getBaseSaleUnit() == null) {
                log.warn("InventoryService[]getMinSaleQuantity[]baseSaleQuantity or baseSaleUnit is null! inventory:{}", JSON.toJSONString(inventory));
                continue;
            }
            map.put(inventory.getSku(), inventory.getBaseSaleQuantity() * inventory.getBaseSaleUnit());
        }
        return map;
    }

    @Override
    @InMemoryCache(expiryTimeInSeconds = 1 * 60)
    public BigDecimal selectCostPriceByCache(String sku, Integer areaNo) {
        return this.selectCostPrice(sku, areaNo);
    }

    @Override
    public List<ProductVO> queryProductSpec(ProductSpecificationQueryInput input) {
        if (RequestHolder.isMajor() && (input == null || CollectionUtils.isEmpty(input.getPdIds()))) {//NOSONAR
            return null;
        }
        Integer areaNo = RequestHolder.getMerchantAreaNo();
        List<ProductVO> productList = new ArrayList<>(input.getPdIds().size());
        input.getPdIds().forEach(pdId -> {
            List<AreaSku> areaSkus = areaSkuCache.getAreaSkuInfoByPdId(pdId);
            if (CollectionUtils.isEmpty(areaSkus)) {
                log.info("InventoryService[]queryProductSpec[]areaSkus is null! pdId:{}", pdId);
                return;
            }

            //过滤当前区域可见的sku
            areaSkus = areaSkus.stream().filter(areaSku -> Objects.equals(areaSku.getAreaNo(), areaNo)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(areaSkus)) {
                log.info("InventoryService[]queryProductSpec[]areaSkus is empty! pdId:{}", pdId);
                return;
            }

            ProductVO productVO = new ProductVO();
            productVO.setPdId(pdId.intValue());
            productVO.setIsMultipleSku(areaSkus.size() > 1 ? CommonStatus.YES.getCode() : CommonStatus.NO.getCode());
            productList.add(productVO);
        });
        return productList;
    }

    @Override
    public List<ProductInfoVO> getHotList(HotListSearchInput input) {
        if (input == null || input.getFirstFrontCategoryId() == null) {
            log.info("前台类目信息为空");
            return new ArrayList<>();
        }

        //获取当前用户的业态
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        if (merchantSubject == null) {
            log.info("获取用户信息失败或当前用户未登录");
            return new ArrayList<>();
        }

        if (RequestHolder.isMajor()) {
            return new ArrayList<>();
        }

        if (StringUtils.isBlank(merchantSubject.getType())) {
            log.info("当前客户主营类型为空");
            return new ArrayList<>();
        }

        List<String> skus;
        try {
            Object o = redisTemplate.opsForHash()
                    .get(KeyConstant.MINI_RANK, input.getFirstFrontCategoryId() + "-" + merchantSubject.getType());
            skus = o == null ? null : Arrays.asList(String.valueOf(o).split(","));
        } catch (Exception e) {
            log.error("获取mini榜单数据异常", e);
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(skus)) {
            log.info("获取mini榜单sku数据为空");
            return new ArrayList<>();
        }

        //根据sku查询商品信息
        HomeProductQueryVo homeProductQueryVo = buildHomeProductVo(merchantSubject, new ProductSearchInput());
        if (homeProductQueryVo == null) {
            return new ArrayList<>();
        }

        //假如是大客户需要过滤不在报价单sku
        if (Objects.equals(merchantSubject.getSize(), MerchantSizeEnum.MAJOR_CUSTOMER.getValue()) && !CollectionUtils.isEmpty(homeProductQueryVo.getSkuList())
                && Objects.equals(merchantSubject.getSkuShow(), MajorSkuShowEnum.PART.getCode())) {
            Set<String> skuList = new HashSet<>(homeProductQueryVo.getSkuList());
            skus = skus.stream().filter(sku -> skuList.contains(sku)).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(skus)) {
            log.info("需要查询sku数据为空");
            return new ArrayList<>();
        }

        homeProductQueryVo.setSkuQueryList(skus);
        homeProductQueryVo.setSkuList(null);
        PageInfo<ProductInfoVO> productInfoVOPageInfo = salePriceTransService.selectHomeProductVO3(homeProductQueryVo, new PageVo(1, skus.size()));
        if (productInfoVOPageInfo == null) {
            return new ArrayList<>();
        }

        //待优化 大客户价格展示不同
        newMajorPrice(productInfoVOPageInfo.getList(), merchantSubject);
        checkoutSku(productInfoVOPageInfo.getList(), merchantSubject);
        return productInfoVOPageInfo.getList();
    }

    @Override
    public Set<Long> selectPdIdsSetBySkus(Set<String> skus) {
        return inventoryMapper.selectPdIdsSetBySkus(skus);
    }

    /**
     * 分sku限制加购上限：
     * ① 库存≥100时，单次加购限制为100件；
     * ② 50≤库存＜100，单次加购限制为50件；
     * ③ 30≤库存＜50，单次加购限制为30件；
     * ④ 10≤库存＜30，单次加购限制为10；
     * ⑤ 库存＜10，无加购限制，保持线上逻辑。
     *
     * @param quantity
     * @return 库存上限
     */
    public static Integer getPurchaseLimit(Integer quantity) {
        if (quantity >= 100) {
            return 100;
        } else if (quantity >= 50) {
            return 50;
        } else if (quantity >= 30) {
            return 30;
        } else if (quantity >= 10) {
            return 10;
        } else {
            return null;
        }
    }

    public Integer getShowAvg(ProductInfoVO productInfoVO) {
        String weight = productInfoVO.getWeight();
        //去除规格说明部分 (特殊说明)
        if (weight.contains(Global.LEFT_BRACKET)) {
            weight = weight.substring(0, weight.indexOf(Global.LEFT_BRACKET));
        }
        if (weight.contains(Global.SLASH)) {
            weight = weight.split(Global.SLASH)[0];
        }
        if (weight.matches(RegConstant.WEIGHT_REG1) || weight.matches(RegConstant.WEIGHT_REG2)) {
            return productInfoVO.getAveragePriceFlag();
        }
        return 0;
    }

    /**
     * 过滤控价品 -- 假如隐藏实付价则不返回预估到手价  假如是隐藏面价需要判断是否超过控价线
     *
     * @param controlProducts
     */
    private boolean checkControlProducts(MarketPriceControlProducts controlProducts) {
        //假如隐藏实付价则不返回预估到手价  假如是隐藏面价需要判断是否超过控价线
        if (controlProducts == null) {
            return true;
        }

        if (Objects.equals(controlProducts.getPriceHide(), MarketControlPriceHideEnum.HIDE.getCode())
                || (Objects.equals(controlProducts.getFacePriceHide(), MarketControlPriceHideEnum.HIDE.getCode())
                && (controlProducts.getPriceControlLine() == null || controlProducts.getPriceControlLine().compareTo(new BigDecimal(BigInteger.ZERO)) <= 0))) {
            log.info("省心送列表过滤控价品->controlProducts:{}", JSON.toJSONString(controlProducts));
            return false;
        }

        return true;
    }
}
