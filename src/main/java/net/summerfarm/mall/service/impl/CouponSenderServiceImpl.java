package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.enums.CouponTypeEnum;
import net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum;
import net.summerfarm.enums.coupon.CouponValidTypeEnum;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.bo.coupon.ReceiveIdCountBO;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.market.coupon.CouponDTO;
import net.summerfarm.mall.model.dto.market.coupon.SkuCouponDTO;
import net.summerfarm.mall.model.dto.market.coupon.SkuCouponReqDTO;
import net.summerfarm.mall.model.dto.order.OrderItemCalcDTO;
import net.summerfarm.mall.model.vo.InventoryVO;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import net.summerfarm.mall.model.vo.TimingRuleVO;
import net.summerfarm.mall.service.*;
import net.xianmu.common.cache.InMemoryCache;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CouponSenderServiceImpl  implements CouponSenderService {
    @Resource
    CouponSenderSetupMapper couponSenderSetupMapper;

    @Resource
    private CouponSenderSetupService couponSenderSetupService;

    @Resource
    MerchantCouponMapper merchantCouponMapper;
    @Resource
    CouponMapper couponMapper;
    @Resource
    CouponReceiveLogMapper couponReceiveLogMapper;

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private TimingRuleMapper timingRuleMapper;

    @Resource
    private CouponBlackAndWhiteMapper couponBlackAndWhiteMapper;

    @Resource
    private CategoryService categoryService;

    @Resource
    private CouponSenderRuleMapper couponSenderRuleMapper;

    @Resource
    private AreaService areaService;

    @Resource
    private CouponService couponService;

    @Resource
    private TimingRuleService timingRuleService;

    @Resource
    @Lazy
    private MerchantCouponService merchantCouponService;

    @Resource
    private InventoryService inventoryService;

    @Resource
    private ProductService productService;

    @Resource
    private MerchantPoolService merchantPoolService;

    @Override
    public CommonResult<List<Coupon>> list(boolean withBlackAndWhite, Long mId) {
        if (RequestHolder.isMajor()){ //NOSONAR
            log.info("大客户不返回优惠券列表信息");
            return CommonResult.ok();
        }

        //获取有效的发放设置ID
        Set<Integer> effectiveIds = listEffectiveIds(mId);
        if(CollectionUtils.isEmpty(effectiveIds)){
            return CommonResult.ok();
        }

        //根据发放设置ID查询卡劵信息
        List<Coupon> coupons = couponSenderSetupMapper.selectCouponByIdIn(effectiveIds);
        if (CollectionUtils.isEmpty(coupons)) {
            return CommonResult.ok();
        }

        if(withBlackAndWhite) {
            coupons = getCoupons (mId, coupons);
        }else{
            coupons = getCouponsV1(mId, coupons);
        }
        return CommonResult.ok(coupons);
    }

    /**
     * copy from getCoupons（）
     * 差异在于不查询商品 黑白名单 不设置Coupon.skuScope字段
     * @param mId
     * @param coupons
     * @return
     */
    private List<Coupon> getCouponsV1(Long mId, List<Coupon> coupons) {
        //查询有效期内是否存在未使用卡劵
        Set<Integer> couponIds = coupons.stream().map(Coupon::getId).collect(Collectors.toSet());
        List<ReceiveIdCountBO> receiveIdCountBos = merchantCouponMapper.selectUnusedByReceiveIdIn(couponIds, mId);
        Map<Integer, ReceiveIdCountBO> countBOMap = receiveIdCountBos.stream().collect(Collectors.toMap(x -> x.getCouponId(), Function.identity(), (p1, p2) -> p2));

        //查询卡券已领取张数-判断是否还可以领取 不能领取则返回已领取
        List<ReceiveIdCountBO> alreadyReceiveIdCountBOS = merchantCouponMapper.getUserReceiveCount(new ArrayList<>(couponIds), mId, CouponReceiveTypeEnum.RECEIVE.getCode());
        Map<Integer, ReceiveIdCountBO> alreadyCouponMap = alreadyReceiveIdCountBOS.stream().collect(Collectors.toMap(x -> x.getCouponId(), Function.identity(), (p1, p2) -> p2));

        coupons.forEach(coupon -> {
            setCouponValidPeriod(coupon);

            //兼容老逻辑 写死为未领取（现在只校验优惠劵的次数和余量、即使余量和次数用完也可以展示为待领取）
            coupon.setEffectiveNum(CouponEffectiveNumEnum.UNCLAIMED.getCode());

            // 判断是否在有效期内存在未使用的劵
            ReceiveIdCountBO receiveIdCountBO = countBOMap.get(coupon.getId());
            if (Objects.nonNull(receiveIdCountBO)) {
                coupon.setUseStatus(CommonStatus.YES.getCode());
                coupon.setReceiveStatus(CouponReceiveStatusEnum.USED.getCode());
                coupon.setEffectiveNum(CouponEffectiveNumEnum.RECEIVE.getCode());
                coupon.setStartDate(receiveIdCountBO.getStartDate());
                coupon.setVaildDate(receiveIdCountBO.getVaildDate());
            } else if (coupon.getGrantLimit() > 0 && coupon.getGrantAmount() <= 0) {
                coupon.setReceiveStatus(CouponReceiveStatusEnum.SOLD_OUT.getCode());
            } else {
                //默认为立即领取
                coupon.setReceiveStatus(CouponReceiveStatusEnum.IMMEDIATE_CLAIM.getCode());

                //是否已领取（领取数量是否超过限制）
                if (!CollectionUtils.isEmpty(alreadyCouponMap) && alreadyCouponMap.containsKey(coupon.getId()) && coupon.getQuantityClaimed() > 0) {
                    ReceiveIdCountBO alreadyCoupon = alreadyCouponMap.get(coupon.getId());
                    if (alreadyCoupon.getNum() >= coupon.getQuantityClaimed()) {
                        coupon.setReceiveStatus(CouponReceiveStatusEnum.ALREADY_RECEIVED.getCode());
                    }
                }
            }
            coupon.setCouponId(coupon.getId());
        });
        coupons = coupons.stream().filter(x -> !x.getVaildDate().isBefore(LocalDateTime.now())).collect(
                Collectors.toList());
        return coupons;
    }

    @Override
    public List<Coupon> listCoupons(List<Integer> couponIds) {
        List<Coupon> coupons = couponMapper.selectByIdIn(couponIds);
        for (Coupon coupon : coupons) {
            setCouponValidPeriod(coupon);
        }
        return coupons;
    }

    /**
     * 设置优惠券有效期
     * @param coupon
     */
    private void setCouponValidPeriod(Coupon coupon) {
        LocalDateTime validDate = null;
        LocalDateTime startTime = null;
        int couponType = coupon.getType();
        if (couponType == CouponValidTypeEnum.FIXED_INTERVAL.getType()) {
            int validTime = coupon.getVaildTime();
            validDate = LocalDateTime.of(LocalDate.now().plusDays(validTime), LocalTime.of(23, 59, 59));
            startTime = coupon.getStartTime() == null ? LocalDateTime.now() : LocalDateTime.now().plusDays(
                    coupon.getStartTime());
        }
        else if (couponType == CouponValidTypeEnum.FIXED_TIME.getType()) {
            validDate = coupon.getVaildDate();
            startTime = coupon.getStartDate() == null ? LocalDateTime.now() : coupon.getStartDate();
        } else {
            return;
        }
        //修改时间
        String format = startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        coupon.setStartDate(LocalDateTime.parse(format,DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        coupon.setVaildDate(validDate);
    }

    /**
     * @description: 获取有效的发放设置ID
     * @author: lzh
     * @date: 2023/7/27 15:00
     * @param: [mId]
     * @return: java.util.List<java.lang.Integer>
     **/
    private Set<Integer> listEffectiveIds(Long mId) {
        //查询当前用户的运营区域
        Integer merchantAreaNo = RequestHolder.getMerchantAreaNo();
        Area userArea = RequestHolder.getMerchantArea();

        //查看当前生效的用户领取的优惠劵发放设置
        Set<Integer> effectiveIds = new HashSet<>();
        // 从缓存走
//        CouponSenderSetup condition = new CouponSenderSetup();
//        condition.setSenderType(CouponSenderSetupSenderTypeEnum.USER_RECEIVE.getType());
//        condition.setStatus(CouponSenderSetupStatusEnum.IN_EFFECT.getStatus());
//        condition.setStartTime(LocalDateTime.now());
        List<CouponSenderSetup> couponSenderSetUps = couponSenderSetupService.listInUseSetupsWithCacheByType(CouponSenderSetupSenderTypeEnum.USER_RECEIVE);
        if (CollectionUtils.isEmpty(couponSenderSetUps)) {
            log.warn("未找到有效的优惠劵发放设置,mId:{}, areaNo:{}", mId, merchantAreaNo);
            return effectiveIds;
        } else {
            LocalDateTime now = LocalDateTime.now();
            couponSenderSetUps = couponSenderSetUps.stream().filter(couponSenderSetup -> {
                if (couponSenderSetup.getEndTime() != null && couponSenderSetup.getEndTime().isBefore(now)) {
                    log.warn("该优惠劵发放设置已经过期了!{}", couponSenderSetup);
                    return false;
                }
                // 默认全部都包含
                return true;
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(couponSenderSetUps)) {
                log.warn("从缓存中获取到的优惠劵发放设置全部都失效了,mId:{}, areaNo:{}", mId, merchantAreaNo);
                return effectiveIds;
            }
        }

        // 按发放规则类型进行分组
        Map<Integer, List<CouponSenderSetup>> couponSenderSetupTypeMap =
                couponSenderSetUps.stream().collect(Collectors.groupingBy(CouponSenderSetup::getType));

        //获取圈人发放设置
        List<CouponSenderSetup> crowds = couponSenderSetupTypeMap.get(CouponScopeTypeEnum.CROWD.getCode());
        if (!CollectionUtils.isEmpty(crowds)) {
            List<Integer> couponSendIds = crowds.stream().map(CouponSenderSetup::getId).collect(Collectors.toList());

            //查询发放设置的圈人规则
            List<CouponSenderRule> couponSenderRules =  couponSenderRuleMapper.getListBySendIds(couponSendIds, CouponScopeTypeEnum.CROWD.getCode());
            if (!CollectionUtil.isEmpty(couponSenderRules)) {
                Map<Long, List<CouponSenderRule>> senderRuleMap = couponSenderRules.stream()
                        .collect(Collectors.groupingBy(CouponSenderRule::getScopeId));

                //获取圈人信息
                List<MerchantPoolDetail> merchantPoolDetailList = merchantPoolService.getDetailByMIdWithCache(mId);
                List<Long> filerPoolInfoIds = merchantPoolDetailList.stream().map(MerchantPoolDetail::getPoolInfoId)
                        .collect(Collectors.toList());
                if (!CollectionUtil.isEmpty(filerPoolInfoIds)) {
                    filerPoolInfoIds.stream().forEach(filerPoolInfoId -> {
                        List<CouponSenderRule> senderRuleList = senderRuleMap.get(filerPoolInfoId);
                        if (CollectionUtils.isEmpty(senderRuleList)) {
                            return;
                        }
                        Set<Integer> couponSenderIds = senderRuleList.stream().map(CouponSenderRule::getCouponSenderId).collect(Collectors.toSet());
                        effectiveIds.addAll(couponSenderIds);
                    });
                }
            }
        }

        //获取运营城市发放设置
        List<CouponSenderSetup> citys = couponSenderSetupTypeMap.get(CouponScopeTypeEnum.CITY.getCode());
        if (!CollectionUtils.isEmpty(citys)) {
            List<Integer> sendIds = citys.stream().map(CouponSenderSetup::getId).collect(Collectors.toList());

            //查询发放设置的圈人规则
            List<CouponSenderRule> couponSenderRules =  couponSenderRuleMapper.getListBySendIds(sendIds, CouponScopeTypeEnum.CITY.getCode());
            if (!CollectionUtil.isEmpty(couponSenderRules)) {
                List<Long> areaNos = couponSenderRules.stream().map(CouponSenderRule::getScopeId).collect(Collectors.toList());
                Map<Long, List<CouponSenderRule>> senderRuleMap = couponSenderRules.stream()
                        .collect(Collectors.groupingBy(CouponSenderRule::getScopeId));
                areaNos.stream().forEach(areaNo -> {
                    List<CouponSenderRule> senderRuleList = senderRuleMap.get(areaNo);
                    if (Objects.equals(areaNo, merchantAreaNo.longValue()) && !CollectionUtils.isEmpty(senderRuleList)) {
                        Set<Integer> couponSenderIds = senderRuleList.stream().map(CouponSenderRule::getCouponSenderId).collect(Collectors.toSet());
                        effectiveIds.addAll(couponSenderIds);
                    }
                });
            }
        }

        //获取运营大区发放设置
        List<CouponSenderSetup> regions = couponSenderSetupTypeMap.get(CouponScopeTypeEnum.REGION.getCode());
        if (!CollectionUtils.isEmpty(regions)) {
            List<Integer> sendIds = regions.stream().map(CouponSenderSetup::getId).collect(Collectors.toList());

            //查询发放设置的圈人规则
            List<CouponSenderRule> couponSenderRules =  couponSenderRuleMapper.getListBySendIds(sendIds, CouponScopeTypeEnum.REGION.getCode());
            if (!CollectionUtil.isEmpty(couponSenderRules)) {
                // Set<Long> largeIds = couponSenderRules.stream().map(CouponSenderRule::getScopeId).collect(Collectors.toSet());
                Map<Long, List<CouponSenderRule>> senderRuleMap = couponSenderRules.stream()
                        .collect(Collectors.groupingBy(CouponSenderRule::getScopeId));

                // 用户的request holder 肯定会有LargeAreaNo；没有则取数据库
                // 改成用用户的大区来匹配
                if (null == userArea || null == userArea.getLargeAreaNo()) {
                    userArea = areaService.selectAreaWithCache(merchantAreaNo);
                    if (null == userArea) {
                        // 说明没有大区，不太可能。
                        log.warn("user large area is null, merchant area is {},mId:{}", merchantAreaNo, mId);
                        return effectiveIds;
                    }
                }

                Integer largeAreaNo = userArea.getLargeAreaNo();
                List<CouponSenderRule> countrySenderRulesUserMatched = senderRuleMap.get(largeAreaNo.longValue());
                if (!CollectionUtils.isEmpty(countrySenderRulesUserMatched)) {
                    effectiveIds.addAll(countrySenderRulesUserMatched.stream().map(CouponSenderRule::getCouponSenderId).collect(Collectors.toList()));
                }
//根据大区获取所有城市
//                List<Area> areas = areaMapper.selectByLargeAreaNos(largeIds);
//                if (!CollectionUtil.isEmpty(areas)) {
//                    areas.stream().forEach(area -> {
//                        List<CouponSenderRule> senderRuleList = senderRuleMap.get(area.getLargeAreaNo().longValue());
//                        if (Objects.equals(area.getAreaNo(), merchantAreaNo) && !CollectionUtils.isEmpty(senderRuleList)) {
//                            Set<Integer> couponSenderIds = senderRuleList.stream().map(CouponSenderRule::getCouponSenderId).collect(Collectors.toSet());
//                            effectiveIds.addAll(couponSenderIds);
//                        }
//                    });
//                }
            }
        }
        return effectiveIds;
    }

    @Override
    public void sendCoupon(Long mId, CouponConfigDetail detail) {
        Coupon coupon = couponMapper.selectByPrimaryKey(detail.getCouponId());
        if(coupon == null){
            throw new DefaultServiceException(1, "充值券信息异常");
        }

        LocalDateTime now = LocalDateTime.now();
        MerchantCoupon secondMerchantCoupon = new MerchantCoupon(mId, coupon.getId(), now.plusDays(coupon.getVaildTime()), null, now);
        for (int count = 0; count < detail.getNumber(); count++) {
            secondMerchantCoupon.setId(null);
            merchantCouponMapper.insertSelective(secondMerchantCoupon);
        }
    }

    @Override
    public CommonResult<List<SkuCouponDTO>> listAllValidCouponFromCacheV2(List<OrderItemCalcDTO> calcDTOList, List<Coupon> coupons) {
        List<SkuCouponDTO> list = Lists.newArrayList();
        if (CollectionUtil.isEmpty(coupons)) {
            return CommonResult.ok(list);
        }

        //批量获取类目id
        List<String> skus = calcDTOList.stream().map(OrderItemCalcDTO::getSku).collect(Collectors.toList());

        //批量查询省心送sku
        List<TimingRuleVO> ruleVOList = timingRuleService.getTimingInfoByAreaNoCache(RequestHolder.getMerchantAreaNo());
        Map<String, TimingRuleVO> timingRuleVOMap = ruleVOList.stream()
                .collect(Collectors.toMap(TimingRuleVO::getTimingSku, Function.identity(), (a, b) -> b));

        //sku类目信息
        Map<String, Integer> skuCategoryMap = calcDTOList.stream()
                .collect(Collectors.toMap(OrderItemCalcDTO::getSku, OrderItemCalcDTO::getCategoryId));
        Map<String, List<Coupon>> couponMap = Maps.newHashMap();

        //过滤出可以sku信息
        filterUsableCoupon(coupons, skus, timingRuleVOMap, skuCategoryMap, couponMap);

        list = couponMap.entrySet().stream().map(x -> {
            SkuCouponDTO couponDTO = new SkuCouponDTO();
            couponDTO.setSku(x.getKey());
            couponDTO.setCouponList(x.getValue());
            return couponDTO;
        }).collect(Collectors.toList());
        return CommonResult.ok(list);
    }

    @Override
    @InMemoryCache(expiryTimeInSeconds = 1 * 60)
    public List<Coupon> listUsableCouponByCache(Long mId) {
        CommonResult<List<Coupon>> commonResult = this.list(false, mId);
        // log.info("预估到手价获取领券中心可用优惠券信息-mId:{}, commonResult:{}", mId, JSON.toJSONString(commonResult));
        if (commonResult == null || CollectionUtils.isEmpty(commonResult.getData())) {
            return Collections.emptyList();
        }
        return commonResult.getData();
    }

    @Override
    public CommonResult<List<SkuCouponDTO>> listAllValidCouponFromCache(SkuCouponReqDTO reqDTO) {
        List<SkuCouponDTO> list = Lists.newArrayList();
        Long mId = RequestHolder.getMId();
        CommonResult<List<Coupon>> commonResult = list(false, mId);
        List<Coupon> coupons = commonResult.getData();

        //获取用户自己卡包优惠劵
        coupons = getMerchantCoupons(reqDTO.getShowMerchantCoupon(), coupons, mId, true);

        if (CollectionUtil.isEmpty(coupons)) {
            return CommonResult.ok(list);
        }
        List<String> skus = reqDTO.getSkus();
        if (reqDTO.getPdId() != null) {
            skus = inventoryMapper.selectByPdId(reqDTO.getPdId(), RequestHolder.getMerchantAreaNo());
        }
        if (CollectionUtil.isEmpty(skus)) {
            return CommonResult.ok(list);
        }
        //批量获取类目id
        List<InventoryVO> voList = inventoryMapper.selectBySkuList(skus);
        if (CollectionUtil.isEmpty(voList) || CollectionUtil.isEmpty(coupons)) {
            return CommonResult.ok(list);
        }

        //批量查询省心送sku
        List<TimingRuleVO> ruleVOList = timingRuleMapper.listBySkus(RequestHolder.getMerchantAreaNo(), skus,
                TimingRuleEnum.type.TIMING.ordinal());
        Map<String, TimingRuleVO> timingRuleVOMap = ruleVOList.stream()
                .collect(Collectors.toMap(TimingRuleVO::getTimingSku, Function.identity()));
        Map<String, Integer> skuCategoryMap = voList.stream()
                .collect(Collectors.toMap(InventoryVO::getSku, InventoryVO::getCategoryId));
        Map<String, List<Coupon>> couponMap = Maps.newHashMap();

        //过滤出可以sku信息
        filterUsableCoupon(coupons, skus, timingRuleVOMap, skuCategoryMap, couponMap);

        list = couponMap.entrySet().stream().map(x -> {
            SkuCouponDTO couponDTO = new SkuCouponDTO();
            couponDTO.setSku(x.getKey());
            couponDTO.setCouponList(x.getValue());
            return couponDTO;
        }).collect(Collectors.toList());
        return CommonResult.ok(list);
    }

    @Override
    public CommonResult<List<SkuCouponDTO>> listAllValidCouponFromCacheV3(SkuCouponReqDTO reqDTO, List<Coupon> coupons, Long mId) {
        List<SkuCouponDTO> list = Lists.newArrayList();

        //获取用户自己卡包优惠劵
        List<Coupon> merchantCoupons = getMerchantCoupons(reqDTO.getShowMerchantCoupon(), coupons, mId, true);
        if (CollectionUtil.isEmpty(merchantCoupons)) {
            return CommonResult.ok(list);
        }
        List<String> skus = reqDTO.getSkus();
        if (CollectionUtil.isEmpty(skus)) {
            return CommonResult.ok(list);
        }

        Map<String, Inventory> inventoryMap = inventoryService.selectInventoryWithCacheBatch(new HashSet<>(skus));
        if (CollectionUtil.isEmpty(inventoryMap)) {
            log.error("根据sku获取库存信息失败, skus:{}", skus);
            return CommonResult.ok(list);
        }

        Set<Long> pdIds = inventoryMap.values().stream().map(Inventory::getPdId).collect(Collectors.toSet());
        Map<Long, Products> productsMap = productService.selectProductsWithCachePipeline(pdIds);

        //批量查询省心送sku
        List<TimingRuleVO> ruleVOList = timingRuleService.getTimingInfoByAreaNoCache(RequestHolder.getMerchantAreaNo());
        Map<String, TimingRuleVO> timingRuleVOMap = ruleVOList.stream()
                .collect(Collectors.toMap(TimingRuleVO::getTimingSku, Function.identity(), (a, b) -> b));

        //sku类目信息
        Map<String, Integer> skuCategoryMap = new HashMap<>();
        inventoryMap.keySet().forEach(sku -> {
            Inventory inventory = inventoryMap.get(sku);
            if (inventory == null) {
                log.error("根据sku获取sku信息失败, sku:{}", sku);
                return;
            }
            Products products = productsMap.get(inventory.getPdId());
            if (products == null) {
                log.error("根据pdId获取商品信息失败, pdId:{}", inventory.getPdId());
                return;
            }
            skuCategoryMap.put(sku, products.getCategoryId());
        });

        //过滤出可以sku信息
        Map<String, List<Coupon>> couponMap = Maps.newHashMap();
        filterUsableCoupon(merchantCoupons, skus, timingRuleVOMap, skuCategoryMap, couponMap);

        list = couponMap.entrySet().stream().map(x -> {
            SkuCouponDTO couponDTO = new SkuCouponDTO();
            couponDTO.setSku(x.getKey());
            couponDTO.setCouponList(x.getValue());
            return couponDTO;
        }).collect(Collectors.toList());
        return CommonResult.ok(list);
    }

    private void filterUsableCoupon(List<Coupon> coupons, List<String> skus, Map<String, TimingRuleVO> timingRuleVOMap, Map<String, Integer> skuCategoryMap, Map<String, List<Coupon>> couponMap) {
        for (Coupon coupon : coupons) {
            Map<Integer, Set<String>> couponBlackAndWhiteMap = couponService.getCouponBlackAndWhiteMap (coupon.getId ());
            Set<String> black = couponBlackAndWhiteMap.getOrDefault (BlackAndWhiteTypeEnum.BLACK.getCode (),Collections.emptySet ());
            Set<String> white = couponBlackAndWhiteMap.getOrDefault (BlackAndWhiteTypeEnum.WHITE.getCode (),Collections.emptySet ());

            //只取普通商品优惠券、过滤掉还未生效的优惠券
            boolean filterFlag = !Objects.equals(coupon.getAgioType(), 1) || LocalDateTime.now().isBefore(coupon.getStartDate());
            if (filterFlag) {
                continue;
            }
            coupon.setAll(false);
            String skuStr = coupon.getSku();
            String categoryIdStr = coupon.getCategoryId();
            if (StringUtils.isBlank(skuStr) && StringUtils.isBlank(categoryIdStr)) {
                coupon.setAll(true);
            }
            if ((Objects.equals(skuStr, "{}") || StringUtils.isBlank(skuStr)) && Objects.equals(categoryIdStr, "{}")) {
                coupon.setAll(true);
            }
            if (StringUtils.isNotBlank(skuStr) && !Objects.equals(skuStr, "{}")) {
                Set<String> skuSet = JSON.parseObject(skuStr).keySet();
                coupon.setSkus(skuSet);
                coupon.setAll(false);
            } else if (!CollectionUtils.isEmpty(white)) {
                coupon.setSkus(white);
                coupon.setAll(false);
            }
            if (StringUtils.isNotBlank(categoryIdStr) && !Objects.equals(categoryIdStr, "{}")) {
                List<Integer> categoryIds = JSON.parseObject(categoryIdStr).keySet().stream()
                        .map(Integer::valueOf).collect(
                                Collectors.toList());

                //将一级类目、二级、三级 全部转换成三级类目
                List<Integer> cacheSublevelCategoryIds = categoryService.getCacheSublevelCategoryIds(coupon.getId().longValue(), categoryIds);
                coupon.setCategoryIds(new HashSet<>(cacheSublevelCategoryIds));
            }

            //商品范围：根据后台选择的商品范围
            if ((StringUtils.isNotBlank(coupon.getSku()) && !CollectionUtils.isEmpty(JSON.parseObject(coupon.getSku(),
                    new TypeReference<Map<String, String>>() {}))) || (!CollectionUtils.isEmpty(white))) {
                //指定商品
                coupon.setSkuScope(CouponSkuScopeEnum.PARTIAL_SKU.getCode());
            } else if (StringUtils.isNotBlank(coupon.getCategoryId()) && !CollectionUtils.isEmpty(JSON.parseObject(coupon.getCategoryId(), new TypeReference<Map<String, String>>() {}))) {
                //指定类目 区分有无黑名单
                coupon.setSkuScope(CouponSkuScopeEnum.PARTIAL_CATEGORY.getCode());
                if (!CollectionUtil.isEmpty(black)) {
                    coupon.setSkuScope(CouponSkuScopeEnum.PARTIAL_CATEGORY_EXCEPTIONALITY.getCode());
                }
            } else {
                //全部商品 区分有无黑名单
                coupon.setSkuScope(CouponSkuScopeEnum.ALL.getCode());
                if (!CollectionUtil.isEmpty(black)) {
                    coupon.setSkuScope(CouponSkuScopeEnum.ALL_EXCEPTIONALITY.getCode());
                }
            }

            for (String sku : skus) {
                //当前sku非省心送商品则需要过滤省心送、预售优惠券
                if (timingRuleVOMap.get(sku) == null && !Objects.equals(coupon.getActivityScope(),
                        CouponActivityScopeEnum.OTHER.ordinal()) && !Objects.equals(coupon.getActivityScope(),
                        CouponActivityScopeEnum.ALL.ordinal())) {
                    continue;
                }

                //过滤黑名单
                if (!CollectionUtils.isEmpty(black) && black.contains(sku)) {
                    log.warn("CouponSenderServiceImpl[]listAllValidCoupon[]sku in black coupon:{}, sku:{}", JSON.toJSONString(coupon), sku);
                    continue;
                }

                List<Coupon> couponList = couponMap.get(sku);
                if (CollectionUtil.isEmpty(couponList)) {
                    couponList = Lists.newArrayList();
                }
                Integer categoryId = skuCategoryMap.get(sku);
                //是否全部商品
                if (coupon.getAll()) { //NOSONAR
                    couponList.add(coupon);
                }
                //是否包含sku
                if (CollectionUtil.isNotEmpty(coupon.getSkus()) && coupon.getSkus().contains(sku)) {
                    couponList.add(coupon);
                }
                //是否包含类目
                if (CollectionUtil.isNotEmpty(coupon.getCategoryIds()) && coupon.getCategoryIds().contains(categoryId)) {
                    couponList.add(coupon);
                }
                couponMap.put(sku, couponList);
            }
        }
    }

    /**
     * @description: 获取用户卡包里面卡劵进行展示
     * @author: lzh
     * @date: 2023/8/16 17:24
     * @param: [reqDTO, coupons, mId]
     * @return: java.util.List<net.summerfarm.mall.model.domain.Coupon>
     **/
    private List<Coupon> getMerchantCoupons(Boolean showMerchantCoupon, List<Coupon> coupons, Long mId, boolean isUsingCache) {
        List<Coupon> merchantCoupons = new ArrayList<>();
        if (!CollectionUtil.isEmpty(coupons)) {
            merchantCoupons.addAll(coupons);
        }
        if (Objects.nonNull(showMerchantCoupon) && showMerchantCoupon) { //NOSONAR
            List<MerchantCouponVO> allMerchantCoupons;
            if (isUsingCache) {
                allMerchantCoupons = merchantCouponService.getUsableCouponByCache(mId);
                allMerchantCoupons = allMerchantCoupons.stream().filter(merchantCouponVO ->
                        merchantCouponVO.getAgioType().equals(CouponTypeEnum.NORMAL.getType())).collect(Collectors.toList());
            } else {
                allMerchantCoupons = merchantCouponMapper.selectUsableCouponWithoutThreshold(CouponTypeEnum.NORMAL.getType(), mId);
            }

            List<Integer> couponCollect = merchantCoupons.stream().map(Coupon::getId).collect(Collectors.toList());

            //组装卡劵信息
            if (!CollectionUtil.isEmpty(allMerchantCoupons)) {

                //主要用于过滤用户卡包里面相同卡劵不知道是发放设置领取的 还是 后台人工发放
                Iterator<MerchantCouponVO> couponVOIterator = allMerchantCoupons.iterator();
                while (couponVOIterator.hasNext()) {
                    MerchantCouponVO merchantCouponVO = couponVOIterator.next();
                    if (!CollectionUtil.isEmpty(couponCollect) && couponCollect.contains(merchantCouponVO.getCouponId()) //NOSONAR
                            && Objects.equals(merchantCouponVO.getReceiveType(), CouponReceiveTypeEnum.RECEIVE.getCode())) {
                        couponVOIterator.remove();
                    }
                }

                if (!CollectionUtils.isEmpty(allMerchantCoupons)) {
                    for (MerchantCouponVO allMerchantCoupon : allMerchantCoupons) {
                        Coupon coupon = new Coupon();
                        coupon.setCouponId(allMerchantCoupon.getCouponId());
                        coupon.setSku(allMerchantCoupon.getSku());
                        coupon.setCategoryId(allMerchantCoupon.getCategoryId());
                        coupon.setActivityScope(allMerchantCoupon.getActivityScope());
                        coupon.setAgioType(allMerchantCoupon.getAgioType());
                        coupon.setVaildDate(allMerchantCoupon.getVaildDate());
                        coupon.setStartDate(allMerchantCoupon.getStartTime());
                        coupon.setName(allMerchantCoupon.getName());
                        coupon.setMoney(allMerchantCoupon.getMoney());
                        coupon.setThreshold(allMerchantCoupon.getThreshold());
                        coupon.setUseStatus(allMerchantCoupon.getUsed().intValue());
                        coupon.setReceiveStatus(CouponReceiveStatusEnum.USED.getCode());
                        coupon.setEffectiveNum(CouponEffectiveNumEnum.UNCLAIMED.getCode());
                        coupon.setId(allMerchantCoupon.getCouponId());
                        coupon.setGrouping(allMerchantCoupon.getGrouping());
                        merchantCoupons.add(coupon);
                    }
                }
            }
        }
        return merchantCoupons;
    }

    @Override
    public List<Coupon> getBatchCouponInfo(CouponDTO couponDTO) {
        if (Objects.isNull(couponDTO) || CollectionUtils.isEmpty(couponDTO.getCouponId())) {
            return null;
        }
        Long mId = couponDTO.getMId();
        if (Objects.isNull(mId)) {
            mId = RequestHolder.getMId();
        }

        //获取卡劵基本信息
        List<Long> couponIds = couponDTO.getCouponId().stream().map(e -> e.longValue()).collect(Collectors.toList());
        List<Coupon> coupons = couponMapper.batchSelectByIds(couponIds);
        if (CollectionUtils.isEmpty(coupons)) {
            return null;
        }

        return getCoupons(mId, coupons);
    }

    /**
     * @description: 封装卡劵信息
     * @author: lzh
     * @date: 2023/8/4 15:01
     * @param: [mId, coupons]
     * @return: java.util.List<net.summerfarm.mall.model.domain.Coupon>
     **/
    private List<Coupon> getCoupons(Long mId, List<Coupon> coupons) {
        //查询有效期内是否存在未使用卡劵
        Set<Integer> couponIds = coupons.stream().map(Coupon::getId).collect(Collectors.toSet());
        List<ReceiveIdCountBO> receiveIdCountBos = merchantCouponMapper.selectUnusedByReceiveIdIn(couponIds, mId);
        Map<Integer, ReceiveIdCountBO> countBOMap = receiveIdCountBos.stream().collect(Collectors.toMap(x -> x.getCouponId(), Function.identity(), (p1, p2) -> p2));

        //查询黑名单数据
        List<Long> couponIdList = coupons.stream().map(e -> e.getId().longValue()).collect(Collectors.toList());
        CouponBlackAndWhite couponBlackAndWhite = new CouponBlackAndWhite();
        //couponBlackAndWhite.setType(BlackAndWhiteTypeEnum.BLACK.getCode());
        couponBlackAndWhite.setCouponIds(couponIdList);
        List<CouponBlackAndWhite> blackAndWhiteList = couponBlackAndWhiteMapper.getAllByEntity(couponBlackAndWhite);
        Map<Long, List<CouponBlackAndWhite>> blackMap = null;
        Map<Long, List<CouponBlackAndWhite>> whiteMap = null;
        if (!CollectionUtils.isEmpty(blackAndWhiteList)) {
            Map<Integer, List<CouponBlackAndWhite>> collectCouponBlackAndWhite = blackAndWhiteList.stream().collect(Collectors.groupingBy(CouponBlackAndWhite::getType));
            if (!CollectionUtils.isEmpty(collectCouponBlackAndWhite.get(BlackAndWhiteTypeEnum.BLACK.getCode()))) {
                blackMap = collectCouponBlackAndWhite.get(BlackAndWhiteTypeEnum.BLACK.getCode()).stream().collect(Collectors.groupingBy(CouponBlackAndWhite::getCouponId));
            }
            if (!CollectionUtils.isEmpty(collectCouponBlackAndWhite.get(BlackAndWhiteTypeEnum.WHITE.getCode()))) {
                whiteMap = collectCouponBlackAndWhite.get(BlackAndWhiteTypeEnum.WHITE.getCode()).stream().collect(Collectors.groupingBy(CouponBlackAndWhite::getCouponId));
            }
        }
        //Map<Long, List<CouponBlackAndWhite>> blackAndWhiteListMap = blackAndWhiteList.stream().collect(Collectors.groupingBy(CouponBlackAndWhite::getCouponId));

        //查询卡券已领取张数-判断是否还可以领取 不能领取则返回已领取
        List<ReceiveIdCountBO> alreadyReceiveIdCountBOS = merchantCouponMapper.getUserReceiveCount(new ArrayList<>(couponIds), mId, CouponReceiveTypeEnum.RECEIVE.getCode());
        Map<Integer, ReceiveIdCountBO> alreadyCouponMap = alreadyReceiveIdCountBOS.stream().collect(Collectors.toMap(x -> x.getCouponId(), Function.identity(), (p1, p2) -> p2));

        //循环剩余的发放设置集合 -- 新改逻辑用户在不限制张数和次数情况下可以无限领取卡劵（存在未使用的需要使用了才可以领取）
        Map<Long, List<CouponBlackAndWhite>> finalBlackAndWhiteListMap = blackMap;
        Map<Long, List<CouponBlackAndWhite>> finalWhiteMap = whiteMap;
        coupons.forEach(coupon -> {
            setCouponValidPeriod(coupon);

            //兼容老逻辑 写死为未领取（现在只校验优惠劵的次数和余量、即使余量和次数用完也可以展示为待领取）
            coupon.setEffectiveNum(CouponEffectiveNumEnum.UNCLAIMED.getCode());

            // 判断是否在有效期内存在未使用的劵
            ReceiveIdCountBO receiveIdCountBO = countBOMap.get(coupon.getId());
            if (Objects.nonNull(receiveIdCountBO)) {
                coupon.setUseStatus(CommonStatus.YES.getCode());
                coupon.setReceiveStatus(CouponReceiveStatusEnum.USED.getCode());
                coupon.setEffectiveNum(CouponEffectiveNumEnum.RECEIVE.getCode());
                coupon.setStartDate(receiveIdCountBO.getStartDate());
                coupon.setVaildDate(receiveIdCountBO.getVaildDate());
            } else if (coupon.getGrantLimit() > 0 && coupon.getGrantAmount() <= 0) {
                coupon.setReceiveStatus(CouponReceiveStatusEnum.SOLD_OUT.getCode());
            } else {
                //默认为立即领取
                coupon.setReceiveStatus(CouponReceiveStatusEnum.IMMEDIATE_CLAIM.getCode());

                //是否已领取（领取数量是否超过限制）
                if (!CollectionUtils.isEmpty(alreadyCouponMap) && alreadyCouponMap.containsKey(coupon.getId()) && coupon.getQuantityClaimed() > 0) {
                    ReceiveIdCountBO alreadyCoupon = alreadyCouponMap.get(coupon.getId());
                    if (alreadyCoupon.getNum() >= coupon.getQuantityClaimed()) {
                        coupon.setReceiveStatus(CouponReceiveStatusEnum.ALREADY_RECEIVED.getCode());
                    }
                }
            }

            //商品范围：根据后台选择的商品范围
            if ((StringUtils.isNotBlank(coupon.getSku()) && !CollectionUtils.isEmpty(JSON.parseObject(coupon.getSku(),
                    new TypeReference<Map<String, String>>() {})))  || (!CollectionUtils.isEmpty(finalWhiteMap) &&
                    !CollectionUtils.isEmpty(finalWhiteMap.get(coupon.getId().longValue())))) {
                //指定商品
                coupon.setSkuScope(CouponSkuScopeEnum.PARTIAL_SKU.getCode());
            } else if (StringUtils.isNotBlank(coupon.getCategoryId()) && !CollectionUtils.isEmpty(JSON.parseObject(coupon.getCategoryId(), new TypeReference<Map<String, String>>() {}))) {
                //指定类目 区分有无黑名单
                coupon.setSkuScope(CouponSkuScopeEnum.PARTIAL_CATEGORY.getCode());
                if (!CollectionUtil.isEmpty(finalBlackAndWhiteListMap) && !CollectionUtils.isEmpty(finalBlackAndWhiteListMap.get(coupon.getId().longValue()))) {
                    coupon.setSkuScope(CouponSkuScopeEnum.PARTIAL_CATEGORY_EXCEPTIONALITY.getCode());
                }
            } else {
                //全部商品 区分有无黑名单
                coupon.setSkuScope(CouponSkuScopeEnum.ALL.getCode());
                if (!CollectionUtil.isEmpty(finalBlackAndWhiteListMap) && !CollectionUtils.isEmpty(finalBlackAndWhiteListMap.get(coupon.getId().longValue()))) {
                    coupon.setSkuScope(CouponSkuScopeEnum.ALL_EXCEPTIONALITY.getCode());
                }
            }


            coupon.setCouponId(coupon.getId());
        });
        coupons = coupons.stream().filter(x -> !x.getVaildDate().isBefore(LocalDateTime.now())).collect(
                Collectors.toList());
        return coupons;
    }
}
