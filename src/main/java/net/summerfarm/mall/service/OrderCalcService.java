package net.summerfarm.mall.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.model.dto.order.UserContextParam;
import net.summerfarm.mall.model.vo.order.OrderResultVO;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.model.vo.price.TakeActualPriceVO;

import java.util.List;

public interface OrderCalcService {

    /**
     * 计算到手价（原有方法，依赖RequestHolder获取用户信息）
     * @param placeOrderVO 计算到手价VO
     * @return 计算结果
     */
    List<TakeActualPriceVO> takePriceHandler(PlaceOrderVO placeOrderVO);

    /**
     * 计算到手价（新方法，通过参数传入用户上下文信息，便于单元测试）
     * @param placeOrderVO 计算到手价VO
     * @param userContext 用户上下文参数
     * @return 计算结果
     */
    List<TakeActualPriceVO> takePriceHandler(PlaceOrderVO placeOrderVO, UserContextParam userContext);
}
