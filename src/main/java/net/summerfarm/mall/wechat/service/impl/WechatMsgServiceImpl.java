package net.summerfarm.mall.wechat.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.common.util.JsonUtil;
import com.aliyuncs.utils.StringUtils;
import com.cosfo.message.client.resp.MsgSendLogResp;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.enums.MerchantEnum;
import net.summerfarm.mall.mapper.AreaMapper;
import net.summerfarm.mall.mapper.MerchantCouponMapper;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.mapper.MerchantSubAccountMapper;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.domain.MerchantSubAccount;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.MerchantCouponVO;
import net.summerfarm.mall.model.vo.merchant.subaccount.MerchantSubAccountQuery;
import net.summerfarm.mall.repository.MerchantSubAccountRepository;
import net.summerfarm.mall.task.AsyncTaskService;
import net.summerfarm.mall.wechat.model.TemplateMsgResponse;
import net.summerfarm.mall.wechat.service.WeChatService;
import net.summerfarm.mall.wechat.service.WechatMsgService;
import net.summerfarm.mall.wechat.templatemessage.*;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Package: net.summerfarm.wechat.service.impl
 * @Description: 微信消息
 * @author: <EMAIL>
 * @Date: 2016/9/30
 */
@Service
@Slf4j
public class WechatMsgServiceImpl implements WechatMsgService {

    private static final Logger logger = LoggerFactory.getLogger(WechatMsgServiceImpl.class);

    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private WeChatService weChatService;
    @Resource
    private TemplateMsgSender templateMsgSender;
    @Resource
    private AreaMapper areaMapper;

    @Resource
    private MerchantCouponMapper merchantCouponMapper;
    @Resource
    private MerchantSubAccountMapper merchantSubAccountMapper;
    @Resource
    @Lazy
    AsyncTaskService asyncTaskService;
    @Resource
    MerchantSubAccountRepository merchantSubAccountRepository;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public String registerApprovedMsg(Long mId) {
        logger.info("接收到门店审核消息 门店 {}", mId);
        //查询出要发送信息用户的相关信息
        Map selectKeys = new HashMap();
        selectKeys.put("mId", mId);
        Merchant merchant = merchantMapper.selectOne(selectKeys);
        if (merchant == null) {
            log.error("门店信息不存在。mid：{}", mId);
            return "FAIL";
        }
        logger.info("接收到门店审核消息 门店 {}", JSON.toJSONString(merchant));

        //审核不通过 不发企业微信
        if (merchant.getIslock() != 0 || !MerchantEnum.OperateStatusEnum.NORMAL.getCode().equals(merchant.getOperateStatus())) {
            log.error("审核未通过，mid：{}", mId);
            return "FAIL";
        }
        logger.info("门店审核通过开始发送消息，门店 {}", JSON.toJSONString(merchant));

        //发企业微信的信息
        asyncTaskService.sendEnterpriseWxMsg(mId);
        weChatService.afterRegister(merchant);
        MsgSendLogResp result = templateMsgSender.sendTemplateMsg(RegisterApprovedMsg.templateMessage(merchant.getOpenid(), merchant.getPhone()));

        //发送mq到crm消息通知
        if (!ObjectUtils.isEmpty(result) && result.getSendStatus() == 0) {
            return "SUCCESS";
        }
        return "FAIL";
    }

    @Override
    public void areaStatusNotice(Integer areaNo, String notifyTitle, String notifyContent, String notifyRemake) {
        List<Merchant> merchants = merchantMapper.selectMerchantByArea(areaNo);
        for (Merchant merchant : merchants) {
            String message = DeliveryNoticeMsg.templateAreaMessage(merchant.getOpenid(), notifyTitle, notifyContent, notifyRemake);
            templateMsgSender.sendTemplateMsg(message);
        }
    }

    @Override
    public void deliveryPlanFinish(Integer mId, String orderNo, String msg) {
        Merchant merchant = merchantMapper.selectOneByMid(Long.valueOf(mId));
        if (ObjectUtils.isEmpty(merchant)) {
            return;
        }
        String openid = merchant.getOpenid();
        String message = DeliveryNoticeMsg.templateDeliveryFinishMessage(openid, orderNo, msg);
        log.info("配送完成通知：{}", JSON.toJSONString(message));
        templateMsgSender.sendTemplateMsg(message);
    }

    @Override
    public void couponSendNoticeConsumer(Integer merchantCouponId) {
        if (Objects.isNull(merchantCouponId)) {
            return;
        }

        MerchantCouponVO merchantCouponVO = merchantCouponMapper.selectUnusedById(merchantCouponId);
        if (Objects.isNull(merchantCouponVO)) {
            return;
        }

        Merchant merchant = merchantMapper.selectOneByMid(merchantCouponVO.getmId());
        if (Objects.isNull(merchant)) {
            return;
        }
        String openid = merchant.getOpenid();
        String message = CouponSendMsg.templateMessage(openid, merchantCouponVO);
        templateMsgSender.sendTemplateMsg(message);
    }

    @Override
    public void deliveryPlanShortFinish(Integer mId, String orderNo, String shortSku, String shortSkuMsg, String orderTime) {
        Merchant merchant = merchantMapper.selectOneByMid(Long.valueOf(mId));
        String openid = merchant.getOpenid();
        String message = DeliveryNoticeMsg.templateDeliveryShortFinishMessage(openid, orderNo, shortSku, shortSkuMsg, orderTime);
        templateMsgSender.sendTemplateMsg(message);
    }

    @Override
    public void templateDeliveryDelayMessage(Integer mId, String orderNo, String deliveryTime, String msg) {
        Merchant merchant = merchantMapper.selectOneByMid(Long.valueOf(mId));
        String openid = merchant.getOpenid();
        String message = DeliveryNoticeMsg.templateDeliveryDelayMessage(openid, orderNo, deliveryTime, msg);
        templateMsgSender.sendTemplateMsg(message);
    }

    @Override
    public void templateDeliveryLockMessage(Integer mId, String orderNo, String orderTime, String msg) {
        Merchant merchant = merchantMapper.selectOneByMid(Long.valueOf(mId));
        String openid = merchant.getOpenid();
        String message = DeliveryNoticeMsg.templateDeliveryLockMessage(openid, orderNo, orderTime, msg);
        templateMsgSender.sendTemplateMsg(message);
    }

    @Override
    public void subAccountFeedBack(Long accountId, Long followId, String templateId, String feedTime) {
        MerchantSubAccountQuery query = new MerchantSubAccountQuery();
        query.setAccountId(accountId);
        MerchantSubAccount merchantSubAccount = merchantSubAccountRepository.selectOne(query);
        if (merchantSubAccount ==null || StringUtils.isEmpty(merchantSubAccount.getOpenid())) {
            logger.info("子账号回访查询账号信息 {}", merchantSubAccount== null? "" :JsonUtil.toJson(merchantSubAccount));
            return;
        }
        logger.info("子账号回访查询账号信息 {}", JsonUtil.toJson(merchantSubAccount));
        String message = DeliveryNoticeMsg.templateFeedBackMessage(followId, templateId, merchantSubAccount.getOpenid(), feedTime);
        templateMsgSender.sendTemplateMsg(message);
    }

    @Override
    public void stockOutWeChatMessage(Orders orders, String shortSkuName, String shortSkuAmount, BigDecimal actualTotalPrice) {
        Merchant merchant = merchantMapper.selectOneByMid(orders.getmId());
        String openid = merchant.getOpenid();
        String message = StockOutNoticeMsg.templateMessage(openid, orders, shortSkuName, shortSkuAmount, actualTotalPrice);
        if (Objects.equals(merchant.getBusinessLine(), MerchantEnum.BusinessLineEnum.POP.getCode())) {
            templateMsgSender.sendTemplateMsgV2(message, WxOfficialAccountsChannelEnum.POP_MALL.channelCode);
        } else {
            templateMsgSender.sendTemplateMsgV2(message, WxOfficialAccountsChannelEnum.XM_MALL.channelCode);
        }
    }
}
