package net.summerfarm.mall.wechat.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.Conf;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.domain.PurchasesConfig;
import net.summerfarm.mall.model.vo.WxAccountInfoVO;
import net.summerfarm.mall.task.AsyncTaskService;
import net.summerfarm.mall.wechat.api.constant.ApiConsts;
import net.summerfarm.mall.wechat.service.WeChatPayService;
import net.summerfarm.mall.wechat.utils.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

/**
 * @Package: net.summerfarm.wechat.service.impl
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/10/8
 */
@Service
public class WeChatPayServiceImpl implements WeChatPayService {

    private static final Logger logger = LoggerFactory.getLogger(WeChatPayService.class);

    @Resource
    @Lazy
    private AsyncTaskService asyncTaskService;

    //处理库存
    @Override
    public void asyncTaskStock(List<String> skus) {

        PurchasesConfig purchasesConfig = new PurchasesConfig();
        purchasesConfig.setAreaNo(RequestHolder.getMerchantArea().getAreaNo());
        purchasesConfig.setSkus(skus);
        asyncTaskService.purchasesArrival(purchasesConfig);
    }

}
