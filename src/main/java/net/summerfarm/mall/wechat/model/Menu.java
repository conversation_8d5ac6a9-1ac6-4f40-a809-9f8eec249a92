package net.summerfarm.mall.wechat.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import net.summerfarm.common.exceptions.DefaultServiceException;

import java.util.List;

/**
 * @Package: net.summerfarm.mall.common.wechat.model
 * @Description: 公众号自定义菜单
 * @author: <EMAIL>
 * @Date: 2017/5/27
 */
@JsonInclude(JsonInclude.Include.NON_NULL) //转json时属性为null不转换
public class Menu {

    //最多三个一级菜单
    private List<MenuButton> button;

    private Matchrule matchrule;

    /**
     * 菜单ID，查询时会返回，删除个性化菜单时会用到
     *
     * @since 1.3.7
     */
    @JSONField(name = "menuid")
    private String menuId;

    public List<MenuButton> getButton() {
        return button;
    }

    public void setButton(List<MenuButton> button) {
        if (null == button || button.size() > 3) {
            throw new DefaultServiceException(0,"主菜单最多3个");
        }
        this.button = button;
    }

    public Matchrule getMatchrule() {
        return matchrule;
    }

    public void setMatchrule(Matchrule matchrule) {
        this.matchrule = matchrule;
    }

    public String getMenuId() {
        return menuId;
    }

    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }
}
