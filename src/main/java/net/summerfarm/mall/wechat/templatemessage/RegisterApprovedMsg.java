package net.summerfarm.mall.wechat.templatemessage;

import com.alibaba.fastjson.JSON;
import net.summerfarm.mall.Conf;
import net.summerfarm.mall.common.util.WeChatUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

/**
 * @Package: net.summerfarm.wechat.templatemessage
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/4/9
 */
public class RegisterApprovedMsg implements TemplateMsg {

    public static String templateMessage(String openId, String mphone) {
        WxTemplate temp = new WxTemplate();
        temp.setUrl(WeChatUtils.getWeChatCode(Conf.REGISTER_SUCCESS_URL));
        temp.setTouser(openId);
        temp.setTopcolor("#000000");
//        temp.setTemplate_id("IeG1tDOIrjQKEH5S0x_JpWhviDcs_U5IOQEMSeWtf0Y");// 注册审核成功通知模板
        temp.setTemplate_id("Q5z0ZoGEvNIxzQb1FTFfD-VAY8IiAXlePQ_Pea3lFZY");// 注册审核成功通知模板
        Map<String,TemplateData> m = new HashMap<String,TemplateData>();
        TemplateData first = new TemplateData();
        first.setColor("#000000");
        first.setValue("尊敬的用户，你的注册申请已经通过审核!");
        m.put("first", first);
        TemplateData phone = new TemplateData();
        phone.setColor("#0033FF");
        phone.setValue(mphone);
        m.put("keyword1", phone);
        TemplateData time= new TemplateData();
        time.setColor("#000000");
        time.setValue(LocalDateTime.now().toString());
        m.put("keyword2", time);

        TemplateData remark = new TemplateData();
        remark.setColor("#000000");
        //临时需求
        if (LocalDateTime.now().isBefore(LocalDateTime.of(LocalDate.of(2021,4,1), LocalTime.MIN))){
            remark.setValue("感谢您的注册，点击进入鲜沐商城进行采购，更有奶茶小料新品等您品鉴~");
        } else {
            remark.setValue("感谢您的注册，点击进入鲜沐商城进行采购。");
        }
        m.put("remark", remark);
        temp.setData(m);

        // 消息对象转JSON串
        return JSON.toJSONString(temp);
    }
}
