package net.summerfarm.mall.controller.frequentSkuPool;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.FrequentSkuPoolEnums;
import net.summerfarm.mall.enums.PopupTypeEnum;
import net.summerfarm.mall.model.input.frequentSkuPool.*;
import net.summerfarm.mall.model.vo.FrontCategoryVO;
import net.summerfarm.mall.model.vo.MerchantFrequentlyConfigVO;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.model.vo.frequentSkuPool.BusinessFormatListVO;
import net.summerfarm.mall.model.vo.frequentSkuPool.FrequentSkuInShoppingCartVO;
import net.summerfarm.mall.model.vo.frequentSkuPool.FrequentSkuPoolVO;
import net.summerfarm.mall.service.FrequentSkuPoolService;
import net.summerfarm.mall.service.helper.MerchantPopupHelper;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 常购清单接口
 * <AUTHOR>
 * @Date 2025/5/16 15:33
 * @Version 1.0
 */
@Slf4j
@RestController
public class FrequentSkuPoolController {

    @Resource
    private FrequentSkuPoolService frequentSkuPoolService;

    @Resource
    private MerchantPopupHelper merchantPopupHelper;

    /**
     * 添加常购商品
     * @param input 添加常购商品入参
     * @return CommonResult
     */
    @PostMapping("/frequent-sku-pool/update/add")
    @RequiresAuthority
    public CommonResult<Void> addFrequentSkuPool(@RequestBody FrequentSkuPoolAddInput input) {
        frequentSkuPoolService.addData(RequestHolder.getMId(), input.getSkuList(), input.getMasterOrderNo(), input.getSource());
        return CommonResult.ok();
    }

    /**
     * 删除常购商品
     * @param input 删除常购商品入参
     * @return CommonResult
     */
    @PostMapping("/frequent-sku-pool/update/delete")
    @RequiresAuthority
    public CommonResult<Void> deleteFrequentSkuPool(@RequestBody FrequentSkuPoolRemoveInput input) {
        frequentSkuPoolService.deleteData(RequestHolder.getMId(), input.getSku());
        return CommonResult.ok();
    }

    /**
     * 置顶常购商品
     * @param input 置顶常购商品入参
     * @return CommonResult
     */
    @PostMapping("/frequent-sku-pool/update/top")
    @RequiresAuthority
    public CommonResult<Void> topSku(@RequestBody FrequentSkuPoolTopInput input) {
        frequentSkuPoolService.updateTopSku(RequestHolder.getMId(), input.getSku(), FrequentSkuPoolEnums.Top.TOP);
        return CommonResult.ok();
    }

    /**
     * 取消置顶常购商品
     * @param input 取消置顶常购商品入参
     * @return CommonResult
     */
    @PostMapping("/frequent-sku-pool/update/cancelTop")
    @RequiresAuthority
    public CommonResult<Void> cancelTopSku(@RequestBody FrequentSkuPoolTopInput input) {
        frequentSkuPoolService.updateTopSku(RequestHolder.getMId(), input.getSku(), FrequentSkuPoolEnums.Top.UN_TOP);
        return CommonResult.ok();
    }

    /**
     * 常购清单列表
     * @param input 常购商品分页入参
     * @return CommonResult
     */
    @PostMapping("/frequent-sku-pool/query/page")
    @RequiresAuthority
    public CommonResult<PageInfo<FrequentSkuPoolVO>> pageFrequentSkuPool(@RequestBody FrequentSkuPoolPageInput input) {
        return CommonResult.ok(frequentSkuPoolService.pageFrequentSkuPool(input));
    }

    /**
     * 常购清单弹窗推荐
     * @return CommonResult
     */
    @PostMapping("/frequent-sku-pool/query/recommend")
    @RequiresAuthority
    public CommonResult<List<FrequentSkuPoolVO>> recommendFrequentSkuPool() {
        return CommonResult.ok(frequentSkuPoolService.recommendFrequentSkuPool());
    }

    /**
     * 常购清单列表类目信息
     * @return CommonResult
     */
    @PostMapping("/frequent-sku-pool/query/category")
    @RequiresAuthority
    public CommonResult<List<FrontCategoryVO>> frequentSkuPoolCategory() {
        return CommonResult.ok(frequentSkuPoolService.frequentSkuPoolCategory());
    }

    /**
     * 常购清单消息提醒查询
     * @return CommonResult
     */
    @PostMapping("/frequent-sku-pool/query/config")
    @RequiresAuthority
    public CommonResult<MerchantFrequentlyConfigVO> queryMerchantFrequentlyConfig() {
        return CommonResult.ok(frequentSkuPoolService.queryMerchantFrequentlyConfig());
    }

    /**
     * 常购清单消息提醒修改
     * @return CommonResult
     */
    @PostMapping("/frequent-sku-pool/update/config")
    @RequiresAuthority
    public CommonResult<Boolean> updateMerchantFrequentlyConfig(@RequestBody MerchantFrequentlyConfigUpdateInput input) {
        return CommonResult.ok(frequentSkuPoolService.updateMerchantFrequentlyConfig(input));
    }

    /**
     * 业态榜单top20查询接口
     * @return CommonResult
     */
    @PostMapping("/frequent-sku-pool/query/business_format_list")
    @RequiresAuthority
    public CommonResult<List<BusinessFormatListVO>> businessFormatList() {
        return CommonResult.ok(frequentSkuPoolService.businessRankingList());
    }


    /**
     * 是否需要引导 true 需要引导 false 不需要引导
     * @return CommonResult
     */
    @PostMapping("/frequent-sku-pool/query/need_guide")
    @RequiresAuthority
    public CommonResult<Boolean> needGuide() {
        return CommonResult.ok(merchantPopupHelper.needPopup(RequestHolder.getMId(), PopupTypeEnum.MERCHANT_FREQUENTLY_BUYING_SKU_FIRST_TIME_GUIDE));
    }

    /**
     * 查询在常购清单中的购物车商品
     * @return CommonResult
     */
    @PostMapping("/frequent-sku-pool/query/sku_list_in_shopping_cart")
    @RequiresAuthority
    public CommonResult<FrequentSkuInShoppingCartVO> querySkuListInShoppingCart() {
        return CommonResult.ok(frequentSkuPoolService.querySkuListInShoppingCart());
    }

    /**
     * 取消推荐常购商品 - 用于 用户点击「推荐弹窗」上的取消按钮
     * @param input 取消推荐常购商品入参
     * @return CommonResult
     */
    @PostMapping("/frequent-sku-pool/update/cancelRecommend")
    @RequiresAuthority
    public CommonResult<Void> cancelRecommendFrequentSkuPool(@RequestBody FrequentSkuPoolCancelRecommendInput input) {
        try {
            frequentSkuPoolService.cancelRecommendData(RequestHolder.getMId(), input.getSkuList());
        } catch (Exception e) {
            log.error("采购助手取消推荐数据失败  >>> {}", JSON.toJSONString(input), e);
            // 这里为了避免过多信息打扰用户，不把失败信息返回给前端
            return CommonResult.ok();
        }
        return CommonResult.ok();
    }

}
