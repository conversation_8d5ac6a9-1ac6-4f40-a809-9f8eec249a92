package net.summerfarm.mall.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.model.dto.price.BatchTakePriceRequest;
import net.summerfarm.mall.model.dto.price.BatchTakePriceResponse;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.model.vo.price.TakeActualPriceVO;
import net.summerfarm.mall.service.BatchPriceQueryService;
import net.summerfarm.mall.service.OrderCalcService;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "价格查询接口")
@RestController()
@RequestMapping("/price")
public class PriceController {

    @Resource
    private OrderCalcService orderCalcService;

    @Resource
    private BatchPriceQueryService batchPriceQueryService;


    /**
     * 获取到手价-非购物车勾选不需要均摊
     * @return
     */
    @RequiresAuthority
    @PostMapping(value = "/query/take-actual-price")
    public CommonResult<List<TakeActualPriceVO>> takeActualPrice(@RequestBody PlaceOrderVO placeOrderVO) {
        //获取到手价明细标记，此标记为true代表获取明细并且不均摊优惠
        placeOrderVO.setTakePriceFlag(Boolean.TRUE);
        return CommonResult.ok(orderCalcService.takePriceHandler(placeOrderVO));
    }

    /**
     * 获取到手价-需要均摊
     * @return
     */
    @RequiresAuthority
    @PostMapping(value = "/query/take-allocation-price")
    public CommonResult<List<TakeActualPriceVO>> takeAllocationPrice(@RequestBody PlaceOrderVO placeOrderVO) {
        //获取到手价明细标记，此标记为FALSE代表不获取明细并且均摊优惠
        placeOrderVO.setTakePriceFlag(Boolean.FALSE);
        return CommonResult.ok(orderCalcService.takePriceHandler(placeOrderVO));
    }

    /**
     * 批量查询SKU到手价（无需登录，通过签名验证）
     *
     * @param request 查询请求
     * @return 查询结果
     */
    @ApiOperation(value = "批量查询SKU到手价", notes = "通过门店ID和SKU列表查询到手价，无需登录，使用签名验证")
    @PostMapping(value = "/batch/take-actual-price")
    public CommonResult<BatchTakePriceResponse> batchTakeActualPrice(@Validated @RequestBody BatchTakePriceRequest request) {

        // 1. 验证签名
        if (!batchPriceQueryService.verifySignature(request)) {
            return CommonResult.fail(net.xianmu.common.result.ResultStatusEnum.BAD_REQUEST, "签名验证失败");
        }

        // 防止太多SKU：
        if (CollectionUtils.isNotEmpty(request.getSkuList()) && request.getSkuList().size() > 20) {
            return CommonResult.fail(net.xianmu.common.result.ResultStatusEnum.BAD_REQUEST, "SKU数量超过限制:20");
        }

        // 2. 执行查询
        BatchTakePriceResponse response = batchPriceQueryService.batchQueryTakePrice(request);

        return CommonResult.ok(response);
    }

}
