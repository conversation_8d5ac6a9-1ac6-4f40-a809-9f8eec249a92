package net.summerfarm.mall.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.datacollect.DataBuryPoint;
import net.summerfarm.mall.common.datacollect.UnCollectByAspect;
import net.summerfarm.mall.common.util.IPUtil;
import net.summerfarm.mall.common.util.IpWhiteListUtil;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.constant.CommonRedisKey;
import net.summerfarm.mall.mapper.OrderDeliveryRecordMapper;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.input.CalcAfterSaleCouponInput;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.TimingAutoRefundVO;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.helper.AfterSaleOrderHelper;
import net.xianmu.common.result.CommonResult;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.jdom.JDOMException;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Package: net.summerfarm.trade.controller
 * @Description: 售后
 * @author: <EMAIL>
 * @Date: 2017/7/3
 */
@Api(tags = "售后控制类")
@Slf4j
@RestController
@RequestMapping(value = "/after-sale")
public class AfterSaleOrderController {

    private static final Logger logger = LoggerFactory.getLogger(AfterSaleOrderController.class);
    @Resource
    private OrderDeliveryRecordMapper orderDeliveryRecordMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private AfterSaleOrderHelper afterSaleOrderHelper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 分页查询
     *
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "售后分页查询", httpMethod = "GET")
    @RequiresAuthority
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页数", paramType = "path", defaultValue = "1", required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path", defaultValue = "10", required = true)
    })
    @RequestMapping(value = "/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult findPage(@PathVariable int pageIndex, @PathVariable int pageSize) {//先按订单和商品名称分组，后再查询
        return afterSaleOrderService.findPage(pageIndex, pageSize);
    }


    /**
     * 详情
     *
     * @return
     */
    @ApiOperation(value = "售后订单详情", httpMethod = "GET", response = AfterSaleOrderVO.class)
    @RequiresAuthority
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderNo", value = "订单编号", paramType = "path", defaultValue = "01153449744152425", required = true),
            @ApiImplicitParam(name = "sku", value = "sku编号", paramType = "path", defaultValue = "668330443", required = true)
    })
    @RequestMapping(value = "/order", method = RequestMethod.GET)
    public AjaxResult findAfterSaleOrder(String orderNo, String sku, int suitId,Integer type) {
        return afterSaleOrderService.findAfterSaleOrder(orderNo, sku, suitId, type);
    }

    /**
     * 查看值
     *
     * @return
     */
    @RequiresAuthority
    @RequestMapping(value = "/countview", method = RequestMethod.GET)
    public AjaxResult countView() {
        return afterSaleOrderService.countView();
    }

    /**
     * 售后页面
     *
     * @param orderNo
     * @param sku
     * @return
     */
    @ApiOperation(value = "查询即将售后的订单信息", httpMethod = "GET", response = AfterSaleOrderVO.class)
    @RequiresAuthority
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderNo", value = "订单编号", paramType = "query", required = true),
            @ApiImplicitParam(name = "sku", value = "sku编号", paramType = "query", required = true)
    })
    @RequestMapping(value = "/pre", method = RequestMethod.GET)
    public AjaxResult pre(String orderNo, String sku,@RequestParam(required = false)Integer deliveryId) {
        return afterSaleOrderService.pre(orderNo, sku, RequestHolder.getMId(),deliveryId);
    }

    /**
     * 新增售后
     *
     * @param afterSaleOrderVO
     * @return
     */
    @ApiOperation(value = "新增售后订单", httpMethod = "POST")
    @RequiresAuthority
    @RequestMapping(method = RequestMethod.POST)
    @XmLock(prefixKey = CommonRedisKey.AfterSaleLock.AFTER_SALE, key = "{afterSaleOrderVO.orderNo}")
    public AjaxResult save( AfterSaleOrderVO afterSaleOrderVO) throws InterruptedException {
        log.info("商城发起售后的数据:"+JSON.toJSONString(afterSaleOrderVO));
        return afterSaleOrderService.newSave(afterSaleOrderVO);
    }

    /**
     * 取消售后
     *
     * @param afterSaleOrderNo
     * @return
     */
    @ApiOperation(value = "申请取消售后", httpMethod = "DELETE")
    @ApiImplicitParam(name = "afterSaleOrderNo", value = "售后订单编号", paramType = "path", required = true)
    @RequiresAuthority
    @RequestMapping(value = "/{afterSaleOrderNo}", method = RequestMethod.DELETE)
    public AjaxResult cancel(@PathVariable String afterSaleOrderNo) {
        return afterSaleOrderService.cancel(afterSaleOrderNo);
    }


    /**
     * 更新凭证
     *
     * @return
     */
    @ApiOperation(value = "申请取消售后", httpMethod = "DELETE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "afterSaleOrderNo", value = "售后订单编号", paramType = "path", required = true),
            @ApiImplicitParam(name = "proofPic", value = "新的凭证", paramType = "query", required = true),
            @ApiImplicitParam(name = "applyRemark", value = "备注", paramType = "query")
    })
    @RequiresAuthority
    @RequestMapping(value = "/proof/{afterSaleOrderNo}", method = RequestMethod.POST)
    public AjaxResult updatePicProof(@PathVariable String afterSaleOrderNo, String proofPic, String applyRemark) {
        return afterSaleOrderService.updatePicProof(afterSaleOrderNo, proofPic, applyRemark);
    }

    /**
     * 省心送订单可以已到货售后的配送计划
     */
    @ApiOperation(value = "省心送订单可以已到货售后的配送计划", httpMethod = "GET")
    @RequestMapping(value = "/deliveryPlan", method = RequestMethod.GET)
    public AjaxResult afterOrderDeliveryPlan(String orderNo){
        return afterSaleOrderService.afterOrderDeliveryPlan(orderNo);
    }

    /**
     * 订单售后
     */
    @ApiOperation(value = "可以售后的订单", httpMethod = "GET")
    @RequestMapping(value = "/orderVOs", method = RequestMethod.GET)
    public AjaxResult afterOrder(){
        return afterSaleOrderService.afterSaleOrder();
    }

    @ApiOperation(value = "是否有售后期内的订单", httpMethod = "GET")
    @RequestMapping(value = "/exitAfterOrder", method = RequestMethod.GET)
    @RequiresAuthority
    public AjaxResult afterSaleOrderIsExit(){
        return afterSaleOrderService.afterSaleOrderNumber();
    }


    /**
     * 获取可发起已到货/未到货售后
     * @param orderNo
     * @param isManage
     * @return
     */
    @RequestMapping(value = "/deliveryStatus", method = RequestMethod.GET)
    public AjaxResult deliveryStatus(String orderNo,Boolean isManage,Integer deliveryId, String sku){
        return afterSaleOrderService.deliveryStatus(orderNo,isManage,deliveryId, sku);
    }


    /**
     * 获取可售后服务类型
     * @param orderNo
     * @param sku
     * @param deliveryed
     * @param isManage
     * @return
     */
    @RequestMapping(value = "/getHandleType", method = RequestMethod.GET)
    public AjaxResult getHandleType( String orderNo, String sku, Integer deliveryed, Boolean isManage){
        return afterSaleOrderService.getHandleType(orderNo,sku,deliveryed,isManage);
    }

    /**
     * 计算最大可售后数量
     */
    @RequestMapping(value = "/calculateQuantity", method = RequestMethod.POST)
    public AjaxResult calculateQuantity(@RequestBody AfterSaleOrderVO afterSaleOrderVO){
        logger.info("获取最大售后数量参数【商城发起】:"+JSON.toJSONString(afterSaleOrderVO));
        return afterSaleOrderService.getMaxQuantity(afterSaleOrderVO);
    }

    /**
     * 获取最大可售后金额
     * @param afterSaleOrderVO
     * @return
     */
    @RequestMapping(value = "/afterSaleMoney", method = RequestMethod.POST)
    public AjaxResult afterSaleMoney(@RequestBody AfterSaleOrderVO afterSaleOrderVO){
        logger.info("获取最大售后金额参数【商城发起】:"+JSON.toJSONString(afterSaleOrderVO));
        return afterSaleOrderService.getAfterSaleMoney(afterSaleOrderVO);
    }

    /**
     * 提交售后前校验运费扣减弹窗
     * @param afterSaleOrderVO
     * @return
     */
    @RequestMapping(value = "/query/check-delivery-fee", method = RequestMethod.POST)
    public CommonResult checkDeliveryFee(@RequestBody AfterSaleOrderVO afterSaleOrderVO){
        logger.info("提交售后前校验运费扣减弹窗【商城发起】:"+JSON.toJSONString(afterSaleOrderVO));
        return afterSaleOrderService.checkDeliveryFee(afterSaleOrderVO);
    }


    @PostMapping("/query/return-shipping")
    public AjaxResult queryShipping(@RequestBody AfterSaleOrderVO afterSaleOrderVO){
        logger.info("参数after："+JSON.toJSONString(afterSaleOrderVO));
        Orders orders = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        if (orders.getDeliveryFee() == null || orders.getDeliveryFee().compareTo(BigDecimal.ZERO)==0){
            return AjaxResult.getOK(-1);
        }
        //判断是否使用运费券
        BigDecimal couponDeliveryFee = orderDeliveryRecordMapper.selectCouponDeliveryFeeByOrderNo(afterSaleOrderVO.getOrderNo());
        if (couponDeliveryFee.compareTo(orders.getDeliveryFee()) >= 0){
            return AjaxResult.getOK(-1);
        }
        BigDecimal bigDecimal = afterSaleOrderHelper.fareMoney(afterSaleOrderVO);
        return AjaxResult.getOK(bigDecimal);
    }


    /**
     * 省心送自动售后
     * @param timingAutoRefundVO
     * @return
     */
    @PostMapping(value = "/upsert/timing_auto_refund")
    public void getOrderDelivery(@RequestBody TimingAutoRefundVO timingAutoRefundVO) {
        log.info("省心送自动售后参数:{}",timingAutoRefundVO.getOrderNo());
         afterSaleOrderService.timingAutoRefund(timingAutoRefundVO.getOrderNo());
    }
}
