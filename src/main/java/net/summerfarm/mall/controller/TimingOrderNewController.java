package net.summerfarm.mall.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.RedissonLockKey;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.input.timing.BatchPlaceTimingOrderInput;
import net.summerfarm.mall.model.input.timing.BatchPreTimingOrderInput;
import net.summerfarm.mall.model.vo.timing.PlaceTimingOrderResultVO;
import net.summerfarm.mall.model.vo.timing.TimingPreOrderVO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

/**
 * 省心送下单V2
 * <AUTHOR>
 * @Date 2025/7/3 11:26
 * @Version 1.0
 */

@Api(tags = "省心送订单（新）")
@Slf4j
@RestController
public class TimingOrderNewController {

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 省心送批量预下单接口
     * @param input 省心送批量预下单 - 传参
     * @return 预下单结果
     */
    @RequiresAuthority
    @ApiOperation(value="", httpMethod = "POST")
    @PostMapping("/order/checkTimingOrderV2")
    public CommonResult<TimingPreOrderVO> checkTimingDeliveryOrder(@Validated @RequestBody BatchPreTimingOrderInput input) {
        return CommonResult.ok();
    }

    /**
     * 省心送批量下单接口
     * @param input 省心送批量下单 - 传参
     * @return 下单结果
     */
    @RequiresAuthority
    @PostMapping("/order/timingV2")
    public CommonResult<PlaceTimingOrderResultVO> placeTimingOrder(@Validated @RequestBody BatchPlaceTimingOrderInput input) {
        // 同一mId并发下单控制
        RLock redissonLock = redissonClient.getLock(RedissonLockKey.TIMING + RequestHolder.getMId());
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                throw new BizException(1, "请重新下单");
            }
            //todo 批量省心送下单

            return CommonResult.ok();
        } catch (InterruptedException e) {
            log.error("锁获取异常", e);
            throw new BizException("请重新下单");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }
}
