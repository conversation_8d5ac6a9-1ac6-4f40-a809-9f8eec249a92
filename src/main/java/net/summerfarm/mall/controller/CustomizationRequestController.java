package net.summerfarm.mall.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.domain.CustomizationRequestsVO;
import net.summerfarm.mall.model.input.CustomizationRequestsInput;
import net.summerfarm.mall.service.CustomizationRequestService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 定制需求控制器
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Slf4j
@RestController
@RequestMapping("/customization-request")
@Api(tags = "定制需求管理")
public class CustomizationRequestController {

    @Resource
    private CustomizationRequestService customizationRequestService;

    /**
     * 保存定制需求 并返回对应的复制出来的sku
     */
    @RequiresAuthority
    @PostMapping("/customization/querySkuAndSave")
    public CommonResult<Long> saveCustomizationRequest(
            @RequestBody @Validated CustomizationRequestsInput input) {
        return customizationRequestService.saveCustomizationRequest(input);
    }

    /**
     * 修改需求
     */
    @ApiOperation(value = "根据ID查询定制需求", httpMethod = "GET")
    @RequiresAuthority
    @PostMapping("/get/{id}")
    public CommonResult<CustomizationRequestsVO> getCustomizationRequestById(
            @ApiParam(value = "定制需求ID", required = true)
            @PathVariable Long id) {
        return customizationRequestService.getCustomizationRequestById(id);
    }

    /**
     * 根据订单号查询定制需求
     */
    @ApiOperation(value = "根据订单号查询定制需求", httpMethod = "GET")
    @RequiresAuthority
    @GetMapping("/get/by-order-no/{orderNo}")
    public CommonResult<CustomizationRequestsVO> getCustomizationRequestByOrderNo(
            @ApiParam(value = "订单号", required = true)
            @PathVariable String orderNo) {
        return customizationRequestService.getCustomizationRequestByOrderNo(orderNo);
    }



    /**
     * 更新定制需求状态
     */
    @ApiOperation(value = "更新定制需求状态", httpMethod = "PUT")
    @RequiresAuthority
    @PutMapping("/update-status/{id}/{status}")
    public CommonResult<Boolean> updateCustomizationRequestStatus(
            @ApiParam(value = "定制需求ID", required = true)
            @PathVariable Long id,
            @ApiParam(value = "新状态", required = true)
            @PathVariable Integer status) {
        log.info("更新定制需求状态，ID: {}, 新状态: {}", id, status);
        return customizationRequestService.updateCustomizationRequestStatus(id, status);
    }

    /**
     * 更新设计效果图
     */
    @ApiOperation(value = "更新设计效果图", httpMethod = "PUT")
    @RequiresAuthority
    @PutMapping("/update-design-image/{id}")
    public CommonResult<Boolean> updateDesignImage(
            @ApiParam(value = "定制需求ID", required = true)
            @PathVariable Long id,
            @ApiParam(value = "设计效果图URL", required = true)
            @RequestParam String designImage) {
        log.info("更新设计效果图，ID: {}", id);
        return customizationRequestService.updateDesignImage(id, designImage);
    }

    /**
     * 客户确认设计
     */
    @ApiOperation(value = "客户确认设计", httpMethod = "PUT")
    @RequiresAuthority
    @PutMapping("/confirm-design/{id}")
    public CommonResult<Boolean> confirmDesign(
            @ApiParam(value = "定制需求ID", required = true)
            @PathVariable Long id,
            @ApiParam(value = "是否通过", required = true)
            @RequestParam Boolean approved,
            @ApiParam(value = "拒绝原因（不通过时必填）")
            @RequestParam(required = false) String refuseReason) {
        log.info("客户确认设计，ID: {}, 确认结果: {}", id, approved);
        return customizationRequestService.confirmDesign(id, approved, refuseReason);
    }

    /**
     * 添加沟通记录
     */
    @ApiOperation(value = "添加沟通记录", httpMethod = "POST")
    @RequiresAuthority
    @PostMapping("/add-communication-note/{id}")
    public CommonResult<Boolean> addCommunicationNote(
            @ApiParam(value = "定制需求ID", required = true)
            @PathVariable Long id,
            @ApiParam(value = "沟通记录", required = true)
            @RequestParam String communicationNote) {
        log.info("添加沟通记录，ID: {}", id);
        return customizationRequestService.addCommunicationNote(id, communicationNote);
    }

}
