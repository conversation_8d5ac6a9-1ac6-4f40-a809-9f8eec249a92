package net.summerfarm.mall.controller;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.annotation.RequiresAuthority;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.input.CustomizationRequestEnabledInput;
import net.summerfarm.mall.model.vo.CustomizationRequestVO;
import net.summerfarm.mall.model.input.CustomizationRequestQueryInput;
import net.summerfarm.mall.model.input.CustomizationRequestInput;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.service.CustomizationRequestService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 定制需求控制器
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Slf4j
@RestController
@RequestMapping("/customization-request")
@Api(tags = "定制需求管理")
public class CustomizationRequestController {

    @Resource
    private CustomizationRequestService customizationRequestService;

    /**
     * 保存定制需求 并返回对应的复制出来的sku
     */
    @RequiresAuthority
    @PostMapping("/querySkuAndSave")
    public CommonResult<List<ProductInfoVO>> querySkuAndSave(@RequestBody @Validated CustomizationRequestInput input) {
        return CommonResult.ok (customizationRequestService.querySkuAndSave(input));
    }

    /**
     * 根据订单号查询定制需求
     */
    @RequiresAuthority
    @PostMapping("/query/by-order-no")
    public CommonResult<CustomizationRequestVO> getCustomizationRequestByOrderNo(@RequestBody @Validated CustomizationRequestQueryInput input) {
        return CommonResult.ok (customizationRequestService.getCustomizationRequestByOrderNo(input.getOrderNo ();
    }



    /**
     * 拒绝定制需求
     */
    @RequiresAuthority
    @PostMapping("/refuse")
    public CommonResult<Void> refuse(@RequestBody @Validated CustomizationRequestEnabledInput input) {
        customizationRequestService.refuse(input);
        return CommonResult.ok ();
        //        return customizationRequestService.updateDesignImage(id, designImage);
    }

    /**
     * 客户同意设计
     */
    @RequiresAuthority
    @PostMapping("/confirm")
    public CommonResult<Void> confirm(@RequestBody @Validated CustomizationRequestEnabledInput input){
        customizationRequestService.confirm(input);
        return CommonResult.ok ();
    }


}

