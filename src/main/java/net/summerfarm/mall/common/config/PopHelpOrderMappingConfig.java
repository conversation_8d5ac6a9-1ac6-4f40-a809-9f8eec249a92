package net.summerfarm.mall.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.model.dto.merchant.PopHelpOrderMerchantMappingDTO;
import org.springframework.context.annotation.Configuration;

import java.util.*;

/**
 * pop订单城配仓与鲜沐大客户门店映射关系配置
 * @author: zach
 * @Date: 2025-04-27
 */
@Configuration
@Slf4j
public class PopHelpOrderMappingConfig {
    /**
     * pop订单城配仓与鲜沐大客户门店映射关系json
     */
    @NacosValue(value = "${pop.order.merchant.mapping:[{\"popOrderStoreNo\":291,\"helperOrderMId\":289611},{\"popOrderStoreNo\":122,\"helperOrderMId\":289611}]}", autoRefreshed = true)
    private String popHelpOrderMerchantMappingJsonStr;

    public Map<Integer, Long> queryHelpOrderMerchantMapping() {
        log.info("pop订单城配仓与鲜沐大客户门店映射关系 >>> " + this.popHelpOrderMerchantMappingJsonStr);
        if (StringUtils.isBlank(this.popHelpOrderMerchantMappingJsonStr)) {
            return Collections.emptyMap();
        }
        List<PopHelpOrderMerchantMappingDTO> mappingObjs = new ArrayList<>();
        try {
            mappingObjs = JSON.parseArray(this.popHelpOrderMerchantMappingJsonStr, PopHelpOrderMerchantMappingDTO.class);
        } catch (Exception e) {
            log.error("解析pop订单城配仓与鲜沐大客户门店映射关系json异常", e);
        }
        Map<Integer, Long> resultMap = new HashMap<>();
        for (PopHelpOrderMerchantMappingDTO mappingObj : mappingObjs) {
            resultMap.put(mappingObj.getPopOrderStoreNo(), mappingObj.getHelperOrderMId());
        }
        return resultMap;
    }

    /**
     * 根据pop订单城配仓查询鲜沐大客户门店
     * @param popOrderStoreNo pop订单城配仓
     * @return 鲜沐大客户门店
     */
    public Long queryHelpOrderMIdByPopStoreNo(Integer popOrderStoreNo) {
        if (popOrderStoreNo == null) {
            return null;
        }
        Map<Integer, Long> mapping = this.queryHelpOrderMerchantMapping();
        return mapping.get(popOrderStoreNo);
    }
}
