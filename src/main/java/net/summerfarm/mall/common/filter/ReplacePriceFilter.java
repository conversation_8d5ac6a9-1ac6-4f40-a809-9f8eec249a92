package net.summerfarm.mall.common.filter;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.model.vo.MerchantSubject;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.Set;

/**
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/4/2
 */
@Component
@Order(2)
@Slf4j
public class ReplacePriceFilter extends OncePerRequestFilter {

    // 白名单，这些URL不需要进行价格替换
    private static final Set<String> WHITE_LIST_URL = new HashSet<>();

    static {
        // 批量查询SKU到手价的接口，提供给外部服务使用，不需要登录
        WHITE_LIST_URL.add("/price/batch/take-actual-price");
    }



    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        // 如果是白名单中的URL，则直接跳过价格替换逻辑
        if (WHITE_LIST_URL.contains(request.getRequestURI())) {
            filterChain.doFilter(request, response);
            return;
        }
        replacePrice(request, response, filterChain);
    }

    private void replacePrice(HttpServletRequest servletRequest, HttpServletResponse servletResponse, FilterChain filterChain) throws ServletException,IOException {
        WrapperResponse wrapperResponse = new WrapperResponse(servletResponse);

        filterChain.doFilter(servletRequest,wrapperResponse);

        if (!wrapperResponse.isCommitted()) {
            byte[] content = wrapperResponse.getContent();
            Object merchantSubjectAttr = servletRequest.getAttribute(Global.MERCHANT_SUBJECT);
            MerchantSubject merchantSubject = null;

            if(merchantSubjectAttr != null){
                merchantSubject = (MerchantSubject) merchantSubjectAttr;
                servletRequest.setAttribute(Global.MERCHANT_SUBJECT, null);
            }else {
                HttpServletRequest request = (HttpServletRequest) servletRequest;
                String token = request.getHeader("token");
                if(StringUtils.isEmpty(token)){
                    token = RequestHolder.getToken(request);
                }
                // token已经在nginx的日志里面有了
                // log.info("token:{}", token);
                merchantSubject = RequestHolder.getMerchantSubject(token, request);
            }
            // 看起来似乎没必要打印这个。
            // log.info("merchantSubject:{}", JSON.toJSONString(merchantSubject));
            try {
                String result = new String(content, StandardCharsets.UTF_8);
                if (merchantSubject == null) {
                    log.info("客户未登录，替换所有价格信息");
                    //替换掉所有价格数据
                    String reg ="(\"[a-zA-Z]*(p|P)rice\":[\\d.]+)";
                    //阶梯价数据
                    String reg2 ="(\"ladderPrice\":\"\\[\\{([\\\\{}a-zA-Z\":\\d,.]*)}]\")";
                    //平均价数据
                    String reg3 ="(\"avgPrice\":\"\\d{1,5}\\.{0,1}\\d{0,2}\\/[\\w\\u4e00-\\u9fa5a-zA-Z]{1,2}\")";
                    result = result.replaceAll(reg,"\"xxx\":\"xxx\"").replaceAll(reg2,"\"xxx\":\"xxx\"").replaceAll(reg3,"\"xxx\":\"xxx\"");
                }
                servletResponse.setContentLength(-1);
                servletResponse.getWriter().write(result);
            } finally {
                // 防止发生异常,destroy方法不被调用，资源未被清理
                RequestHolder.clearCurrentUserInfo();
            }
        }
    }


    @Override
    public void destroy() {
        RequestHolder.clearCurrentUserInfo();
    }
}
