package net.summerfarm.mall.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.xianmu.rocketmq.support.producer.MqProducer;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MQSendTestJob extends XianMuJavaProcessorV2 {

    @Autowired
    private MqProducer mqProducer;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        mqProducer.send("topic_scp_replenishment_order", "not_exist_tag", "pre test 123");
        return new ProcessResult(true);
    }

}