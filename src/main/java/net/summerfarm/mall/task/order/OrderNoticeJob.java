package net.summerfarm.mall.task.order;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.service.DeliveryPlanService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 *  订单配送提醒通知
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderNoticeJob extends XianMuJavaProcessorV2 {


    @Resource
    private DeliveryPlanService deliveryPlanService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("订单配送提醒通知 start :{}", LocalDateTime.now());
        deliveryPlanService.orderDeliveryNotice();
        log.info("订单配送提醒通知 end :{}", LocalDateTime.now());
        return new ProcessResult(true);
    }
}