package net.summerfarm.mall.task.order;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.service.RefundService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 实际退款
 * <AUTHOR>
 */
@Component
@Slf4j
public class HandleRealRefundJob extends XianMuJavaProcessorV2 {


    @Resource
    private RefundService refundService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("实际退款 start :{}", LocalDateTime.now());
        refundService.refundReal();
        log.info("实际退款 end :{}", LocalDateTime.now());
        return new ProcessResult(true);
    }
}