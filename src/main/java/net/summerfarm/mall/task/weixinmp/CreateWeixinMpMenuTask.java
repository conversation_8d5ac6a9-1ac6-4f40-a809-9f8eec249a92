package net.summerfarm.mall.task.weixinmp;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.WeChatUtils;
import net.summerfarm.mall.service.facade.AuthWechatFacade;
import net.summerfarm.mall.wechat.model.Menu;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
// 用来创建微信的公众号菜单
// 支持多个按钮以及按钮组合
// 支持view类型的按钮，跳转到指定的url
// 主菜单最多只能3个。sub_button可以有多个
// {"button":[
// {"name":"鲜沐商城","url":"/home.html","type":"view"},
// {"name":"客服","url":"/home.html#/loading?name=selfProblem","type":"view"},
// {"name":"常用","sub_button":[
//      {"name":"配方集市","url":"/home.html?name=new-meetings&id=2342&isMobile=true","type":"view"},
//      {"name":"鲜沐之家","url":"https://www.summerfarm.net","type":"view"},
//      {"name":"发起售后","url":"/home.html#/loading?name=selfOrderCanAfterSale","type":"view"},
//      {"name":"我的订单","url":"/home.html#/loading?name=selfOrder","type":"view"}]
//  }]
// }
@Slf4j
public class CreateWeixinMpMenuTask extends XianMuJavaProcessorV2 {

    @Resource
    private AuthWechatFacade authWechatFacade;

    private static final String DEFAULT_MENU_JSON = "{\"button\":[{\"name\":\"鲜沐商城\",\"url\":\"/home.html\",\"type\":\"view\"},{\"name\":\"客服\",\"url\":\"/home.html#/loading?name=selfProblem\",\"type\":\"view\"},{\"name\":\"常用\",\"sub_button\":[{\"name\":\"配方集市\",\"url\":\"/home.html?name=new-meetings&id=2342&isMobile=true\",\"type\":\"view\"},{\"name\":\"鲜沐之家\",\"url\":\"https://www.summerfarm.net\",\"type\":\"view\"},{\"name\":\"发起售后\",\"url\":\"/home.html#/loading?name=selfOrderCanAfterSale\",\"type\":\"view\"},{\"name\":\"我的订单\",\"url\":\"/home.html#/loading?name=selfOrder\",\"type\":\"view\"}]}]}";

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        String menuJson = context.getJobParameters();
        if (StringUtils.isBlank(menuJson)) {
            menuJson = context.getInstanceParameters();
        }
        if (StringUtils.isBlank(menuJson)) {
            menuJson = DEFAULT_MENU_JSON;
        }
        log.info("即将创建公众号的菜单，menuJson:{}", menuJson);
        Menu menuObj = JSON.parseObject(menuJson, Menu.class);
        if (menuObj == null || CollectionUtils.isEmpty(menuObj.getButton())) {
            return new ProcessResult(false, "menu is empty:" + menuJson);
        }
        String redisAccessToken = authWechatFacade.queryChannelToken(WxOfficialAccountsChannelEnum.XM_MALL.channelCode);
        boolean isSuccess = WeChatUtils.onLoadMenu(menuObj, redisAccessToken);
        return new ProcessResult(isSuccess, isSuccess ? "success" : "fail");
    }
}
