package net.summerfarm.mall.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 常购清单相关枚举
 */
public interface FrequentSkuPoolEnums {
    /**
     * 状态枚举
     */
    @Getter
    @AllArgsConstructor
    enum Status {
        DELETE(-1,"删除"),
        JOINED_LIST(1,"已加入清单"),
        ;

        private final Integer code;

        private final String desc;
    }


    /**
     * 置顶枚举
     */
    @Getter
    @AllArgsConstructor
     enum Top {
        UN_TOP(0, "不置顶"),
        TOP(1, "置顶");

        /**
         * 编码
         */
        private final Integer code;
        /**
         * 描述
         */
        private final String desc;
    }

    /**
     * 数据来源枚举  0：系统初始化 1：推荐弹窗 2：商品列表  3：商品详情  4：购物车 5：支付成功页
     */
    @Getter
    @AllArgsConstructor
    enum DataSource {
        SYSTEM_INIT(0, "系统初始化"),
        RECOMMEND_POPUP(1, "推荐弹窗"),
        PRODUCT_LIST(2, "商品列表"),
        PRODUCT_DETAIL(3, "商品详情"),
        SHOPPING_CART(4, "购物车"),
        PAY_SUCCESS_PAGE(5, "支付成功页"),
        RECOMMEND_POPUP_CANCEL(6, "推荐弹窗-取消按钮"),
        ;

        /**
         * 编码
         */
        private final Integer code;
        /**
         * 描述
         */
        private final String desc;
    }
}
