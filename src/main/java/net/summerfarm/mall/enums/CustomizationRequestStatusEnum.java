package net.summerfarm.mall.enums;

/**
 * 定制需求状态枚举
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public enum CustomizationRequestStatusEnum {

    /**
     * 定制需求待生成(订单未支付)
     */
    PENDING_GENERATION(0, "定制需求待生成(订单未支付)"),

    /**
     * 待设计师设计(订单已支付)
     */
    PENDING_DESIGN(1, "待设计师设计(订单已支付)"),

    /**
     * 设计师发起确认
     */
    DESIGN_CONFIRMATION(2, "设计师发起确认"),

    /**
     * 客户通过
     */
    CUSTOMER_APPROVED(3, "客户通过"),

    /**
     * 客户不通过
     */
    CUSTOMER_REJECTED(4, "客户不通过"),

    /**
     * 关闭(订单取消/退款)
     */
    CLOSED(5, "关闭(订单取消/退款)");

    private final Integer code;
    private final String description;

    CustomizationRequestStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据状态码获取枚举
     * @param code 状态码
     * @return 枚举值
     */
    public static CustomizationRequestStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CustomizationRequestStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查状态是否有效
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidStatus(Integer code) {
        return getByCode(code) != null;
    }
}
