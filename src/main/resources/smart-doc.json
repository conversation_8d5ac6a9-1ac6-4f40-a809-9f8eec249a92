{"pathPrefix": "", "isStrict": false, "allInOne": true, "outPath": "target/doc", "coverOld": true, "createDebugPage": true, "packageFilters": "", "md5EncryptedHtmlName": false, "style": "xt256", "projectName": "smart-doc", "framework": "spring", "skipTransientField": true, "sortByTitle": false, "showAuthor": true, "inlineEnum": true, "recursionLimit": 7, "allInOneDocFileName": "index.html", "requestExample": "true", "responseExample": "true", "requestParamsTable": true, "responseParamsTable": true, "displayActualType": true, "ignoreRequestParams": ["org.springframework.ui.ModelMap"], "dataDictionaries": [{"title": "status字典", "enumClassName": "net.xianmu.common.result.ResultStatusEnum", "codeField": "status", "descField": "msg"}], "customResponseFields": [{"name": "status", "desc": "响应status，参考http状态码", "ownerClassName": "net.xianmu.common.result.CommonResult"}, {"name": "errCode", "desc": "错误码、开发排查用、非必填", "ownerClassName": "net.xianmu.common.result.CommonResult"}, {"name": "msg", "desc": "响应msg，前端展示用", "ownerClassName": "net.xianmu.common.result.CommonResult"}, {"name": "data", "desc": "响应数据，严格按照泛型填写", "ownerClassName": "net.xianmu.common.result.CommonResult"}, {"name": "pageNum", "desc": "当前页", "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "pageSize", "desc": "每页的数量", "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "size", "desc": "当前页的数量", "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "startRow", "desc": "当前页面第一个元素在数据库中的行号", "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "endRow", "desc": "当前页面最后一个元素在数据库中的行号", "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "pages", "desc": "总页数", "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "prePage", "desc": "前一页", "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "nextPage", "desc": "下一页", "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "firstPage", "desc": "是否为第一页", "ignore": true, "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "lastPage", "desc": "是否为最后一页", "ignore": true, "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "hasPreviousPage", "desc": "是否有前一页", "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "hasNextPage", "desc": "是否有下一页", "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "navigatePages", "desc": "导航页码数", "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "navigatepageNums", "desc": "所有导航页号", "ownerClassName": "com.github.pagehelper.PageInfo", "ignore": true}, {"name": "navigateFirstPage", "desc": "导航条上的第一页", "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "navigateLastPage", "desc": "导航条上的最后一页", "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "total", "desc": "总记录数", "ownerClassName": "com.github.pagehelper.PageInfo"}, {"name": "list", "desc": "结果集", "ownerClassName": "com.github.pagehelper.PageInfo"}], "customRequestFields": [{"name": "pageIndex", "desc": "分页-页码", "ownerClassName": "net.xianmu.common.result.BasePageInput"}, {"name": "pageSize", "desc": "分页-分页大小", "ownerClassName": "net.xianmu.common.result.BasePageInput"}, {"name": "sortList", "desc": "排序信息", "ownerClassName": "net.xianmu.common.result.BasePageInput"}, {"name": "queryInput", "desc": "查询泛型类", "ownerClassName": "net.xianmu.common.result.BasePageInput"}, {"name": "sortBy", "desc": "排序字段", "ownerClassName": "net.xianmu.common.result.PageSortInput"}, {"name": "descOrder", "desc": "是否倒序", "ownerClassName": "net.xianmu.common.result.PageSortInput"}], "requestHeaders": [{"name": "token", "type": "string", "desc": "desc", "value": "token请求头的值", "required": false, "since": "1.0.0"}], "responseBodyAdvice": {"className": "net.xianmu.common.result.CommonResult"}, "groups": [{"name": "测试分组", "apis": "net.summerfarm.mall.controller.*"}]}