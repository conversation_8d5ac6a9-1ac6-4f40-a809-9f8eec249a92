spring:
  application:
    id: summerfarm-mall
    name: summerfarm-mall
  profiles:
    active: dev
  http:
    encoding:
      charset: UTF-8
#    active: product

rocketmq:
  name-server=localhost:9876:

## Mybatis 配置
mybatis:
  type-aliases-package: net.summerfarm.mall.model.domain
  mapper-locations: classpath:net.summerfarm.mall.mapper/*.xml

# 日志配置
logging:
  pattern:
    console: "%d - %msg%n"
  level:
    root:  INFO
    org.springframework:  INFO
    org.mybatis:  INFO
    net.summerfarm: INFO
server:
  servlet:
    session:
      timeout: 3600
  port: 80

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

management:
  health:
    elasticsearch:
      enabled: false
log-path: ${APP_LOG_DIR:../log}

##启用shutdown
#endpoints:
#  shutdown:
#    enabled: true
#    #禁用密码验证
#    sensitive: false
