<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.CouponConfigDetailMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.CouponConfigDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="coupon_id" jdbcType="INTEGER" property="couponId" />
    <result column="coupon_config_id" jdbcType="INTEGER" property="couponConfigId" />
    <result column="name" jdbcType="INTEGER" property="name" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="threshold" jdbcType="DECIMAL" property="threshold" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="effective_time" jdbcType="INTEGER" property="effectiveTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    id, coupon_id, coupon_config_id, `name`, money, threshold, `number`, effective_time, 
    create_time, creator, `status`, update_time, updater
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from coupon_config_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from coupon_config_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CouponConfigDetail" useGeneratedKeys="true">
    insert into coupon_config_detail (coupon_id, coupon_config_id, `name`, 
      money, threshold, `number`, 
      effective_time, create_time, creator, 
      `status`, update_time, updater
      )
    values (#{couponId,jdbcType=INTEGER}, #{couponConfigId,jdbcType=INTEGER}, #{name,jdbcType=INTEGER}, 
      #{money,jdbcType=DECIMAL}, #{threshold,jdbcType=DECIMAL}, #{number,jdbcType=INTEGER}, 
      #{effectiveTime,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CouponConfigDetail" useGeneratedKeys="true">
    insert into coupon_config_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="couponConfigId != null">
        coupon_config_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="threshold != null">
        threshold,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="effectiveTime != null">
        effective_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="couponId != null">
        #{couponId,jdbcType=INTEGER},
      </if>
      <if test="couponConfigId != null">
        #{couponConfigId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="threshold != null">
        #{threshold,jdbcType=DECIMAL},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="effectiveTime != null">
        #{effectiveTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.CouponConfigDetail">
    update coupon_config_detail
    <set>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=INTEGER},
      </if>
      <if test="couponConfigId != null">
        coupon_config_id = #{couponConfigId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="threshold != null">
        threshold = #{threshold,jdbcType=DECIMAL},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=INTEGER},
      </if>
      <if test="effectiveTime != null">
        effective_time = #{effectiveTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.CouponConfigDetail">
    update coupon_config_detail
    set coupon_id = #{couponId,jdbcType=INTEGER},
      coupon_config_id = #{couponConfigId,jdbcType=INTEGER},
      `name` = #{name,jdbcType=INTEGER},
      money = #{money,jdbcType=DECIMAL},
      threshold = #{threshold,jdbcType=DECIMAL},
      `number` = #{number,jdbcType=INTEGER},
      effective_time = #{effectiveTime,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByConfigId" resultType="net.summerfarm.mall.model.vo.CouponConfigDetailVO">
    select ccd.id,
           ccd.coupon_id couponId,
           ccd.coupon_config_id couponConfigId,
           c.name couponName,
           ccd.money,
           ccd.threshold,
           ccd.number,
           ccd.effective_time effectiveTime
    from coupon_config_detail ccd
           left join coupon c on c.id = ccd.coupon_id
    where ccd.status = 0 and ccd.coupon_config_id = #{configId} order by ccd.id desc
  </select>
</mapper>