<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MerchantCardRecordMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MerchantCardRecord">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="merchant_card_id" property="merchantCardId" jdbcType="INTEGER" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="add_time" property="addTime" />
    </resultMap>

    <sql id="BaseColumn">
        id,merchant_card_id,order_no,add_time
    </sql>

    <select id="selectByOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM merchant_card_record
        WHERE order_no = #{orderNo,jdbcType=VARCHAR}
    </select>

    <insert id="insert" parameterType="net.summerfarm.mall.model.domain.MerchantCardRecord">
        INSERT INTO merchant_card_record(merchant_card_id,order_no,add_time)
        VALUES (#{merchantCardId} ,#{orderNo} ,#{addTime} )
    </insert>

    <delete id="deleteByOrderNo" parameterType="java.lang.String">
        DELETE
        FROM merchant_card_record
        WHERE order_no = #{orderNo}
    </delete>
</mapper>