<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.TimingOrderRefundTimeMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.TimingOrderRefundTime">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="refund_time" jdbcType="DATE" property="refundTime" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, refund_time, m_id, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from timing_order_refund_time
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByRefundDate" resultType="net.summerfarm.mall.model.domain.TimingOrderRefundTime">
      select id, order_no orderNo, refund_time refundTime, m_id mId
      from timing_order_refund_time
      where refund_time = #{refundDate,jdbcType=DATE}
    </select>
  <select id="selectOrderNoByRefundDate" resultType="java.lang.String">
    select order_no as orderNo
      from timing_order_refund_time
      where refund_time = #{refundDate,jdbcType=DATE}
  </select>
  <select id="selectByOrderNo" resultType="net.summerfarm.mall.model.domain.TimingOrderRefundTime">
    select id, order_no orderNo, refund_time refundTime, m_id mId
      from timing_order_refund_time
      where order_no = #{orderNo,jdbcType=VARCHAR}
  </select>
  <select id="selectOrderNoForView" resultType="java.lang.String">
    select order_no as orderNo
      from timing_order_refund_time
        where m_id = #{mId}
            and refund_time <![CDATA[<=]]> #{endDate}
            and refund_time <![CDATA[>]]> #{startDate}
  </select>
  <select id="selectByOrderNoList" resultType="net.summerfarm.mall.model.domain.TimingOrderRefundTime">
    select id, order_no orderNo, refund_time refundTime, m_id mId
    from timing_order_refund_time
    where order_no in
    <foreach collection="orderNos" close=")" open="(" separator="," item="orderNo">
      #{orderNo}
    </foreach>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from timing_order_refund_time
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByOrderNo">
    delete from timing_order_refund_time
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.TimingOrderRefundTime" useGeneratedKeys="true">
    insert into timing_order_refund_time (order_no, refund_time, m_id, 
      create_time, update_time)
    values (#{orderNo,jdbcType=VARCHAR}, #{refundTime,jdbcType=DATE}, #{mId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.TimingOrderRefundTime" useGeneratedKeys="true">
    insert into timing_order_refund_time
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="refundTime != null">
        refund_time,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=DATE},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert">
    insert into timing_order_refund_time (order_no, refund_time, m_id,
    create_time, update_time)
    values
    <foreach collection="records" item="item" index="index" separator=",">
      (#{item.orderNo,jdbcType=VARCHAR}, #{item.refundTime,jdbcType=DATE}, #{item.mId,jdbcType=BIGINT},
      #{item.createTime,jdbcType=TIMESTAMP},#{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
    ON DUPLICATE KEY UPDATE
    order_no = VALUES(order_no);
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.TimingOrderRefundTime">
    update timing_order_refund_time
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="refundTime != null">
        refund_time = #{refundTime,jdbcType=DATE},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.TimingOrderRefundTime">
    update timing_order_refund_time
    set order_no = #{orderNo,jdbcType=VARCHAR},
      refund_time = #{refundTime,jdbcType=DATE},
      m_id = #{mId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>