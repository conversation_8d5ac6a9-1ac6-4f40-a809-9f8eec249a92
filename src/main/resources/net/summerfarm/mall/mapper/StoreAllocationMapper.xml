<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.StoreAllocationMapper" >
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.StoreAllocation">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified"/>
        <result column="status" property="status" />
        <result column="type" property="type" />
        <result column="store_no" property="storeNo" />
        <result column="out_store_no" property="outStoreNo" />
        <result column="allocation_time" property="allocationTime" />
        <result column="next_day_reach" property="nextDayReach" />
    </resultMap>

    <sql id="Base_Column_List" >
        id,gmt_create,gmt_modified,status,`type`,store_no,out_store_no,allocation_time,next_day_reach
    </sql>

    <select id="selectByStoreNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from store_allocation
        where store_no = #{storeNo} and status = 0
    </select>
</mapper>