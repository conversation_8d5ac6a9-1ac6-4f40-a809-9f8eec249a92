<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.ConfigMapper" >

  <select id="selectAll" resultType="net.summerfarm.mall.model.domain.Config">
    SELECT * from config
  </select>

  <select id="selectByKey" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.domain.Config">
    SELECT *
    FROM config c
    WHERE c.key = #{key}
  </select>
  <select id="selectByValue" resultType="java.lang.String">
    select `key`
    from config
    where value = #{value}
  </select>

  <select id="selectOne" resultType="net.summerfarm.mall.model.domain.Config">
    SELECT co.id,co.key,co.value,co.remark
    FROM config co
    WHERE co.key = #{key}
  </select>
</mapper>