<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.ShoppingListMapper">
    <sql id="Base_Column_List">
        m_id, accountId, sku, product_type, quantity, `check`, update_time, del_flag
    </sql>

    <update id="updateChecked2Del" parameterType="java.lang.Long" >
        UPDATE shopping_list t set t.del_flag =1 , t.update_time = now()
        WHERE t.`check` = 1
          AND t.m_id = #{mId}
          AND t.account_id = #{accountId}
    </update>

    <select id="select4cart" resultType="net.summerfarm.mall.model.vo.OrderItemVO">
        SELECT t.m_id,t.account_id accountId, t.sku,p.pd_name pdName, p.category_id categoryId, t.quantity, a.on_sale onSale,
        if(a.share = 0,a.quantity,ar.online_quantity) stock,a.share,
        a.sales_mode salesMode, a.limited_quantity limitedQuantity, i.maturity, i.weight, i.base_sale_quantity baseSaleQuantity, i.base_sale_unit baseSaleUnit,c.type,t.product_type productType,
        ifnull(i.sku_pic, p.picture_path) picturePath, p.storage_location storageLocation, a.ladder_price ladderPrice, t.check,p.pd_id pdId, t.suit_id suitId,IFNULL(a.price,i.sale_price) salePrice,
        (case when a.original_price  = 0.00 then a.price
        when a.original_price is null then a.price
        else a.original_price
        end ) originalPrice,
        i.volume,i.weight_num weightNum ,i.type skuType,if(c.type = 4, null, a.info) qualityInfo
        <if test="adminId != null">
            ,mp.price price,mp.direct, mp.pay_method payMethod, t2.sale_price activityOriginPrice
        </if>

        FROM shopping_list t
        LEFT JOIN inventory i on t.sku = i.sku
        LEFT JOIN products p on p.pd_id =i.pd_id
        LEFT JOIN category c on c.id=p.category_id
        LEFT JOIN area_sku a on t.sku = a.sku
        LEFT JOIN area area on a.area_no=area.area_no
        INNER JOIN warehouse_inventory_mapping wim on wim.store_no = area.parent_no and wim.sku = t.sku
        INNER JOIN area_store ar on ar.sku=wim.sku and wim.warehouse_no = ar.area_no
        <if test="adminId != null">
            LEFT JOIN major_price mp on mp.sku=a.sku and mp.area_no=a.area_no and mp.admin_id=#{adminId} and mp.direct=#{direct}
            and mp.valid_time <![CDATA[<=]]> now() and  mp.invalid_time <![CDATA[>]]> now()
            LEFT JOIN (
            SELECT as2.sku, as2.sale_price
            FROM activity_sku as2
            INNER JOIN activity a2 ON as2.activity_id = a2.id
            WHERE a2.type = 0
            AND as2.sku_status = 1
            AND a2.status = 2
            AND a2.start_time <![CDATA[<=]]> now()
            AND a2.end_time <![CDATA[>=]]> now()
            AND a2.area_no = #{areaNo}
            GROUP BY as2.sku
            ) t2 ON i.sku = t2.sku
        </if>
        WHERE t.m_id = #{mId}
        AND t.account_id = #{accountId}
        AND a.area_no = #{areaNo}
        AND t.del_flag = 0
        <if test="check != null">
            AND  t.`check` = #{check}
        </if>
        order by t.update_time desc
    </select>
    <select id="select4cartOutTrolley" resultType="net.summerfarm.mall.model.vo.OrderItemVO">
        SELECT
        #{mId}                          mId,
        #{accountId}                    accountId,
        0                               productType,
        a.sku,
        p.pd_name                         pdName,
        p.category_id                     categoryId,
        a.on_sale                         onSale,
        if(a.share = 0,a.quantity,ar.online_quantity) stock,
        a.share,
        a.sales_mode                      salesMode,
        a.limited_quantity                limitedQuantity,
        i.maturity,
        i.weight,
        i.base_sale_quantity              baseSaleQuantity,
        i.base_sale_unit                  baseSaleUnit,
        c.type,
        ifnull(i.sku_pic, p.picture_path) picturePath,
        p.storage_location                storageLocation,
        a.ladder_price                    ladderPrice,
        p.pd_id                           pdId,
        IFNULL(a.price, i.sale_price)     salePrice,
        (case
        when a.original_price = 0.00 then a.price
        when a.original_price is null then a.price
        else a.original_price
        end)                          originalPrice,
        i.volume,
        i.weight_num                      weightNum,
        i.type                            skuType,
        if(c.type = 4, null, a.info)      qualityInfo
        <if test="adminId != null">
            ,mp.price price,mp.direct, mp.pay_method payMethod, t2.sale_price activityOriginPrice
        </if>
        FROM area_sku a
        LEFT JOIN inventory i on a.sku = i.sku
        LEFT JOIN products p on p.pd_id = i.pd_id
        LEFT JOIN category c on c.id = p.category_id
        LEFT JOIN area area on a.area_no = area.area_no
        INNER JOIN warehouse_inventory_mapping wim on wim.store_no = area.parent_no and wim.sku = i.sku
        INNER JOIN area_store ar on ar.sku=wim.sku and wim.warehouse_no = ar.area_no
        <if test="adminId != null">
            LEFT JOIN major_price mp on mp.sku=a.sku and mp.area_no=a.area_no and mp.admin_id=#{adminId} and mp.direct=#{direct}
            and mp.valid_time <![CDATA[<=]]> now() and  mp.invalid_time <![CDATA[>]]> now()
            LEFT JOIN (
            SELECT as2.sku, as2.sale_price
            FROM activity_sku as2
            INNER JOIN activity a2 ON as2.activity_id = a2.id
            WHERE a2.type = 0
            AND as2.sku_status = 1
            AND a2.status = 2
            AND a2.start_time <![CDATA[<=]]> now()
            AND a2.end_time <![CDATA[>=]]> now()
            AND a2.area_no = #{areaNo}
            GROUP BY as2.sku
            ) t2 ON i.sku = t2.sku
        </if>
        WHERE a.area_no = #{areaNo}
        and i.sku in
        <foreach collection="skuArr" item="el" separator="," open="(" close=")">
            #{el}
        </foreach>
    </select>
    <insert id="merge" parameterType="net.summerfarm.mall.model.vo.TrolleyVO" >
        INSERT INTO `shopping_list` (`m_id`, `account_id`,`sku`, suit_id, product_type,`quantity`, `update_time`)
        VALUES (#{mId}, #{accountId} , #{sku}, #{suitId}, #{productType}, #{quantity}, NOW())
            ON DUPLICATE KEY UPDATE quantity = if(del_flag = 0 and #{sumFlag}, quantity, 0) + VALUES(quantity) ,update_time = NOW(), del_flag = 0 ,`check` = 1
    </insert>

    <update id="deleteShop" parameterType="net.summerfarm.mall.model.domain.ShoppingList">
        update shopping_list
        set del_flag=1
    </update>


    <delete id="deleteByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.ShoppingList">
        delete from shopping_list
        where m_id = #{mId,jdbcType=BIGINT}
          and account_id = #{accountId, jdbcType=BIGINT}
          and sku = #{sku,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="net.summerfarm.mall.model.domain.Trolley">
        insert into trolley (m_id, account_id, sku, quantity,
                             `check`, update_time, del_flag
        )
        values (#{mId,jdbcType=BIGINT},#{accountId,jdbcType=BIGINT},#{sku,jdbcType=VARCHAR}, #{quantity,jdbcType=INTEGER},
                #{check,jdbcType=TINYINT}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=BIT}
               )
    </insert>

    <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.Trolley">
        insert into trolley
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                m_id,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="check != null">
                `check`,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                #{mId,jdbcType=BIGINT},
            </if>
            <if test="accountId != null">
                #{accountId,jdbcType=BIGINT}
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="check != null">
                #{check,jdbcType=TINYINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=BIT},
            </if>
        </trim>
    </insert>

    <update id="cancelCheck" >
        UPDATE shopping_list
        SET `check` = 0
        where m_id = #{mId,jdbcType=BIGINT}
          and account_id = #{accountId,jdbcType=BIGINT}
          and sku = #{sku,jdbcType=VARCHAR}
          AND suit_id =#{suitId}
    </update>

    <update id="updateQuantity" >
        UPDATE shopping_list
        SET quantity = #{quantity,jdbcType=INTEGER}
        where m_id = #{mId,jdbcType=BIGINT}
          and account_id = #{accountId,jdbcType=BIGINT}
          and sku = #{sku,jdbcType=VARCHAR}
          and suit_id =#{suitId}
          and product_type = #{productType}
    </update>

    <update id="updateSelectiveBatch" parameterType="net.summerfarm.mall.model.domain.Trolley">
        update trolley
        SET
        <if test="quantity != null">
            quantity = #{quantity,jdbcType=INTEGER},
        </if>
        <if test="check != null">
            `check` = #{check,jdbcType=TINYINT},
        </if>
        <if test="delFlag != null">
            del_flag = #{delFlag,jdbcType=BIT},
        </if>
        update_time = now()
        where m_id = #{mId,jdbcType=BIGINT}
        and account_id = #{accountId,jdbcType=BIGINT}
    </update>


    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.ShoppingList">
        update shopping_list
        SET
        <if test="quantity != null">
            quantity = #{quantity,jdbcType=INTEGER},
        </if>
        <if test="check != null">
            `check` = #{check,jdbcType=TINYINT},
        </if>
        <if test="delFlag != null">
            del_flag = #{delFlag,jdbcType=BIT},
        </if>
        update_time = now()
        where m_id = #{mId,jdbcType=BIGINT}
        and account_id = #{accountId,jdbcType=BIGINT}
        <if test="sku != null">
            and sku = #{sku,jdbcType=VARCHAR}
        </if>
        AND suit_id = #{suitId}
        AND product_type = #{productType}
    </update>

    <select id="select" parameterType="net.summerfarm.mall.model.domain.ShoppingList"
            resultType="net.summerfarm.mall.model.domain.ShoppingList">
        SELECT m_id mId, account_id accountId, sku, suit_id suitId,product_type productType, quantity, `check`, del_flag delFlag
        FROM shopping_list
        WHERE m_id = #{mId}
        AND account_id = #{accountId}
        <if test="sku != null">
            AND sku = #{sku}
        </if>
        <if test="check != null">
            AND `check` = #{check}
        </if>
        <if test="delFlag != null">
            AND del_flag = #{delFlag}
        </if>
    </select>

    <select id="selectActivityAmount" resultType="java.math.BigDecimal">
        select sum(amount)  from (
                                     select t.sku , a.id ,a.name,(ask.sale_price - ask.activity_price) * t.quantity amount from shopping_list  t
                                                                                                                                    inner join activity_sku ask on ask.sku = t.sku AND ask.sku_status = 1
                                                                                                                                    inner join activity a on ask.activity_id = a.id and a.type = 0 and a.status = 2 and a.start_time  <![CDATA[<=]]> now() and a.end_time >= now() and a.area_no = #{areaNo}
                                     where t.m_id = #{mId} and t.del_flag = 0 and t.`check` = 1
                                 ) df
    </select>

    <select id="selectActivity" resultType="net.summerfarm.mall.model.input.OrderPreferentialOutput">
        select sum(amount) amount ,df.name  from (
                                                     select t.sku , a.id ,a.name,(ask.sale_price - ask.activity_price) * t.quantity amount from trolley  t
                                                                                                                                                    inner join activity_sku ask on ask.sku = t.sku AND ask.sku_status = 1
                                                                                                                                                    LEFT JOIN area_sku ak on t.sku = ak.sku
                                                                                                                                                    inner join activity a on ask.activity_id = a.id and a.type = 0 and a.status = 2 and a.start_time  <![CDATA[<=]]> now()
                                                         and a.end_time >= now()
                                                     where t.m_id = #{mId} and t.del_flag = 0 and t.product_type = 0 and t.`check` = 1 and ak.area_no = #{areaNo}
                                                 ) df group by df.id
    </select>
    <update id="clearGiftItem">
        update trolley set del_flag = 1 where product_type = 1 and m_id = #{mId} and account_id = #{accountId}
    </update>

    <select id="selectAreaNo" resultType="net.summerfarm.mall.model.domain.MarketRule">
        select show_name
        from market_rule
        where area_no = #{areaNo} and (name = '餐饮狂欢节乳品满立减' or name ='餐饮狂欢节鲜果满立减' or name ='餐饮狂欢节辅料满立减')
    </select>

    <select id="selectActivityPrice" resultType="java.lang.Integer">
        select IFNULL(sum((IFNULL(ak.price,i.sale_price) * sl.quantity)),0) AS sumPrice
        from market_rule_detail mrt left join category c on mrt.category_id = c.id
        left join products p on c.id= p.category_id
        left join inventory i on p.pd_id = i.pd_id
        left join shopping_list sl on i.sku = sl.sku
        left join area_sku ak on sl.sku = ak.sku
        where c.type=#{type} and sl.m_id=#{mId} and ak.area_no=#{areaNo} and sl.`check`=1 and mrt.sku = sl.sku and sl.del_flag = 0
    </select>
    <select id="selectSkuNum" resultType="net.summerfarm.mall.model.domain.ShoppingList">
        select t.quantity
        from shopping_list t
        left join area_sku s on t.sku = s.sku
        where t.m_id = #{mId} and s.area_no = #{areaNo} and t.account_id = #{accountId} and t.sku=#{sku} and t.suit_id= #{suitId} and t.del_flag = 0 and t.product_type = 0
    </select>
</mapper>