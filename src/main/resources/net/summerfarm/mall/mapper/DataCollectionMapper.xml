<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.DataCollectionMapper">
    <insert id="insertData" parameterType="net.summerfarm.mall.model.domain.DataCollection">
        INSERT INTO datacollection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ip != null"> ip, </if>
            <if test="uri != null"> uri, </if>
            <if test="paramData != null"> param_data, </if>
            <if test="method != null"> method, </if>
            <if test="interviewTime != null"> interview_time, </if>
            <if test="source != null"> source, </if>
            <if test="jsessionid != null"> jsessionid, </if>
            <if test="mid != null"> m_id, </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="ip != null"> #{ip}, </if>
            <if test="uri != null"> #{uri}, </if>
            <if test="paramData != null"> #{paramData}, </if>
            <if test="method != null"> #{method}, </if>
            <if test="interviewTime != null"> #{interviewTime}, </if>
            <if test="source != null"> #{source}, </if>
            <if test="jsessionid != null"> #{jsessionid}, </if>
            <if test="mid != null"> #{mid}, </if>
        </trim>
    </insert>
</mapper>