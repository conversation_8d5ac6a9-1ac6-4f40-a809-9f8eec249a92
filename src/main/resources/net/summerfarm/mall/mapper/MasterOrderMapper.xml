<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MasterOrderMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MasterOrder">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="masterOrderNo" column="master_order_no" jdbcType="VARCHAR"/>
            <result property="mId" column="m_id" jdbcType="BIGINT"/>
            <result property="orderTime" column="order_time" jdbcType="TIMESTAMP"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="SMALLINT"/>
            <result property="totalPrice" column="total_price" jdbcType="DECIMAL"/>
            <result property="originPrice" column="origin_price" jdbcType="DECIMAL"/>
            <result property="areaNo" column="area_no" jdbcType="INTEGER"/>
            <result property="mSize" column="m_size" jdbcType="VARCHAR"/>
            <result property="accountId" column="account_id" jdbcType="BIGINT"/>
            <result property="adminId" column="admin_id" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,master_order_no,m_id,
        order_time,type,status,
        total_price,origin_price,area_no,
        m_size,account_id,admin_id,
        create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from master_order
        where  id = #{id,jdbcType=BIGINT} 
    </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from master_order
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MasterOrder" useGeneratedKeys="true">
        insert into master_order
        ( id,master_order_no,m_id
        ,order_time,type,status
        ,total_price,origin_price,area_no
        ,m_size,account_id,admin_id
        ,create_time,update_time)
        values (#{id,jdbcType=BIGINT},#{masterOrderNo,jdbcType=VARCHAR},#{mId,jdbcType=BIGINT}
        ,#{orderTime,jdbcType=TIMESTAMP},#{type,jdbcType=INTEGER},#{status,jdbcType=SMALLINT}
        ,#{totalPrice,jdbcType=DECIMAL},#{originPrice,jdbcType=DECIMAL},#{areaNo,jdbcType=INTEGER}
        ,#{mSize,jdbcType=VARCHAR},#{accountId,jdbcType=BIGINT},#{adminId,jdbcType=INTEGER}
        ,#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MasterOrder" useGeneratedKeys="true">
        insert into master_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="masterOrderNo != null">master_order_no,</if>
                <if test="mId != null">m_id,</if>
                <if test="orderTime != null">order_time,</if>
                <if test="type != null">type,</if>
                <if test="status != null">status,</if>
                <if test="totalPrice != null">total_price,</if>
                <if test="originPrice != null">origin_price,</if>
                <if test="areaNo != null">area_no,</if>
                <if test="mSize != null">m_size,</if>
                <if test="accountId != null">account_id,</if>
                <if test="adminId != null">admin_id,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="masterOrderNo != null">#{masterOrderNo,jdbcType=VARCHAR},</if>
                <if test="mId != null">#{mId,jdbcType=BIGINT},</if>
                <if test="orderTime != null">#{orderTime,jdbcType=TIMESTAMP},</if>
                <if test="type != null">#{type,jdbcType=INTEGER},</if>
                <if test="status != null">#{status,jdbcType=SMALLINT},</if>
                <if test="totalPrice != null">#{totalPrice,jdbcType=DECIMAL},</if>
                <if test="originPrice != null">#{originPrice,jdbcType=DECIMAL},</if>
                <if test="areaNo != null">#{areaNo,jdbcType=INTEGER},</if>
                <if test="mSize != null">#{mSize,jdbcType=VARCHAR},</if>
                <if test="accountId != null">#{accountId,jdbcType=BIGINT},</if>
                <if test="adminId != null">#{adminId,jdbcType=INTEGER},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MasterOrder">
        update master_order
        <set>
                <if test="masterOrderNo != null">
                    master_order_no = #{masterOrderNo,jdbcType=VARCHAR},
                </if>
                <if test="mId != null">
                    m_id = #{mId,jdbcType=BIGINT},
                </if>
                <if test="orderTime != null">
                    order_time = #{orderTime,jdbcType=TIMESTAMP},
                </if>
                <if test="type != null">
                    type = #{type,jdbcType=INTEGER},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=SMALLINT},
                </if>
                <if test="totalPrice != null">
                    total_price = #{totalPrice,jdbcType=DECIMAL},
                </if>
                <if test="originPrice != null">
                    origin_price = #{originPrice,jdbcType=DECIMAL},
                </if>
                <if test="areaNo != null">
                    area_no = #{areaNo,jdbcType=INTEGER},
                </if>
                <if test="mSize != null">
                    m_size = #{mSize,jdbcType=VARCHAR},
                </if>
                <if test="accountId != null">
                    account_id = #{accountId,jdbcType=BIGINT},
                </if>
                <if test="adminId != null">
                    admin_id = #{adminId,jdbcType=INTEGER},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.MasterOrder">
        update master_order
        set 
            master_order_no =  #{masterOrderNo,jdbcType=VARCHAR},
            m_id =  #{mId,jdbcType=BIGINT},
            order_time =  #{orderTime,jdbcType=TIMESTAMP},
            type =  #{type,jdbcType=INTEGER},
            status =  #{status,jdbcType=SMALLINT},
            total_price =  #{totalPrice,jdbcType=DECIMAL},
            origin_price =  #{originPrice,jdbcType=DECIMAL},
            area_no =  #{areaNo,jdbcType=INTEGER},
            m_size =  #{mSize,jdbcType=VARCHAR},
            account_id =  #{accountId,jdbcType=BIGINT},
            admin_id =  #{adminId,jdbcType=INTEGER},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <select id="selectByMasterOrderNo"  parameterType="java.lang.String" resultMap="BaseResultMap">
         select
         <include refid="Base_Column_List" />
         from master_order
         where  master_order_no =  #{masterOrderNo,jdbcType=VARCHAR}
     </select>
    <update id="updateByMasterOrderSelective">
        update master_order
        <set>
          <if test="masterOrderNo != null">
            master_order_no = #{masterOrderNo,jdbcType=VARCHAR},
          </if>
          <if test="mId != null">
            m_id = #{mId,jdbcType=BIGINT},
          </if>
          <if test="orderTime != null">
            order_time = #{orderTime,jdbcType=TIMESTAMP},
          </if>
          <if test="type != null">
            type = #{type,jdbcType=INTEGER},
          </if>
          <if test="status != null">
            status = #{status,jdbcType=SMALLINT},
          </if>
          <if test="totalPrice != null">
            total_price = #{totalPrice,jdbcType=DECIMAL},
          </if>
          <if test="originPrice != null">
            origin_price = #{originPrice,jdbcType=DECIMAL},
          </if>
          <if test="areaNo != null">
            area_no = #{areaNo,jdbcType=INTEGER},
          </if>
          <if test="mSize != null">
            m_size = #{mSize,jdbcType=VARCHAR},
          </if>
          <if test="accountId != null">
            account_id = #{accountId,jdbcType=BIGINT},
          </if>
          <if test="adminId != null">
            admin_id = #{adminId,jdbcType=INTEGER},
          </if>
          <if test="createTime != null">
            create_time = #{createTime,jdbcType=TIMESTAMP},
          </if>
          <if test="updateTime != null">
            update_time = #{updateTime,jdbcType=TIMESTAMP},
          </if>
        </set>
        where  master_order_no = #{masterOrderNo,jdbcType=BIGINT}
    </update>
  <update id="updateMasterOrderStatus">
    update master_order set status = #{newStatus}
    where  master_order_no = #{masterOrderNo,jdbcType=BIGINT} and status = #{oldStatus}
  </update>
</mapper>
