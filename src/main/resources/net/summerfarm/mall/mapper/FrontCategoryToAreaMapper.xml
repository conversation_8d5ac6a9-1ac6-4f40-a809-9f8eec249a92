<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.FrontCategoryToAreaMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.FrontCategoryToArea">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="front_category_id" jdbcType="INTEGER" property="frontCategoryId" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="display" jdbcType="INTEGER" property="display" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, front_category_id, area_no, display, priority, updater, update_time, creator, 
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from front_category_to_area
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from front_category_to_area
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.FrontCategoryToArea" useGeneratedKeys="true">
    insert into front_category_to_area (front_category_id, area_no, display, 
      priority, updater, update_time, 
      creator, create_time)
    values (#{frontCategoryId,jdbcType=INTEGER}, #{areaNo,jdbcType=INTEGER}, #{display,jdbcType=INTEGER}, 
      #{priority,jdbcType=INTEGER}, #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.FrontCategoryToArea" useGeneratedKeys="true">
    insert into front_category_to_area
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="frontCategoryId != null">
        front_category_id,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="display != null">
        display,
      </if>
      <if test="priority != null">
        priority,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="frontCategoryId != null">
        #{frontCategoryId,jdbcType=INTEGER},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="display != null">
        #{display,jdbcType=INTEGER},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.FrontCategoryToArea">
    update front_category_to_area
    <set>
      <if test="frontCategoryId != null">
        front_category_id = #{frontCategoryId,jdbcType=INTEGER},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="display != null">
        display = #{display,jdbcType=INTEGER},
      </if>
      <if test="priority != null">
        priority = #{priority,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.FrontCategoryToArea">
    update front_category_to_area
    set front_category_id = #{frontCategoryId,jdbcType=INTEGER},
      area_no = #{areaNo,jdbcType=INTEGER},
      display = #{display,jdbcType=INTEGER},
      priority = #{priority,jdbcType=INTEGER},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>