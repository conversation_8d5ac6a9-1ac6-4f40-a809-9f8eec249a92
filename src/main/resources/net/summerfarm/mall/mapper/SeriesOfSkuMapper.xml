<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.SeriesOfSkuMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.SeriesOfSku">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="series_type" jdbcType="INTEGER" property="seriesType" />
    <result column="series_id" jdbcType="INTEGER" property="seriesId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <resultMap id="voMap" type="net.summerfarm.mall.model.vo.SeriesOfSkuVO" extends="BaseResultMap">
    <result column="pd_id" jdbcType="BIGINT" property="pdId"/>
    <result column="pd_name" jdbcType="VARCHAR" property="pdName"/>
    <result column="weight" jdbcType="VARCHAR" property="weight"/>
    <result column="picturePath" jdbcType="VARCHAR" property="picturePath"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, series_type, series_id, sku, sort, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from series_of_sku
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from series_of_sku
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.SeriesOfSku" useGeneratedKeys="true">
    insert into series_of_sku (series_type, series_id, sku, 
      sort, creator, create_time
      )
    values (#{seriesType,jdbcType=INTEGER}, #{seriesId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.SeriesOfSku" useGeneratedKeys="true">
    insert into series_of_sku
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seriesType != null">
        series_type,
      </if>
      <if test="seriesId != null">
        series_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seriesType != null">
        #{seriesType,jdbcType=INTEGER},
      </if>
      <if test="seriesId != null">
        #{seriesId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.SeriesOfSku">
    update series_of_sku
    <set>
      <if test="seriesType != null">
        series_type = #{seriesType,jdbcType=INTEGER},
      </if>
      <if test="seriesId != null">
        series_id = #{seriesId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.SeriesOfSku">
    update series_of_sku
    set series_type = #{seriesType,jdbcType=INTEGER},
      series_id = #{seriesId,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByModel" resultMap="voMap">
    select id,
      series_type,
      series_id,
      sos.sku,
      sort,
      sos.creator,
      sos.create_time,
      i.weight,
      i.pd_id,
      p.pd_name,
      ifnull(i.sku_pic, p.picture_path) picturePath
    from series_of_sku sos
    left join inventory i on sos.sku = i.sku
    left join products p on i.pd_id = p.pd_id
    where i.outdated = 0 and p.outdated = 0
    <if test="seriesType != null">
      and sos.series_type = #{seriesType}
    </if>
    <if test="seriesId != null">
      and  sos.series_id = #{seriesId}
    </if>
    <choose>
      <when test="seriesType != null and seriesType == 0">
        order by sos.sort desc
      </when>
      <otherwise>
        order by sos.sort
      </otherwise>
    </choose>
  </select>
</mapper>