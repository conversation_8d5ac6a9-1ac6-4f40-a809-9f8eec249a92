<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.OrdersCouponMapper" >

  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.OrdersCoupon" >
    insert into orders_coupon
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="merchantCouponId != null" >
        merchant_coupon_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="orderNo != null" >
        #{orderNo},
      </if>
      <if test="merchantCouponId != null" >
        #{merchantCouponId},
      </if>
    </trim>
  </insert>

  <select id="select" parameterType="hashmap" resultType="net.summerfarm.mall.model.vo.MerchantCouponVO">
    SELECT mc.id, mc.coupon_id couponId,mc.vaild_date vaildDate, mc.used, c.name, c.code, c.money, c.threshold,
    c.new_hand newHand,c.reamrk, c.grouping, mc.add_time addTime,c.agio_type agioType,c.category_id categoryId,c.sku,mc.m_id mId
    FROM orders_coupon oc
    left join  merchant_coupon mc on oc.merchant_coupon_id=mc.id
    LEFT JOIN coupon c on mc.coupon_id = c.id
    <where>
      <if test="orderNo !=null">
        AND oc.order_no = #{orderNo}
      </if>
    </where>
    order by mc.id
  </select>
  <delete id="deleteByOrderNo">
    delete from orders_coupon where order_no = #{orderNo}
  </delete>
  <insert id="insertBatch">
    insert orders_coupon(order_no,merchant_coupon_id)
    values
      <foreach collection="mcIdSet" item="mcId" separator=",">
        (#{orderNo},#{mcId})
      </foreach>
  </insert>
</mapper>