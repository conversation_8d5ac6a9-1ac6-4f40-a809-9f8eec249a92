<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.FollowUpEvaluationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.FollowUpEvaluation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="follow_record_id" jdbcType="INTEGER" property="followRecordId" />
    <result column="satisfaction_level" jdbcType="TINYINT" property="satisfactionLevel" />
    <result column="tag" jdbcType="VARCHAR" property="tag" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, follow_record_id, satisfaction_level, tag, remark, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from follow_up_evaluation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByFollowRecordId" resultType="net.summerfarm.mall.model.domain.FollowUpEvaluation">
    select
    <include refid="Base_Column_List" />
    from follow_up_evaluation
    where follow_record_id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from follow_up_evaluation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.FollowUpEvaluation" useGeneratedKeys="true">
    insert into follow_up_evaluation (follow_record_id, satisfaction_level, 
      tag, remark, create_time, 
      update_time)
    values (#{followRecordId,jdbcType=INTEGER}, #{satisfactionLevel,jdbcType=TINYINT}, 
      #{tag,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.FollowUpEvaluation" useGeneratedKeys="true">
    insert into follow_up_evaluation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="followRecordId != null">
        follow_record_id,
      </if>
      <if test="satisfactionLevel != null">
        satisfaction_level,
      </if>
      <if test="tag != null">
        tag,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="followRecordId != null">
        #{followRecordId,jdbcType=INTEGER},
      </if>
      <if test="satisfactionLevel != null">
        #{satisfactionLevel,jdbcType=TINYINT},
      </if>
      <if test="tag != null">
        #{tag,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.FollowUpEvaluation">
    update follow_up_evaluation
    <set>
      <if test="followRecordId != null">
        follow_record_id = #{followRecordId,jdbcType=INTEGER},
      </if>
      <if test="satisfactionLevel != null">
        satisfaction_level = #{satisfactionLevel,jdbcType=TINYINT},
      </if>
      <if test="tag != null">
        tag = #{tag,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.FollowUpEvaluation">
    update follow_up_evaluation
    set follow_record_id = #{followRecordId,jdbcType=INTEGER},
      satisfaction_level = #{satisfactionLevel,jdbcType=TINYINT},
      tag = #{tag,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>