<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.DiscountCardMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.DiscountCard">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="vaild_days" jdbcType="INTEGER" property="vaildDays" />
    <result column="vaild_times" jdbcType="INTEGER" property="vaildTimes" />
    <result column="updator" jdbcType="VARCHAR" property="updator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="card_type" property="cardType"/>
    <result column="appoint_time" property="appointTime"/>
    <result column="area_nos" property="areaNos"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, `name`, price, discount, vaild_days, vaild_times, updator, update_time, creator, 
    create_time,card_type,appoint_time,area_nos
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from discount_card
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from discount_card
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.DiscountCard" useGeneratedKeys="true">
    insert into discount_card (`name`, price, discount,
      vaild_days, vaild_times, updator,
      update_time, creator, create_time
      )
    values (#{name,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, #{discount,jdbcType=DECIMAL},
      #{vaildDays,jdbcType=INTEGER}, #{vaildTimes,jdbcType=INTEGER}, #{updator,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.DiscountCard" useGeneratedKeys="true">
    insert into discount_card
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="discount != null">
        discount,
      </if>
      <if test="vaildDays != null">
        vaild_days,
      </if>
      <if test="vaildTimes != null">
        vaild_times,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="cardType != null">
        card_type,
      </if>
      <if test="appointTime != null">
        appoint_time,
      </if>
      <if test="areaNos != null">
        area_nos,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=DECIMAL},
      </if>
      <if test="vaildDays != null">
        #{vaildDays,jdbcType=INTEGER},
      </if>
      <if test="vaildTimes != null">
        #{vaildTimes,jdbcType=INTEGER},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardType != null">
        #{cardType},
      </if>
      <if test="appointTime != null">
        #{appointTime},
      </if>
      <if test="areaNos != null">
        #{areaNos},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.DiscountCard">
    update discount_card
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="discount != null">
        discount = #{discount,jdbcType=DECIMAL},
      </if>
      <if test="vaildDays != null">
        vaild_days = #{vaildDays,jdbcType=INTEGER},
      </if>
      <if test="vaildTimes != null">
        vaild_times = #{vaildTimes,jdbcType=INTEGER},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.DiscountCard">
    update discount_card
    set `name` = #{name,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      discount = #{discount,jdbcType=DECIMAL},
      vaild_days = #{vaildDays,jdbcType=INTEGER},
      vaild_times = #{vaildTimes,jdbcType=INTEGER},
      updator = #{updator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectDiscountCard" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from discount_card
    <where>
      <if test="cardType != null">
         and card_type = #{cardType}
      </if>
      <if test="appointTime">
       and appoint_time  <![CDATA[>]]>  #{appointTime}
      </if>
    </where>
  </select>

  <select id="listByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from discount_card
    <where>
        id in
    <foreach collection="ids" open="(" separator="," close=")" item="id">
      #{id}
    </foreach>
    </where>
  </select>

  <select id="selectAllDiscountCard" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from discount_card
  </select>
</mapper>