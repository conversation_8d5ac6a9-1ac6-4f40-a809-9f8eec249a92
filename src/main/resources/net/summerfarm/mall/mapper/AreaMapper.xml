<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.AreaMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Area" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="area_no" property="areaNo" jdbcType="INTEGER" />
    <result column="area_name" property="areaName" jdbcType="VARCHAR" />
    <result column="admin_id" property="adminId" jdbcType="INTEGER" />
    <result column="parent_no" property="parentNo" jdbcType="INTEGER" />
    <result column="large_area_no" property="largeAreaNo" jdbcType="INTEGER" />
    <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL" />
    <result column="status" property="status" jdbcType="BIT" />
    <result column="delivery_rule" property="deliveryRule" jdbcType="VARCHAR" />
    <result column="member_rule" property="memberRule" jdbcType="VARCHAR" />
    <result column="company_account_id" property="companyAccountId" jdbcType="INTEGER" />
    <result column="map_section" property="mapSection" jdbcType="VARCHAR"/>
    <result column="free_day" property="freeDay" jdbcType="VARCHAR"/>
    <result column="next_delivery_date" property="nextDeliveryDate" jdbcType="INTEGER"/>
    <result column="pay_channel" property="payChannel" jdbcType="INTEGER"/>
    <result column="change_flag" jdbcType="BOOLEAN" property="changeFlag"/>
    <result column="change_store_no" jdbcType="INTEGER" property="changeStoreNo"/>
    <result column="change_status" jdbcType="INTEGER" property="changeStatus"/>
    <result column="support_add_order" property="supportAddOrder"/>
    <result column="wxlite_pay_channel" jdbcType="INTEGER" property="wxlitePayChannel"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, area_no, area_name, admin_id, parent_no, status,delivery_fee,delivery_rule,member_rule,company_account_id,map_section,free_day
    ,type, next_delivery_date,pay_channel,change_flag,change_store_no,change_status,support_add_order,large_area_no,wxlite_pay_channel
  </sql>

  <select id="selectByMId" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
        a.id, a.area_no, a.area_name, a.admin_id, a.parent_no, a.status, a.change_flag, a.change_store_no,
        a.change_status ,support_add_order, large_area_no, delivery_rule, delivery_fee
    from area a , merchant m
    where a.area_no = m.area_no
    AND m.m_id = #{mId}
  </select>

  <select id="selectByAreaNo" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from area
    where area_no = #{areaNo}
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
  select
  <include refid="Base_Column_List" />
  from area
  where id = #{id,jdbcType=INTEGER}
</select>

  <select id="selectSecond" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from area
  </select>
  <select id="select" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from area
  </select>

  <select id="selectSupportAddOrder" resultType="net.summerfarm.mall.model.domain.Area">
    select a.area_no areaNo
    from area a
             inner join (select area_no
                        from fence
                        where store_no = #{storeNo}
                          and status in(0,3)
                        group by area_no) ar on ar.area_no = a.area_no
    where a.support_add_order = 0
  </select>

  <select id="listByAreaNoList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from area
    where area_no in
    <foreach collection="areaNoList" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="getListByStatus" resultMap="BaseResultMap" parameterType="java.lang.Integer">
    select
    <include refid="Base_Column_List" />
    from area
    where status = #{status,jdbcType=INTEGER}
  </select>

  <select id="selectByLargeAreaNos" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from area
    where status = 1 and large_area_no in
    <foreach collection="largeIds" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="getUpdatedAreaList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from area
    where update_time > #{updatedInPassedTime}
  </select>
</mapper>