<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.WarehouseStorageCenterMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.WarehouseStorageCenter">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="manage_admin_id" jdbcType="INTEGER" property="manageAdminId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="area_manage_id" jdbcType="INTEGER" property="areaManageId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="poi_note" jdbcType="VARCHAR" property="poiNote" />
    <result column="mail_to_address" jdbcType="VARCHAR" property="mailToAddress" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="person_contact" jdbcType="VARCHAR" property="personContact" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
  </resultMap>
  <sql id="Base_Column_List">
    id, warehouse_no, warehouse_name,
    manage_admin_id, type, area_manage_id,
    status, address, poi_note,
    mail_to_address, updater, update_time,
    creator, create_time, person_contact,
    phone
  </sql>
  <select id="listByWarehouseNoList" resultMap="BaseResultMap">
    select warehouse_no, warehouse_name
    from warehouse_storage_center
    where warehouse_no in
    <foreach collection="list" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>
</mapper>