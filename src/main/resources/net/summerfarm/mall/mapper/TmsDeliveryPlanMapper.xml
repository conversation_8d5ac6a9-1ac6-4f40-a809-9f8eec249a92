<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.TmsDeliveryPlanMapper">

    <select id="selectPlanByOuterNo" resultType="net.summerfarm.mall.model.domain.TmsDeliveryPlan">
          select
           id,
           delivery_time deliveryTime,
           contact_id contactId,
           store_no storeNo,
           `type`,
           delivery_type deliveryType,
           tenant_id tenantId,
           store_id storeId,
           status,
           order_no orderNo
        from tms_delivery_plan
        where  order_no =#{orderNo}
    </select>
</mapper>