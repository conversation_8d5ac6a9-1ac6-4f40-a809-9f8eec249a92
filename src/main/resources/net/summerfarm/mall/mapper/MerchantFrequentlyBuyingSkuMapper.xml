<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MerchantFrequentlyBuyingSkuMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantFrequentlyBuyingSku">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="top" jdbcType="INTEGER" property="top" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="recent_delete_time" jdbcType="TIMESTAMP" property="recentDeleteTime" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, create_time, update_time, m_id, sku, status, top, source, recent_delete_time, category_id
  </sql>
 
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_frequently_buying_sku
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="pageFrequentSkuPool" resultType="net.summerfarm.mall.model.vo.frequentSkuPool.FrequentSkuPoolVO">
    select ms.sku, ms.top, md.last_order_time lastOrderTime, md.last_order_quantity lastOrderQuantity, md.last_thirty_days_order_count lastThirtyDaysOrderCount,
           md.last_sixty_days_order_count lastSixtyDaysOrderCount, md.last_two_years_order_count lastTwoYearsOrderCount
    from merchant_frequently_buying_sku ms left join merchant_sku_order_data md on ms.sku = md.sku and md.m_id = ms.m_id and md.day_tag = #{dayTag}
    where ms.m_id = #{mId}
      and ms.status = #{status}
      <if test="categoryIdList != null and categoryIdList.size() > 0">
        and ms.category_id in
        <foreach item="item" index="index" collection="categoryIdList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    order by ms.top desc, md.last_thirty_days_order_count desc, md.last_order_time desc, ms.sku desc
  </select>


  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantFrequentlyBuyingSku">
    insert into merchant_frequently_buying_sku (id, create_time, update_time, 
      m_id, sku, status, top, 
      source, recent_delete_time)
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{mId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{top,jdbcType=INTEGER}, 
      #{source,jdbcType=INTEGER}, #{recentDeleteTime,jdbcType=TIMESTAMP})
  </insert>

  <insert id="batchInsert" parameterType="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantFrequentlyBuyingSku" useGeneratedKeys="true" keyProperty="id">
  insert into merchant_frequently_buying_sku (m_id, sku, status, top, source, recent_delete_time, category_id) values
  <foreach item="item" index="index" collection="list" separator=",">
    (#{item.mId,jdbcType=BIGINT}, #{item.sku,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.top,jdbcType=INTEGER}, #{item.source,jdbcType=INTEGER}, #{item.recentDeleteTime,jdbcType=TIMESTAMP}, #{item.categoryId,jdbcType=INTEGER})
  </foreach>
</insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantFrequentlyBuyingSku">
    insert into merchant_frequently_buying_sku
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="top != null">
        top,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="recentDeleteTime != null">
        recent_delete_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="top != null">
        #{top,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="recentDeleteTime != null">
        #{recentDeleteTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantFrequentlyBuyingSku">
    update merchant_frequently_buying_sku
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="top != null">
        top = #{top,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="recentDeleteTime != null">
        recent_delete_time = #{recentDeleteTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantFrequentlyBuyingSku">
    update merchant_frequently_buying_sku
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      m_id = #{mId,jdbcType=BIGINT},
      sku = #{sku,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      top = #{top,jdbcType=INTEGER},
      source = #{source,jdbcType=INTEGER},
      recent_delete_time = #{recentDeleteTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateStatusByMIdAndSku">
    update merchant_frequently_buying_sku
    set status = #{status,jdbcType=INTEGER},
        top = #{top,jdbcType=INTEGER},
      recent_delete_time = #{recentDeleteTime,jdbcType=TIMESTAMP}
    where m_id = #{mId,jdbcType=BIGINT}
    and sku = #{sku,jdbcType=VARCHAR}
  </update>

  <update id="updateStatusByMIdAndSkuList">
    update merchant_frequently_buying_sku
    set status = #{status,jdbcType=INTEGER},
      recent_delete_time = #{recentDeleteTime,jdbcType=TIMESTAMP}
    where m_id = #{mId,jdbcType=BIGINT}
    and sku in
    <foreach item="item" index="index" collection="skuCodeList" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>

  <update id="updateTopByMIdAndSku">
    update merchant_frequently_buying_sku
    set top = #{top,jdbcType=INTEGER}
    where m_id = #{mId,jdbcType=BIGINT}
    and sku = #{sku,jdbcType=VARCHAR}
  </update>

  <select id="listAllCategoryIdByMId" resultType="java.lang.Integer">
    select distinct category_id
    from merchant_frequently_buying_sku
    where m_id = #{mId,jdbcType=BIGINT}
    and status = #{status,jdbcType=INTEGER}
  </select>

  <select id="listAllByMIdAndSkuCodeList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_frequently_buying_sku
    where m_id = #{mId,jdbcType=BIGINT}
    and sku in
    <foreach collection="skuCodeList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
</select>

  <select id="getAllByMIdAndStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_frequently_buying_sku
    where m_id = #{mId,jdbcType=BIGINT}
    and status = #{status,jdbcType=INTEGER}
  </select>


</mapper>