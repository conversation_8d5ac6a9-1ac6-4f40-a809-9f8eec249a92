<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.CustomizationRequestSkuMappingMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.CustomizationRequestSkuMapping">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="customization_request_id" property="customizationRequestId" jdbcType="BIGINT"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="source_sku" property="sourceSku" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, customization_request_id, product_name, sku, source_sku, create_time, update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from customization_request_sku_mapping
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from customization_request_sku_mapping
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CustomizationRequestSkuMapping" useGeneratedKeys="true">
        insert into customization_request_sku_mapping (customization_request_id, product_name, sku, source_sku)
        values (#{customizationRequestId,jdbcType=BIGINT}, #{productName,jdbcType=VARCHAR}, 
        #{sku,jdbcType=VARCHAR}, #{sourceSku,jdbcType=VARCHAR})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CustomizationRequestSkuMapping" useGeneratedKeys="true">
        insert into customization_request_sku_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customizationRequestId != null">customization_request_id,</if>
            <if test="productName != null">product_name,</if>
            <if test="sku != null">sku,</if>
            <if test="sourceSku != null">source_sku,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customizationRequestId != null">#{customizationRequestId,jdbcType=BIGINT},</if>
            <if test="productName != null">#{productName,jdbcType=VARCHAR},</if>
            <if test="sku != null">#{sku,jdbcType=VARCHAR},</if>
            <if test="sourceSku != null">#{sourceSku,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.CustomizationRequestSkuMapping">
        update customization_request_sku_mapping
        <set>
            <if test="customizationRequestId != null">customization_request_id = #{customizationRequestId,jdbcType=BIGINT},</if>
            <if test="productName != null">product_name = #{productName,jdbcType=VARCHAR},</if>
            <if test="sku != null">sku = #{sku,jdbcType=VARCHAR},</if>
            <if test="sourceSku != null">source_sku = #{sourceSku,jdbcType=VARCHAR},</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.CustomizationRequestSkuMapping">
        update customization_request_sku_mapping
        set customization_request_id = #{customizationRequestId,jdbcType=BIGINT},
        product_name = #{productName,jdbcType=VARCHAR},
        sku = #{sku,jdbcType=VARCHAR},
        source_sku = #{sourceSku,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByCustomizationRequestId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from customization_request_sku_mapping
        where customization_request_id = #{customizationRequestId,jdbcType=BIGINT}
        order by create_time desc
    </select>

    <select id="selectBySku" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from customization_request_sku_mapping
        where sku = #{sku,jdbcType=VARCHAR}
    </select>

    <select id="selectBySourceSku" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from customization_request_sku_mapping
        where source_sku = #{sourceSku,jdbcType=VARCHAR}
        order by create_time desc
    </select>

    <delete id="deleteByCustomizationRequestId" parameterType="java.lang.Long">
        delete from customization_request_sku_mapping
        where customization_request_id = #{customizationRequestId,jdbcType=BIGINT}
    </delete>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into customization_request_sku_mapping (customization_request_id, product_name, sku, source_sku)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.customizationRequestId,jdbcType=BIGINT}, #{item.productName,jdbcType=VARCHAR}, 
            #{item.sku,jdbcType=VARCHAR}, #{item.sourceSku,jdbcType=VARCHAR})
        </foreach>
    </insert>

</mapper>
