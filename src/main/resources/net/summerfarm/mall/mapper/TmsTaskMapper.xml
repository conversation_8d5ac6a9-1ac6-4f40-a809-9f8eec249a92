<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.TmsTaskMapper">

    <select id="selectTmsTask" resultType="net.summerfarm.mall.model.domain.TmsTask">
        select addtime addTime,
               pick_up_time pickUpTime,
               loading_photos loadingPhotos,
               out_warehouse_temperature outWarehouseTemperature,
               over_out_time_reason overOutTimeReason,
               out_time outTime,
               punch_time punchTime
        from  tms_task
        where delivery_time =#{deliverTime} and store_no =#{storeNo} and path =#{path}
    </select>
</mapper>