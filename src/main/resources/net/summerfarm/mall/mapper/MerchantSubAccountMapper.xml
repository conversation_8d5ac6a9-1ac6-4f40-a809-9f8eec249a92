<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MerchantSubAccountMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MerchantSubAccount" >
    <result column="account_id" property="accountId" jdbcType="BIGINT" />
    <result column="m_id" property="mId" jdbcType="BIGINT" />
    <result column="contact" property="contact" jdbcType="VARCHAR" />
    <result column="phone" property="phone" jdbcType="VARCHAR" />
    <result column="unionid" property="unionid" jdbcType="VARCHAR" />
    <result column="openid" property="openid" jdbcType="VARCHAR" />
    <result column="mp_openid" property="mpOpenid" jdbcType="VARCHAR" />
    <result column="pop_view" property="popView" jdbcType="INTEGER" />
    <result column="first_pop_view" property="firstPopView" jdbcType="INTEGER" />
    <result column="cash_amount" property="cashAmount" jdbcType="DECIMAL" />
    <result column="cash_update_time" property="cashUpdateTime" jdbcType="TIMESTAMP" />
    <result column="login_time" property="loginTime" jdbcType="TIMESTAMP" />
    <result column="last_order_time" property="lastOrderTime" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="delete_flag" property="deleteFlag" jdbcType="INTEGER" />
    <result column="m_info" property="mInfo" jdbcType="VARCHAR" />
    <result column="register_time" property="registerTime" jdbcType="TIMESTAMP" />
    <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
    <result column="audit_user" property="auditUser" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="INTEGER"/>
  </resultMap>
  <sql id="Base_Column_List">
    account_id, m_id, contact,
      phone, unionid, openid,
      mp_openid, pop_view, first_pop_view,
      cash_amount, cash_update_time, login_time,
      last_order_time, status, delete_flag,
      m_info, register_time, audit_time,
      audit_user,`type`
  </sql>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.MerchantSubAccount" >
    insert into merchant_sub_account (account_id, m_id, contact, 
      phone, unionid, openid, 
      mp_openid, pop_view, first_pop_view, 
      cash_amount, cash_update_time, login_time, 
      last_order_time, status, delete_flag, 
      m_info, register_time, audit_time, 
      audit_user,`type`)
    values (#{accountId,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT}, #{contact,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, #{openid,jdbcType=VARCHAR}, 
      #{mpOpenid,jdbcType=VARCHAR}, #{popView,jdbcType=INTEGER}, #{firstPopView,jdbcType=INTEGER}, 
      #{cashAmount,jdbcType=DECIMAL}, #{cashUpdateTime,jdbcType=TIMESTAMP}, #{loginTime,jdbcType=TIMESTAMP}, 
      #{lastOrderTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{deleteFlag,jdbcType=INTEGER}, 
      #{mInfo,jdbcType=VARCHAR}, #{registerTime,jdbcType=TIMESTAMP}, #{auditTime,jdbcType=TIMESTAMP}, 
      #{auditUser,jdbcType=INTEGER}, #{type,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyColumn="account_id" keyProperty="accountId" parameterType="net.summerfarm.mall.model.domain.MerchantSubAccount" >
    insert into merchant_sub_account
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="accountId != null" >
        account_id,
      </if>
      <if test="mId != null" >
        m_id,
      </if>
      <if test="contact != null" >
        contact,
      </if>
      <if test="phone != null" >
        phone,
      </if>
      <if test="unionid != null" >
        unionid,
      </if>
      <if test="openid != null" >
        openid,
      </if>
      <if test="mpOpenid != null" >
        mp_openid,
      </if>
      <if test="popView != null" >
        pop_view,
      </if>
      <if test="firstPopView != null" >
        first_pop_view,
      </if>
      <if test="cashAmount != null" >
        cash_amount,
      </if>
      <if test="cashUpdateTime != null" >
        cash_update_time,
      </if>
      <if test="loginTime != null" >
        login_time,
      </if>
      <if test="lastOrderTime != null" >
        last_order_time,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="deleteFlag != null" >
        delete_flag,
      </if>
      <if test="mInfo != null" >
        m_info,
      </if>
      <if test="registerTime != null" >
        register_time,
      </if>
      <if test="auditTime != null" >
        audit_time,
      </if>
      <if test="auditUser != null" >
        audit_user,
      </if>
      <if test="type != null" >
        type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="accountId != null" >
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="mId != null" >
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="contact != null" >
        #{contact,jdbcType=VARCHAR},
      </if>
      <if test="phone != null" >
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null" >
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="openid != null" >
        #{openid,jdbcType=VARCHAR},
      </if>
      <if test="mpOpenid != null" >
        #{mpOpenid,jdbcType=VARCHAR},
      </if>
      <if test="popView != null" >
        #{popView,jdbcType=INTEGER},
      </if>
      <if test="firstPopView != null" >
        #{firstPopView,jdbcType=INTEGER},
      </if>
      <if test="cashAmount != null" >
        #{cashAmount,jdbcType=DECIMAL},
      </if>
      <if test="cashUpdateTime != null" >
        #{cashUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loginTime != null" >
        #{loginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastOrderTime != null" >
        #{lastOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="deleteFlag != null" >
        #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="mInfo != null" >
        #{mInfo,jdbcType=VARCHAR},
      </if>
      <if test="registerTime != null" >
        #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditTime != null" >
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditUser != null" >
        #{auditUser,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MerchantSubAccount">
    update merchant_sub_account
    <set>
      <if test="mId != null" >
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="contact != null" >
        contact = #{contact,jdbcType=VARCHAR},
      </if>
      <if test="phone != null" >
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null" >
        unionid = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="openid != null" >
        openid = #{openid,jdbcType=VARCHAR},
      </if>
      <if test="mpOpenid != null" >
        mp_openid = #{mpOpenid,jdbcType=VARCHAR},
      </if>
      <if test="popView != null" >
        pop_view = #{popView,jdbcType=INTEGER},
      </if>
      <if test="firstPopView != null" >
        first_pop_view = #{firstPopView,jdbcType=INTEGER},
      </if>
      <if test="cashAmount != null" >
        cash_amount = #{cashAmount,jdbcType=DECIMAL},
      </if>
      <if test="cashUpdateTime != null" >
        cash_update_time = #{cashUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loginTime != null" >
        login_time = #{loginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastOrderTime != null" >
        last_order_time = #{lastOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="deleteFlag != null" >
        delete_flag = #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="mInfo != null" >
        m_info = #{mInfo,jdbcType=VARCHAR},
      </if>
      <if test="registerTime != null" >
        register_time = #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditTime != null" >
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditUser != null" >
        audit_user = #{auditUser,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=INTEGER},
      </if>
    </set>
    where account_id = #{accountId}
  </update>
  <select id="selectByMId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account
    where m_id = #{mId} and delete_flag = 1
    <if test="status != null">
      and status = #{status}
    </if>
    order by status desc,account_id asc
  </select>
  <select id="selectOne" parameterType="hashmap" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account
    <where>
      delete_flag = 1
      <if test="accountId != null">
        and account_id = #{accountId}
      </if>
      <if test="phone != null">
        and phone = #{phone}
      </if>
      <if test="unionid != null">
        and unionid = #{unionid}
      </if>
      <if test="openId != null">
        and openid = #{openId}
      </if>
      <if test="mpOpenId != null">
        and mp_openid = #{mpOpenId}
      </if>
    </where>
  </select>
  <select id="selectAccountInfo" resultType="net.summerfarm.mall.model.domain.MerchantSubAccount">
    select account_id accountId, contact,phone, `type`,status
    from merchant_sub_account
    <where>
      delete_flag = 1
      <if test="mId != null">
        and m_id = #{mId}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
    </where>
    order by `type` asc, account_id asc
  </select>
  <update id="deleteByPrimaryKey" parameterType="long">
    update merchant_sub_account set delete_flag = 0 where account_id = #{accountId}
  </update>
  <update id="updateStatus">
    update merchant_sub_account set status = #{status} where account_id = #{accountId}
  </update>
  <update id="updateCashAmount">
     UPDATE merchant_sub_account
      SET cash_amount = #{cashAmount} + cash_amount,
      cash_update_time = #{cashUpdateTime}
    WHERE m_id = #{mId} and account_id = #{accountId}
  </update>
  <select id="selectIgnoreDel" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account
    <where>
      <if test="accountId != null">
        and account_id = #{accountId}
      </if>
      <if test="phone != null">
        and phone = #{phone}
      </if>
      <if test="unionid != null">
        and unionid = #{unionid}
      </if>
      <if test="openId != null">
        and openid = #{openId}
      </if>
      <if test="mpOpenId != null">
        and mp_openid = #{mpOpenId}
      </if>
    </where>
    order by account_id desc limit 1
  </select>
  <update id="updateUnPassByOpenId">
    UPDATE merchant_sub_account
        SET contact = #{contact},register_time=#{registerTime},delete_flag = 1
    WHERE m_id = #{mId} and openid = #{openId}
  </update>
  <delete id="deleteByMId">
    delete from merchant_sub_account where m_id = #{mId}
  </delete>
  <select id="selectMangerByMId" parameterType="long" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from merchant_sub_account where m_id = #{mId} and delete_flag = 1 and type = 0
  </select>

  <update id="updateLoginTime">
    UPDATE merchant_sub_account
        SET login_time = #{loginTime}
    WHERE openid = #{openId} and delete_flag = 1
  </update>

  <select id="queryByOpenId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account where openid = #{openid} and delete_flag = 1
  </select>

  <select id="listContactByAccountIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account
    where account_id in
    <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
      #{accountId}
    </foreach>
    </select>

  <select id="selectByEntity" parameterType="net.summerfarm.mall.model.domain.MerchantSubAccount" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account
    <where>
      <if test="deleteFlag != null">
        and delete_flag = #{deleteFlag}
      </if>
      <if test="accountId != null">
        and account_id = #{accountId}
      </if>
      <if test="type != null">
        and `type` = #{type}
      </if>
    </where>
  </select>

  <select id="selectAllAccountByMId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account
    where m_id = #{mId}
  </select>

  <update id="batchUpdateByPrimaryKey" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" separator=";">
      update merchant_sub_account
      <set>
        <if test="item.phone != null" >
          phone = #{item.phone,jdbcType=VARCHAR},
        </if>
        <if test="item.unionid != null" >
          unionid = #{item.unionid,jdbcType=VARCHAR},
        </if>
        <if test="item.openid != null" >
          openid = #{item.openid,jdbcType=VARCHAR},
        </if>
        <if test="item.mpOpenid != null" >
          mp_openid = #{item.mpOpenid,jdbcType=VARCHAR},
        </if>
        <if test="item.status != null" >
          status = #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.deleteFlag != null" >
          delete_flag = #{item.deleteFlag,jdbcType=INTEGER},
        </if>
      </set>
      where account_id = #{item.accountId}
    </foreach>
  </update>

  <select id="selectNoUnionIdLimit" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account
    where length(`openid`) >12 and  unionid is null
    ORDER BY  `account_id`  desc
      limit #{offset}, #{pageSize}

  </select>

  <select id="selectCountByMAreaNosUnionid" resultType="java.lang.Long">
    SELECT  COUNT(1) FROM  `merchant_sub_account`  msa
    JOIN `merchant` m on msa.`m_id`   = m.`m_id`
    where m.`area_no` in
    <foreach collection="areaCodes" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and msa.`unionid` = #{unionid}

  </select>

</mapper>