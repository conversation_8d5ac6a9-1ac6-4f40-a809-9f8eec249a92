<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.FrontCategoryToCategoryMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.FrontCategoryToCategory">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="front_category_id" jdbcType="INTEGER" property="frontCategoryId" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, front_category_id, category_id, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from front_category_to_category
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from front_category_to_category
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.FrontCategoryToCategory" useGeneratedKeys="true">
    insert into front_category_to_category (front_category_id, category_id, creator, 
      create_time)
    values (#{frontCategoryId,jdbcType=INTEGER}, #{categoryId,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.FrontCategoryToCategory" useGeneratedKeys="true">
    insert into front_category_to_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="frontCategoryId != null">
        front_category_id,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="frontCategoryId != null">
        #{frontCategoryId,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.FrontCategoryToCategory">
    update front_category_to_category
    <set>
      <if test="frontCategoryId != null">
        front_category_id = #{frontCategoryId,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.FrontCategoryToCategory">
    update front_category_to_category
    set front_category_id = #{frontCategoryId,jdbcType=INTEGER},
      category_id = #{categoryId,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectCategoryId" resultType="integer">
    select fctc.category_id
    from front_category_to_category fctc
    where fctc.front_category_id = #{frontCategoryId}
  </select>
  <select id="selectCategoryIdsByFrontCategoryIds" resultType="integer">
    select fctc.category_id
    from front_category_to_category fctc
    where fctc.front_category_id in
            <foreach collection="frontCategoryIds" item="frontCategoryId" open="(" close=")" separator=",">
              #{frontCategoryId}
            </foreach>
  </select>

  <select id="listBySecondFrontId" resultType="integer">
    select distinct fctc.category_id
    from front_category_to_category fctc
        left join front_category fc on fctc.front_category_id = fc.id
    where fc.parent_id = #{frontCategoryId}
  </select>
</mapper>