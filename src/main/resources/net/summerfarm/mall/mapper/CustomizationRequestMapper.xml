<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.CustomizationRequestMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.CustomizationRequests">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result column="account_name" property="accountName" jdbcType="VARCHAR"/>
        <result column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="initiate_order_no" property="initiateOrderNo" jdbcType="VARCHAR"/>
        <result column="color_count" property="colorCount" jdbcType="INTEGER"/>
        <result column="design_submit_time" property="designSubmitTime" jdbcType="TIMESTAMP"/>
        <result column="refuse_time" property="refuseTime" jdbcType="TIMESTAMP"/>
        <result column="close_time" property="closeTime" jdbcType="TIMESTAMP"/>
        <result column="agree_time" property="agreeTime" jdbcType="TIMESTAMP"/>
        <result column="reference_image" property="referenceImage" jdbcType="VARCHAR"/>
        <result column="log_image" property="logImage" jdbcType="VARCHAR"/>
        <result column="sample_image" property="sampleImage" jdbcType="VARCHAR"/>
        <result column="design_image" property="designImage" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="refuse_reason" property="refuseReason" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="communication_notes" property="communicationNotes" jdbcType="LONGVARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, store_name, account_name, m_id, initiate_order_no, color_count, design_submit_time,
        refuse_time, close_time, agree_time, reference_image, log_image, sample_image,
        design_image, status, refuse_reason, remark, communication_notes, create_time, update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from customization_requests
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from customization_requests
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CustomizationRequests" useGeneratedKeys="true">
        insert into customization_requests (store_name, account_name, m_id,
        initiate_order_no, color_count, design_submit_time,
        refuse_time, close_time, agree_time,
        reference_image, log_image, sample_image, design_image, status,
        refuse_reason, remark, communication_notes)
        values (#{storeName,jdbcType=VARCHAR}, #{accountName,jdbcType=VARCHAR}, #{mId,jdbcType=BIGINT},
        #{initiateOrderNo,jdbcType=VARCHAR}, #{colorCount,jdbcType=INTEGER}, #{designSubmitTime,jdbcType=TIMESTAMP},
        #{refuseTime,jdbcType=TIMESTAMP}, #{closeTime,jdbcType=TIMESTAMP}, #{agreeTime,jdbcType=TIMESTAMP},
        #{referenceImage,jdbcType=VARCHAR}, #{logImage,jdbcType=VARCHAR}, #{sampleImage,jdbcType=VARCHAR}, 
        #{designImage,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
        #{refuseReason,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{communicationNotes,jdbcType=LONGVARCHAR})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CustomizationRequests" useGeneratedKeys="true">
        insert into customization_requests
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeName != null">store_name,</if>
            <if test="accountName != null">account_name,</if>
            <if test="mId != null">m_id,</if>
            <if test="initiateOrderNo != null">initiate_order_no,</if>
            <if test="colorCount != null">color_count,</if>
            <if test="designSubmitTime != null">design_submit_time,</if>
            <if test="refuseTime != null">refuse_time,</if>
            <if test="closeTime != null">close_time,</if>
            <if test="agreeTime != null">agree_time,</if>
            <if test="referenceImage != null">reference_image,</if>
            <if test="logImage != null">log_image,</if>
            <if test="sampleImage != null">sample_image,</if>
            <if test="designImage != null">design_image,</if>
            <if test="status != null">status,</if>
            <if test="refuseReason != null">refuse_reason,</if>
            <if test="remark != null">remark,</if>
            <if test="communicationNotes != null">communication_notes,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="storeName != null">#{storeName,jdbcType=VARCHAR},</if>
            <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
            <if test="mId != null">#{mId,jdbcType=BIGINT},</if>
            <if test="initiateOrderNo != null">#{initiateOrderNo,jdbcType=VARCHAR},</if>
            <if test="colorCount != null">#{colorCount,jdbcType=INTEGER},</if>
            <if test="designSubmitTime != null">#{designSubmitTime,jdbcType=TIMESTAMP},</if>
            <if test="refuseTime != null">#{refuseTime,jdbcType=TIMESTAMP},</if>
            <if test="closeTime != null">#{closeTime,jdbcType=TIMESTAMP},</if>
            <if test="agreeTime != null">#{agreeTime,jdbcType=TIMESTAMP},</if>
            <if test="referenceImage != null">#{referenceImage,jdbcType=VARCHAR},</if>
            <if test="logImage != null">#{logImage,jdbcType=VARCHAR},</if>
            <if test="sampleImage != null">#{sampleImage,jdbcType=VARCHAR},</if>
            <if test="designImage != null">#{designImage,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=INTEGER},</if>
            <if test="refuseReason != null">#{refuseReason,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="communicationNotes != null">#{communicationNotes,jdbcType=LONGVARCHAR},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.CustomizationRequests">
        update customization_requests
        <set>
            <if test="storeName != null">store_name = #{storeName,jdbcType=VARCHAR},</if>
            <if test="accountName != null">account_name = #{accountName,jdbcType=VARCHAR},</if>
            <if test="mId != null">m_id = #{mId,jdbcType=BIGINT},</if>
            <if test="initiateOrderNo != null">initiate_order_no = #{initiateOrderNo,jdbcType=VARCHAR},</if>
            <if test="colorCount != null">color_count = #{colorCount,jdbcType=INTEGER},</if>
            <if test="designSubmitTime != null">design_submit_time = #{designSubmitTime,jdbcType=TIMESTAMP},</if>
            <if test="refuseTime != null">refuse_time = #{refuseTime,jdbcType=TIMESTAMP},</if>
            <if test="closeTime != null">close_time = #{closeTime,jdbcType=TIMESTAMP},</if>
            <if test="agreeTime != null">agree_time = #{agreeTime,jdbcType=TIMESTAMP},</if>
            <if test="referenceImage != null">reference_image = #{referenceImage,jdbcType=VARCHAR},</if>
            <if test="logImage != null">log_image = #{logImage,jdbcType=VARCHAR},</if>
            <if test="sampleImage != null">sample_image = #{sampleImage,jdbcType=VARCHAR},</if>
            <if test="designImage != null">design_image = #{designImage,jdbcType=VARCHAR},</if>
            <if test="status != null">status = #{status,jdbcType=INTEGER},</if>
            <if test="refuseReason != null">refuse_reason = #{refuseReason,jdbcType=VARCHAR},</if>
            <if test="remark != null">remark = #{remark,jdbcType=VARCHAR},</if>
            <if test="communicationNotes != null">communication_notes = #{communicationNotes,jdbcType=LONGVARCHAR},</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.CustomizationRequests">
        update customization_requests
        set store_name = #{storeName,jdbcType=VARCHAR},
        account_name = #{accountName,jdbcType=VARCHAR},
        m_id = #{mId,jdbcType=BIGINT},
        initiate_order_no = #{initiateOrderNo,jdbcType=VARCHAR},
        color_count = #{colorCount,jdbcType=INTEGER},
        design_submit_time = #{designSubmitTime,jdbcType=TIMESTAMP},
        refuse_time = #{refuseTime,jdbcType=TIMESTAMP},
        close_time = #{closeTime,jdbcType=TIMESTAMP},
        agree_time = #{agreeTime,jdbcType=TIMESTAMP},
        reference_image = #{referenceImage,jdbcType=VARCHAR},
        log_image = #{logImage,jdbcType=VARCHAR},
        sample_image = #{sampleImage,jdbcType=VARCHAR},
        design_image = #{designImage,jdbcType=VARCHAR},
        status = #{status,jdbcType=INTEGER},
        refuse_reason = #{refuseReason,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        communication_notes = #{communicationNotes,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByMerchantId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from customization_requests
        where m_id = #{mId,jdbcType=BIGINT}
        order by create_time desc
    </select>

    <select id="selectByOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from customization_requests
        where initiate_order_no = #{orderNo,jdbcType=VARCHAR}
    </select>

    <select id="selectByStatus" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from customization_requests
        where status = #{status,jdbcType=INTEGER}
        order by create_time desc
    </select>

    <select id="selectByMerchantIdAndStatus" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from customization_requests
        where m_id = #{mId,jdbcType=BIGINT} and status = #{status,jdbcType=INTEGER}
        order by create_time desc
    </select>

</mapper>
