<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MajorRebateMapper" >


  <select id="selectOne" resultType="net.summerfarm.mall.model.domain.MajorRebate" parameterType="net.summerfarm.mall.model.domain.MajorRebate">

    SELECT id, sku, name, weight, type, number,admin_id adminId, area_no areaNo,area_name areaName , cate
    FROM major_rebate b
    where b.status=1
    <if test="areaNo != null">
      AND b.area_no =#{areaNo}
    </if>
    <if test="adminId != null">
      AND b.admin_id =#{adminId}
    </if>
    <if test="sku != null">
      AND b.sku = #{sku}
    </if>

    <if test="cate != null">
      AND b.cate = #{cate}
    </if>
  </select>

  <select id="selectValidSkuList" resultType="net.summerfarm.mall.model.domain.MajorRebate">
    select id,
       sku,
       name,
       weight,
       type,
       number,
       admin_id adminId,
       area_no areaNo,
       area_name areaName,
       cate,
       status
     from major_rebate
     where cate = 2 and status = 1 and admin_id = #{adminId} and sku = #{sku}
  </select>
</mapper>