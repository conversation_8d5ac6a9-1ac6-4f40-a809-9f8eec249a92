<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.AfterSaleDeliveryDetailMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.AfterSaleDeliveryDetail">

    </resultMap>

    <sql id="Base_Column_List">
    admin_id, create_time, login_fail_times, is_disabled, username, password,
    login_time, realname, gender, department, phone, operate_id,close_order_type closeOrderType
  </sql>

    <insert id="insertAfterSaleDeliveryDetail" parameterType="net.summerfarm.mall.model.domain.AfterSaleDeliveryDetail">
      insert into  after_sale_delivery_detail (gmt_create,gmt_modified,as_delivery_path_id,sku,weight,quantity,pd_name,`type`,status)
      values (now(),now(),#{asDeliveryPathId},#{sku},#{weight},#{quantity},#{pdName},#{type},#{status})
  </insert>

    <insert id="insertAfterSaleDeliveryDetailList" parameterType="net.summerfarm.mall.model.domain.AfterSaleDeliveryDetail">

        insert into  after_sale_delivery_detail (gmt_create,gmt_modified,as_delivery_path_id,sku,weight,quantity,pd_name,`type`,status)
        values
        <foreach collection="list" separator="," item="item">
            (now(),now(),#{item.asDeliveryPathId},#{item.sku},#{item.weight},#{item.quantity},#{item.pdName},#{item.type},#{item.status})
        </foreach>
    </insert>

    <select id="selectDetail" resultType="net.summerfarm.mall.model.domain.AfterSaleDeliveryDetail">
     select  gmt_create as createTime,gmt_modified as updateTime,as_delivery_path_id as asDeliveryPathId,sku,weight,quantity,pd_name as pdName,`type`,status
     from  after_sale_delivery_detail
     where as_delivery_path_id = #{asDeliveryPathId}
    </select>

    <select id="selectDetailByIds" resultType="net.summerfarm.mall.model.domain.AfterSaleDeliveryDetail">
        select as_delivery_path_id as asDeliveryPathId,sku,quantity
        from  after_sale_delivery_detail
        where as_delivery_path_id in
        <foreach collection="asDeliveryPathIds" open="(" separator="," close=")" item="asDeliveryPathId">
            #{asDeliveryPathId}
        </foreach>
    </select>
</mapper>