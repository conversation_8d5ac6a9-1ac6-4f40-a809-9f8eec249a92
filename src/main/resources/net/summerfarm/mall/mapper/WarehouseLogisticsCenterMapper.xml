<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.WarehouseLogisticsCenterMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.WarehouseLogisticsCenter" >
    <result column="id" property="id" jdbcType="INTEGER" />
    <result column="store_no" property="storeNo" jdbcType="INTEGER" />
    <result column="store_name" property="storeName" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="manage_admin_id" property="manageAdminId" jdbcType="INTEGER" />
    <result column="poi_note" property="poiNote" jdbcType="VARCHAR" />
    <result column="address" property="address" jdbcType="VARCHAR" />
    <result column="close_order_type" property="closeOrderType" jdbcType="INTEGER" />
    <result column="origin_store_no" property="originStoreNo" jdbcType="INTEGER" />
    <result column="sot_finish_time" property="sotFinishTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="INTEGER" />
    <result column="updater" property="updater" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="close_time" property="closeTime" jdbcType="VARCHAR" />
    <result column="update_close_time" property="updateCloseTime" jdbcType="VARCHAR" />
    <result column="person_contact" property="personContact" jdbcType="VARCHAR" />
    <result column="phone" property="phone" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List">
    id, store_no, store_name,
    status, manage_admin_id, poi_note,
    address, close_order_type, origin_store_no,
    sot_finish_time, creator, updater,
    update_time, create_time, close_time,
    update_close_time, person_contact, phone
  </sql>

  <select id="listByStoreNoList" resultMap="BaseResultMap">
    select store_no, store_name
    from warehouse_logistics_center
    where store_no in
    <foreach collection="list" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>
</mapper>