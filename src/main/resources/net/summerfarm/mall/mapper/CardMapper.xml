<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.CardMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Card">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="card_type" property="cardType" jdbcType="TINYINT" />
        <result column="money" property="money" jdbcType="DECIMAL"/>
        <result column="threshold" property="threshold" jdbcType="DECIMAL"/>
        <result column="type" property="type" jdbcType="TINYINT"/>
        <result column="vaild_date" property="vaildDate"/>
        <result column="vaild_time" property="vaildTime" jdbcType="INTEGER"/>
        <result column="grouping" property="grouping" jdbcType="INTEGER" />
        <result column="times" property="times" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="add_time" property="addTime"/>
        <result column="status" property="status" jdbcType="INTEGER" />
    </resultMap>

    <sql id="BaseColumn">
        id,name,code,card_type,money,threshold,type,vaild_date,vaild_time,grouping,times,remark,add_time,status
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM card
        WHERE id = #{id,jdbcType=INTEGER}
    </select>
</mapper>