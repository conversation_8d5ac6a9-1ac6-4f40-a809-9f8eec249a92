<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="net.summerfarm.mall.mapper.MarketRuleHistoryMapper">


    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MarketRuleHistory">


     insert into market_rule_history (detail,order_no,market_rule_id,value,rule_level,type,send_status)
     values (#{detail}, #{orderNo}, #{marketRuleId},#{value}, #{ruleLevel},#{type},#{sendStatus})


  </insert>

    <select id="select"   resultType="net.summerfarm.mall.model.domain.MarketRuleHistory">
      select order_no orderNo,value,market_rule_id marketRuleId,detail,rule_level ruleLevel,value, send_status sendStatus, id from market_rule_history
      where order_no=#{orderNo} and type=#{type}


    </select>

    <update id="updateSendStatus">
        update market_rule_history set send_status=#{sendStatus} where id=#{id}
    </update>

</mapper>