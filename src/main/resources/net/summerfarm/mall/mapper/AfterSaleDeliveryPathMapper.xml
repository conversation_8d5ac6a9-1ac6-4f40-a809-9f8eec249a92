<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.AfterSaleDeliveryPathMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.AfterSaleDeliveryPath">

    </resultMap>

    <sql id="Base_Column_List">
    admin_id, create_time, login_fail_times, is_disabled, username, password,
    login_time, realname, gender, department, phone, operate_id,close_order_type closeOrderType
  </sql>

  <insert id="insertAfterSaleDeliveryPath" parameterType="net.summerfarm.mall.model.domain.AfterSaleDeliveryPath"
          keyProperty="id" useGeneratedKeys="true" keyColumn="id">
      insert into  after_sale_delivery_path (gmt_create,gmt_modified,m_id,delivery_time,concat_id,after_sale_no,out_store_no,status,`type`)
      values (now(),now(),#{mId},#{deliveryTime},#{concatId},#{afterSaleNo},#{outStoreNo},#{status},#{type})
  </insert>
    <update id="updateAfterSaleDeliveryPath" parameterType="net.summerfarm.mall.model.domain.AfterSaleDeliveryPath">
        update after_sale_delivery_path
        <set>
            <if test="status != null">
                status = #{status},
            </if>
        </set>
        where after_sale_no =#{afterSaleNo}
    </update>
    <update id="updateDeliveryPathByPath">
     update after_sale_delivery_path set status = #{status}
         where delivery_time =#{deliveryTime} and concat_id=#{concatId} and status > 0
    </update>
    <update id="updateDeliveryPathStatus">
        update after_sale_delivery_path set status = 2
         where delivery_time =#{deliveryTime} and concat_id=#{contactId} and status > 0
    </update>
    <select id="selectByAfterSaleOrderNo" resultType="net.summerfarm.mall.model.domain.AfterSaleDeliveryPath">
        select id, after_sale_no afterSaleNo,status, delivery_time deliveryTime from after_sale_delivery_path where after_sale_no = #{afterSaleOrderNo} limit 1
    </select>
    <select id="selectById" resultType="net.summerfarm.mall.model.domain.AfterSaleDeliveryPath">

     select p.id,p.gmt_create as createTime,p.gmt_modified as updateTime,p.m_id as mId ,p.delivery_time as deliveryTime,p.concat_id concatId,p.after_sale_no as afterSaleNo,
       p.type,p.out_store_no as outStoreNo,p.status
       from after_sale_delivery_path p  where p.id = #{id}

    </select>

    <select id="findByAfterSaleOrderNo" resultType="net.summerfarm.mall.model.domain.AfterSaleDeliveryPath">
        select id,gmt_create as createTime,gmt_modified as updateTime,m_id as mId ,delivery_time as deliveryTime,
        concat_id concatId,after_sale_no as afterSaleNo,
        type,out_store_no as outStoreNo,status
        from after_sale_delivery_path
        where after_sale_no = #{afterSaleOrderNo} limit 1
    </select>

    <select id="countByDeliveryDate" resultType="int">
        select count(*)
        from after_sale_delivery_path asdp
        left join after_sale_order aso on aso.after_sale_order_no = asdp.after_sale_no
        where asdp.delivery_time = #{deliveryTime,jdbcType=DATE}
        and aso.status != 0
    </select>

    <select id="listByDeliveryDate" resultType="net.summerfarm.mall.model.vo.ofc.AfterSaleOrderCompareVO">
        select asdp.id as afterSaleDeliveryPathId,
                asdp.delivery_time as deliveryTime,
                asdp.concat_id as contactId,
                asdp.after_sale_no as afterSaleOrderNo,
                asdp.type as afterSaleOrderType,
                asdp.out_store_no as orderStoreNo,
                aso.order_no as orderNo,
                aso.status as status
        from after_sale_delivery_path asdp
        left join after_sale_order aso on aso.after_sale_order_no = asdp.after_sale_no
        where asdp.delivery_time = #{deliveryTime,jdbcType=DATE}
        and aso.status != 0
        limit #{pageIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>
    <select id="selectAfterSaleDetail" resultType="int">
        select count(*)
        from after_sale_delivery_path asdp
        inner join after_sale_delivery_detail asdd on asdp.id = asdd.as_delivery_path_id and asdd.status = 1
        inner join after_sale_order aso on asdp.after_sale_no = aso.after_sale_order_no
        inner join inventory i on asdd.sku = i.sku
        inner join products p on i.pd_id = p.pd_id
        LEFT JOIN admin ad on i.admin_id = ad.admin_id
        left join category c on c.id = p.category_id
        where asdp.delivery_time= #{deliveryTime} and asdp.concat_id = #{concatId} and asdp.status >= 1 and asdp.after_sale_no =#{afterSaleNo}
    </select>

    <select id="selectPathByNo" resultType="net.summerfarm.mall.model.domain.AfterSaleDeliveryPath">
        select id,delivery_time deliveryTime,out_store_no outStoreNo,concat_id  concatId,`type`
        from after_sale_delivery_path where after_sale_no = #{afterSaleNo}
    </select>

    <select id="selectDetail" resultType="net.summerfarm.mall.model.domain.AfterSaleDeliveryDetail">
        select sku,quantity from after_sale_delivery_detail
        where as_delivery_path_id = #{id} and status = 1 and `type` = 0
    </select>

    <select id="getPathDTOByAfterSaleOrderNo" resultType="net.summerfarm.mall.model.dto.AfterSaleDeliveryPathDTO">
        select asdp.after_sale_no afterSaleNo, asdd.sku sku
        from after_sale_delivery_path asdp inner join after_sale_delivery_detail asdd on asdp.id = asdd.as_delivery_path_id
        where asdp.after_sale_no = #{afterSaleOrderNo} and asdd.status = 1
    </select>
    <select id="selectExchangeGoods" resultType="net.summerfarm.mall.model.domain.ExchangeGoods">
        select asdd.sku,asdd.quantity,asdd.pd_name pdName,asdd.weight from after_sale_delivery_path asdp
        left join after_sale_delivery_detail asdd on asdp.id = asdd.as_delivery_path_id
        where asdd.type = 1  and asdp.after_sale_no = #{afterSaleNo}
    </select>

    <update id="updateDeliveryPath">
        update after_sale_delivery_path set status = #{status} where after_sale_no  = #{afterSaleNo}
    </update>
  <update id="updateById" parameterType="net.summerfarm.mall.model.domain.AfterSaleDeliveryPath">
    update after_sale_delivery_path
    <set>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime},
      </if>
      <if test="concatId != null">
        concat_id = #{concatId},
      </if>
      <if test="outStoreNo != null">
        out_store_no = #{outStoreNo},
      </if>
      <if test="type != null">
        type = #{type},
      </if>
      <if test="updateTime != null">
          gmt_modified = #{updateTime},
      </if>
    </set>
    where id = #{id}
  </update>
</mapper>