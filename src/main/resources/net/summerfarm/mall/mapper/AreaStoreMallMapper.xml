<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.AreaStoreMallMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.AreaStore" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="area_no" property="areaNo" jdbcType="INTEGER" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, area_no, sku, quantity, update_time
  </sql>


    <update id="updateOnlineQuantityAndChange">
        UPDATE area_store ar SET ar.online_quantity = #{onlineChange} + ar.online_quantity, ar.`change` = #{changeChange} + ar.`change`
        WHERE ar.sku = #{sku} AND ar.area_no = #{storeNo}
    </update>

  <update id="updateLockStock">
    update area_store ar set ar.lock_quantity=#{lockQuantity}+ar.lock_quantity
    WHERE ar.sku=#{sku} and ar.area_no=#{storeNo}
  </update>

  <update id="updateSaleLockStock">
    update area_store ar set ar.lock_quantity=#{lockQuantity}+ar.lock_quantity, ar.sale_lock_quantity = #{lockQuantity} + ar.sale_lock_quantity
    ,ar.reserve_use_quantity = ar.reserve_use_quantity + #{reserveQuantity}
    WHERE ar.sku=#{sku} and ar.area_no= #{storeNo}
  </update>




    <select id="selectByStoreNoAndSku" resultType="net.summerfarm.mall.model.domain.AreaStore">
    SELECT t.id, t.area_no areaNo, t.sku, t.quantity, t.admin_id adminId, t.lead_time leadTime,t.sync,
           t.online_quantity onlineQuantity,t.lock_quantity lockQuantity,t.change, auto_transfer autoTransfer,
           t.safe_quantity safeQuantity,
           t.reserve_max_quantity reserveMaxQuantity,
           t.reserve_min_quantity reserveMinQuantity,
           t.reserve_use_quantity reserveUseQuantity,
           t.support_reserved supportReserved,
           t.warning_quantity warningQuantity,
           t.send_warning_flag sendWarningFlag
    FROM area_store t
    WHERE t.area_no = #{storeNo}
      and t.sku = #{sku}
  </select>

  <select id="selectByOrderNoNew" resultType="net.summerfarm.mall.model.domain.AreaStore">
    SELECT ar.online_quantity onlineQuantity, ar.sku
    FROM orders o
    INNER JOIN order_item oi ON o.order_no = oi.order_no AND o.order_no = #{orderNo}
    LEFT join (
         select area_no,store_no from fence where   status = 0
         group by area_no
        ) f on f.area_no = o.area_no
    left join warehouse_inventory_mapping wim  on wim.sku = oi.sku and wim.store_no = f.store_no
    LEFT JOIN area_store ar ON ar.area_no = wim.warehouse_no AND oi.sku = ar.sku
  </select>

  <select id="selectByStoreNoAndSkuNew" resultType="net.summerfarm.mall.model.domain.AreaStore">
    SELECT t.id, t.area_no areaNo, t.sku, t.quantity, t.admin_id adminId, t.lead_time leadTime,t.sync,
           t.online_quantity onlineQuantity,t.lock_quantity lockQuantity,t.change, auto_transfer autoTransfer,
           t.safe_quantity safeQuantity,
           t.reserve_max_quantity reserveMaxQuantity,
           t.reserve_min_quantity reserveMinQuantity,
           t.reserve_use_quantity reserveUseQuantity,
           t.support_reserved supportReserved
    FROM area_store t
    left join warehouse_inventory_mapping wim on wim.warehouse_no = t.area_no and t.sku = wim.sku
    WHERE wim.store_no = #{storeNo} and t.sku = #{sku}
  </select>
    <select id="onlineQuantity" resultType="int">
        SELECT IFNULL(online_quantity,0)
        from area_store
        WHERE area_no = #{areaNo} and sku = #{sku}
    </select>

  <select id="selectByStoreNoAndSkus" resultType="net.summerfarm.mall.model.domain.AreaStore">
      select wim.sku,online_quantity onlineQuantity
      from warehouse_inventory_mapping wim
      LEFT JOIN area_store a3 on a3.sku = wim.sku and a3.area_no = wim.warehouse_no
      where wim.sku in
            <foreach collection="skus" item="sku" separator="," open="(" close=")">
                #{sku}
            </foreach>
  </select>

    <select id="selectWithOutDataPermission" resultType="net.summerfarm.mall.model.domain.AreaStore"
            parameterType="net.summerfarm.mall.model.domain.AreaStore">
        SELECT t.id, t.area_no areaNo, t.sku, t.quantity, t.admin_id adminId, t.lead_time leadTime,t.sync,t.auto_transfer autoTransfer,
        t.online_quantity onlineQuantity,t.road_quantity roadQuantity,t.lock_quantity lockQuantity,t.safe_quantity safeQuantity,t.change,t.status,advance_quantity advanceQuantity,
        t.cost_price costPrice,t.market_price marketPrice,t.reserve_max_quantity reserveMaxQuantity,t.reserve_min_quantity reserveMinQuantity ,t.reserve_use_quantity reserveUseQuantity,
        t.support_reserved supportReserved,t.sale_lock_quantity saleLockQuantity
        , t.tenant_id tenantId, t.warehouse_tenant_id warehouseTenantId
        FROM area_store t
        <where>
            <if test="areaNo != null">
                and t.area_no = #{areaNo}
            </if>
            <if test="sku != null">
                and t.sku = #{sku}
            </if>
        </where>
    </select>

    <select id="selectOne" resultType="net.summerfarm.mall.model.domain.AreaStore"
            parameterType="net.summerfarm.mall.model.domain.AreaStore">
        SELECT t.id, t.area_no areaNo, t.sku, t.quantity, t.admin_id adminId, t.lead_time leadTime,t.sync,
        t.online_quantity onlineQuantity,t.road_quantity roadQuantity,t.lock_quantity lockQuantity,
        t.safe_quantity safeQuantity,t.change,t.status,t.cost_price costPrice,t.market_price marketPrice,
        t.reserve_max_quantity reserveMaxQuantity,t.reserve_min_quantity reserveMinQuantity,
        t.reserve_use_quantity reserveUseQuantity,t.support_reserved supportReserved,t.advance_quantity advanceQuantity,
        t.sale_lock_quantity saleLockQuantity
        , t.tenant_id tenantId, t.warehouse_tenant_id warehouseTenantId
        FROM area_store t
        <where>
            <if test="areaNo != null">
                and t.area_no = #{areaNo}
            </if>
            <if test="sku != null">
                and t.sku = #{sku}
            </if>
        </where>
    </select>


    <select id = "selectByAreaNoSku" resultType="net.summerfarm.mall.model.domain.AreaStore">
        SELECT ar.id, ar.area_no areaNo, ar.sku, ar.quantity, ar.admin_id adminId, ar.lead_time leadTime,ar.sync,
        ar.online_quantity onlineQuantity,ar.road_quantity roadQuantity,ar.lock_quantity lockQuantity,
        ar.safe_quantity safeQuantity,ar.change,ar.status,ar.cost_price costPrice,ar.market_price marketPrice,
        ar.reserve_max_quantity reserveMaxQuantity,ar.reserve_min_quantity reserveMinQuantity,
        ar.reserve_use_quantity reserveUseQuantity,ar.support_reserved supportReserved,ar.advance_quantity advanceQuantity,
        ar.sale_lock_quantity saleLockQuantity
        , ar.tenant_id tenantId, ar.warehouse_tenant_id warehouseTenantId
        from  (
                  select area_no,store_no from fence where area_no = #{areaNo} and status = 0 limit  1
              ) f
        INNER JOIN warehouse_inventory_mapping wim on wim.store_no = f.store_no
        INNER JOIN area_store ar on ar.sku=wim.sku and wim.warehouse_no = ar.area_no
        <where>
            <if test="skus != null and skus.size > 0">
                and wim.`sku` in
                <foreach collection="skus" open="(" separator="," close=")" item="sku">
                    #{sku}
                </foreach>
            </if>
        </where>
    </select>
</mapper>