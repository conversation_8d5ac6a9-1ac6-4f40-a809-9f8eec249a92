<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.CompleteDeliveryMapper">

    <select id="selectCompleteDeliveryTime" resultType="java.lang.String">
        select cd.complete_delivery_time
        from complete_delivery cd
                 inner join complete_delivery_ad_code_mapping m on cd.id = m.complete_delivery_id
                 inner join ad_code_msg ad on m.ad_code = ad.ad_code and ad.status = 0
        where ad.city = #{city} and ad.area = #{area} and cd.status = 0;
    </select>
</mapper>
