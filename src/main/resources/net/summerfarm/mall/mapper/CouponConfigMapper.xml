<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.CouponConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.CouponConfig">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="buy_money" jdbcType="DECIMAL" property="buyMoney" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `level`, buy_money, create_time, creator, `status`, update_time, updater
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from coupon_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from coupon_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CouponConfig" useGeneratedKeys="true">
    insert into coupon_config (`level`, buy_money, create_time, 
      creator, `status`, update_time, 
      updater)
    values (#{level,jdbcType=INTEGER}, #{buyMoney,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CouponConfig" useGeneratedKeys="true">
    insert into coupon_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="level != null">
        `level`,
      </if>
      <if test="buyMoney != null">
        buy_money,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="level != null">
        #{level,jdbcType=INTEGER},
      </if>
      <if test="buyMoney != null">
        #{buyMoney,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.CouponConfig">
    update coupon_config
    <set>
      <if test="level != null">
        `level` = #{level,jdbcType=INTEGER},
      </if>
      <if test="buyMoney != null">
        buy_money = #{buyMoney,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.CouponConfig">
    update coupon_config
    set `level` = #{level,jdbcType=INTEGER},
      buy_money = #{buyMoney,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectValid" resultType="net.summerfarm.mall.model.vo.CouponConfigVO">
    select id, buy_money buyMoney
    from coupon_config where status = 0 order by buy_money
  </select>
</mapper>