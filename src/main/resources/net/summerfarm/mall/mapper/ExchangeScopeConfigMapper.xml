<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.ExchangeScopeConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.ExchangeScopeConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="base_info_id" jdbcType="BIGINT" property="baseInfoId" />
    <result column="scope_id" jdbcType="BIGINT" property="scopeId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `base_info_id`, `scope_id`, `type`, `status`, `creator`, `update_time`, `create_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from exchange_scope_config
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from exchange_scope_config
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.ExchangeScopeConfig">
    insert into exchange_scope_config (`id`, `base_info_id`, `scope_id`, 
      `type`, `status`, `creator`, 
      `update_time`, `create_time`)
    values (#{id,jdbcType=BIGINT}, #{baseInfoId,jdbcType=BIGINT}, #{scopeId,jdbcType=BIGINT}, 
      #{type,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{creator,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.ExchangeScopeConfig">
    insert into exchange_scope_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="baseInfoId != null">
        `base_info_id`,
      </if>
      <if test="scopeId != null">
        `scope_id`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="creator != null">
        `creator`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="baseInfoId != null">
        #{baseInfoId,jdbcType=BIGINT},
      </if>
      <if test="scopeId != null">
        #{scopeId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.ExchangeScopeConfig">
    update exchange_scope_config
    <set>
      <if test="baseInfoId != null">
        `base_info_id` = #{baseInfoId,jdbcType=BIGINT},
      </if>
      <if test="scopeId != null">
        `scope_id` = #{scopeId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        `creator` = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="listByBaseInfoIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from
    exchange_scope_config
    <where>
      status = 1
      and type = 0
      and base_info_id in
      <foreach collection="list" open="(" separator="," close=")" item="baseInfoId">
        #{baseInfoId}
      </foreach>
    </where>
  </select>

  <select id="selectById" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from
    exchange_scope_config
    where id = #{id} and status = 1 and type = 0
  </select>
</mapper>