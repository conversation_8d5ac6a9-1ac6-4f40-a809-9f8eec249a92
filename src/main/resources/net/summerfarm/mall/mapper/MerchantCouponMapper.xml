<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MerchantCouponMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MerchantCoupon" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="m_id" property="mId" jdbcType="BIGINT" />
    <result column="coupon_id" property="couponId" jdbcType="INTEGER" />
    <result column="vaild_date" property="vaildDate" jdbcType="TIMESTAMP" />
    <result column="used" property="used" jdbcType="TINYINT" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="view" property="view" jdbcType="INTEGER" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
      <result column="start_time" property="startTime"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, m_id, coupon_id, vaild_date, used, add_time,view,order_no, start_time
  </sql>

  <select id="select" parameterType="hashmap" resultType="net.summerfarm.mall.model.vo.MerchantCouponVO">
    SELECT mc.id, mc.coupon_id couponId,mc.vaild_date vaildDate, mc.used, c.name, c.code, c.money, c.threshold, mc.start_time startTime,
    c.new_hand newHand,c.reamrk, c.grouping, mc.add_time addTime,c.agio_type agioType,c.category_id categoryId,c.sku,mc.m_id mId, c.activity_scope activityScope
    FROM merchant_coupon mc
    LEFT JOIN coupon c on mc.coupon_id = c.id
    <where>
      <if test="flag">
        and c.agio_type in(1,2,3)
      </if>
      <if test="!flag">
        and c.agio_type = 4
      </if>
      <if test="selectKeys.grouping !=null">
        AND c.grouping = #{selectKeys.grouping}
      </if>
      <if test="selectKeys.orderNo !=null">
        AND mc.order_no = #{selectKeys.orderNo}
      </if>
      <if test="selectKeys.mId != null">
        AND mc.m_id = #{selectKeys.mId}
      </if>
      <if test="selectKeys.vaildDateStart != null">
        AND mc.vaild_date > #{selectKeys.vaildDateStart}
      </if>
      <if test="selectKeys.newHand != null">
        AND c.new_hand =#{selectKeys.newHand}
      </if>
      <if test="selectKeys.used != null">
        AND mc.used = #{selectKeys.used}
      </if>
      <if test="selectKeys.status!=null">
        <if test="selectKeys.status == 0 || selectKeys.status ==1">
          AND mc.used = #{selectKeys.status}
          AND mc.vaild_date <![CDATA[>=]]> NOW()
        </if>
        <if test="selectKeys.status == 2">
          AND mc.vaild_date <![CDATA[<]]> NOW()
        </if>
      </if>
    </where>
    order by mc.id
  </select>
  <select id="selectByCoupon" parameterType="hashmap" resultType="net.summerfarm.mall.model.vo.MerchantCouponVO">
    SELECT mc.id, mc.coupon_id couponId,mc.vaild_date vaildDate, mc.used, c.name, c.code, c.money, c.threshold, mc.start_time startTime,
    c.new_hand newHand,c.reamrk, c.grouping, mc.add_time addTime,c.agio_type agioType,c.category_id categoryId,c.sku,mc.m_id mId
    FROM merchant_coupon mc
    LEFT JOIN coupon c on mc.coupon_id = c.id
    <where>
      mc.vaild_date <![CDATA[>=]]> '2021-06-19'
      and mc.start_time  <![CDATA[<=]]> '2021-06-22'
      <if test="flag">
        and c.agio_type in(1,2,3)
      </if>
      <if test="!flag">
        and c.agio_type = 4
      </if>
      <if test="selectKeys.grouping !=null">
        AND c.grouping = #{selectKeys.grouping}
      </if>
      <if test="selectKeys.orderNo !=null">
        AND mc.order_no = #{selectKeys.orderNo}
      </if>
      <if test="selectKeys.mId != null">
        AND mc.m_id = #{selectKeys.mId}
      </if>
      <if test="selectKeys.vaildDateStart != null">
        AND mc.vaild_date > #{selectKeys.vaildDateStart}
      </if>
      <if test="selectKeys.newHand != null">
        AND c.new_hand =#{selectKeys.newHand}
      </if>
      <if test="selectKeys.used != null">
        AND mc.used = #{selectKeys.used}
      </if>

    </where>
    order by mc.id
  </select>



  <select id="selectMerchantCouponVO" parameterType="java.lang.Integer" resultType="net.summerfarm.mall.model.vo.MerchantCouponVO" >
    SELECT mc.id, mc.m_id mId, mc.coupon_id couponId,mc.vaild_date vaildDate, mc.start_time startTime, mc.used, c.name, c.code, c.money, c.threshold, c.new_hand newHand,c.reamrk,c.agio_type agioType,c.category_id categoryId,c.sku, c.activity_scope activityScope, c.grouping
    FROM merchant_coupon mc
    LEFT JOIN coupon c on mc.coupon_id = c.id
    WHERE mc.vaild_date > now()
    AND mc.used = 0
    AND mc.id = #{id}
  </select>

    <select id="selectUnusedById" parameterType="java.lang.Integer" resultType="net.summerfarm.mall.model.vo.MerchantCouponVO" >
        SELECT mc.id, mc.m_id mId, mc.coupon_id couponId,mc.vaild_date vaildDate, mc.start_time startTime, mc.used,
        c.name, c.code, c.money, c.threshold, c.new_hand newHand,c.reamrk,c.agio_type agioType,c.category_id categoryId,c.sku
        FROM merchant_coupon mc
        LEFT JOIN coupon c on mc.coupon_id = c.id
        WHERE mc.used = 0
        AND mc.id = #{id}
    </select>

  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.MerchantCoupon"
          useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into merchant_coupon
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="mId != null" >
        m_id,
      </if>
      <if test="couponId != null" >
        coupon_id,
      </if>
      <if test="vaildDate != null" >
        vaild_date,
      </if>
      <if test="sender!=null">
        sender,
      </if>
      <if test="used != null" >
        used,
      </if>
      <if test="addTime != null" >
        add_time,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="receiveType!=null">
        receive_type,
      </if>
      <if test="relatedId!=null">
        related_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="mId != null" >
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null" >
        #{couponId,jdbcType=INTEGER},
      </if>
      <if test="vaildDate != null" >
        #{vaildDate,jdbcType=TIMESTAMP},
      </if>
      <if test="sender != null" >
        #{sender},
      </if>
      <if test="used != null" >
        #{used,jdbcType=TINYINT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startTime != null">
        #{startTime} ,
      </if>
      <if test="receiveType!=null">
        #{receiveType},
      </if>
      <if test="relatedId!=null">
        #{relatedId},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MerchantCoupon" >
    update merchant_coupon
    <set >
      <if test="mId != null" >
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null" >
        coupon_id = #{couponId,jdbcType=INTEGER},
      </if>
      <if test="vaildDate != null" >
        vaild_date = #{vaildDate,jdbcType=TIMESTAMP},
      </if>
      <if test="used != null" >
        used = #{used,jdbcType=TINYINT},
      </if>
      <if test="addTime != null" >
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      order_no = #{orderNo}
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateOrderNo">
    update merchant_coupon set order_no =NULL, m_id = #{mId}, used=0 where order_no=#{orderNo}
  </update>

  <select id="countUnused"  resultType="java.lang.Integer" >
    SELECT count(1) FROM merchant_coupon mc
    WHERE mc.vaild_date > #{date}
    AND mc.used =0
    AND mc.m_id = #{mId}
  </select>

  <select id="countExpire"  resultType="java.lang.Integer" >
    SELECT count(1) FROM merchant_coupon mc
    WHERE mc.vaild_date > now() and mc.vaild_date  <![CDATA[ < ]]> #{date}
    AND mc.used =0
    AND mc.m_id = #{mId}
    <if test="view !=null">
      and view =#{view}
    </if>
  </select>

  <update id="updateViewByMId" parameterType="java.lang.Long" >
    update merchant_coupon
    set view =1
    where id = #{mId}
  </update>


  <select id="selectCoupon" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM merchant_coupon
    WHERE m_id=#{mId}
    AND vaild_date <![CDATA[>=]]> #{vaildDate}
    AND coupon_id=#{couponId}
  </select>

  <select id="selectListMerchantCouponVO" parameterType="java.lang.Integer" resultType="net.summerfarm.mall.model.vo.MerchantCouponVO" >
    SELECT mc.id, mc.m_id mId, mc.coupon_id couponId,mc.vaild_date vaildDate, mc.used, c.name, c.code, c.money, c.threshold, c.new_hand newHand,c.reamrk,c.agio_type agioType,c.category_id categoryId,c.sku
    FROM merchant_coupon mc
    LEFT JOIN coupon c on mc.coupon_id = c.id
    WHERE mc.vaild_date > now()
    AND mc.used = 0
    AND mc.id in
      <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
        #{item}
      </foreach>

  </select>

  <select id="selectCouponVO" parameterType="java.lang.Integer" resultType="net.summerfarm.mall.model.vo.MerchantCouponVO" >
    SELECT mc.id, mc.m_id mId, mc.coupon_id couponId,mc.vaild_date vaildDate, mc.used, c.name, c.code, c.money, c.threshold, c.new_hand newHand,c.reamrk,c.agio_type agioType,c.category_id categoryId,c.sku
    FROM merchant_coupon mc
    LEFT JOIN coupon c on mc.coupon_id = c.id
    WHERE mc.vaild_date > now()
    AND mc.id = #{id}
  </select>


  <update id="updateByOrderNo">
    update merchant_coupon set order_no =NULL, m_id = #{mId}, used=0 where id = (
      select id from (
          select mc.id
          from merchant_coupon mc
          inner join coupon c on mc.coupon_id = c.id and  mc.order_no =#{orderNo} and c.agio_type = #{type}
      ) mcn
    )
  </update>

  <select id="queryMerchantCoupon" resultType="net.summerfarm.mall.model.vo.MerchantCouponVO" >
    SELECT mc.id, mc.m_id mId, mc.coupon_id couponId,mc.vaild_date vaildDate, mc.used, c.name, c.code,
           c.money, c.threshold, c.new_hand newHand,c.reamrk,c.agio_type agioType,c.category_id categoryId,c.sku,
           c.activity_scope activityScope
    from merchant_coupon mc
        inner join coupon c on mc.coupon_id = c.id  and mc.m_id = #{mId}
        and  c.grouping in (0,2,3,5,6,7) and mc.vaild_date > #{dateTime} and mc.start_time  <![CDATA[ < ]]> #{dateTime} and mc.used = 0 and c.agio_type =1
        order by c.money desc
  </select>


  <select id="queryMerchantCouponMsg" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM merchant_coupon
    WHERE m_id = #{mId} AND coupon_id = #{couponId}
  </select>
  <select id="selectUsableCoupon"  resultType="net.summerfarm.mall.model.vo.MerchantCouponVO" >
    SELECT mc.id,
      mc.coupon_id  couponId,
      mc.vaild_date vaildDate,
      mc.used,
      c.name,
      c.code,
      c.money,
      c.threshold,
      mc.start_time startTime,
      c.new_hand    newHand,
      c.reamrk,
      c.grouping,
      mc.add_time   addTime,
      c.agio_type   agioType,
      c.category_id categoryId,
      c.sku,
      mc.m_id       mId,
      c.activity_scope activityScope
    FROM merchant_coupon mc
    LEFT JOIN coupon c on mc.coupon_id = c.id
    where mc.used = 0
    and mc.start_time &lt;= now()
    AND mc.vaild_date > now()
      AND c.agio_type = #{type}
      AND mc.m_id = #{mId}
      and c.threshold &lt;= #{totalPrice}
      and c.money &lt;= #{totalPrice}
    order by c.money desc
  </select>
  <select id="selectUsableCouponWithoutThreshold"  resultType="net.summerfarm.mall.model.vo.MerchantCouponVO" >
    SELECT mc.id,
    mc.coupon_id  couponId,
    mc.vaild_date vaildDate,
    mc.used,
    c.name,
    c.code,
    c.money,
    c.threshold,
    mc.start_time startTime,
    c.new_hand    newHand,
    c.reamrk,
    c.grouping,
    mc.add_time   addTime,
    c.agio_type   agioType,
    c.category_id categoryId,
    c.sku,
    mc.m_id       mId,
    c.activity_scope activityScope,
    mc.receive_type  receiveType
    FROM merchant_coupon mc
    LEFT JOIN coupon c on mc.coupon_id = c.id
    where mc.used = 0
    and mc.start_time &lt;= now()
    AND mc.vaild_date > now()
    AND c.agio_type = #{type}
    AND mc.m_id = #{mId}
  </select>
  <select id="selectUnusedByReceiveId" resultType="java.lang.Integer">
    SELECT count(1)
    FROM merchant_coupon mc
    WHERE mc.used = 1
      AND mc.coupon_id = #{id} and m_id = #{mId}
  </select>

  <resultMap id="ReceiveIdCountBOResultMap" type="net.summerfarm.mall.model.bo.coupon.ReceiveIdCountBO">
    <result column="coupon_id" property="couponId" />
    <result column="num" property="num"/>
    <result column="vaild_date" property="vaildDate"/>
    <result column="start_time" property="startDate"/>
  </resultMap>
  <select id="selectUnusedByReceiveIdIn" resultMap="ReceiveIdCountBOResultMap">
    SELECT coupon_id, count(1) num, vaild_date, start_time
    FROM merchant_coupon mc
    WHERE mc.used = 0 and m_id = #{mId} and vaild_date > now() and receive_type = 1
            AND mc.coupon_id in
    <foreach collection="ids" item="id" separator="," open="(" close=")">
      #{id}
    </foreach>
    group by coupon_id
  </select>


  <select id="selectByMid" resultType="net.summerfarm.mall.model.vo.MerchantCouponVO">
    SELECT mc.id, mc.coupon_id couponId,mc.vaild_date vaildDate, mc.used, c.name, c.money, c.threshold, mc.start_time startTime,
    c.agio_type agioType,c.category_id categoryId,c.sku, c.activity_scope activityScope, c.grouping
    FROM merchant_coupon mc
    LEFT JOIN coupon c on mc.coupon_id = c.id
    <where>
      <if test="type == 0">
        c.agio_type in (1,5)
      </if>
      <if test="type == 1">
        c.agio_type in (2,3)
      </if>
      <if test="type == 2">
        c.agio_type = 4
      </if>
       AND mc.m_id = #{mId}
      <if test="status!=null">
        <if test="status == 0">
          AND mc.used = #{status}
          AND mc.vaild_date <![CDATA[>=]]> NOW()
        </if>
        <if test="status ==1">
          AND mc.used = #{status}
        </if>
        <if test="status == 2">
          AND mc.used = 0
          AND mc.vaild_date <![CDATA[<]]> NOW()
        </if>
      </if>
    </where>
    order by mc.id
  </select>

  <insert id="insertList" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into merchant_coupon(m_id, coupon_id, vaild_date, add_time, start_time, receive_type)
    values
    <foreach collection="merchantCoupons" separator="," item="merchant">
        (#{merchant.mId},
         #{merchant.couponId},
         #{merchant.vaildDate},
         #{merchant.addTime},
         #{merchant.startTime},
         #{merchant.receiveType})
    </foreach>
  </insert>

  <update id="batchUseCoupon">
    update merchant_coupon
    set used = 1,
        update_time = now(),
        order_no = #{orderNo}
    where id in
        <foreach collection="mcIdSet" item="mcId" separator="," open="(" close=")">
          #{mcId}
        </foreach>
  </update>
  <update id="updateByOrderNoAfter">
    update merchant_coupon set order_no =NULL, m_id = #{mId}, used=0 where id = (
      select id from (
          select mc.id
          from merchant_coupon mc
          inner join coupon c on mc.coupon_id = c.id and  mc.order_no =#{orderNo} and c.agio_type = #{type} and c.grouping = 1
      ) mcn
    )
  </update>

  <select id="selectByMap" resultType="net.summerfarm.mall.model.vo.MerchantCouponVO">
    SELECT mc.id, mc.coupon_id couponId,mc.vaild_date vaildDate, mc.used, c.name, c.code, c.money, c.threshold, mc.start_time startTime,
    c.new_hand newHand,c.reamrk, c.grouping, mc.add_time addTime,c.agio_type agioType,c.category_id categoryId,c.sku,mc.m_id mId, c.activity_scope activityScope
    FROM merchant_coupon mc
    inner JOIN coupon c on mc.coupon_id = c.id
    <where>
      <if test="grouping !=null">
        AND c.grouping = #{grouping}
      </if>
      <if test="mId != null">
        AND mc.m_id = #{mId}
      </if>
      <if test="used != null">
        AND mc.used = #{used}
      </if>
      <if test="status != null">
        AND c.status = #{status}
      </if>
      <if test="valid != null">
        AND mc.vaild_date <![CDATA[>=]]> NOW()
      </if>
      <if test="ids != null  and ids.size() > 0">
        AND mc.id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
          #{id}
        </foreach>
      </if>
    </where>
    order by mc.id
  </select>
  <select id="selectTimingByMid" resultType="net.summerfarm.mall.model.vo.MerchantCouponVO">
    SELECT mc.id, mc.coupon_id couponId,mc.vaild_date vaildDate, mc.used, c.name, c.money, c.threshold, mc.start_time startTime,
    c.agio_type agioType,c.category_id categoryId,c.sku, c.activity_scope activityScope, c.grouping
    FROM merchant_coupon mc
    LEFT JOIN coupon c on mc.coupon_id = c.id
    <where>
      <if test="type == 0">
        c.agio_type = 1
      </if>
      <if test="type == 1">
        c.agio_type in (2,3)
      </if>
      <if test="type == 2">
        c.agio_type = 4
      </if>
      AND mc.m_id = #{mId}
      <if test="status!=null">
        <if test="status == 0">
          AND mc.used = #{status}
          AND mc.vaild_date <![CDATA[>=]]> NOW()
          and mc.start_time &lt;= now()
        </if>
        <if test="status ==1">
          AND mc.used = #{status}
        </if>
        <if test="status == 2">
          AND mc.used = 0
          AND mc.vaild_date <![CDATA[<]]> NOW()
        </if>
      </if>
    </where>
    order by mc.id
  </select>
  <select id="selectDeliveryFeeCoupon" resultType="net.summerfarm.mall.model.vo.MerchantCouponVO">
    SELECT mc.id, mc.coupon_id couponId,mc.vaild_date vaildDate, mc.used, c.name, c.code, c.money, c.threshold, mc.start_time startTime,
    c.new_hand newHand,c.reamrk, c.grouping, mc.add_time addTime,c.agio_type agioType,c.category_id categoryId,c.sku,mc.m_id mId, c.activity_scope activityScope
    from merchant_coupon mc
    inner join coupon c on mc.coupon_id = c.id and  mc.order_no =#{orderNo} and c.agio_type = #{type}
  </select>

    <select id="selectHasDeliveryFeeCouponByOrders" resultType="java.lang.Boolean">
      SELECT if(count(*) > 0, true, false) FROM merchant_coupon a
      LEFT JOIN coupon c on a.coupon_id = c.id
      where c.agio_type = 2
      <if test="orderNos != null">
        and a.order_no in
        <foreach collection="orderNos" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
    </select>


  <select id="getUserReceiveCount" resultMap="ReceiveIdCountBOResultMap">
    SELECT coupon_id, count(1) num
    FROM merchant_coupon mc
    WHERE m_id = #{mId} and receive_type = #{receiveType}
    AND mc.coupon_id in
    <foreach collection="couponIds" item="id" separator="," open="(" close=")">
      #{id}
    </foreach>
    group by coupon_id
  </select>
  <select id="selectMerchantCouponWithoutStatus" resultType="net.summerfarm.mall.model.vo.MerchantCouponVO">
    SELECT mc.id, mc.m_id mId, mc.coupon_id couponId,mc.vaild_date vaildDate, mc.start_time startTime, mc.used, c.name, c.code, c.money, c.threshold, c.new_hand newHand,c.reamrk,c.agio_type agioType,c.category_id categoryId,c.sku, c.activity_scope activityScope, c.grouping
    FROM merchant_coupon mc
    LEFT JOIN coupon c on mc.coupon_id = c.id
    WHERE mc.id = #{id}
  </select>

  <select id="getMerchantCoupon" resultType="net.summerfarm.mall.model.vo.MerchantCouponVO">
    SELECT mc.id, mc.coupon_id couponId,mc.vaild_date vaildDate, mc.used, c.name, c.money, c.threshold, mc.start_time startTime,
    c.agio_type agioType,c.category_id categoryId,c.sku, c.activity_scope activityScope, c.grouping
    FROM merchant_coupon mc
    LEFT JOIN coupon c on mc.coupon_id = c.id
    where mc.m_id = #{mId} and mc.receive_type = #{receiveType}
    and mc.coupon_id in
    <foreach collection="couponIds" item="id" separator="," open="(" close=")">
      #{id}
    </foreach>
    and mc.related_id in
    <foreach collection="relatedIds" item="id" separator="," open="(" close=")">
      #{id}
    </foreach>
    order by mc.id
  </select>
  <select id="getUserReceiveCountInfo" resultMap="ReceiveIdCountBOResultMap">
    SELECT coupon_id, count(1) num
    FROM merchant_coupon
    WHERE m_id = #{mId} and receive_type = #{receiveType}
    group by coupon_id
  </select>

  <select id="selectUsableCouponByMid" resultType="net.summerfarm.mall.model.vo.MerchantCouponVO">
    SELECT mc.id,
           mc.coupon_id  couponId,
           mc.vaild_date vaildDate,
           mc.used,
           c.name,
           c.code,
           c.money,
           c.threshold,
           mc.start_time startTime,
           c.new_hand    newHand,
           c.reamrk,
           c.grouping,
           mc.add_time   addTime,
           c.agio_type   agioType,
           c.category_id categoryId,
           c.sku,
           mc.m_id       mId,
           c.activity_scope activityScope,
           mc.receive_type  receiveType
    FROM merchant_coupon mc
           LEFT JOIN coupon c on mc.coupon_id = c.id
    where mc.used = 0
      and mc.start_time &lt;= now()
      AND mc.vaild_date > now()
      AND mc.m_id = #{mId}
  </select>

  <select id="selectUnusedCouponIdsWithinDays" parameterType="int" resultType="Integer">
    SELECT DISTINCT
      `coupon_id`
    FROM
      `merchant_coupon`
    WHERE
      add_time <![CDATA[>=]]> DATE_ADD(NOW(), interval -#{days} day)
      AND `used` = 0
      AND `vaild_date` > NOW()
  </select>
</mapper>
