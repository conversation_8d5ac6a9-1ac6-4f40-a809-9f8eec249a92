<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MarketPriceControlProductsMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MarketPriceControlProducts">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="pd_id" jdbcType="BIGINT" property="pdId" />
    <result column="pd_no" jdbcType="VARCHAR" property="pdNo" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
    <result column="price_hide" jdbcType="INTEGER" property="priceHide" />
    <result column="face_price_hide" jdbcType="INTEGER" property="facePriceHide" />
    <result column="price_control_line" jdbcType="DECIMAL" property="priceControlLine" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, creator, updater, pd_id, pd_no, pd_name, price_hide, face_price_hide,
    price_control_line, sku
  </sql>
  <select id="selectByPdId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from market_price_control_products
    where pd_id = #{pdId}
    limit 1
  </select>
  <select id="selectAllControlProducts" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from market_price_control_products
  </select>

  <select id="selectBySkuIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from market_price_control_products
    where price_hide = #{priceHide} and sku in
    <foreach collection="skus" item="sku" open="(" separator="," close=")">
      #{sku}
    </foreach>
  </select>

  <select id="selectBySku" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from market_price_control_products
    where sku = #{sku}
    limit 1
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from market_price_control_products
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MarketPriceControlProducts" useGeneratedKeys="true">
    insert into market_price_control_products (create_time, update_time, creator, 
      updater, pd_id, pd_no, 
      pd_name)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{updater,jdbcType=VARCHAR}, #{pdId,jdbcType=BIGINT}, #{pdNo,jdbcType=VARCHAR}, 
      #{pdName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MarketPriceControlProducts" useGeneratedKeys="true">
    insert into market_price_control_products
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="pdNo != null">
        pd_no,
      </if>
      <if test="pdName != null">
        pd_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=BIGINT},
      </if>
      <if test="pdNo != null">
        #{pdNo,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null">
        #{pdName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MarketPriceControlProducts">
    update market_price_control_products
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=BIGINT},
      </if>
      <if test="pdNo != null">
        pd_no = #{pdNo,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null">
        pd_name = #{pdName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.MarketPriceControlProducts">
    update market_price_control_products
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      updater = #{updater,jdbcType=VARCHAR},
      pd_id = #{pdId,jdbcType=BIGINT},
      pd_no = #{pdNo,jdbcType=VARCHAR},
      pd_name = #{pdName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>