<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.OrderRelationMapper">

  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.OrderRelation">
    <id property="id" column="id" jdbcType="BIGINT"/>
    <result property="masterOrderNo" column="master_order_no" jdbcType="VARCHAR"/>
    <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
    <result property="precisionDeliveryFee" column="precision_delivery_fee" jdbcType="DECIMAL"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
  </resultMap>

  <sql id="Base_Column_List">
    id
    ,master_order_no,order_no,precision_delivery_fee, create_time,update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from order_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id"
    parameterType="net.summerfarm.mall.model.domain.OrderRelation" useGeneratedKeys="true">
    insert into order_relation
    ( id, master_order_no, order_no, precision_delivery_fee
    , create_time, update_time)
    values ( #{id,jdbcType=BIGINT}, #{masterOrderNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}
           , #{precisionDeliveryFee, jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id"
    parameterType="net.summerfarm.mall.model.domain.OrderRelation" useGeneratedKeys="true">
    insert into order_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">id,</if>
      <if test="masterOrderNo != null">master_order_no,</if>
      <if test="orderNo != null">order_no,</if>
      <if test="precisionDeliveryFee != null">precision_delivery_fee,</if>
      <if test="createTime != null">create_time,</if>
      <if test="updateTime != null">update_time,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">#{id,jdbcType=BIGINT},</if>
      <if test="masterOrderNo != null">#{masterOrderNo,jdbcType=VARCHAR},</if>
      <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
      <if test="precisionDeliveryFee != null">#{precisionDeliveryFee,jdbcType=DECIMAL},</if>
      <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
      <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.mall.model.domain.OrderRelation">
    update order_relation
    <set>
      <if test="masterOrderNo != null">
        master_order_no = #{masterOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="precisionDeliveryFee != null">
        precision_delivery_fee =#{precisionDeliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.OrderRelation">
    update order_relation
    set master_order_no = #{masterOrderNo,jdbcType=VARCHAR},
        order_no        = #{orderNo,jdbcType=VARCHAR},
        precision_delivery_fee = #{precisionDeliveryFee,jdbcType=DECIMAL},
        create_time     = #{createTime,jdbcType=TIMESTAMP},
        update_time     = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectOrderNoByMasterOrderNo" resultType="java.lang.String">
    select order_no
    from order_relation
    where master_order_no = #{masterOrderNo,jdbcType=VARCHAR}
  </select>
  <select id="selectByOrderNoBatch" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_relation
    where order_no in
    <foreach collection="orderNoList" open="(" close=") " separator="," item="orderNo">
      #{orderNo}
    </foreach>
  </select>
</mapper>
