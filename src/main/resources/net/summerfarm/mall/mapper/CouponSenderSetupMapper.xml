<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.CouponSenderSetupMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.CouponSenderSetup">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="sender_type" jdbcType="INTEGER" property="senderType"/>
    <result column="status" jdbcType="INTEGER" property="status"/>
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `name`, start_time, end_time, `type`, sender_type, status, creator, create_time, updater, update_time
  </sql>
  <sql id="Base_Column_List_With_Alias">
    css.id, css.`name`, css.start_time, css.end_time, css.`type`, css.sender_type, css.status, css.creator,
    css.create_time, css.updater, css.update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from coupon_sender_setup
    where id = #{id,jdbcType=INTEGER}
  </select>
    <select id="selectSenderSetup" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from coupon_sender_setup
      where #{now} between start_time and end_time

    </select>
    <select id="selectByCouponIdAndStatus" resultMap="BaseResultMap">
      select <include refid="Base_Column_List_With_Alias" />
      from coupon_sender_setup css left join coupon_sender_relation csr on css.id = csr.coupon_sender_id
      where csr.coupon_id = #{couponId} and status = #{status} and css.sender_type = #{senderType} limit 1
    </select>
    <select id="selectCouponById" resultType="net.summerfarm.mall.model.domain.Coupon">
      select c.id,c.start_time startTime,c.vaild_date vaildDate,c.vaild_time vaildTime,
             c.type,c.agio_type agioType,c.money,c.threshold,c.name,c.category_id categoryId,c.sku,
             csr.number num,
             csr.coupon_sender_id couponSenderId
             from coupon_sender_relation csr
            left join coupon c on csr.coupon_id = c.id
      where csr.coupon_sender_id = #{id}
    </select>
    <select id="selectCouponByIdIn" resultType="net.summerfarm.mall.model.domain.Coupon">
      select c.id,c.start_time startTime, c.start_date startDate, c.vaild_date vaildDate,c.vaild_time vaildTime,
      c.type,c.agio_type agioType,c.money,c.threshold,c.name,c.category_id categoryId,c.sku,
      csr.number num,
      csr.coupon_sender_id couponSenderId,
      c.activity_scope activityScope,
      c.quantity_claimed quantityClaimed,
      c.grant_amount grantAmount,
      c.grant_limit grantLimit,
      c.grouping
      from coupon_sender_relation csr
      left join coupon c on csr.coupon_id = c.id
      where csr.coupon_sender_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
          #{id}
        </foreach>
      GROUP BY csr.coupon_id
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from coupon_sender_setup
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CouponSenderSetup" useGeneratedKeys="true">
    insert into coupon_sender_setup (`name`, start_time, end_time, 
      `type`, creator, create_time, 
      updater, update_time)
    values (#{name,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{type,jdbcType=BOOLEAN}, #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CouponSenderSetup" useGeneratedKeys="true">
    insert into coupon_sender_setup
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=BOOLEAN},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.CouponSenderSetup">
    update coupon_sender_setup
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=BOOLEAN},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.CouponSenderSetup">
    update coupon_sender_setup
    set `name` = #{name,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      `type` = #{type,jdbcType=BOOLEAN},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByAll" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from coupon_sender_setup
    <where>
      <if test="id != null">
        and id=#{id,jdbcType=INTEGER}
      </if>
      <if test="name != null">
        and `name`=#{name,jdbcType=VARCHAR}
      </if>
      <if test="startTime != null">
        and now() >= start_time and end_time > now()
      </if>
      <if test="endTime != null">
        and end_time=#{endTime,jdbcType=TIMESTAMP}
      </if>
      <if test="type != null">
        and `type`=#{type,jdbcType=INTEGER}
      </if>
      <if test="senderType != null">
        and sender_type=#{senderType,jdbcType=INTEGER}
      </if>
      <if test="status != null">
        and `status`=#{status,jdbcType=INTEGER}
      </if>
      <if test="creator != null">
        and creator=#{creator,jdbcType=INTEGER}
      </if>
      <if test="createTime != null">
        and create_time=#{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updater != null">
        and updater=#{updater,jdbcType=INTEGER}
      </if>
      <if test="updateTime != null">
        and update_time=#{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </select>
</mapper>