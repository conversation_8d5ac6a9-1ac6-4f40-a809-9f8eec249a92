<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.ActivityBasicInfoMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.market.ActivityBasicInfo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="is_permanent" jdbcType="TINYINT" property="isPermanent"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="need_pre" jdbcType="TINYINT" property="needPre"/>
    <result column="pre_start_time" jdbcType="TIMESTAMP" property="preStartTime"/>
    <result column="pre_end_time" jdbcType="TIMESTAMP" property="preEndTime"/>
    <result column="type" jdbcType="INTEGER" property="type"/>
    <result column="tag" jdbcType="TINYINT" property="tag"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="creator_id" jdbcType="INTEGER" property="creatorId"/>
    <result column="updater_id" jdbcType="INTEGER" property="updaterId"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <resultMap id="ResultMapForList" type="net.summerfarm.mall.model.dto.market.activity.ActivityInfoDTO">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="is_permanent" jdbcType="TINYINT" property="isPermanent"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="type" jdbcType="INTEGER" property="type"/>
    <result column="platform" jdbcType="INTEGER" property="platform"/>
    <result column="item_config_id" jdbcType="BIGINT" property="itemConfigId"/>
    <result column="scope_id" jdbcType="BIGINT" property="scopeId"/>
    <result column="scope_type" jdbcType="TINYINT" property="scopeType"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
    , `name`, `start_time`, `end_time`, `is_permanent`, `status`, `need_pre`, `pre_start_time`,
    `pre_end_time`, `type`, `tag`, `remark`, `creator_id`, `updater_id`, `del_flag`,
    `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_basic_info
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from activity_basic_info
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.market.ActivityBasicInfo">
    insert into activity_basic_info (`id`, `name`, `start_time`,
                                     `end_time`, `is_permanent`, `status`,
                                     `need_pre`, `pre_start_time`, `pre_end_time`,
                                     `type`, `tag`, `remark`,
                                     `creator_id`, `updater_id`, `del_flag`,
                                     `create_time`, `update_time`)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP},
            #{endTime,jdbcType=TIMESTAMP}, #{isPermanent,jdbcType=TINYINT},
            #{status,jdbcType=TINYINT},
            #{needPre,jdbcType=TINYINT}, #{preStartTime,jdbcType=TIMESTAMP},
            #{preEndTime,jdbcType=TIMESTAMP},
            #{type,jdbcType=INTEGER}, #{tag,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR},
            #{creatorId,jdbcType=INTEGER}, #{updaterId,jdbcType=INTEGER},
            #{delFlag,jdbcType=TINYINT},
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.market.ActivityBasicInfo"
    useGeneratedKeys="true" keyProperty="id">
    insert into activity_basic_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="startTime != null">
        `start_time`,
      </if>
      <if test="endTime != null">
        `end_time`,
      </if>
      <if test="isPermanent != null">
        `is_permanent`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="needPre != null">
        `need_pre`,
      </if>
      <if test="preStartTime != null">
        `pre_start_time`,
      </if>
      <if test="preEndTime != null">
        `pre_end_time`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="tag != null">
        `tag`,
      </if>
      <if test="remark != null">
        `remark`,
      </if>
      <if test="creatorId != null">
        `creator_id`,
      </if>
      <if test="updaterId != null">
        `updater_id`,
      </if>
      <if test="delFlag != null">
        `del_flag`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isPermanent != null">
        #{isPermanent,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="needPre != null">
        #{needPre,jdbcType=TINYINT},
      </if>
      <if test="preStartTime != null">
        #{preStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preEndTime != null">
        #{preEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="tag != null">
        #{tag,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.mall.model.domain.market.ActivityBasicInfo">
    update activity_basic_info
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        `start_time` = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        `end_time` = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isPermanent != null">
        `is_permanent` = #{isPermanent,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="needPre != null">
        `need_pre` = #{needPre,jdbcType=TINYINT},
      </if>
      <if test="preStartTime != null">
        `pre_start_time` = #{preStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preEndTime != null">
        `pre_end_time` = #{preEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="tag != null">
        `tag` = #{tag,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        `remark` = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        `creator_id` = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="updaterId != null">
        `updater_id` = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        `del_flag` = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.market.ActivityBasicInfo">
    update activity_basic_info
    set `name`           = #{name,jdbcType=VARCHAR},
        `start_time`     = #{startTime,jdbcType=TIMESTAMP},
        `end_time`       = #{endTime,jdbcType=TIMESTAMP},
        `is_permanent`   = #{isPermanent,jdbcType=TINYINT},
        `status`         = #{status,jdbcType=TINYINT},
        `need_pre`       = #{needPre,jdbcType=TINYINT},
        `pre_start_time` = #{preStartTime,jdbcType=TIMESTAMP},
        `pre_end_time`   = #{preEndTime,jdbcType=TIMESTAMP},
        `type`           = #{type,jdbcType=INTEGER},
        `tag`            = #{tag,jdbcType=TINYINT},
        `remark`         = #{remark,jdbcType=VARCHAR},
        `creator_id`     = #{creatorId,jdbcType=INTEGER},
        `updater_id`     = #{updaterId,jdbcType=INTEGER},
        `del_flag`       = #{delFlag,jdbcType=TINYINT},
        `create_time`    = #{createTime,jdbcType=TIMESTAMP},
        `update_time`    = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByScope" resultMap="ResultMapForList">
    select abi.*,asco.scope_id, asco.scope_type,aic.id item_config_id ,ascc.platform
    from activity_basic_info abi
    left join activity_scene_config ascc on abi.id = ascc.basic_info_id
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_scope_config asco on abi.id = asco.basic_info_id
    where abi.type in
    <foreach collection="types" item="type" open="(" separator="," close=")">
        #{type}
    </foreach>
    and (abi.start_time &lt;= now() and abi.end_time &gt;= now()
    or abi.is_permanent = 1) and status = 1
    and abi.del_flag = 0 and asco.del_flag = 0 and ascc.del_flag = 0
    and (asco.scope_id,asco.scope_type) in
    <foreach collection="scopes" item="item" open="(" separator="," close=")">
      (#{item.scopeId},#{item.scopeType})
    </foreach>
  </select>

<!--  <select id="listByQuery" parameterType="net.summerfarm.model.DTO.market.ActivityBasicQueryDTO"-->
<!--    resultMap="ResultMapForPage">-->
<!--    select abi.*, asco.*-->
<!--    from activity_basic_info abi-->
<!--    left join activity_scope_config asco on abi.id = asco.basic_info_id-->
<!--    <where>-->
<!--      abi.del_flag = 0-->
<!--      <if test="id != null">-->
<!--        and abi.id = #{id}-->
<!--      </if>-->
<!--      <if test="type != null">-->
<!--        and abi.type = #{type}-->
<!--      </if>-->
<!--      <if test="name != null">-->
<!--        and abi.name like CONCAT('%', #{name},'%')-->
<!--      </if>-->
<!--      <if test="scopeId != null and scopeType != null">-->
<!--        and scope_type = #{scopeType}-->
<!--        and scope_id = #{scopeId}-->
<!--      </if>-->
<!--      <if test="status != null">-->
<!--        <choose>-->
<!--          <when test="status == 0">-->
<!--            and (abi.start_time &lt; now()-->
<!--            or (abi.start_time &lt;= now() and abi.end_time &gt;= now() and status = 0)-->
<!--            or (abi.is_permanent = 1 and status = 0))-->
<!--          </when>-->
<!--          <when test="status == 1">-->
<!--            and ((abi.start_time &lt;= now() and abi.end_time &gt;= now())-->
<!--            or abi.is_permanent = 1)-->
<!--            and status = 1-->
<!--          </when>-->
<!--          <when test="status == 2">-->
<!--            and abi.end_time &lt; now()-->
<!--          </when>-->
<!--        </choose>-->
<!--      </if>-->
<!--      <if test="creatorId != null">-->
<!--        and abi.create_id = #{creatorId}-->
<!--      </if>-->
<!--    </where>-->
<!--    order by abi.id desc-->
<!--  </select>-->

  <select id="listByScope" resultMap="net.summerfarm.mall.mapper.ActivityItemConfigMapper.BaseResultMap">
    select aic.id, aic.basic_info_id
    from activity_basic_info abi
        left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_scope_config asco on abi.id = asco.basic_info_id
    <where>
      abi.del_flag = 0 and aic.del_flag = 0 and asco.del_flag = 0
      <if test="type != null">
        and abi.type = #{type}
      </if>
      <if test="list != null and list.size > 0">
        and (scope_id,scope_type) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
          (#{item.scopeId},#{item.scopeType})
        </foreach>
      </if>
      <if test="status != null">
        <choose>
          <when test="status == 0">
            and (abi.start_time &lt; now()
            or (abi.start_time &lt;= now() and abi.end_time &gt;= now() and status = 0)
            or (abi.is_permanent = 1 and status = 0))
          </when>
          <when test="status == 1">
            and ((abi.start_time &lt;= now() and abi.end_time &gt;= now())
            or abi.is_permanent = 1)
            and status = 1
          </when>
          <when test="status == 2">
            and abi.end_time &lt; now()
          </when>
        </choose>
      </if>
    </where>
    order by abi.id desc
  </select>

  <select id="listActivity" resultType="net.summerfarm.mall.model.dto.market.activity.ActivityItemScopeDTO">
    select aic.basic_info_id basicInfoId,aic.id itemConfigId, asco.scope_id scopeId
    from activity_basic_info abi
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_scope_config asco on abi.id = asco.basic_info_id
    <where>
      abi.del_flag = 0 and aic.del_flag = 0 and asco.del_flag = 0
      and abi.type in(0,1)
      <if test="list != null and list.size > 0">
        and (scope_id,scope_type) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
          (#{item.scopeId},#{item.scopeType})
        </foreach>
      </if>
      and ((abi.start_time &lt;= now() and abi.end_time &gt;= now())
      or abi.is_permanent = 1)
      and status = 1
    </where>
  </select>

</mapper>
