<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.AdminMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Admin">
        <id column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="login_fail_times" property="loginFailTimes" jdbcType="INTEGER"/>
        <result column="is_disabled" property="isDisabled" jdbcType="BIT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
        <result column="realname" property="realname" jdbcType="VARCHAR"/>
        <result column="name_remakes" property="nameRemakes" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="BIT"/>
        <result column="department" property="department" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="operate_id" property="operateId" jdbcType="VARCHAR"/>
        <result column="close_order_type" property="closeOrderType"/>
        <result column="contract_method" property="contractMethod"/>
        <result column="close_order_time" property="closeOrderTime"/>
        <result column="admin_switch" property="adminSwitch"/>
        <result column="admin_type" property="adminType"/>
        <result column="admin_chain" property="adminChain"/>
        <result column="admin_grade" property="adminGrade"/>
        <result column="low_price_remainder" property="lowPriceRemainder"/>
        <result column="not_included_area" property="notIncludedArea"/>
        <result column="saler_id" property="salerId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
    admin_id, create_time, login_fail_times, is_disabled, username, password,saler_id,
    login_time, realname, gender, department, phone, operate_id, close_order_type, contract_method, name_remakes,
    close_order_time, admin_switch, admin_type, admin_chain, admin_grade, low_price_remainder,not_included_area
  </sql>

    <!--auto code-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from admin
        where admin_id = #{adminId,jdbcType=INTEGER}
    </select>


    <select id="selectBdListByKaAdminId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin a
        where a.is_disabled = 0
        and a.admin_id in (
        select fur.admin_id from merchant m
        left join follow_up_relation fur on m.m_id = fur.m_id and fur.reassign = 0
        where m.admin_id = #{kaAdminId}
        group by fur.admin_id
        )
    </select>
    <select id="selectAdminInfoByadminId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin
        where admin_id = #{adminId}
    </select>

    <select id="selectAdminBasicInfoByAdminId" resultType="net.summerfarm.mall.model.bo.admin.AdminBriefInfoBO" parameterType="java.lang.Integer">
        select admin_id as adminId, name_remakes as nameRemakes, sku_sorting as skuSorting
        from admin
        where admin_id = #{adminId,jdbcType=INTEGER}
    </select>
    <select id="selectAdminBasicInfoByMId" resultType="net.summerfarm.mall.model.bo.admin.AdminBriefInfoBO" parameterType="java.lang.Long">
        select a.admin_id as adminId, a.name_remakes as nameRemakes, a.sku_sorting as skuSorting
        from admin a
        left join merchant m on m.admin_id = a.admin_id
        where m_id = #{mId}
    </select>
    <select id="selectAdminDetails" resultType="net.summerfarm.mall.model.domain.Admin">
        SELECT
        a.admin_id adminId, a.create_time createTime, a.login_fail_times loginFailTimes, a.is_disabled isDisabled, a.username, a.password,
    a.login_time loginTime, a.realname, a.gender, a.department, a.phone, a.operate_id operateId, a.close_order_type closeOrderType, a.contract_method contractMethod, a.name_remakes nameRemakes,
    a.close_order_time closeOrderTime, a.admin_switch adminSwitch, a.admin_type adminType, a.admin_chain adminChain, a.admin_grade adminGrade,aae.user_id userId,m.m_id mId,m.mname mName, a.sku_sorting skuSorting
        FROM follow_up_relation fur
        left join admin a on fur.admin_id = a.admin_id
        left join admin_auth_extend aae on a.admin_id = aae.admin_id
        left join merchant m on m.m_id = fur.m_id
        WHERE fur.m_id = #{mId,jdbcType=BIGINT} and  fur.reassign = 0 and aae.`type` = 0 and aae.status = 0
    </select>

    <select id="listAdminByIds" resultMap="BaseResultMap">
        select admin_id, realname
        from admin
        where admin_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>