<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.CouponSenderRelationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.CouponSenderRelation">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="coupon_id" jdbcType="INTEGER" property="couponId" />
    <result column="coupon_sender_id" jdbcType="INTEGER" property="couponSenderId" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, coupon_id, coupon_sender_id, `number`, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from coupon_sender_relation
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from coupon_sender_relation
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CouponSenderRelation" useGeneratedKeys="true">
    insert into coupon_sender_relation (coupon_id, coupon_sender_id, `number`, 
      creator, create_time)
    values (#{couponId,jdbcType=INTEGER}, #{couponSenderId,jdbcType=INTEGER}, #{number,jdbcType=INTEGER}, 
      #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CouponSenderRelation" useGeneratedKeys="true">
    insert into coupon_sender_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="couponSenderId != null">
        coupon_sender_id,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="couponId != null">
        #{couponId,jdbcType=INTEGER},
      </if>
      <if test="couponSenderId != null">
        #{couponSenderId,jdbcType=INTEGER},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.CouponSenderRelation">
    update coupon_sender_relation
    <set>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=INTEGER},
      </if>
      <if test="couponSenderId != null">
        coupon_sender_id = #{couponSenderId,jdbcType=INTEGER},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.CouponSenderRelation">
    update coupon_sender_relation
    set coupon_id = #{couponId,jdbcType=INTEGER},
      coupon_sender_id = #{couponSenderId,jdbcType=INTEGER},
      `number` = #{number,jdbcType=INTEGER},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="findByCouponId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from coupon_sender_relation
    where coupon_id=#{couponId,jdbcType=INTEGER}
  </select>

  <select id="selectByCouponSenderId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from coupon_sender_relation
    where coupon_sender_id = #{couponSenderId,jdbcType=INTEGER}
  </select>

  <select id="selectByCouponSenderIdIn" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from coupon_sender_relation
    where coupon_sender_id in
    <foreach item="couponSenderId" collection="couponSenderIds" open="(" separator="," close=")">
      #{couponSenderId,jdbcType=INTEGER}
    </foreach>
  </select>
</mapper>