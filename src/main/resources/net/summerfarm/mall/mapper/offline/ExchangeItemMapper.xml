<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.offline.ExchangeItemMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.ExchangeItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="date_flag" jdbcType="INTEGER" property="dateFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `m_id`, `sku`, `type`, `priority`, `date_flag`, `update_time`, `create_time`
  </sql>

  <select id="selectByMid" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from exchange_item
    where `m_id` = #{mId} and `date_flag` = #{dateFlag}
  </select>

  <select id="selectByQuery" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
        from exchange_item
    <where>
      <if test="itemDTO.mId != null">
        and `m_id` = #{itemDTO.mId}
      </if>
      <if test="itemDTO.sku != null">
        and `sku` = #{itemDTO.sku}
      </if>
      <if test="itemDTO.type != null">
        and `type` = #{itemDTO.type}
      </if>
      <if test="itemDTO.priority != null">
        and `priority` = #{itemDTO.priority}
      </if>
      <if test="itemDTO.dateFlag != null">
        and `date_flag` = #{itemDTO.dateFlag}
      </if>
    </where>
  </select>

</mapper>