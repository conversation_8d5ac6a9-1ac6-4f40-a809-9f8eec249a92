<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.offline.ExpandActivityProductsMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.ExpandActivityProducts">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="date_flag" jdbcType="INTEGER" property="dateFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, area_no, sku, `type`, date_flag, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from expand_activity_products
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByChurnRisk" resultType="java.lang.String">
      select
      sku
      from expand_activity_products
     where m_id = #{mId,jdbcType=BIGINT} and type = 0 and date_flag = #{dateFlag,jdbcType=INTEGER}
    </select>
  <select id="selectByRecall" resultType="java.lang.String">
    select
    sku
    from expand_activity_products
    where m_id = #{mId,jdbcType=BIGINT} and type = 1 and date_flag = #{dateFlag,jdbcType=INTEGER}
  </select>
  <select id="selectByPullNew" resultType="java.lang.String">
    select
    sku
    from expand_activity_products
    where m_id = #{mId,jdbcType=BIGINT} and type = 2 and date_flag = #{dateFlag,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from expand_activity_products
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.ExpandActivityProducts" useGeneratedKeys="true">
    insert into expand_activity_products (m_id, area_no, sku, 
      `type`, date_flag, create_time, 
      update_time)
    values (#{mId,jdbcType=BIGINT}, #{areaNo,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{dateFlag,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.ExpandActivityProducts" useGeneratedKeys="true">
    insert into expand_activity_products
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="dateFlag != null">
        date_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="dateFlag != null">
        #{dateFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.ExpandActivityProducts">
    update expand_activity_products
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="dateFlag != null">
        date_flag = #{dateFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.ExpandActivityProducts">
    update expand_activity_products
    set m_id = #{mId,jdbcType=BIGINT},
      area_no = #{areaNo,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      date_flag = #{dateFlag,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>