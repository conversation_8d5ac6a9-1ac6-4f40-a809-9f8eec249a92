<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.offline.SkuSalesRankingListMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.offline.SkuSalesRankingList">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="day_tag" jdbcType="VARCHAR" property="dayTag" />
    <result column="merchant_type" jdbcType="VARCHAR" property="merchantType" />
    <result column="sku_list" jdbcType="VARCHAR" property="skuList" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, create_time, update_time, day_tag, merchant_type, sku_list
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sku_sales_ranking_list
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sku_sales_ranking_list
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.offline.SkuSalesRankingList">
    insert into sku_sales_ranking_list (id, create_time, update_time, 
      day_tag, merchant_type, sku_list
      )
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{dayTag,jdbcType=VARCHAR}, #{merchantType,jdbcType=VARCHAR}, #{skuList,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.offline.SkuSalesRankingList">
    insert into sku_sales_ranking_list
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="dayTag != null">
        day_tag,
      </if>
      <if test="merchantType != null">
        merchant_type,
      </if>
      <if test="skuList != null">
        sku_list,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dayTag != null">
        #{dayTag,jdbcType=VARCHAR},
      </if>
      <if test="merchantType != null">
        #{merchantType,jdbcType=VARCHAR},
      </if>
      <if test="skuList != null">
        #{skuList,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
 
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.offline.SkuSalesRankingList">
    update sku_sales_ranking_list
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dayTag != null">
        day_tag = #{dayTag,jdbcType=VARCHAR},
      </if>
      <if test="merchantType != null">
        merchant_type = #{merchantType,jdbcType=VARCHAR},
      </if>
      <if test="skuList != null">
        sku_list = #{skuList,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.offline.SkuSalesRankingList">
    update sku_sales_ranking_list
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      day_tag = #{dayTag,jdbcType=VARCHAR},
      merchant_type = #{merchantType,jdbcType=VARCHAR},
      sku_list = #{skuList,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectMaxDayTag" resultType="java.lang.String">
    select max(day_tag) from sku_sales_ranking_list
</select>

<select id="listByDayTag" resultMap="BaseResultMap">
select
    <include refid="Base_Column_List" />
    from sku_sales_ranking_list
    where day_tag = #{dayTag,jdbcType=VARCHAR}
</select>
</mapper>