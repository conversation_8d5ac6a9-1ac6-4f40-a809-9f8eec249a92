<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.offline.DataSynchronizationInformationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.DataSynchronizationInformation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="date_flag" jdbcType="INTEGER" property="dateFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `table_name`, date_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from data_synchronization_information
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByTableName" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM data_synchronization_information
    WHERE table_name = #{tableName}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from data_synchronization_information
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.DataSynchronizationInformation" useGeneratedKeys="true">
    insert into data_synchronization_information (`table_name`, date_flag, update_time, 
      create_time)
    values (#{tableName,jdbcType=VARCHAR}, #{dateFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.DataSynchronizationInformation" useGeneratedKeys="true">
    insert into data_synchronization_information
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tableName != null">
        `table_name`,
      </if>
      <if test="dateFlag != null">
        date_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tableName != null">
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="dateFlag != null">
        #{dateFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.DataSynchronizationInformation">
    update data_synchronization_information
    <set>
      <if test="tableName != null">
        `table_name` = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="dateFlag != null">
        date_flag = #{dateFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.DataSynchronizationInformation">
    update data_synchronization_information
    set `table_name` = #{tableName,jdbcType=VARCHAR},
      date_flag = #{dateFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>