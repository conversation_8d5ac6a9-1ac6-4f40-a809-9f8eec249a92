<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.offline.TimingSkuTopMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.offline.TimingSkuTop">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="date_flag" jdbcType="INTEGER" property="dateFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `sku`, `area_no`, `priority`, `date_flag`, `update_time`, `create_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from timing_sku_top
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <select id="listByAreaNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from timing_sku_top
    where area_no = #{areaNo}
    and date_flag = #{dateFlag}
    order by priority desc
  </select>

</mapper>