<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.offline.CrmMerchantDayGmvMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.CrmMerchantDayGmv">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="m_id" jdbcType="BIGINT" property="mId" />
        <result column="merchant_name" jdbcType="VARCHAR" property="merchantName" />
        <result column="area_no" jdbcType="INTEGER" property="areaNo" />
        <result column="merchant_total_gmv" jdbcType="DECIMAL" property="merchantTotalGmv" />
        <result column="distribution_gmv" jdbcType="DECIMAL" property="distributionGmv" />
        <result column="delivery_unit_price" jdbcType="DECIMAL" property="deliveryUnitPrice" />
        <result column="distribution_amout" jdbcType="INTEGER" property="distributionAmout" />
        <result column="fruit_gmv" jdbcType="DECIMAL" property="fruitGmv" />
        <result column="dairy_gmv" jdbcType="DECIMAL" property="dairyGmv" />
        <result column="non_dairy_gmv" jdbcType="DECIMAL" property="nonDairyGmv" />
        <result column="brand_gmv" jdbcType="DECIMAL" property="brandGmv" />
        <result column="core_merchant_tag" jdbcType="TINYINT" property="coreMerchantTag" />
        <result column="day_tag" jdbcType="INTEGER" property="dayTag" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>

    <sql id="Base_Column_List">
    id, m_id, merchant_name, area_no, merchant_total_gmv, distribution_gmv, delivery_unit_price,
    distribution_amout, fruit_gmv, dairy_gmv, non_dairy_gmv, brand_gmv, core_merchant_tag,
    day_tag, update_time, create_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from crm_merchant_day_gmv
        where m_id = #{id,jdbcType=BIGINT}
    </select>
</mapper>