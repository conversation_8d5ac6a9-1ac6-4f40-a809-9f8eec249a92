<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.offline.CommonlyRecommendedMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.CommonlyRecommended">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="purchases" jdbcType="INTEGER" property="purchases" />
    <result column="date_flag" jdbcType="INTEGER" property="dateFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sku, m_id, score, purchases, date_flag, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from commonly_recommended
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectCommonlyRecommended" resultType="net.summerfarm.mall.model.domain.CommonlyRecommended">
    select
    <include refid="Base_Column_List" />
    from commonly_recommended
    where m_id = #{mId,jdbcType=BIGINT} and date_flag = #{dateFlag,jdbcType=INTEGER}
    order by score desc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from commonly_recommended
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CommonlyRecommended" useGeneratedKeys="true">
    insert into commonly_recommended (sku, m_id, score, 
      purchases, date_flag, create_time, 
      update_time)
    values (#{sku,jdbcType=VARCHAR}, #{mId,jdbcType=BIGINT}, #{score,jdbcType=DECIMAL},
      #{purchases,jdbcType=INTEGER}, #{dateFlag,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CommonlyRecommended" useGeneratedKeys="true">
    insert into commonly_recommended
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sku != null">
        sku,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="purchases != null">
        purchases,
      </if>
      <if test="dateFlag != null">
        date_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="score != null">
        #{score,jdbcType=DECIMAL},
      </if>
      <if test="purchases != null">
        #{purchases,jdbcType=INTEGER},
      </if>
      <if test="dateFlag != null">
        #{dateFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.CommonlyRecommended">
    update commonly_recommended
    <set>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=DECIMAL},
      </if>
      <if test="purchases != null">
        purchases = #{purchases,jdbcType=INTEGER},
      </if>
      <if test="dateFlag != null">
        date_flag = #{dateFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.CommonlyRecommended">
    update commonly_recommended
    set sku = #{sku,jdbcType=VARCHAR},
      m_id = #{mId,jdbcType=BIGINT},
      score = #{score,jdbcType=DECIMAL},
      purchases = #{purchases,jdbcType=INTEGER},
      date_flag = #{dateFlag,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>