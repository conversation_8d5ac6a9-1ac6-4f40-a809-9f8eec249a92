<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.CashRecordMapper">
    <insert id="insert" parameterType="net.summerfarm.mall.model.domain.CashRecord">
        INSERT INTO cash_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                m_id,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="businessId != null">
                business_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                #{mId},
            </if>
            <if test="accountId != null">
                #{accountId},
            </if>
            <if test="amount != null">
                #{amount},
            </if>
            <if test="businessId != null">
                #{businessId},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="selectList" parameterType="net.summerfarm.mall.model.domain.CashRecord"
            resultType="net.summerfarm.mall.model.domain.CashRecord">
        SELECT *
        FROM (
        SELECT rp.update_time createTime,rp.red_pack_amount amount,7 AS type
        FROM red_pack rp
                WHERE rp.m_id = #{mId} and rp.account_id = #{accountId}
                AND rp.`status`=0
        UNION ALL
        SELECT rp.create_time createTime,rp.red_pack_amount amount,6 AS type
                FROM red_pack rp
                WHERE rp.m_id = #{mId} and rp.account_id = #{accountId}
                AND rp.`status`=1
                UNION ALL
                SELECT cr.create_time createTime,cr.amount,cr.type
                FROM cash_record cr
                WHERE cr.m_id = #{mId} and cr.account_id = #{accountId}
        ) t
        ORDER BY t.createTime DESC
    </select>

</mapper>