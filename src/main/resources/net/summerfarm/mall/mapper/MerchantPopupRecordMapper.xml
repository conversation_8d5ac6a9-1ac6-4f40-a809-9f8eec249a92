<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MerchantPopupRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MerchantPopupRecord">
    <!--@mbg.generated-->
    <!--@Table merchant_popup_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="type_id" jdbcType="BIGINT" property="typeId" />
    <result column="last_popup_time" jdbcType="TIMESTAMP" property="lastPopupTime" />
    <result column="count" jdbcType="INTEGER" property="count" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, m_id, type_id, last_popup_time, `count`
  </sql>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MerchantPopupRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into merchant_popup_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="lastPopupTime != null">
        last_popup_time,
      </if>
      <if test="count != null">
        `count`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=BIGINT},
      </if>
      <if test="lastPopupTime != null">
        #{lastPopupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="count != null">
        #{count,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2024-06-20-->
  <select id="selectOneByMIdAndTypeId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_popup_record
    where m_id=#{mId,jdbcType=BIGINT} and type_id=#{typeId,jdbcType=BIGINT}
  </select>

<!--auto generated by MybatisCodeHelper on 2024-06-20-->
  <update id="updateCountAndLastPopupTimeById">
    update merchant_popup_record
    set `count`=#{updatedCount,jdbcType=INTEGER}, last_popup_time=#{updatedLastPopupTime,jdbcType=TIMESTAMP}
    where id=#{id,jdbcType=BIGINT}
  </update>

</mapper>