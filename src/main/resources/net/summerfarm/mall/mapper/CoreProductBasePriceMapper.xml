<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.CoreProductBasePriceMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.CoreProductBasePrice">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="largeAreaNo" column="large_area_no" jdbcType="INTEGER"/>
        <result property="largeAreaName" column="large_area_name" jdbcType="VARCHAR"/>
        <result property="sku" column="sku" jdbcType="VARCHAR"/>
        <result property="pdId" column="pd_id" jdbcType="INTEGER"/>
        <result property="pdName" column="pd_name" jdbcType="VARCHAR"/>
        <result property="weight" column="weight" jdbcType="VARCHAR"/>
        <result property="basePrice" column="base_price" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="merchantSituationCategoryBasePrice" column="merchant_situation_category_base_price" jdbcType="DECIMAL"/>
        <result property="merchantSituationCategoryRedLinePrice" column="merchant_situation_category_red_line_price" jdbcType="DECIMAL"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,large_area_no,large_area_name,sku,pd_id,pd_name,weight,base_price,create_time,update_time,
        merchant_situation_category_base_price, merchant_situation_category_red_line_price
    </sql>

    <select id="listBasePriceByLargeAreaNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from core_product_base_price
        where large_area_no = #{largeAreaNo} and merchant_situation_category_base_price is not null
    </select>

    <select id="listBasePriceBySkus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from core_product_base_price
        where merchant_situation_category_base_price is not null and large_area_no = #{largeAreaNo} and sku in
        <foreach collection="skus" item="item" open="(" close=")"  separator=",">
            #{item}
        </foreach>
    </select>
</mapper>