<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.CirclePeopleRuleMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.CirclePeopleRule">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="BOOLEAN" property="type" />
    <result column="file_id" jdbcType="VARCHAR" property="fileId" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `name`, `type`, file_id, creator, create_time, updater, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from circle_people_rule
    where id = #{id,jdbcType=INTEGER}
  </select>
    <select id="selectAct" resultType="java.lang.Integer">
      select count(1) from circle_people_rule cpri
                            left join circle_people_relation cpr on cpri.id = cpr.rule_id
                            left join circle_people_rule_admin cprm on cpri.id = cprm.rule_id
      where  cpr.type =#{type} and cpr.type_id = #{id}
          <if test="mId!=null">
            and cprm.m_id =#{mId}
          </if>

    </select>
    <select id="selectActMid" resultType="java.lang.Integer">
      select distinct cpr.type_id from circle_people_rule cpri
      left join circle_people_relation cpr on cpri.id = cpr.rule_id
      left join circle_people_rule_admin cpra on cpri.id = cpra.rule_id
      where cpr.type = #{type}
      <if test="mId!=null">
        and cpra.m_id =#{mId}
      </if>
        and cpr.type_id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>

    </select>
  <select id="selectByRuleIdType" resultType="net.summerfarm.mall.model.domain.CirclePeopleRule">
    <if test="type ==0">
      select pb.start_time startTime,pb.end_time endTime ,pb.id as typeId
      from panic_buy pb
      where pb.id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
    <if test="type== 1">
      select ss.start_time startTime,ss.end_time endTime ,ss.id as typeId
      from strict_selection ss
      where ss.id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
    <if test="type==2">
      select b.start_time startTime,b.end_time endTime,b.id typeId
      from banner b
      where b.id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>


  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from circle_people_rule
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CirclePeopleRule" useGeneratedKeys="true">
    insert into circle_people_rule (`name`, `type`, file_id, 
      creator, create_time, updater, 
      update_time)
    values (#{name,jdbcType=VARCHAR}, #{type,jdbcType=BOOLEAN}, #{fileId,jdbcType=VARCHAR}, 
      #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CirclePeopleRule" useGeneratedKeys="true">
    insert into circle_people_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="fileId != null">
        file_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=BOOLEAN},
      </if>
      <if test="fileId != null">
        #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.CirclePeopleRule">
    update circle_people_rule
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=BOOLEAN},
      </if>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.CirclePeopleRule">
    update circle_people_rule
    set `name` = #{name,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=BOOLEAN},
      file_id = #{fileId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>