<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.DeliveryEvaluationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.DeliveryEvaluation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="delivery_plan_id" jdbcType="INTEGER" property="deliveryPlanId" />
    <result column="delivery_time" jdbcType="DATE" property="deliveryTime" />
    <result column="contact_id" jdbcType="BIGINT" property="contactId" />
    <result column="satisfaction_level" jdbcType="TINYINT" property="satisfactionLevel" />
    <result column="tag" jdbcType="VARCHAR" property="tag" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="operator_account_id" jdbcType="BIGINT" property="operatorAccountId" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="operator_phone" jdbcType="VARCHAR" property="operatorPhone" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
  </resultMap>
  <sql id="conditionSql">
    <where>
      <if test="orderNo != null">
        and order_no = #{orderNo,jdbcType=VARCHAR}
      </if>
      <if test="deliveryPlanId != null">
        and delivery_plan_id = #{deliveryPlanId,jdbcType=INTEGER}
      </if>
      <if test="deliveryTime != null">
        and delivery_time = #{deliveryTime,jdbcType=DATE}
      </if>
      <if test="contactId != null">
        and contact_id = #{contactId,jdbcType=BIGINT}
      </if>
    </where>
  </sql>

  <sql id="Base_Column_List">
    id, order_no orderNo, delivery_plan_id deliveryPlanId, delivery_time deliveryTime, contact_id contactId, satisfaction_level satisfactionLevel, tag,
    remark, operator_account_id operatorAccountId, `operator`, operator_phone operatorPhone, create_time createTime, update_time updateTime, type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from delivery_evaluation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from delivery_evaluation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.DeliveryEvaluation" useGeneratedKeys="true">
    insert into delivery_evaluation (order_no, delivery_plan_id, delivery_time, 
      contact_id, satisfaction_level, tag, 
      remark, operator_account_id, `operator`, 
      operator_phone, create_time, update_time, type
      )
    values (#{orderNo,jdbcType=VARCHAR}, #{deliveryPlanId,jdbcType=INTEGER}, #{deliveryTime,jdbcType=DATE}, 
      #{contactId,jdbcType=BIGINT}, #{satisfactionLevel,jdbcType=TINYINT}, #{tag,jdbcType=VARCHAR},
      #{remark,jdbcType=VARCHAR}, #{operatorAccountId,jdbcType=BIGINT}, #{operator,jdbcType=VARCHAR}, 
      #{operatorPhone,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{type,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.DeliveryEvaluation" useGeneratedKeys="true">
    insert into delivery_evaluation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="deliveryPlanId != null">
        delivery_plan_id,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="contactId != null">
        contact_id,
      </if>
      <if test="satisfactionLevel != null">
        satisfaction_level,
      </if>
      <if test="tag != null">
        tag,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="operatorAccountId != null">
        operator_account_id,
      </if>
      <if test="operator != null">
        `operator`,
      </if>
      <if test="operatorPhone != null">
        operator_phone,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="type != null">
        type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPlanId != null">
        #{deliveryPlanId,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=DATE},
      </if>
      <if test="contactId != null">
        #{contactId,jdbcType=BIGINT},
      </if>
      <if test="satisfactionLevel != null">
        #{satisfactionLevel,jdbcType=TINYINT},
      </if>
      <if test="tag != null">
        #{tag,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operatorAccountId != null">
        #{operatorAccountId,jdbcType=BIGINT},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorPhone != null">
        #{operatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch">
    insert into delivery_evaluation (order_no, delivery_plan_id, delivery_time,
    contact_id, satisfaction_level, tag,
    remark, operator_account_id, `operator`,
    operator_phone, create_time, update_time, type
    )
    values
    <foreach collection="list" separator="," item="item">
      (#{item.orderNo,jdbcType=VARCHAR}, #{item.deliveryPlanId,jdbcType=INTEGER}, #{item.deliveryTime,jdbcType=DATE},
      #{item.contactId,jdbcType=BIGINT}, #{item.satisfactionLevel,jdbcType=TINYINT}, #{item.tag,jdbcType=VARCHAR},
      #{item.remark,jdbcType=VARCHAR}, #{item.operatorAccountId,jdbcType=BIGINT}, #{item.operator,jdbcType=VARCHAR},
      #{item.operatorPhone,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
      #{item.type,jdbcType=INTEGER}
      )
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.DeliveryEvaluation">
    update delivery_evaluation
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPlanId != null">
        delivery_plan_id = #{deliveryPlanId,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=DATE},
      </if>
      <if test="contactId != null">
        contact_id = #{contactId,jdbcType=BIGINT},
      </if>
      <if test="satisfactionLevel != null">
        satisfaction_level = #{satisfactionLevel,jdbcType=TINYINT},
      </if>
      <if test="tag != null">
        tag = #{tag,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operatorAccountId != null">
        operator_account_id = #{operatorAccountId,jdbcType=BIGINT},
      </if>
      <if test="operator != null">
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorPhone != null">
        operator_phone = #{operatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.DeliveryEvaluation">
    update delivery_evaluation
    set order_no = #{orderNo,jdbcType=VARCHAR},
      delivery_plan_id = #{deliveryPlanId,jdbcType=INTEGER},
      delivery_time = #{deliveryTime,jdbcType=DATE},
      contact_id = #{contactId,jdbcType=BIGINT},
      satisfaction_level = #{satisfactionLevel,jdbcType=TINYINT},
      tag = #{tag,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      operator_account_id = #{operatorAccountId,jdbcType=BIGINT},
      `operator` = #{operator,jdbcType=VARCHAR},
      operator_phone = #{operatorPhone,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectList" resultType="net.summerfarm.mall.model.domain.DeliveryEvaluation"
          parameterType="net.summerfarm.mall.model.domain.DeliveryEvaluation">
    select
    id, order_no orderNo, delivery_plan_id deliveryPlanId, delivery_time deliveryTime, contact_id contactId, satisfaction_level satisfactionLevel, tag,
    remark, operator_account_id operatorAccountId, `operator`, operator_phone operatorPhone, create_time createTime, update_time updateTime, type
    from delivery_evaluation
    <include refid="conditionSql"/>
  </select>

  <select id="selectListByDeliveryPlanId" resultType="net.summerfarm.mall.model.domain.DeliveryEvaluation">
    select
    <include refid="Base_Column_List" />
    from delivery_evaluation
    where delivery_plan_id in
    <foreach collection="deliveryIds" open="(" close=") " separator="," item="id">
      #{id}
    </foreach>
  </select>
</mapper>