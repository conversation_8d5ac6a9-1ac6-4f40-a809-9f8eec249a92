<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.SMSSceneMapper">
    <resultMap id="smsScene" type="net.summerfarm.mall.model.domain.SMSScene">
        <id property="id" javaType="long" jdbcType="BIGINT" column="id"/>
        <result property="templateId" jdbcType="CHAR" column="template_id" javaType="java.lang.String"/>
        <result property="sceneId" jdbcType="BIGINT" column="scene_id" javaType="long"/>
    </resultMap>
    <select id="getByTemplateId" resultMap="smsScene">
        SELECT id, template_id, title, platform, template, scene_id
        FROM sms_scene
        WHERE template_id = #{templateId}
        limit 1
    </select>

    <select id="selectByScenePlatform" resultMap="smsScene">
        SELECT id, template_id, title, platform, template, scene_id
        FROM sms_scene
        WHERE scene_id = #{sceneId}
          AND platform = #{platform}
        limit 1
    </select>
</mapper>