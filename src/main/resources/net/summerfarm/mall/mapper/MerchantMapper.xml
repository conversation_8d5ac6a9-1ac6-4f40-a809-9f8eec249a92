<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MerchantMapper" >

  <update id="updateChannelCode">
    UPDATE merchant m
    SET m.channel_code = #{channelCode}
    WHERE m.m_id = #{mId}
  </update>

  <select id="selectChannelCode" parameterType="java.lang.Long" resultType="java.lang.String">
    SELECT m.channel_code
    FROM merchant m
    WHERE m.m_id = #{mId}
  </select>


  <select id="selectOpenId" parameterType="java.lang.Long" resultType="java.lang.String">
    SELECT m.openid FROM merchant m
    WHERE m.m_id = #{mId}
  </select>

  <select id="selectOne" parameterType="hashmap" resultType="net.summerfarm.mall.model.domain.Merchant" >
    select m.m_id mId, m.mname, m.mcontact, m.phone, m.openid, m.islock, m.area_no areaNo, m.area,m.province,m.city,m.address,
    m.last_order_time lastOrderTime, m.inviter_channel_code inviterChannelCode,m.unionid,m.mp_openid mpOpenid,m.channel_code channelCode, m.remark,m.direct,m.size,m.server,m.admin_id adminId,m.member_integral memberIntegral,
    m.grade,m.pop_view popView,m.sku_show skuShow,recharge_amount rechargeAmount,m.register_time registerTime,m.cash_amount cashAmount,m.first_login_pop firstLoginPop,m.change_pop changePop,m.house_number houseNumber, m.operate_status operateStatus, m.next_grade nextGrade
    from merchant m
    <where>
      <if test="mId != null">
        AND m.m_id = #{mId}
      </if>
      <if test="openid != null">
        AND m.openid = #{openid}
      </if>
      <if test="phone != null">
        AND m.phone = #{phone}
      </if>
      <if test="unionid != null">
        AND m.unionid = #{unionid}
      </if>
      <if test="channelCode != null">
        AND m.channel_code = #{channelCode}
      </if>
    </where>
    limit 1
  </select>

  <select id="count" resultType="java.lang.Integer" parameterType="hashmap" >
     SELECT COUNT(1) FROM merchant m
     <where>
       <if test="openid != null">
         AND m.openid = #{openid}
       </if>
       <if test="phone != null">
         AND m.phone = #{phone}
       </if>
       <if test="channelCode != null">
         AND channel_code = #{channelCode}
       </if>
       <if test="mname != null" >
         AND mname = #{mname}
       </if>
       <if test="notEqualsOpenid != null">
         AND m.openid <![CDATA[<>]]> #{notEqualsOpenid}
       </if>
     </where>
  </select>

  <!--更新商户信息-->
  <update id="updateByOpenId" parameterType="net.summerfarm.mall.model.domain.Merchant">
    UPDATE merchant
    SET islock='1',
    mcontact = #{mcontact},
    mname = #{mname},
    province = #{province},
    city = #{city},
    area = #{area},
    address = #{address},
    shop_sign = #{shopSign},
    other_proof = #{otherProof},
    business_license = #{businessLicense},
    register_time = #{registerTime},
    unionid = #{unionid}
    <if test="poiNote != null">
      ,poi_note = #{poiNote}
    </if>
	WHERE openid= #{openid}
  </update>

  <!--auto code-->
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="mId" keyColumn="m_id" parameterType="net.summerfarm.mall.model.domain.Merchant" >
    <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="mId">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchant
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="mname != null" >
        mname,
      </if>
      <if test="mcontact != null" >
        mcontact,
      </if>
      <if test="openid != null" >
        openid,
      </if>
      <if test="phone != null" >
        phone,
      </if>
      <if test="islock != null" >
        islock,
      </if>
      <if test="rankId != null" >
        rank_id,
      </if>
      <if test="registerTime != null" >
        register_time,
      </if>
      <if test="loginTime != null" >
        login_time,
      </if>
      <if test="invitecode != null" >
        invitecode,
      </if>
      <if test="inviterChannelCode != null" >
        inviter_channel_code,
      </if>
      <if test="auditTime != null" >
        audit_time,
      </if>
      <if test="auditUser != null" >
        audit_user,
      </if>
      <if test="businessLicense != null" >
        business_license,
      </if>
      <if test="province != null" >
        province,
      </if>
      <if test="city != null" >
        city,
      </if>
      <if test="area != null" >
        area,
      </if>
      <if test="address != null" >
        address,
      </if>
      <if test="poiNote != null" >
        poi_note,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="shopSign != null" >
        shop_sign,
      </if>
      <if test="otherProof != null" >
        other_proof,
      </if>
      <if test="unionid != null" >
        unionid,
      </if>
      <if test="adminId != null" >
        admin_id,
      </if>
      <if test="direct != null" >
        direct,
      </if>
      <if test="size != null" >
        size,
      </if>
      <if test="areaNo != null" >
        area_no,
      </if>
      <if test="mpOpenid != null">
        mp_openid,
      </if>
      <if test="houseNumber != null">
        house_number,
      </if>
      <if test="cluePool != null">
        clue_pool,
      </if>
      <if test="enterpriseScale != null">
        enterprise_scale ,
      </if>
      <if test="companyBrand != null">
        company_brand ,
      </if>
      <if test="type != null">
        `type` ,
      </if>
      <if test="skuShow != null">
        sku_show,
      </if>
      <if test="doorPic != null">
        `door_pic` ,
      </if>
      <if test="operateStatus != null">
        `operate_status` ,
      </if>
      <if test="businessLine != null">
        `business_line` ,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="mname != null" >
        #{mname,jdbcType=VARCHAR},
      </if>
      <if test="mcontact != null" >
        #{mcontact,jdbcType=VARCHAR},
      </if>
      <if test="openid != null" >
        #{openid,jdbcType=VARCHAR},
      </if>
      <if test="phone != null" >
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="islock != null" >
        #{islock,jdbcType=INTEGER},
      </if>
      <if test="rankId != null" >
        #{rankId,jdbcType=INTEGER},
      </if>
      <if test="registerTime != null" >
        #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loginTime != null" >
        #{loginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invitecode != null" >
        #{invitecode,jdbcType=VARCHAR},
      </if>
      <if test="inviterChannelCode != null" >
        #{inviterChannelCode,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null" >
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditUser != null" >
        #{auditUser,jdbcType=INTEGER},
      </if>
      <if test="businessLicense != null" >
        #{businessLicense,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null" >
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="poiNote != null" >
        #{poiNote,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="shopSign != null" >
        #{shopSign,jdbcType=VARCHAR},
      </if>
      <if test="otherProof != null" >
        #{otherProof,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null" >
        #{unionid},
      </if>
      <if test="adminId != null" >
        #{adminId},
      </if>
      <if test="direct != null" >
        #{direct},
      </if>
      <if test="size != null" >
        #{size},
      </if>
      <if test="areaNo != null" >
        #{areaNo},
      </if>
      <if test="mpOpenid != null">
        #{mpOpenid},
      </if>
      <if test="houseNumber != null">
        #{houseNumber},
      </if>
      <if test="cluePool != null">
        #{cluePool},
      </if>
      <if test="enterpriseScale != null">
        #{enterpriseScale} ,
      </if>
      <if test="companyBrand != null">
        #{companyBrand} ,
      </if>
      <if test="type != null">
        #{type},
      </if>
      <if test="skuShow != null">
        #{skuShow},
      </if>
      <if test="doorPic != null">
        #{doorPic},
      </if>
      <if test="operateStatus != null">
        #{operateStatus},
      </if>
      <if test="businessLine != null">
        #{businessLine},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.Merchant" >
    update merchant
    <set >
      <if test="mname != null" >
        mname = #{mname,jdbcType=VARCHAR},
      </if>
      <if test="mcontact != null" >
        mcontact = #{mcontact,jdbcType=VARCHAR},
      </if>
      <if test="openid != null" >
        openid = #{openid,jdbcType=VARCHAR},
      </if>
      <if test="phone != null" >
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="islock != null" >
        islock = #{islock,jdbcType=INTEGER},
      </if>
      <if test="rankId != null" >
        rank_id = #{rankId,jdbcType=INTEGER},
      </if>
      <if test="registerTime != null" >
        register_time = #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loginTime != null" >
        login_time = #{loginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invitecode != null" >
        invitecode = #{invitecode,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null" >
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditUser != null" >
        audit_user = #{auditUser,jdbcType=INTEGER},
      </if>
      <if test="businessLicense != null" >
        business_license = #{businessLicense,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null" >
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null" >
        area_no = #{areaNo,jdbcType=TIMESTAMP},
      </if>
      <if test="address != null" >
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="poiNote != null" >
        poi_note = #{poiNote,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="shopSign != null" >
        shop_sign = #{shopSign,jdbcType=VARCHAR},
      </if>
      <if test="otherProof != null" >
        other_proof = #{otherProof,jdbcType=VARCHAR},
      </if>
      <if test="lastOrderTime != null" >
        last_order_time = #{lastOrderTime},
      </if>
      <if test="unionid != null" >
        unionid = #{unionid},
      </if>
      <if test="mpOpenid != null" >
        mp_openid = #{mpOpenid},
      </if>
      <if test="memberIntegral != null">
        member_integral = #{memberIntegral},
      </if>
      <if test="grade != null">
        grade = #{grade},
      </if>
      <if test="popView != null">
        pop_view = #{popView},
      </if>
      <if test="firstLoginPop != null">
        first_login_pop = #{firstLoginPop},
      </if>
      <if test="changePop != null">
        change_pop = #{changePop},
      </if>
      <if test="operateStatus != null">
        operate_status = #{operateStatus},
      </if>
      <if test="doorPic != null">
        door_pic = #{doorPic},
      </if>
      <if test="direct != null">
        direct = #{direct},
      </if>
      <if test="skuShow != null">
        sku_show = #{skuShow},
      </if>
      <if test="size != null">
        size = #{size},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId},
      </if>
      <if test="submitReviewTime != null">
        submit_review_time = #{submitReviewTime},
      </if>
    </set>
    where m_id = #{mId,jdbcType=BIGINT}
  </update>

  <update id="updateMemberintegral">
    UPDATE merchant SET member_integral=0
  </update>

  <select id="selectMerchantList" parameterType="java.lang.Long" resultType="net.summerfarm.mall.model.domain.Merchant">
    select m.m_id mId, m.role_id roleId, m.mname, m.mcontact, m.phone, m.openid, m.islock, m.area_no areaNo, m.area,m.province,m.city,m.address,ifnull(direct,2) direct,m.enterprise_scale enterpriseScale,
    m.last_order_time lastOrderTime, m.inviter_channel_code inviterChannelCode,m.unionid,m.mp_openid mpOpenid,m.channel_code channelCode, m.remark,m.direct,m.size,m.server,m.admin_id adminId,m.member_integral memberIntegral,
    m.grade,m.pop_view popView,m.sku_show skuShow,recharge_amount rechargeAmount,m.register_time registerTime,m.cash_amount cashAmount,m.first_login_pop firstLoginPop,m.change_pop changePop,m.house_number houseNumber,m.display_button displayButton
    from merchant m
    WHERE m.m_id IN
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <update id="updateRechargeAmount">
    UPDATE merchant
    SET recharge_amount = recharge_amount + #{amount}
    WHERE m_id = #{mId,jdbcType=BIGINT}
  </update>
  <update id="updateManagerInfo" parameterType="net.summerfarm.mall.model.domain.Merchant">
    update merchant
    set mcontact = #{mcontact},
        openid = #{openid},
        phone = #{phone},
        unionid = #{unionid},
        mp_openid = #{mpOpenid},
        cash_amount = #{cashAmount},
        cash_update_time = #{cashUpdateTime},
        login_time = #{loginTime},
        last_order_time = #{lastOrderTime}
    where m_id = #{mId}
  </update>
  <delete id="deleteByPrimaryKey">
    delete from merchant where m_id = #{mId}
  </delete>

  <select id="selectOneByMid" resultType="net.summerfarm.mall.model.domain.Merchant" >
    select m.m_id mId, m.role_id roleId, m.mname, m.mcontact, m.phone, m.openid, m.islock, m.area_no areaNo, m.area,m.province,m.city,m.address,
    m.last_order_time lastOrderTime, m.inviter_channel_code inviterChannelCode,m.unionid,m.mp_openid mpOpenid,m.channel_code channelCode, m.remark,m.direct,m.size,m.server,m.admin_id adminId,m.member_integral memberIntegral,
    m.grade,m.pop_view popView,m.sku_show skuShow,recharge_amount rechargeAmount,m.register_time registerTime,m.cash_amount cashAmount,m.first_login_pop firstLoginPop,m.change_pop changePop,m.house_number houseNumber,
    m.display_button displayButton, m.clue_pool cluePool, m.operate_status operateStatus,m.type type, m.door_pic doorPic,m.business_line businessLine
    from merchant m
    where m.m_id = #{mId}
  </select>

  <select id="selectMallByMid" resultType="net.summerfarm.mall.model.vo.MerchantVO" >
    select m.m_id mId, m.admin_id adminId,m.display_button displayButton, m.recharge_amount rechargeAmount, m.size, m.direct, m.mname, m.phone, m.area_no areaNo
    from merchant m
    where m.m_id = #{mId}
  </select>


  <select id="selectMerchantByArea" resultType="net.summerfarm.mall.model.domain.Merchant" >
    select  m.openid,m.phone
    from merchant m
    where m.area_no = #{areaNo}
  </select>
  <select id="selectIsUserPhone" resultType="net.summerfarm.mall.model.domain.Merchant">
    SELECT m.m_id mId, m.mname, m.mcontact, m.phone,m.openid ,m.size,m.pre_register_flag preRegisterFlag, m.city, m.area, m.clue_pool cluePool, m.business_line businessLine
      from merchant m
        where m.phone = #{phone}
  </select>
  <!--auto Code-->
  <select id="selectByPrimaryKey" resultType="net.summerfarm.mall.model.domain.Merchant" parameterType="java.lang.Long">
        select
        m_id mId, mname, mcontact, phone, islock, rank_id, register_time, login_time,last_order_time,m.openid ,
        invitecode, channel_code, audit_time, audit_user, province, city, area, address,remark,m.area_no areaNo,size,type,
        trade_area,trade_group,admin_id adminId,direct,server,member_integral,recharge_amount,grade,area_no,
        cash_amount,cash_update_time, house_number houseNumber,examine_type examineType,operate_status operateStatus,updater,business_line businessLine
        from merchant m
        where m_id = #{mId,jdbcType=BIGINT}
    </select>
  <select id="selectByMid" resultType="net.summerfarm.mall.model.domain.Merchant" >
    select m.m_id mId, m.role_id roleId, m.mname, m.mcontact, m.phone, m.openid, m.islock, m.area_no areaNo, m.area,m.province,m.city,m.address,ifnull(direct,2) direct,m.enterprise_scale enterpriseScale,
    m.last_order_time lastOrderTime, m.inviter_channel_code inviterChannelCode,m.unionid,m.mp_openid mpOpenid,m.channel_code channelCode, m.remark,m.direct,m.size,m.server,m.admin_id adminId,m.member_integral memberIntegral,
    m.grade,m.pop_view popView,m.sku_show skuShow,recharge_amount rechargeAmount,m.register_time registerTime,m.cash_amount cashAmount,m.first_login_pop firstLoginPop,m.change_pop changePop,m.house_number houseNumber,m.display_button displayButton,m.pre_register_flag preRegisterFlag,m.business_line businessLine
    from merchant m
    where m.m_id = #{mId}
  </select>
  <update id="inCreaseRechargeAmount">
    update merchant
    set recharge_amount = recharge_amount + #{totalPrice}
    where m_id = #{mId}
  </update>

  <select id="selectMNameByMid" resultType="net.summerfarm.mall.model.dto.merchant.merchantQueryDTO">
    select m.mname as merchantName,m.area_no as areaNo
    from merchant m
    where m.m_id = #{mId}
  </select>

  <select id="getMerchantInfoByCId" resultType="net.summerfarm.mall.model.vo.BatchUpdateDeliveryDateVo">
    SELECT
      a.phone,
      m.openid,
      c.phone AS sendPhone
    FROM
      contact c
        LEFT JOIN merchant m ON m.m_id = c.m_id
        LEFT JOIN follow_up_relation fur ON m.m_id = fur.m_id
        AND fur.reassign = 0
        LEFT JOIN admin a ON fur.admin_id = a.admin_id
    WHERE
      c.contact_id = #{contactId}
  </select>

  <select id="selectByOpenId" resultType="net.summerfarm.mall.model.domain.Merchant">
    select m.m_id mId, m.mname, m.mcontact, m.phone, m.openid, m.islock, m.area_no areaNo, m.area,m.province,m.city,m.address,
           m.last_order_time lastOrderTime, m.inviter_channel_code inviterChannelCode,m.unionid,m.mp_openid mpOpenid,
           m.channel_code channelCode, m.remark,m.direct,m.size,m.server,m.admin_id adminId,m.member_integral memberIntegral,
           m.grade,m.pop_view popView,m.sku_show skuShow,recharge_amount rechargeAmount,m.register_time registerTime,
           m.cash_amount cashAmount,m.first_login_pop firstLoginPop,m.change_pop changePop,m.house_number houseNumber
    from merchant m
    where openid = #{openId}
  </select>

    <select id="selectByAccountId" resultType="net.summerfarm.mall.model.dto.merchant.MerchantAndAccountDTO">
        select msa.account_id accountId,
               msa.mp_openid  mpOpenid,
               m.m_id         mId,
               m.operate_status operateStatus,
               m.mname          ,
               msa.phone      ,
               m.province       ,
               m.city           ,
               m.area           ,
               m.address
        from merchant m
        left join merchant_sub_account msa on m.m_id = msa.m_id
        where  msa.account_id = #{accountId}
  </select>
</mapper>