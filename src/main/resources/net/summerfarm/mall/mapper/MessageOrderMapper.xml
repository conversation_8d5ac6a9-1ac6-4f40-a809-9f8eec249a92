<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MessageOrderMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MessageOrder" >
    <id column="sm_id" property="smId" jdbcType="BIGINT" />
    <result column="m_id" property="mId" jdbcType="BIGINT" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="message_type_id" property="messageTypeId" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="net.summerfarm.mall.model.domain.MessageOrder" extends="BaseResultMap" >
    <result column="content" property="content" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    sm_id, m_id, order_no, message_type_id, create_time
  </sql>
  <sql id="Blob_Column_List" >
    content
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from message_order
    where sm_id = #{smId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from message_order
    where sm_id = #{smId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.MessageOrder" >
    insert into message_order (sm_id, m_id, order_no, 
      message_type_id, create_time, content
      )
    values (#{smId,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, 
      #{messageTypeId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{content,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.MessageOrder" >
    insert into message_order
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="smId != null" >
        sm_id,
      </if>
      <if test="mId != null" >
        m_id,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="messageTypeId != null" >
        message_type_id,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="content != null" >
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="smId != null" >
        #{smId,jdbcType=BIGINT},
      </if>
      <if test="mId != null" >
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="messageTypeId != null" >
        #{messageTypeId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime},
      </if>
      <if test="content != null" >
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MessageOrder" >
    update message_order
    <set >
      <if test="mId != null" >
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="messageTypeId != null" >
        message_type_id = #{messageTypeId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where sm_id = #{smId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="net.summerfarm.mall.model.domain.MessageOrder" >
    update message_order
    set m_id = #{mId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      message_type_id = #{messageTypeId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      content = #{content,jdbcType=LONGVARCHAR}
    where sm_id = #{smId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.MessageOrder" >
    update message_order
    set m_id = #{mId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      message_type_id = #{messageTypeId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where sm_id = #{smId,jdbcType=BIGINT}
  </update>
</mapper>