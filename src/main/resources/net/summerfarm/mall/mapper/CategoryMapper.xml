<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.CategoryMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Category" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="parent_id" property="parentId" jdbcType="INTEGER" />
    <result column="category" property="category" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, parent_id, category,type
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from category
    where id = #{categoryId,jdbcType=INTEGER}
  </select>

  <select id="selectTreeNodes" resultType="net.summerfarm.mall.model.domain.Category">
    SELECT c.id, c.parent_id parentId, c.category,c.type
    FROM category c
    WHERE outdated=0
  </select>

  <select id="selectChildrenId" resultType="java.lang.Integer" parameterType="java.lang.Integer">
    SELECT c.id FROM category c
    WHERE c.parent_id = #{parentId} and c.outdated = 0
  </select>

  <select id="selectChildrenIdByList" resultType="java.lang.Integer">
    SELECT c.id FROM category c
    WHERE c.outdated = 0 and c.parent_id in
    <foreach collection="list" open="(" close=") " separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="selectDairy" resultType="java.lang.Integer">
    SELECT c.id
    FROM category c
    WHERE  c.outdated=0 AND c.type=2
  </select>

  <select id="selectOne" resultType="net.summerfarm.mall.model.domain.Category" parameterType="java.lang.Integer">
    SELECT id,parent_id parentId,category,type
    FROM category
    WHERE id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectCategorys" resultType="net.summerfarm.mall.model.domain.Category">
    SELECT c.id, c.parent_id parentId, c.category, c.type
    FROM inventory i
    LEFT JOIN products p ON i.pd_id = p.pd_id
    LEFT JOIN category c ON p.category_id = c.id
    LEFT JOIN area_sku a ON i.sku = a.sku
    <if test="adminId != null">
      LEFT JOIN major_price mp on mp.sku=a.sku and mp.area_no=a.area_no and mp.admin_id=#{adminId} and mp.direct=#{direct}
    </if>
    WHERE a.on_sale = 1
    AND i.outdated = 0
    AND a.area_no = #{areaNo}
    AND a.show = 1
    <if test="skuShow == null">
      AND a.m_type = 0
      <if test="skuList != null and skuList.size!=0">
        AND i.sku IN
        <foreach collection="skuList" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
    </if>

    <if test="'大客户' == msize">
      <if test="skuShow != null and skuShow == 1">
        <if test="skuList != null and skuList.size!=0">
          AND i.sku IN
          <foreach collection="skuList" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
        </if>
      </if>

      <if test="skuShow != null and skuShow == 2">
        AND (
        a.m_type = 0
        <if test="skuList != null and skuList.size!=0">
          OR i.sku IN
          <foreach collection="skuList" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
        </if>
        )
      </if>
    </if>

    <if test="unShowCate != null and unShowCate.size!=0">
      and i.type = 0
      and c.id not in
      <foreach collection="unShowCate" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
    GROUP BY c.id
  </select>
  <select id="queryBySku" resultMap="BaseResultMap">
    select c.id, c.parent_id, c.category, c.outdated, c.icon, c.type
    from category c
    inner join products p on c.id = p.category_id
    inner join inventory i on p.pd_id = i.pd_id and i.sku = #{sku}
  </select>


  <select id="selectByIds" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from category
    where id in
    <foreach collection="list" open="(" close=") " separator="," item="item">
      #{item}
    </foreach>
  </select>
  <select id="selectCategoryIdByName" resultType="java.lang.Integer">
    SELECT c.id
    FROM category c
    WHERE  c.outdated=0 AND c.`category`= #{name}
  </select>
  <select id="selectChildrenIds" resultType="java.lang.Integer">
    SELECT c.id
    FROM category c
    WHERE c.outdated = 0 and c.parent_id in
    <foreach collection="ids" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="getAllCategory" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from category
    WHERE outdated = 0
  </select>


  <select id="getCategoryByIds" resultType="net.summerfarm.mall.model.dto.CategoryDto">
    select
    c.`id`,
    c.`category`,
    c.`outdated`,
    c.`type`,
    fc.`id` as frontCategoryId,
    fc.`name` as frontCategoryName,
    fc.`parent_id` as parentFrontCategoryId
    from category c
    inner join front_category_to_category fct on c.`id` = fct.`category_id`
    inner join front_category fc on fc.`id` = `fct`.`front_category_id`
    inner join front_category_to_area fcta on fc.id = fcta.front_category_id
    where c.id in
    <foreach collection="list" open="(" close=") " separator="," item="item">
      #{item}
    </foreach>
    and fcta.area_no =  #{areaNo} and fcta.display = 1
    and c.outdated = 0 and fc.outdated = 0 group by fc.`id`
  </select>

</mapper>
