<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.CashToRpMapper">

    <insert id="insertBatch" parameterType="net.summerfarm.mall.model.domain.CashToRp">
        INSERT INTO cash_to_rp(cash_id,rp_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.cashId},#{item.rpId})
        </foreach>
    </insert>

</mapper>