<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.CouponMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Coupon" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="code" property="code" jdbcType="VARCHAR" />
    <result column="money" property="money" jdbcType="DECIMAL" />
    <result column="threshold" property="threshold" jdbcType="DECIMAL" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="vaild_date" property="vaildDate" jdbcType="TIMESTAMP" />
    <result column="vaild_time" property="vaildTime" jdbcType="INTEGER" />
    <result column="new_hand" property="newHand" jdbcType="TINYINT" />
    <result column="reamrk" property="reamrk" jdbcType="VARCHAR" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="agio_type" property="agioType" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="grouping" property="grouping" jdbcType="INTEGER" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="category_id" property="categoryId" jdbcType="VARCHAR" />
    <result column="start_time" property="startTime"/>
    <result column="start_date" property="startDate"/>
    <result column="activity_scope" property="activityScope"/>
    <result column="quantity_claimed" property="quantityClaimed"/>
    <result column="grant_amount" property="grantAmount"/>
    <result column="grant_limit" property="grantLimit"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, name, code, money, threshold, type, vaild_date, vaild_time, new_hand, reamrk, 
    add_time,status,agio_type,`grouping`,sku,category_id, start_time, start_date, activity_scope,
    quantity_claimed, grant_amount, grant_limit
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from coupon
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectCoupon" parameterType="net.summerfarm.mall.model.domain.Coupon" resultMap="BaseResultMap">
    SELECT
    id, name, code, money, threshold, type, vaild_date vaildDate, vaild_time vaildTime, new_hand newHand, reamrk,
    add_time addTime,status,agio_type agioType,`grouping`,sku,category_id categoryId, start_time startTime
    FROM coupon
    WHERE money=#{money}
    AND threshold=#{threshold}
    AND agio_type=#{agioType}
    AND grouping=#{grouping}
    <if test="name != null">
      AND name=#{name}
    </if>
    <if test="type != null">
      AND type=#{type}
    </if>
    <if test="vaildDate != null">
      AND vaild_date=#{vaildDate}
    </if>
    <if test="vaildTime != null">
      AND vaild_time=#{vaildTime}
    </if>
    <if test="newHand != null">
      AND new_hand=#{newHand}
    </if>
    <if test="code != null">
      AND code=#{code}
    </if>
    <if test="startTime != null">
      AND start_time = #{startTime}
    </if>
    <if test="startDate != null">
      AND start_date = #{startDate}
    </if>
  </select>
  <insert id="insertNewCoupon" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.Coupon">
    INSERT INTO coupon
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="threshold != null">
        threshold,
      </if>
      <if test="agioType != null">
        agio_type,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="vaildDate != null">
        vaild_date,
      </if>
      <if test="vaildTime != null">
        vaild_time,
      </if>
      <if test="grouping != null">
        `grouping`,
      </if>
      <if test="newHand != null">
        new_hand,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="reamrk != null">
        reamrk,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="autoCreated != null">
        auto_created,
      </if>
      <if test="quantityClaimed != null">
        quantity_claimed,
      </if>
      <if test="grantAmount != null">
        grant_amount,
      </if>
      <if test="grantLimit != null">
        grant_limit,
      </if>
      <if test="activityScope != null">
        activity_scope,
      </if>
      <if test="creator != null">
        creator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="threshold != null">
        #{threshold,jdbcType=DECIMAL},
      </if>
      <if test="agioType != null">
        #{agioType,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="vaildDate != null">
        #{vaildDate,jdbcType=TIMESTAMP},
      </if>
      <if test="vaildTime != null">
        #{vaildTime,jdbcType=INTEGER},
      </if>
      <if test="grouping != null">
        #{grouping,jdbcType=INTEGER},
      </if>
      <if test="newHand != null">
        #{newHand,jdbcType=TINYINT},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="reamrk != null">
        #{reamrk,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime} ,
      </if>
      <if test="startDate != null">
        #{startDate} ,
      </if>
      <if test="autoCreated != null">
        #{autoCreated},
      </if>
      <if test="quantityClaimed != null">
        #{quantityClaimed},
      </if>
      <if test="grantAmount != null">
        #{grantAmount},
      </if>
      <if test="grantLimit != null">
        #{grantLimit},
      </if>
      <if test="activityScope != null">
        #{activityScope},
      </if>
      <if test="creator != null">
        #{creator},
      </if>
    </trim>

  </insert>


  <select id="selectByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
    select
    <include refid="Base_Column_List" />
    from coupon
    where threshold = money + 0.01 and code = #{code} and status = 1
    limit 1
  </select>
    <select id="selectMerchantRp" resultType="net.summerfarm.mall.model.vo.MerchantAddCoupon">
      select mc.m_id as id,c.money as money,mc.vaild_date as vaildDate,m.phone,m.mname,m.openid
        from merchant_coupon mc
        left join coupon c on mc.coupon_id = c.id
        left join merchant m on mc.m_id = m.m_id
      where mc.coupon_id in(
        select distinct id from coupon where name = '619奖励红包'
      )
    </select>
  <select id="selectIsSendRpWx" resultType="java.lang.Integer">
    select ifnull(count(*),0) from coupon c
                    left join merchant_coupon mc on c.id = mc.coupon_id
    where m_id = #{mId}
      and c.name = '619奖励红包'
  </select>

  <select id="selectByIdIn" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from coupon
    where id in
    <foreach item="item" index="index" collection="idCollection"
             open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
    </foreach>
  </select>

  <select id="batchSelectByIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from coupon
    where id in
    <foreach item="item" index="index" collection="list"
             open="(" separator="," close=")">
      #{item}
    </foreach>
    and status = 1
    order by money desc
  </select>

  <update id="updateGrantAmount">
    update coupon
    set grant_amount = grant_amount - #{amount}
    where id = #{id} and grant_amount >= #{amount}
  </update>
</mapper>