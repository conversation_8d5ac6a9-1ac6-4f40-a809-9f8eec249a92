<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MchPopupOtificationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MchPopupOtification">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="banner_id" jdbcType="TINYINT" property="bannerId" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="recognition_time" jdbcType="DATE" property="recognitionTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, banner_id, m_id, `status`, recognition_time
  </sql>
  <select id="selectByBannerId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mch_popup_otification
    where m_id = #{mId} and banner_id = #{bannerId} and recognition_time = #{recognitionTime} and status = 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mch_popup_otification
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MchPopupOtification" useGeneratedKeys="true">
    insert into mch_popup_otification (create_time, banner_id, m_id,
                                       `status`, recognition_time)
    values (#{createTime,jdbcType=TIMESTAMP}, #{bannerId,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT},
            #{status,jdbcType=TINYINT}, #{recognitionTime,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MchPopupOtification" useGeneratedKeys="true">
    insert into mch_popup_otification
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="bannerId != null">
        banner_id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="recognitionTime != null">
        recognition_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bannerId != null">
        #{bannerId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="recognitionTime != null">
        #{recognitionTime,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MchPopupOtification">
    update mch_popup_otification
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bannerId != null">
        banner_id = #{bannerId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="recognitionTime != null">
        recognition_time = #{recognitionTime,jdbcType=DATE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.MchPopupOtification">
    update mch_popup_otification
    set create_time = #{createTime,jdbcType=TIMESTAMP},
        banner_id = #{bannerId,jdbcType=BIGINT},
        m_id = #{mId,jdbcType=BIGINT},
        `status` = #{status,jdbcType=TINYINT},
        recognition_time = #{recognitionTime,jdbcType=DATE}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listByDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mch_popup_otification
    where m_id = #{mId} and recognition_time = #{recognitionTime} and status = 1
  </select>
</mapper>