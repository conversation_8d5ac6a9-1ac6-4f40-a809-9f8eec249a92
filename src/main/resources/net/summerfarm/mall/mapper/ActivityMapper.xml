<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.ActivityMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Activity">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="logo" property="logo" jdbcType="VARCHAR"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="start_job" property="startJob"/>
        <result column="end_job" property="endJob"/>
        <result column="priority" property="priority" jdbcType="INTEGER"/>
        <result column="info" property="info" jdbcType="VARCHAR"/>
        <result column="in_logo" property="inLogo" jdbcType="VARCHAR"/>
        <result column="type" property="type" />
        <result column="banner_show" property="bannerShow"/>
        <result column="activity_type" property="activityType" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,`name`,logo,area_no,start_time,end_time,`status`,start_job,end_job,priority,info,in_logo,`type`,advance,banner_show,
        activity_type,create_time,update_time
    </sql>

    <select id="selectOpen"   resultMap="BaseResultMap">
        select
        *
        from activity
        where status=2 and banner_show = true
        AND start_time &lt; now()
         AND   end_time &gt; now()
         AND area_no=#{areaNo}
        <if test="type != null" >
            AND type = #{type}
        </if>
        order by priority desc
    </select>


    <select id="selectById"  parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        *
        from activity
        where status=2
        AND start_time &lt; now()
        AND   end_time &gt; now()
        AND id=#{activityId}
    </select>

    <select id="selectOpenBySku"   resultMap="BaseResultMap">
        select
            a.*
        from activity a
        left join activity_sku ak on a.id=ak.activity_id
        where ak.sku_status=1
          AND a.status=2
          AND a.start_time &lt; now()
          AND a.end_time &gt; now()
          AND a.area_no=#{areaNo} AND ak.sku=#{sku} and a.type=#{type}
    </select>
    <select id="isOnActivity" resultType="boolean">
        select count(1)>0 from activity_sku ak
            left join activity a on ak.activity_id = a.id
            left join area_sku s on ak.sku = s.sku and a.area_no = s.area_no
        where ak.sku_status = 1 and a.status = 2 and a.type = 0
            and a.area_no = #{areaNo} and ak.sku = #{sku} and a.start_time &lt;= #{now} and a.end_time &gt; #{now}
    </select>

    <select id="selectOpenByName" resultMap="BaseResultMap">
         select
        *
        from activity
        where status=2
        AND start_time &lt; now()
        AND   end_time &gt; now()
        AND `name` = #{name}  and area_no =#{areaNo}
    </select>

    <resultMap id="ActivitySkuMap" type="net.summerfarm.mall.model.domain.ActivitySku">
        <id column="id" property="id"/>
        <result column="activity_id" property="activityId"/>
        <result column="sku" property="sku"/>
        <result column="sku_name" property="skuName"/>
        <result column="weight" property="weight"/>
        <result column="unit" property="unit"/>
        <result column="logo" property="logo"/>
        <result column="activity_price" property="activityPrice"/>
        <result column="limited_quantity" property="limitedQuantity"/>
        <result column="activity_stock" property="activityStock"/>
    </resultMap>
    <select id="selectActivitySkuBySkuAndAreaNo" resultMap="ActivitySkuMap">
        select ak.id,activity_id,sku,sku_name,weight,unit,ak.logo,activity_price,ak.limited_quantity,ak.activity_stock
        from activity a
            left join activity_sku `ak` on a.id = `ak`.activity_id
        where a.area_no = #{areaNo}
                and ak.sku = #{sku}
                and ak.sku_status = 1
                and a.status = 2
                and a.type = 0
                and a.start_time &lt;= #{now} and a.end_time &gt; #{now}
        limit 1
    </select>

    <resultMap id="ActivitySkuVOMap" type="net.summerfarm.mall.model.vo.ActivitySkuVO">
        <id column="id" property="id"/>
        <result column="activity_id" property="activityId"/>
        <result column="sku" property="sku"/>
        <result column="sku_name" property="skuName"/>
        <result column="weight" property="weight"/>
        <result column="unit" property="unit"/>
        <result column="logo" property="logo"/>
        <result column="activity_price" property="activityPrice"/>
        <result column="start_time" property="startTime"/>
    </resultMap>
    <select id="selectOpenActivitySkuBySkuAndAreaNo" resultMap="ActivitySkuVOMap">
        select ak.id,activity_id,ak.sku,sku_name,weight,unit,ak.logo,activity_price,a.start_time
        from activity a
            inner join activity_sku `ak` on a.id = `ak`.activity_id
            inner join price_strategy ps on ps.business_id = ak.id
            inner join price_strategy_audit_record psar on ps.id = psar.strategy_id
            inner join price_strategy_audit psa on psa.id = audit_id
        where a.area_no = #{areaNo}
                and ak.sku = #{sku}
                and ak.sku_status = 1
                and a.status = 2
                and a.type = 0
                and a.start_time &lt;= #{now} and a.end_time &gt; #{now}
                and psa.status = 1
                and ps.type=1
        limit 1
    </select>

    <select id="selectOpenActivitySkuBySkuAndAreaNoAndTime" resultMap="ActivitySkuVOMap">
        select ak.id,activity_id,ak.sku,sku_name,weight,unit,ak.logo,activity_price,a.start_time
        from activity a
            inner join activity_sku `ak` on a.id = `ak`.activity_id
            inner join price_strategy ps on ps.business_id = ak.id
            inner join price_strategy_audit_record psar on ps.id = psar.strategy_id
            inner join price_strategy_audit psa on psa.id = audit_id
        where a.area_no = #{areaNo}
                and ak.sku = #{sku}
                and ak.sku_status = 1
                and a.status = 2
                and a.type = 0
                and a.start_time &lt;= #{endTime} and a.start_time &gt; #{starTime}
                and psa.status = 1
                and ps.type=1
        limit 1
    </select>


</mapper>