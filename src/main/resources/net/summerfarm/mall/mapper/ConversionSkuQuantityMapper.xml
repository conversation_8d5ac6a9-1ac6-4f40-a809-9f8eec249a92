<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.ConversionSkuQuantityMapper">

    <select id="selectDetail" parameterType="net.summerfarm.mall.model.domain.ConversionSkuQuantity"
            resultType="net.summerfarm.mall.model.domain.ConversionSkuQuantity">
        select min_sale_cnt minSaleCnt, sale_cnt_fifteen saleCntFifteen, sale_cnt_seven saleCntSeven
         from conversion_sku_quantity
         where sku = #{sku} and warehouse_no = #{warehouseNo} and `date` = #{date}
    </select>

</mapper>