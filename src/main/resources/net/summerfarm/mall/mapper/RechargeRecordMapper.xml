<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.RechargeRecordMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.RechargeRecord">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="recharge_record_no" property="rechargeRecordNo" jdbcType="VARCHAR"/>
        <result column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="account_id" property="accountId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="record_no" property="recordNo" jdbcType="VARCHAR"/>
        <result column="old_amount" property="oldAmount" jdbcType="DECIMAL"/>
        <result column="new_amount" property="newAmount" jdbcType="DECIMAL"/>
        <result column="addtime" property="addtime"/>
    </resultMap>

    <sql id="BaseColumn">
        id,recharge_record_no,m_id,account_id,type,record_no,old_amount,new_amount,addtime
    </sql>

    <insert id="insert" parameterType="net.summerfarm.mall.model.domain.RechargeRecord">
        INSERT INTO
        recharge_record(recharge_record_no,m_id,account_id,type,record_no,old_amount,new_amount,addtime)
        VALUES (#{rechargeRecordNo} ,#{mId}, #{accountId},#{type} ,#{recordNo},#{oldAmount} ,#{newAmount} ,#{addtime} )
    </insert>

    <select id="selectList" parameterType="net.summerfarm.mall.model.vo.RechargeRecordVO" resultType="net.summerfarm.mall.model.vo.RechargeRecordVO">
        SELECT
        id,recharge_record_no rechargeRecordNo,m_id mId,account_id accountId,type,record_no recordNo,old_amount oldAmount,new_amount newAmount,addtime
        FROM recharge_record
        <where>
            <choose>
                <when test="type == null">
                    AND `type` != 3
                </when>
                <when test="type == 4 or type == 2">
                    AND `type` in (2, 4)
                </when>
                <otherwise>
                    AND `type` = #{type}
                </otherwise>
            </choose>
           
            <if test="mId != null">
                AND m_id = #{mId}
            </if>
            <if test="type != null">
                AND type = #{type}
            </if>
            <if test="queryStartTime != null">
                AND addtime >= #{queryStartTime}
            </if>
            <if test="queryEndTime != null">
                AND addtime &lt; #{queryEndTime}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectOne" parameterType="net.summerfarm.mall.model.domain.RechargeRecord" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM recharge_record
        <where>
            <if test="rechargeRecordNo != null">
                AND recharge_record_no = #{rechargeRecordNo}
            </if>
        </where>
    </select>

</mapper>