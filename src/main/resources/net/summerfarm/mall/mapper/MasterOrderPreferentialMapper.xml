<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MasterOrderPreferentialMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MasterOrderPreferential">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="masterOrderNo" column="master_order_no" jdbcType="VARCHAR"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="amount" column="amount" jdbcType="DECIMAL"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="relatedId" column="related_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,master_order_no,order_no,
        amount,type,related_id,
        create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from master_order_preferential
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from master_order_preferential
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MasterOrderPreferential" useGeneratedKeys="true">
        insert into master_order_preferential
        ( id,master_order_no,order_no
        ,amount,type,related_id
        ,create_time,update_time)
        values (#{id,jdbcType=BIGINT},#{masterOrderNo,jdbcType=VARCHAR},#{orderNo,jdbcType=VARCHAR}
        ,#{amount,jdbcType=DECIMAL},#{type,jdbcType=INTEGER},#{relatedId,jdbcType=BIGINT}
        ,#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MasterOrderPreferential" useGeneratedKeys="true">
        insert into master_order_preferential
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="masterOrderNo != null">master_order_no,</if>
                <if test="orderNo != null">order_no,</if>
                <if test="amount != null">amount,</if>
                <if test="type != null">type,</if>
                <if test="relatedId != null">related_id,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="masterOrderNo != null">#{masterOrderNo,jdbcType=VARCHAR},</if>
                <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
                <if test="amount != null">#{amount,jdbcType=DECIMAL},</if>
                <if test="type != null">#{type,jdbcType=INTEGER},</if>
                <if test="relatedId != null">#{relatedId,jdbcType=BIGINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MasterOrderPreferential">
        update master_order_preferential
        <set>
                <if test="masterOrderNo != null">
                    master_order_no = #{masterOrderNo,jdbcType=VARCHAR},
                </if>
                <if test="orderNo != null">
                    order_no = #{orderNo,jdbcType=VARCHAR},
                </if>
                <if test="amount != null">
                    amount = #{amount,jdbcType=DECIMAL},
                </if>
                <if test="type != null">
                    type = #{type,jdbcType=INTEGER},
                </if>
                <if test="relatedId != null">
                    related_id = #{relatedId,jdbcType=BIGINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.MasterOrderPreferential">
        update master_order_preferential
        set 
            master_order_no =  #{masterOrderNo,jdbcType=VARCHAR},
            order_no =  #{orderNo,jdbcType=VARCHAR},
            amount =  #{amount,jdbcType=DECIMAL},
            type =  #{type,jdbcType=INTEGER},
            related_id =  #{relatedId,jdbcType=BIGINT},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
