<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.CirclePeopleRelationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.CirclePeopleRelation">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="BOOLEAN" property="type" />
    <result column="rule_id" jdbcType="INTEGER" property="ruleId" />
    <result column="type_id" jdbcType="INTEGER" property="typeId" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `type`, rule_id, type_id, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from circle_people_relation
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from circle_people_relation
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CirclePeopleRelation" useGeneratedKeys="true">
    insert into circle_people_relation (`type`, rule_id, type_id, 
      creator, create_time)
    values (#{type,jdbcType=BOOLEAN}, #{ruleId,jdbcType=INTEGER}, #{typeId,jdbcType=INTEGER}, 
      #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CirclePeopleRelation" useGeneratedKeys="true">
    insert into circle_people_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        `type`,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=BOOLEAN},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.CirclePeopleRelation">
    update circle_people_relation
    <set>
      <if test="type != null">
        `type` = #{type,jdbcType=BOOLEAN},
      </if>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="typeId != null">
        type_id = #{typeId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.CirclePeopleRelation">
    update circle_people_relation
    set `type` = #{type,jdbcType=BOOLEAN},
      rule_id = #{ruleId,jdbcType=INTEGER},
      type_id = #{typeId,jdbcType=INTEGER},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>