<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.AreaSkuMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.AreaSku" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="area_no" property="areaNo" jdbcType="INTEGER" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="original_price" property="originalPrice" jdbcType="DECIMAL" />
    <result column="price" property="price" jdbcType="DECIMAL" />
    <result column="on_sale" property="onSale" jdbcType="BIT" />
    <result column="share" property="share"/>
    <result column="priority" property="priority" jdbcType="INTEGER" />
    <result column="pd_priority" property="pdPriority" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="corner_status" property="cornerStatus" jdbcType="INTEGER"/>
    <result column="sales_mode" property="salesMode" jdbcType="INTEGER"/>
    <result column="limited_quantity" property="limitedQuantity" jdbcType="INTEGER"/>
    <result column="ladder_price" property="ladderPrice" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, sku, area_no, quantity, original_price, price,ladder_price, on_sale, priority, update_time,
    add_time,corner_status,sales_mode,limited_quantity
  </sql>

  <resultMap id="ResultMapExt" type="net.summerfarm.mall.model.vo.AreaSkuVO" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="area_no" property="areaNo" jdbcType="INTEGER" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="original_price" property="originalPrice" jdbcType="DECIMAL" />
    <result column="price" property="price" jdbcType="DECIMAL" />
    <result column="on_sale" property="onSale" jdbcType="BIT" />
    <result column="share" property="share"/>
    <result column="priority" property="priority" jdbcType="INTEGER" />
    <result column="pd_priority" property="pdPriority" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="corner_status" property="cornerStatus" jdbcType="INTEGER"/>
    <result column="sales_mode" property="salesMode" jdbcType="INTEGER"/>
    <result column="limited_quantity" property="limitedQuantity" jdbcType="INTEGER"/>
    <result column="ladder_price" property="ladderPrice" jdbcType="VARCHAR"/>
    <result column="inv_id" property="skuId" jdbcType="BIGINT" />
  </resultMap>

  <select id="selectByStoreAndSku" resultMap="BaseResultMap">
    select
    t.sku, t.area_no, t.share, t.pd_priority
    from area_sku t
    LEFT JOIN area a on t.area_no = a.area_no
    LEFT join (
         select area_no,store_no from fence where   status = 0
         group by area_no
        ) f on f.area_no = area.area_no
    where f.store_no = #{storeNo}
    AND t.sku = #{sku}
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from area_sku
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from area_sku
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.AreaSku" >
    insert into area_sku (id, sku, area_no, 
      quantity, original_price, price, 
      on_sale, priority, update_time, 
      add_time)
    values (#{id,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{areaNo,jdbcType=INTEGER}, 
      #{quantity,jdbcType=INTEGER}, #{originalPrice,jdbcType=DECIMAL}, #{price,jdbcType=DECIMAL}, 
      #{onSale,jdbcType=BIT}, #{priority,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{addTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.AreaSku" >
    insert into area_sku
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="sku != null" >
        sku,
      </if>
      <if test="areaNo != null" >
        area_no,
      </if>
      <if test="quantity != null" >
        quantity,
      </if>
      <if test="originalPrice != null" >
        original_price,
      </if>
      <if test="price != null" >
        price,
      </if>
      <if test="onSale != null" >
        on_sale,
      </if>
      <if test="priority != null" >
        priority,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="addTime != null" >
        add_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null" >
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="quantity != null" >
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="originalPrice != null" >
        #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="onSale != null" >
        #{onSale,jdbcType=BIT},
      </if>
      <if test="priority != null" >
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.AreaSku" >
    update area_sku
    <set >
      <if test="sku != null" >
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null" >
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="quantity != null" >
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="originalPrice != null" >
        original_price = #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="onSale != null" >
        on_sale = #{onSale,jdbcType=BIT},
      </if>
      <if test="priority != null" >
        priority = #{priority,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null" >
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.AreaSku" >
    update area_sku
    set sku = #{sku,jdbcType=VARCHAR},
      area_no = #{areaNo,jdbcType=INTEGER},
      quantity = #{quantity,jdbcType=INTEGER},
      original_price = #{originalPrice,jdbcType=DECIMAL},
      price = #{price,jdbcType=DECIMAL},
      on_sale = #{onSale,jdbcType=BIT},
      priority = #{priority,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      open_sale = #{openSale,jdbcType=INTEGER},
      close_sale = #{closeSale,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updatePdPriority">
    update area_sku set pd_priority = #{pdPriority} where sku = #{sku} and area_no = #{areaNo}
  </update>
  <select id="selectByAreaNoAndSku" resultMap="BaseResultMap">
     select
     <include refid="Base_Column_List"/>
     from area_sku where area_no = #{areaNo} and sku = #{sku}
  </select>

  <update id="updateOnSale">
    update area_sku
    <set>
      <if test="onSale != null">
        on_sale = #{onSale,jdbcType=BIT},
      </if>
    </set>
    where
    sku = #{sku}
    AND area_no = #{areaNo,jdbcType=INTEGER}
  </update>

  <update id="clearOpenSale">
    update area_sku
    <set>
      open_sale = null ,
      open_sale_time = null ,
    </set>
    where
    sku = #{sku}
    <if test="areaNo != null">
      AND area_no = #{areaNo,jdbcType=INTEGER}
    </if>
  </update>

  <update id="clearCloseSale">
    update area_sku
    <set>
      close_sale = null ,
      close_sale_time = null ,
    </set>
    where
    sku = #{sku}
    <if test="areaNo != null">
      AND area_no = #{areaNo,jdbcType=INTEGER}
    </if>
  </update>

  <select id="selectSelfStoreAutoSale" resultType="net.summerfarm.mall.model.domain.AreaSku">
    SELECT ak.id, ak.on_sale onSale, ak.sku, ak.area_no areaNo
    FROM area a
    Inner join (
        select area_no,store_no from fence where status = 0 and store_no = #{storeNo}
        group by area_no
    ) f on a.area_no = f.area_no
    INNER JOIN area_sku ak ON a.area_no = ak.area_no
    WHERE ak.sku = #{sku}
    AND ak.on_sale = #{onSale}
    <if test="openSale != null">
      AND ak.open_sale = #{openSale}
    </if>
    <if test="closeSale != null">
      AND ak.close_sale = #{closeSale}
    </if>
  </select>


  <update id="updateQualityDate">
    update area_sku ak
    left join area a on ak.area_no = a.area_no
    set ak.update_time = now()
    <choose>
      <when test="qualityDate != null">
        ,ak.info = #{qualityDate}
      </when>
      <otherwise>
        ,ak.info = null
      </otherwise>
    </choose>
    where sku = #{sku}
    <if test="areaNo != null">
      and ak.area_no = #{areaNo}
    </if>
  </update>

  <select id="selectAutoSaleNew" resultType="net.summerfarm.mall.model.domain.AreaSku">

    SELECT ak.id, ak.on_sale onSale, ak.sku, ak.area_no areaNo
    FROM area_store ar
    INNER join warehouse_inventory_mapping wim on ar.area_no = wim.warehouse_no and wim.sku = ar.sku
    inner join (
      select area_no,store_no from fence where status = 0
      group by area_no,store_no
    ) f on f.store_no = wim.store_no
    INNER JOIN area_sku ak ON f.area_no = ak.area_no AND ak.sku = ar.sku
    <where>
      ar.area_no = #{storeNo} and  ar.sku= #{sku}  AND ak.on_sale = #{onSale}
      <if test="openSale != null">
        AND ak.open_sale = #{openSale}
      </if>
      <if test="closeSale != null">
        AND ak.close_sale = #{closeSale}
      </if>
    </where>
    group by ak.area_no

  </select>

  <select id="selectVONew" resultType="net.summerfarm.mall.model.vo.AreaSkuVO">
        SELECT  ar.online_quantity  onlineQuantity,IFNULL(t.quantity,0) trolleyQuantity
        FROM inventory i
        INNER JOIN area_sku ak ON i.sku=ak.sku
        AND ak.sku = #{sku}
        AND ak.area_no = #{areaNo}
        AND ak.on_sale = 1
        AND i.outdated = 0
        INNER JOIN (
            select area_no,store_no from fence where status = 0 and area_no = #{areaNo}  limit 1
        ) f on f.area_no = ak.area_no
        INNER join  warehouse_inventory_mapping wim  on wim.store_no = f.store_no and wim.sku = #{sku}
        INNER JOIN area_store ar ON ak.sku=ar.sku AND wim.warehouse_no = ar.area_no
        LEFT JOIN trolley t ON t.sku=ak.sku
        AND t.m_id = #{mId}
        AND t.account_id = #{accountId}
        AND t.suit_id = #{suitId}
        AND t.del_flag = 0
        AND t.parent_sku = '0'
        limit 1
    </select>

  <select id="selectNearExpiredSku" resultType="net.summerfarm.mall.model.dto.product.ExpiredEarlyWarningDTO">
    select i.sku, ifnull(p.warn_time,10) warnTime
    from inventory i
        inner join area_sku ask on i.sku = ask.sku
        left join products p on i.pd_id = p.pd_id
    where i.ext_type = #{extType} and ask.on_sale = 1
          and ask.area_no = #{areaNo}
          and i.sku = #{sku}
  </select>

  <select id="selectByAreaNoAndSkus" resultType="net.summerfarm.mall.model.vo.ProductInfoVO">
    select  a.sku, a.info, a.pd_priority priority, a.ladder_price ladderPrice, a.sales_mode salesMode,
           IFNULL(a.price, i.sale_price) salePrice, a.price originalPrice, a.limited_quantity limitedQuantity,
           a.show_advance showAdvance, a.advance, a.fix_num fixNum,
           i.pd_id pdId, i.weight, i.unit, i.sku, i.base_sale_quantity baseSaleQuantity,i.base_sale_unit baseSaleUnit,
           i.ext_type extType, i.sku_name skuName, i.sku_pic skuPic,i.quote_type quoteType,
           p.pd_name pdName, p.pddetail pddetail, p.picture_path picturePath, p.category_id categoryId,
           p.slogan, p.other_slogan otherSlogan,  p.quality_time qualityTime, p.quality_time_unit qualityTimeUnit,
           c.type as cateType,i.type
           <if test="adminId != null">
            ,mp.price price,mp.direct direct, a.price activityOriginPrice
            ,(case when mp.mall_show = 0 then mp.mall_show
            when mp.mall_show = 1 then mp.mall_show
            else mc.type
            end ) mallShow
           </if>
    from area_sku a left join inventory i on a.sku = i.sku
                  left join products p on i.pd_id = p.pd_id
                  left join category c on p.category_id = c.id
                  <if test="adminId != null">
                    LEFT JOIN major_price mp on mp.sku=a.sku and mp.area_no=a.area_no and mp.admin_id=#{adminId} and mp.direct=#{direct}
                    and mp.valid_time <![CDATA[<=]]> now() and  mp.invalid_time <![CDATA[>]]> now()
                    LEFT JOIN major_category mc on mc.category_id = p.category_id and mc.area_no = a.area_no and mc.admin_id = #{adminId} and mc.direct = #{direct} and mc.status = 1
                  </if>
    where a.area_no = #{areaNo} and a.sku in <foreach collection="skus" item="sku" open="(" close=")" separator=",">
                                            #{sku}
                                         </foreach>
   </select>
  <select id="selectMskuList" resultType="java.lang.String">
    select ak.sku
    from area_sku ak
           left join inventory i on ak.sku = i.sku
    where ak.m_type = 1
      and ak.on_sale = 1
      and ak.`show` = 1
      and ak.area_no = #{areaNo}
      and i.outdated  = 0
      and i.admin_id = #{adminId}
  </select>

  <select id="selectAreaSkuByAreaNoAndSkus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from area_sku
    where area_no = #{areaNo}
      and sku in
    <foreach collection="skus" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectValidAndOnSale" resultMap="ResultMapExt">
    select
    ak.*, i.inv_id
    from area_sku ak
        left join inventory i on ak.sku = i.sku
    where area_no = #{areaNo} and i.sku = #{sku} and ak.on_sale = 1 and i.outdated  = 0
  </select>
  <select id="selectAgentSku" resultType="java.lang.String">
    select i.sku from area_sku ak
      inner JOIN inventory i on i.sku = ak.sku
    where i.type = 1 and ak.area_no = #{areaNo} and i.admin_id = #{adminId}
  </select>

    <select id="getAreaSkuInfoByPdId" resultMap="BaseResultMap">
      SELECT `ak`.`sku`,  ak.`area_no`
      FROM area_sku ak left join inventory i on ak.sku = i.sku
      WHERE i.`pd_id` = #{pdId} and ak.on_sale = 1 and ak.m_type = 0
    </select>
</mapper>