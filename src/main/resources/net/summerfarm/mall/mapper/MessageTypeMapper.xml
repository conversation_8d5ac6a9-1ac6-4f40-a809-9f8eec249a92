<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MessageTypeMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MessageType" >
    <id column="message_type_id" property="messageTypeId" jdbcType="INTEGER" />
    <result column="message_type" property="messageType" jdbcType="VARCHAR" />
    <result column="message_title" property="messageTitle" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    message_type_id, message_type, message_title
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from message_type
    where message_type_id = #{messageTypeId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from message_type
    where message_type_id = #{messageTypeId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.MessageType" >
    insert into message_type (message_type_id, message_type, message_title
      )
    values (#{messageTypeId,jdbcType=INTEGER}, #{messageType,jdbcType=VARCHAR}, #{messageTitle,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.MessageType" >
    insert into message_type
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="messageTypeId != null" >
        message_type_id,
      </if>
      <if test="messageType != null" >
        message_type,
      </if>
      <if test="messageTitle != null" >
        message_title,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="messageTypeId != null" >
        #{messageTypeId,jdbcType=INTEGER},
      </if>
      <if test="messageType != null" >
        #{messageType,jdbcType=VARCHAR},
      </if>
      <if test="messageTitle != null" >
        #{messageTitle,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MessageType" >
    update message_type
    <set >
      <if test="messageType != null" >
        message_type = #{messageType,jdbcType=VARCHAR},
      </if>
      <if test="messageTitle != null" >
        message_title = #{messageTitle,jdbcType=VARCHAR},
      </if>
    </set>
    where message_type_id = #{messageTypeId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.MessageType" >
    update message_type
    set message_type = #{messageType,jdbcType=VARCHAR},
      message_title = #{messageTitle,jdbcType=VARCHAR}
    where message_type_id = #{messageTypeId,jdbcType=INTEGER}
  </update>
</mapper>