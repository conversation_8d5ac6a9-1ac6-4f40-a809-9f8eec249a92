<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.ContactMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Contact" >
    <id column="contact_id" property="contactId" jdbcType="BIGINT" />
    <result column="m_id" property="mId" jdbcType="BIGINT" />
    <result column="contact" property="contact" jdbcType="VARCHAR" />
    <result column="position" property="position" jdbcType="VARCHAR" />
    <result column="gender" property="gender" jdbcType="BIT" />
    <result column="phone" property="phone" jdbcType="VARCHAR" />
    <result column="email" property="email" jdbcType="VARCHAR" />
    <result column="weixincode" property="weixincode" jdbcType="VARCHAR" />
    <result column="province" property="province" jdbcType="VARCHAR"/>
    <result column="city" property="city" jdbcType="VARCHAR"/>
    <result column="area" property="area" jdbcType="VARCHAR"/>
    <result column="address" property="address" jdbcType="VARCHAR"/>
    <result column="delivery_car" property="deliveryCar" jdbcType="VARCHAR"/>
    <result column="status" property="status" jdbcType="INTEGER"/>
    <result column="remark" property="remark" jdbcType="VARCHAR"/>
    <result column="is_default" property="isDefault" jdbcType="INTEGER"/>
    <result column="poi_note" property="poiNote" jdbcType="VARCHAR" />
    <result column="distance" property="distance" jdbcType="DECIMAL" />
    <result column="path" property="path" jdbcType="VARCHAR"/>
    <result column="house_number" property="houseNumber" />
    <result column="store_no" property="storeNo"/>
    <result column="delivery_frequent" property="deliveryFrequent"/>
    <result column="delivery_rule" property="deliveryRule" jdbcType="VARCHAR"/>
    <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL"/>
    <result column="address_remark" property="addressRemark" jdbcType="VARCHAR"/>
    <result column="address_completion_flag" property="addressCompletionFlag" jdbcType="INTEGER"/>
  </resultMap>

  <sql id="Base_Column_List">
    contact_id, m_id, contact, position, gender, phone,
     email, weixincode,province,city,area,address,status,remark,is_default,delivery_car,poi_note,distance,path,house_number,store_no,
    delivery_frequent, delivery_rule, delivery_fee,address_remark,address_completion_flag
  </sql>

  <select id="selectByMid"  resultMap="BaseResultMap" >
    SELECT <include refid="Base_Column_List" />
    FROM contact
    WHERE m_id = #{mId,jdbcType=BIGINT}
    <if test="status !=null">
      and status=#{status}
    </if>
    order by  is_default desc
  </select>
  <select id="isRepeat" parameterType="net.summerfarm.mall.model.domain.Contact" resultType="boolean">
    select count(1) > 0 from contact where m_id = #{mId} and status in (1,3) and  province = #{province} and city = #{city} and area = #{area} and address = #{address} and contact = #{contact} and phone = #{phone}
  </select>


  <update id="updateByMid">
    update contact c
    set
    c.is_default=0
    where c.m_id = #{mId,jdbcType=BIGINT}
    and c.status=1
  </update>

  <!--auto code-->
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from contact
    where contact_id = #{contactId,jdbcType=BIGINT}
  </select>


  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.Contact" keyColumn="contact_id" keyProperty="contactId" useGeneratedKeys="true">
    insert into contact
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="mId != null" >
        m_id,
      </if>
      <if test="contact != null" >
        contact,
      </if>
      <if test="position != null" >
        position,
      </if>
      <if test="gender != null" >
        gender,
      </if>
      <if test="phone != null" >
        phone,
      </if>
      <if test="email != null" >
        email,
      </if>
      <if test="weixincode != null" >
        weixincode,
      </if>
      <if test="province != null" >
        province,
      </if>
      <if test="city != null" >
        city,
      </if>
      <if test="area != null" >
        area,
      </if>
      <if test="address != null" >
        address,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="isDefault != null" >
        is_default,
      </if>
      <if test="poiNote != null">
        poi_note,
      </if>
      <if test="distance != null">
        distance,
      </if>
      <if test="path != null">
        path,
      </if>
      <if test="houseNumber != null">
        house_number,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="deliveryFrequent != null">
        delivery_frequent,
      </if>
      <if test="deliveryRule != null">
        delivery_rule,
      </if>
      <if test="deliveryFee != null">
        delivery_fee,
      </if>
      <if test="addressRemark != null">
        address_remark,
      </if>
      <if test="addressCompletionFlag != null">
        address_completion_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="mId != null" >
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="contact != null" >
        #{contact,jdbcType=VARCHAR},
      </if>
      <if test="position != null" >
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="gender != null" >
        #{gender,jdbcType=BIT},
      </if>
      <if test="phone != null" >
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null" >
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="weixincode != null" >
        #{weixincode,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        #{province},
      </if>
      <if test="city != null" >
        #{city},
      </if>
      <if test="area != null" >
        #{area},
      </if>
      <if test="address != null" >
        #{address},
      </if>
      <if test="status != null" >
        #{status},
      </if>
      <if test="remark != null" >
        #{remark},
      </if>
      <if test="isDefault != null" >
        #{isDefault},
      </if>
      <if test="poiNote != null">
        #{poiNote},
      </if>
      <if test="distance != null">
        #{distance},
      </if>
      <if test="path != null">
        #{path},
      </if>
      <if test="houseNumber != null">
        #{houseNumber},
      </if>
      <if test="storeNo != null">
        #{storeNo},
      </if>
      <if test="deliveryFrequent != null">
        #{deliveryFrequent},
      </if>
      <if test="deliveryRule != null">
        #{deliveryRule},
      </if>
      <if test="deliveryFee != null">
        #{deliveryFee},
      </if>
      <if test="addressRemark != null">
        #{addressRemark},
      </if>
      <if test="addressCompletionFlag != null">
        #{addressCompletionFlag},
      </if>
    </trim>
  </insert>


  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.Contact" >
    update contact
    <set >
      <if test="mId != null" >
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="contact != null" >
        contact = #{contact,jdbcType=VARCHAR},
      </if>
      <if test="position != null" >
        position = #{position,jdbcType=VARCHAR},
      </if>
      <if test="gender != null" >
        gender = #{gender,jdbcType=BIT},
      </if>
      <if test="phone != null" >
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null" >
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="weixincode != null" >
        weixincode = #{weixincode,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        province=#{province},
      </if>
      <if test="city != null" >
        city=#{city},
      </if>
      <if test="area != null" >
        area=#{area},
      </if>
      <if test="poiNote != null">
        poi_note=#{poiNote},
      </if>
      <if test="address != null" >
        address=#{address},
      </if>
      <if test="status != null" >
        status=#{status},
      </if>
      <if test="remark != null" >
        remark=#{remark},
      </if>
      <if test="isDefault != null" >
        is_default=#{isDefault},
      </if>
      <if test="houseNumber != null">
        house_number =#{houseNumber},
      </if>
      <if test="deliveryFrequent != null">
        delivery_frequent = #{deliveryFrequent},
      </if>
      <if test="deliveryRule != null">
        delivery_rule = #{deliveryRule},
      </if>
      <if test="deliveryFee != null">
        delivery_fee = #{deliveryFee},
      </if>
      <if test="addressRemark != null">
        address_remark = #{addressRemark},
      </if>
      <if test="addressCompletionFlag != null">
        address_completion_flag = #{addressCompletionFlag},
      </if>
    </set>
    where contact_id = #{contactId,jdbcType=BIGINT}
  </update>
  <delete id="deleteByMId">
    update contact set status = 2 where m_id = #{mId}
  </delete>

  <select id="selectIsDefault" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select <include refid="Base_Column_List" />
    from contact where status = 1 and is_default= 1 and contact_id = #{contactId,jdbcType=BIGINT} limit 1
  </select>

  <select id="selectByMidOrderByDefault"  resultType="net.summerfarm.mall.model.domain.Contact" >
    SELECT c.contact_id contactId, c.contact, c.phone,c.province,c.city,c.area,c.address,c.status,
    c.is_default isDefault,c.remark,c.house_number houseNumber,poi_note poiNote,store_no storeNo
    FROM contact c
    WHERE c.m_id = #{mId,jdbcType=BIGINT}
    <if test="status !=null">
      and c.status=#{status}
    </if>
    order by is_default desc
  </select>
  <select id="selectIsDefaultByMid" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from contact where status = 1  and m_id = #{mId} order by  is_default desc  limit 1
  </select>

  <select id="selectByContactIdIn" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from contact
    where contact_id in
    <foreach item="item" index="index" collection="contactIds"
             open="(" separator="," close=")">
        #{item,jdbcType=BIGINT}
    </foreach>
    </select>

  <select id="selectByEnterpriseInformation" parameterType="long" resultType="net.summerfarm.mall.model.vo.ContactVO">
    SELECT c.contact_id contactId, c.contact, c.phone,c.province,c.city,c.area,c.address,c.status,
    c.is_default isDefault,c.remark,c.house_number houseNumber,c.poi_note poiNote,c.store_no storeNo,m.enterprise_information_id enterpriseInformationId
    FROM contact c
    left join mch_enterprise_address_relation m on c.contact_id = m.contact_id and m.valid_status = 0
    WHERE c.m_id = #{mId,jdbcType=BIGINT}  and ((c.status <![CDATA[<>]]> 2 and m.id is not null) or (c.status = 1))
    order by c.contact_id desc
  </select>
</mapper>
