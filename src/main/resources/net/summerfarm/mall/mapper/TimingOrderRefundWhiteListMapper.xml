<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.TimingOrderRefundWhiteListMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.TimingOrderRefundWhiteList">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_time" jdbcType="TIMESTAMP" property="orderTime" />
    <result column="mname" jdbcType="VARCHAR" property="mname" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, order_time, mname, pd_name, sku, `operator`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from timing_order_refund_white_list
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByOrderNo" resultType="java.lang.Boolean">
    select if(count(*) > 0, true, false)
    from timing_order_refund_white_list
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from timing_order_refund_white_list
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.TimingOrderRefundWhiteList" useGeneratedKeys="true">
    insert into timing_order_refund_white_list (order_no, order_time, mname, 
      pd_name, sku, `operator`, 
      create_time, update_time)
    values (#{orderNo,jdbcType=VARCHAR}, #{orderTime,jdbcType=TIMESTAMP}, #{mname,jdbcType=VARCHAR}, 
      #{pdName,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.TimingOrderRefundWhiteList" useGeneratedKeys="true">
    insert into timing_order_refund_white_list
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderTime != null">
        order_time,
      </if>
      <if test="mname != null">
        mname,
      </if>
      <if test="pdName != null">
        pd_name,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="operator != null">
        `operator`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderTime != null">
        #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mname != null">
        #{mname,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null">
        #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.TimingOrderRefundWhiteList">
    update timing_order_refund_white_list
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderTime != null">
        order_time = #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mname != null">
        mname = #{mname,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null">
        pd_name = #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.TimingOrderRefundWhiteList">
    update timing_order_refund_white_list
    set order_no = #{orderNo,jdbcType=VARCHAR},
      order_time = #{orderTime,jdbcType=TIMESTAMP},
      mname = #{mname,jdbcType=VARCHAR},
      pd_name = #{pdName,jdbcType=VARCHAR},
      sku = #{sku,jdbcType=VARCHAR},
      `operator` = #{operator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>