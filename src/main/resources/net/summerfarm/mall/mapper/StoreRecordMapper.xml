<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.storeRecordMallMapper">
    <select id="selectQuantity" resultType="net.summerfarm.mall.model.domain.StoreRecord">
        select sr.id        id,
           batch        batch,
           sr.sku       sku,
           quantity,
           sr.update_time  updateTime,
           quality_date qualityDate,
           sr.area_no   areaNo,
           a.type       areaType,
           a.area_name  areaName,
           i.weight     weight,
           p.pd_name    pdName
        from store_record sr
                 right join (
            select max(id) id
            from store_record
            where sku = #{sku} and area_no = #{storeNo}
            group by batch, quality_date
        ) t on sr.id = t.id
                 LEFT JOIN inventory i ON sr.sku = i.sku
                 LEFT JOIN products p ON i.pd_id = p.pd_id
                 LEFT JOIN area a on sr.area_no = a.area_no
        where sr.store_quantity > 0
        order by sr.area_no
    </select>
  <select id="selectQuantityBySku" resultType="net.summerfarm.mall.model.domain.StoreRecord">
    select sr.id             id,
       sr.batch          batch,
       sr.sku            sku,
       sr.quantity,
       sr.update_time    updateTime,
       sr.quality_date   qualityDate,
       sr.area_no areaNo
    from store_record sr
             right join (
                 select max(sr.id) id, ao.area_no
                    from area_store ao
                    inner join store_record sr on ao.area_no = sr.area_no and ao.sku = sr.sku
                 where ao.sku = #{sku}
                 group by ao.area_no, batch, quality_date
    ) t on sr.id = t.id
    where sr.store_quantity > 0
    order by sr.area_no
  </select>

  <select id="selectQualityDate" resultType="net.summerfarm.mall.model.domain.StoreRecord">
    select quality_date qualityDate
    from (select store_quantity,quality_date,create_time
          from (select batch,store_quantity,quality_date,create_time
                from store_record
                where sku = #{sku} and area_no = #{warehouseNo}
                order by id desc) a
          group by batch,quality_date) b
    WHERE store_quantity > 0
    and quality_date > date(now())
    order by quality_date asc
    limit 1
  </select>
</mapper>