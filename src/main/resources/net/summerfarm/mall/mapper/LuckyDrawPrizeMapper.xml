<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.LuckyDrawPrizeMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.LuckyDrawPrize">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="prize_name" jdbcType="VARCHAR" property="prizeName" />
    <result column="source_id" jdbcType="INTEGER" property="sourceId" />
    <result column="prize_type" jdbcType="INTEGER" property="prizeType" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="lucky_draw_id" jdbcType="INTEGER" property="luckyDrawId" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="upperlimit" jdbcType="INTEGER" property="upperlimit" />
    <result column="repeat_get" jdbcType="BIT" property="repeatGet" />
    <result column="addtime" property="addtime" />
    <result column="updatetime" property="updatetime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, prize_name, source_id, prize_type, level, lucky_draw_id, score, upperlimit, repeat_get, addtime, updatetime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lucky_draw_prize
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectByLuckyDrawId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lucky_draw_prize
    where lucky_draw_id = #{luckyDrawId}
  </select>


  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.LuckyDrawPrize">
    update lucky_draw_prize
    <set>
      <if test="prizeName != null">
        prize_name = #{prizeName,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="prizeType != null">
        prize_type = #{prizeType,jdbcType=INTEGER},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=INTEGER},
      </if>
      <if test="luckyDrawId != null">
        lucky_draw_id = #{luckyDrawId,jdbcType=INTEGER},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=INTEGER},
      </if>
      <if test="upperlimit != null">
        upperlimit = #{upperlimit,jdbcType=INTEGER},
      </if>
      <if test="repeatGet != null">
        repeat_get = #{repeatGet,jdbcType=BIT},
      </if>
      <if test="addtime != null">
        addtime = #{addtime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatetime != null">
        updatetime = #{updatetime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.LuckyDrawPrize">
    update lucky_draw_prize
    set prize_name = #{prizeName,jdbcType=VARCHAR},
      source_id = #{sourceId,jdbcType=INTEGER},
      prize_type = #{prizeType,jdbcType=INTEGER},
      level = #{level,jdbcType=INTEGER},
      lucky_draw_id = #{luckyDrawId,jdbcType=INTEGER},
      score = #{score,jdbcType=INTEGER},
      upperlimit = #{upperlimit,jdbcType=INTEGER},
        repeat_get = #{repeatGet,jdbcType=BIT},
      addtime = #{addtime,jdbcType=TIMESTAMP},
      updatetime = #{updatetime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>