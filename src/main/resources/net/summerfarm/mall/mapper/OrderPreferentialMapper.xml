<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.OrderPreferentialMapper">

    <insert id="insertBatch">
        INSERT INTO order_preferential (order_no,amount,type,activity_name,related_id)
        values
        <foreach collection="list"  item="item" separator=",">
            (#{item.orderNo},#{item.amount},#{item.type},#{item.activityName},#{item.relatedId})
        </foreach>
    </insert>

    <select id="selectPreferential" resultType="net.summerfarm.mall.model.domain.OrderPreferential">
        select id,type,amount,order_no orderNo,activity_name activityName, related_id relatedId
        from order_preferential where order_no =#{orderNo}
    </select>

    <select id="selectSelective" resultType="net.summerfarm.mall.model.domain.OrderPreferential">
      select id,type,amount,order_no orderNo,activity_name activityName, related_id relatedId
      from order_preferential where order_no = #{orderNo}
      <if test="type != null">
        and type = #{type}
      </if>
    </select>

    <select id="selectPreferentialByOrderList" resultType="net.summerfarm.mall.model.domain.OrderPreferential">
        select id,type,amount,order_no orderNo,activity_name activityName, related_id relatedId
        from order_preferential
        where order_no in
        <foreach collection="orderNos" close=")" open="(" separator="," item="orderNo">
            #{orderNo,jdbcType=VARCHAR}
        </foreach>
    </select>

</mapper>