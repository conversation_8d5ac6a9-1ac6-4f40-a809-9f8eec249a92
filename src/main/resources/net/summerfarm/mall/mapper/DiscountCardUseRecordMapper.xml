<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.DiscountCardUseRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.DiscountCardUseRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="suit_id" jdbcType="INTEGER" property="suitId" />
    <result column="discount_card_merchant_id" jdbcType="INTEGER" property="discountCardMerchantId" />
    <result column="use_times" jdbcType="INTEGER" property="useTimes" />
    <result column="total_discount" jdbcType="DECIMAL" property="totalDiscount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, sku,suit_id, discount_card_merchant_id, use_times, total_discount,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from discount_card_use_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from discount_card_use_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.DiscountCardUseRecord" useGeneratedKeys="true">
    insert into discount_card_use_record (order_no, sku,suit_id, discount_card_merchant_id,
      use_times, total_discount, create_time
      )
    values (#{orderNo,jdbcType=VARCHAR}, #{sku,jdbcType=BIGINT},#{suitId,jdbcType=BIGINT}, #{discountCardMerchantId,jdbcType=INTEGER},
      #{useTimes,jdbcType=INTEGER}, #{totalDiscount,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.DiscountCardUseRecord" useGeneratedKeys="true">
    insert into discount_card_use_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="suitId != null">
        suit_id,
      </if>
      <if test="discountCardMerchantId != null">
        discount_card_merchant_id,
      </if>
      <if test="useTimes != null">
        use_times,
      </if>
      <if test="totalDiscount != null">
        total_discount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=INTEGER},
      </if>
      <if test="suitId != null">
        #{suitId,jdbcType=VARCHAR},
      </if>
      <if test="discountCardMerchantId != null">
        #{discountCardMerchantId,jdbcType=INTEGER},
      </if>
      <if test="useTimes != null">
        #{useTimes,jdbcType=INTEGER},
      </if>
      <if test="totalDiscount != null">
        #{totalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <insert id="insertBatch" parameterType="net.summerfarm.mall.model.domain.DiscountCardUseRecord" keyColumn="id" keyProperty="id">

    insert into discount_card_use_record (order_no, sku,suit_id, discount_card_merchant_id,use_times, total_discount, create_time )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderNo},#{item.sku},#{item.suitId},#{item.discountCardMerchantId},#{item.useTimes},#{item.totalDiscount},#{item.createTime} )
    </foreach>


  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.DiscountCardUseRecord">
    update discount_card_use_record
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=BIGINT},
      </if>
      <if test="suitId != null">
        suit_id = #{suitId,jdbcType=BIGINT},
      </if>
      <if test="discountCardMerchantId != null">
        discount_card_merchant_id = #{discountCardMerchantId,jdbcType=INTEGER},
      </if>
      <if test="useTimes != null">
        use_times = #{useTimes,jdbcType=INTEGER},
      </if>
      <if test="totalDiscount != null">
        total_discount = #{totalDiscount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.DiscountCardUseRecord">
    update discount_card_use_record
    set order_no = #{orderNo,jdbcType=VARCHAR},
      sku = #{sku,jdbcType=BIGINT},
       suit_id = #{suitId,jdbcType=BIGINT},
      discount_card_merchant_id = #{discountCardMerchantId,jdbcType=INTEGER},
      use_times = #{useTimes,jdbcType=INTEGER},
      total_discount = #{totalDiscount,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from discount_card_use_record
    where order_no = #{orderNo,jdbcType=INTEGER}
  </select>
  <delete id="deleteByOrderNo">
    delete from discount_card_use_record
    where order_no = #{orderNo,jdbcType=INTEGER}
  </delete>

  <select id="getValidCountsByMid" resultType="java.lang.Integer">
    SELECT count(*) FROM `discount_card_to_merchant` dcm LEFT JOIN `discount_card` dc on dcm.`discount_card_id` = dc.`id`
    WHERE (dc.`name` = '鲜牛乳黄金卡' or dc.`name` = '奶油黄金卡')
    and dcm.`status` = 1 and dcm.`deadline` >= now() and dcm.`m_id` = #{mId,jdbcType=BIGINT}
    </select>
</mapper>