<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MerchantSkuOrderDataMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantSkuOrderData">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="last_order_time" jdbcType="TIMESTAMP" property="lastOrderTime" />
    <result column="last_order_quantity" jdbcType="INTEGER" property="lastOrderQuantity" />
    <result column="last_thirty_days_order_count" jdbcType="INTEGER" property="lastThirtyDaysOrderCount" />
    <result column="last_sixty_days_order_count" jdbcType="INTEGER" property="lastSixtyDaysOrderCount" />
    <result column="last_two_years_order_count" jdbcType="INTEGER" property="lastTwoYearsOrderCount" />
    <result column="day_tag" jdbcType="VARCHAR" property="dayTag" />
  </resultMap>

  <sql id="Base_Column_List">
    id, create_time, update_time, m_id, sku, last_order_time, last_order_quantity, last_thirty_days_order_count,
    last_sixty_days_order_count, last_two_years_order_count, day_tag
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_sku_order_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_sku_order_data
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantSkuOrderData">
    insert into merchant_sku_order_data (id, create_time, update_time,
      m_id, sku, last_order_time,
      last_order_quantity, last_thirty_days_order_count,
      last_sixty_days_order_count, last_two_years_order_count,
      day_tag)
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{mId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{lastOrderTime,jdbcType=TIMESTAMP},
      #{lastOrderQuantity,jdbcType=INTEGER}, #{lastThirtyDaysOrderCount,jdbcType=INTEGER},
      #{lastSixtyDaysOrderCount,jdbcType=INTEGER}, #{lastTwoYearsOrderCount,jdbcType=INTEGER},
      #{dayTag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantSkuOrderData">
    insert into merchant_sku_order_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="lastOrderTime != null">
        last_order_time,
      </if>
      <if test="lastOrderQuantity != null">
        last_order_quantity,
      </if>
      <if test="lastThirtyDaysOrderCount != null">
        last_thirty_days_order_count,
      </if>
      <if test="lastSixtyDaysOrderCount != null">
        last_sixty_days_order_count,
      </if>
      <if test="lastTwoYearsOrderCount != null">
        last_two_years_order_count,
      </if>
      <if test="dayTag != null">
        day_tag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="lastOrderTime != null">
        #{lastOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastOrderQuantity != null">
        #{lastOrderQuantity,jdbcType=INTEGER},
      </if>
      <if test="lastThirtyDaysOrderCount != null">
        #{lastThirtyDaysOrderCount,jdbcType=INTEGER},
      </if>
      <if test="lastSixtyDaysOrderCount != null">
        #{lastSixtyDaysOrderCount,jdbcType=INTEGER},
      </if>
      <if test="lastTwoYearsOrderCount != null">
        #{lastTwoYearsOrderCount,jdbcType=INTEGER},
      </if>
      <if test="dayTag != null">
        #{dayTag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantSkuOrderData">
    update merchant_sku_order_data
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="lastOrderTime != null">
        last_order_time = #{lastOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastOrderQuantity != null">
        last_order_quantity = #{lastOrderQuantity,jdbcType=INTEGER},
      </if>
      <if test="lastThirtyDaysOrderCount != null">
        last_thirty_days_order_count = #{lastThirtyDaysOrderCount,jdbcType=INTEGER},
      </if>
      <if test="lastSixtyDaysOrderCount != null">
        last_sixty_days_order_count = #{lastSixtyDaysOrderCount,jdbcType=INTEGER},
      </if>
      <if test="lastTwoYearsOrderCount != null">
        last_two_years_order_count = #{lastTwoYearsOrderCount,jdbcType=INTEGER},
      </if>
      <if test="dayTag != null">
        day_tag = #{dayTag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantSkuOrderData">
    update merchant_sku_order_data
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      m_id = #{mId,jdbcType=BIGINT},
      sku = #{sku,jdbcType=VARCHAR},
      last_order_time = #{lastOrderTime,jdbcType=TIMESTAMP},
      last_order_quantity = #{lastOrderQuantity,jdbcType=INTEGER},
      last_thirty_days_order_count = #{lastThirtyDaysOrderCount,jdbcType=INTEGER},
      last_sixty_days_order_count = #{lastSixtyDaysOrderCount,jdbcType=INTEGER},
      last_two_years_order_count = #{lastTwoYearsOrderCount,jdbcType=INTEGER},
      day_tag = #{dayTag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

<select id="selectMaxDayTag" resultType="java.lang.String">
    select max(day_tag) from merchant_sku_order_data
</select>
  <select id="listByMIdAndSixtyDaysData" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_sku_order_data
    where day_tag = #{dayTag,jdbcType=VARCHAR}
    and m_id = #{mId,jdbcType=BIGINT}
    and last_sixty_days_order_count >= 2
  </select>

  <select id="listByMIdAndTwoYearsData" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_sku_order_data
    where day_tag = #{dayTag,jdbcType=VARCHAR}
    and m_id = #{mId,jdbcType=BIGINT}
    and last_two_years_order_count >= 1
  </select>

  <select id="listSixtyDaysRecommendDataByMId" resultType="java.lang.String">
    SELECT md.sku
    FROM merchant_sku_order_data md
    LEFT JOIN merchant_frequently_buying_sku ms
        ON md.m_id = ms.m_id AND md.sku = ms.sku
    WHERE md.day_tag = #{dayTag,jdbcType=VARCHAR}
      AND md.m_id = #{mId,jdbcType=BIGINT}
      AND (
          (ms.id IS NULL AND md.last_sixty_days_order_count >= 2)
          OR
          (ms.status = -1 AND ms.recent_delete_time >= DATE_SUB(NOW(), INTERVAL 60 DAY) AND md.delete_time_order_count >= 2)
          OR
          (ms.status = -1 AND ms.recent_delete_time &lt; DATE_SUB(NOW(), INTERVAL 60 DAY) AND md.last_sixty_days_order_count >= 2)
      )
    </select>

    <select id="listTwoYearsRecommendDataByMId" resultType="java.lang.String">
      SELECT md.sku
      FROM merchant_sku_order_data md
      LEFT JOIN merchant_frequently_buying_sku ms
          ON md.m_id = ms.m_id AND md.sku = ms.sku
      WHERE md.day_tag = #{dayTag,jdbcType=VARCHAR}
        AND md.m_id = #{mId,jdbcType=BIGINT}
        AND (
            (ms.id IS NULL and md.last_two_years_order_count >= 1)
            OR
            (ms.status = -1 AND ms.recent_delete_time > DATE_SUB(NOW(), INTERVAL 730 DAY) AND md.delete_time_order_count >= 1)
        )
    </select>
</mapper>