<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.CompanyAccountMapper">
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultType="net.summerfarm.mall.model.domain.CompanyAccount">
        SELECT id,company_name companyName,wx_account_info wxAccountInfo,admin_id adminId,addtime
        FROM company_account
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByAreaNo" parameterType="java.lang.Integer" resultType="net.summerfarm.mall.model.domain.CompanyAccount">
        SELECT ca.id,ca.company_name companyName,ca.wx_account_info wxAccountInfo,ca.admin_id adminId,ca.addtime
        FROM company_account ca
        INNER JOIN area a ON ca.id = a.company_account_id
        WHERE a.area_no = #{areaNo}
    </select>
    <select id="selectByChannel" parameterType="java.lang.Integer" resultType="net.summerfarm.mall.model.domain.CompanyAccount">
        select
            id,company_name companyName,wx_account_info wxAccountInfo, channel
        from company_account
        where channel = #{channel}
    </select>
    <select id="selectByMId" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.domain.CompanyAccount">
        select id,company_name companyName,wx_account_info wxAccountInfo,admin_id adminId,addtime
        from company_account
        where company_name = #{mId}
    </select>
    <select id="selectByAppId" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.domain.CompanyAccount">
        select id,company_name companyName,wx_account_info wxAccountInfo,admin_id adminId,addtime
        from company_account
        where mch_app_id = #{mchAppId}

    </select>

    <select id="selectByMchxAppId" resultType="net.summerfarm.mall.model.domain.CompanyAccount">
        select id,company_name companyName,wx_account_info wxAccountInfo,admin_id adminId,addtime
        from company_account
        where mchx_app_id = #{mchxAppId}
    </select>

</mapper>