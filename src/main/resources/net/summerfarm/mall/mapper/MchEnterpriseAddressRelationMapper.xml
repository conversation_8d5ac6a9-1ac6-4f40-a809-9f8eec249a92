<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MchEnterpriseAddressRelationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MchEnterpriseAddressRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="enterprise_information_id" jdbcType="BIGINT" property="enterpriseInformationId" />
    <result column="contact_id" jdbcType="BIGINT" property="contactId" />
    <result column="valid_status" jdbcType="INTEGER" property="validStatus" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, enterprise_information_id, contact_id, valid_status, update_time, create_time
  </sql>


  <update id="updateByKey" parameterType="java.lang.Long">
    update mch_enterprise_address_relation
    set  valid_status = 1
    where enterprise_information_id = #{id,jdbcType=BIGINT}  and valid_status = 0
  </update>

  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mch_enterprise_address_relation
    where contact_id = #{contactId} and valid_status = 0
  </select>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MchEnterpriseAddressRelation">
    update mch_enterprise_address_relation
    <set>
      <if test="enterpriseInformationId != null">
        enterprise_information_id = #{enterpriseInformationId,jdbcType=BIGINT},
      </if>
      <if test="contactId != null">
        contact_id = #{contactId,jdbcType=BIGINT},
      </if>
      <if test="validStatus != null">
        valid_status = #{validStatus},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT} and valid_status = 0
  </update>

</mapper>