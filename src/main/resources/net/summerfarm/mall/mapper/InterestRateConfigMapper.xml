<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.InterestRateConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.InterestRateConfig">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="interest_rate" jdbcType="DECIMAL" property="interestRate" />
    <result column="auto_flag" jdbcType="INTEGER" property="autoFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_admin_name" jdbcType="VARCHAR" property="createAdminName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sku, area_no, interest_rate, auto_flag, update_time, create_time, create_admin_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from interest_rate_config
    where id = #{id,jdbcType=INTEGER}
  </select>
    <select id="selectBySkuList" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from interest_rate_config
      where area_no = #{areaNo,jdbcType=INTEGER} and sku in
      <foreach collection="skuList" item="sku" open="(" separator="," close=")">
        #{sku}
      </foreach>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from interest_rate_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.InterestRateConfig" useGeneratedKeys="true">
    insert into interest_rate_config (sku, area_no, interest_rate, 
      auto_flag, update_time, create_time, 
      create_admin_name)
    values (#{sku,jdbcType=VARCHAR}, #{areaNo,jdbcType=INTEGER}, #{interestRate,jdbcType=DECIMAL}, 
      #{autoFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createAdminName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.InterestRateConfig" useGeneratedKeys="true">
    insert into interest_rate_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sku != null">
        sku,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="interestRate != null">
        interest_rate,
      </if>
      <if test="autoFlag != null">
        auto_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createAdminName != null">
        create_admin_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="interestRate != null">
        #{interestRate,jdbcType=DECIMAL},
      </if>
      <if test="autoFlag != null">
        #{autoFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createAdminName != null">
        #{createAdminName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.InterestRateConfig">
    update interest_rate_config
    <set>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="interestRate != null">
        interest_rate = #{interestRate,jdbcType=DECIMAL},
      </if>
      <if test="autoFlag != null">
        auto_flag = #{autoFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createAdminName != null">
        create_admin_name = #{createAdminName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.InterestRateConfig">
    update interest_rate_config
    set sku = #{sku,jdbcType=VARCHAR},
      area_no = #{areaNo,jdbcType=INTEGER},
      interest_rate = #{interestRate,jdbcType=DECIMAL},
      auto_flag = #{autoFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_admin_name = #{createAdminName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>