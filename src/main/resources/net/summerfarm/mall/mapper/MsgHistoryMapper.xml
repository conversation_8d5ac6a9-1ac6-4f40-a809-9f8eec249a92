<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MsgHistoryMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MsgHistory" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="mode" property="mode" jdbcType="VARCHAR" />
    <result column="user_id" property="userId" jdbcType="INTEGER" />
    <result column="operate" property="operate" jdbcType="VARCHAR" />
    <result column="operator" property="operator" jdbcType="VARCHAR" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="net.summerfarm.mall.model.domain.MsgHistory" extends="BaseResultMap" >
    <result column="content" property="content" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, mode, user_id, operate, operator, add_time
  </sql>
  <sql id="Blob_Column_List" >
    content
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from msg_history
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from msg_history
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.MsgHistory" >
    insert into msg_history (id, mode, user_id, 
      operate, operator, add_time, 
      content)
    values (#{id,jdbcType=INTEGER}, #{mode,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER}, 
      #{operate,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{content,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.MsgHistory" >
    insert into msg_history
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="mode != null" >
        mode,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="operate != null" >
        operate,
      </if>
      <if test="operator != null" >
        operator,
      </if>
      <if test="addTime != null" >
        add_time,
      </if>
      <if test="content != null" >
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="mode != null" >
        #{mode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="operate != null" >
        #{operate,jdbcType=VARCHAR},
      </if>
      <if test="operator != null" >
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null" >
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MsgHistory" >
    update msg_history
    <set >
      <if test="mode != null" >
        mode = #{mode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="operate != null" >
        operate = #{operate,jdbcType=VARCHAR},
      </if>
      <if test="operator != null" >
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="net.summerfarm.mall.model.domain.MsgHistory" >
    update msg_history
    set mode = #{mode,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      operate = #{operate,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.MsgHistory" >
    update msg_history
    set mode = #{mode,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      operate = #{operate,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>