<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.InventoryMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Inventory">
        <id column="inv_id" property="invId" jdbcType="BIGINT"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="ait_id" property="aitId" jdbcType="INTEGER"/>
        <result column="pd_id" property="pdId" jdbcType="BIGINT"/>
        <result column="origin" property="origin" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="pack" property="pack" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="production_date" property="productionDate" jdbcType="DATE"/>
        <result column="storage_method" property="storageMethod" jdbcType="VARCHAR"/>
        <result column="slogan" property="slogan" jdbcType="VARCHAR"/>
        <result column="other_slogan" property="otherSlogan" jdbcType="VARCHAR"/>
        <result column="sale_price" property="salePrice" jdbcType="DECIMAL"/>
        <result column="promotion_price" property="promotionPrice" jdbcType="DECIMAL"/>
        <result column="introduction" property="introduction" jdbcType="VARCHAR"/>
        <result column="after_sale_quantity" property="afterSaleQuantity" jdbcType="VARCHAR"/>
        <result column="base_sale_quantity" property="baseSaleQuantity" jdbcType="INTEGER"/>
        <result column="base_sale_unit" property="baseSaleUnit" jdbcType="INTEGER"/>
        <result column="sku_pic" property="skuPic" jdbcType="VARCHAR"/>
        <result column="after_sale_unit" property="afterSaleUnit" jdbcType="VARCHAR"/>
        <result column="weight_num" property="weightNum" jdbcType="VARCHAR"/>
        <result column="volume" property="volume" jdbcType="VARCHAR"/>
        <result column="sub_type" property="subType" jdbcType="INTEGER"/>
        <result column="buyer_id" property="buyerId" jdbcType="NUMERIC"/>
        <result column="quote_type" property="quoteType" jdbcType="INTEGER"/>
        <result column="min_auto_after_sale_threshold" property="minAutoAfterSaleThreshold" jdbcType="INTEGER"/>
        <result column="ext_type" property="extType" jdbcType="INTEGER"/>
    </resultMap>
    <resultMap id="ResultMapWithBLOBs" type="net.summerfarm.mall.model.domain.Inventory" extends="BaseResultMap">
        <result column="introduction" property="introduction" jdbcType="LONGVARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
    inv_id, sku, sku_name, ait_id, pd_id, origin, unit, pack, weight, production_date, ext_type,
    storage_method, slogan, sale_price, promotion_price, introduction, after_sale_quantity,type,base_sale_quantity,
    base_sale_unit,sku_pic, after_sale_unit, weight_num, volume, sub_type, buyer_id, quote_type, min_auto_after_sale_threshold
  </sql>
    <sql id="Blob_Column_List">
    introduction
  </sql>

    <select id="selectBySku" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from inventory
        where sku = #{sku}
    </select>

    <select id="listBySkus" resultMap="BaseResultMap" parameterType="list">
       select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from inventory
        where sku in
        <foreach collection="skuList" open="(" separator="," close=")" item="sku">
          #{sku}
        </foreach>
    </select>


    <select id="selectTimingSkuOne" resultType="net.summerfarm.mall.model.vo.ProductVO">
    SELECT p.pd_name pdName,p.category_id categoryId
        ,ar.online_quantity onlineQuantity, ar.online_quantity quantity,
        ar.reserve_use_quantity reserveUseQuantity, ar.reserve_min_quantity reserveMinQuantity
        , ar.reserve_max_quantity reserveMaxQuantity
      ,a.share, i.sku, a.on_sale, i.maturity, i.weight, p.picture_path, p.storage_location storageLocation,
      i.volume,i.weight_num weightNum
    FROM inventory i
    INNER JOIN products p ON i.pd_id = p.pd_id
    LEFT JOIN area_sku a on i.sku = a.sku
    LEFT JOIN area area on a.area_no = area.area_no
    LEFT join (
    select area_no,store_no from fence where area_no = #{areaNo} and status = 0 limit  1
    ) f on f.area_no = area.area_no
    INNER JOIN warehouse_inventory_mapping wim on wim.store_no = f.store_no and i.sku = wim.sku
    INNER JOIN area_store ar on ar.sku=wim.sku and wim.warehouse_no = ar.area_no
    WHERE i.sku = #{sku} AND a.area_no = #{areaNo}
  </select>

    <select id="count" parameterType="java.lang.String" resultType="java.lang.Integer">
    SELECT count(1) FROM inventory i
    WHERE i.sku = #{sku}
  </select>

    <select id="selectHomeProductVO" resultType="net.summerfarm.mall.model.vo.ProductInfoVO">
      SELECT i.pd_id pdId,p.pd_name pdName, p.pddetail pddetail,a.info,p.picture_path picturePath,
        p.category_id categoryId, a.pd_priority priority,c.type,
        a.ladder_price ladderPrice, a.sales_mode salesMode,
        a.limited_quantity limitedQuantity, i.weight,i.unit,i.sku, a.price
        originalPrice,IFNULL(a.price,i.sale_price)
        salePrice, i.base_sale_quantity baseSaleQuantity,p.slogan,p.other_slogan otherSlogan,
        i.base_sale_unit baseSaleUnit, p.quality_time qualityTime, p.quality_time_unit qualityTimeUnit
      <choose>
        <when test="  adminId != null">
            ,mp.price price,mp.direct direct, a.price activityOriginPrice
            ,(case when mp.mall_show = 0 then mp.mall_show
            when mp.mall_show = 1 then mp.mall_show
            else mc.type
            end ) mallShow
        </when>
        <otherwise>
           ,a.show_advance showAdvance,a.advance
        </otherwise>
      </choose>
        <choose>
            <when test="coreCustomer == true">
                 ,if(a.share = 0,a.quantity, ar.online_quantity) quantity
                ,if(a.share = 0,a.quantity, ar.online_quantity) sortQuantity

            </when>
            <otherwise>
                ,if(ar.reserve_max_quantity > ar.reserve_use_quantity + ar.reserve_min_quantity ,if(ar.online_quantity +
                ar.reserve_use_quantity > ar.reserve_max_quantity,ar.online_quantity - (ar.reserve_max_quantity -
                ar.reserve_use_quantity),0),if(ar.online_quantity > ar.reserve_min_quantity , ar.online_quantity -
                ar.reserve_min_quantity,0)) quantity,
                if(ar.reserve_max_quantity > ar.reserve_use_quantity + ar.reserve_min_quantity ,if(ar.online_quantity +
                ar.reserve_use_quantity > ar.reserve_max_quantity,ar.online_quantity - (ar.reserve_max_quantity -
                ar.reserve_use_quantity),0),if(ar.online_quantity > ar.reserve_min_quantity , ar.online_quantity -
                ar.reserve_min_quantity,0)) sortQuantity

            </otherwise>
        </choose>
      FROM inventory i
      LEFT JOIN products p ON i.pd_id = p.pd_id
      LEFT JOIN category c on c.id=p.category_id
      LEFT JOIN area_sku a ON i.sku = a.sku
      INNER JOIN warehouse_inventory_mapping wim on wim.store_no = #{storeNo} and i.sku = wim.sku
      INNER JOIN area_store ar on ar.sku=wim.sku and wim.warehouse_no = ar.area_no
      left join interest_rate_config irc on a.area_no = irc.area_no and a.sku = irc.sku
      <if test="adminId != null">
        LEFT JOIN major_price mp on mp.sku=a.sku and mp.area_no=a.area_no and mp.admin_id=#{adminId} and mp.direct=#{direct}
          and mp.valid_time <![CDATA[<=]]> now() and  mp.invalid_time <![CDATA[>]]> now()
        LEFT JOIN major_category mc on mc.category_id = p.category_id and mc.area_no = a.area_no and mc.admin_id = #{adminId} and mc.direct = #{direct} and mc.status = 1
      </if>
      WHERE a.on_sale = 1 AND i.outdated = 0
      AND a.area_no = #{areaNo}
      <if test="show !=null">
        AND a.show = #{show}
      </if>
        <if test="type !=null">
            AND i.type = #{type}
        </if>
        <if test="helpOrderFlag != null and helpOrderFlag == 1">
            AND i.sub_type != 1
        </if>
      <if test="skuShow == null">
        AND a.m_type = 0
        <if test="skuList !=null">
          AND i.sku in
          <foreach collection="skuList" open="(" close=")" item="item" separator=",">
            #{item}
          </foreach>
        </if>
      </if>
      <if test="'大客户' == msize">
        <if test="skuShow!=null and skuShow==1">
          <if test="skuList !=null">
            AND i.sku in
            <foreach collection="skuList" open="(" close=")" item="item" separator=",">
              #{item}
            </foreach>
          </if>
        </if>
        <if test="skuShow!=null and skuShow==2">
          and (a.m_type=0
          <if test="skuList !=null">
            OR i.sku in
            <foreach collection="skuList" open="(" close=")" item="item" separator=",">
              #{item}
            </foreach>
          </if>
          )
        </if>
      </if>
      <if test="esVOList != null and esVOList.size() > 0">
        AND p.pd_id in
        <foreach collection="esVOList" open="(" close=")" item="vo" separator=",">
          #{vo.pdId}
        </foreach>
      </if>
      <if test="categoryIds != null and categoryIds.size() != 0">
          AND c.id in
          <foreach collection="categoryIds" open="(" close=")" item="id" separator=",">
              #{id}
          </foreach>
      </if>
      <if test="queryStr != null">
        and (p.pd_name like concat('%',#{queryStr},'%') or i.sku like concat('%',#{queryStr},'%') )
      </if>
      order by if(sortQuantity > 0, 1, 0) desc, a.fix_flag desc, a.fix_num asc, a.pd_priority desc, irc.interest_rate desc
    </select>


    <select id="selectProductInfoByPdId" resultType="net.summerfarm.mall.model.vo.ProductInfoVO">
        SELECT p.pd_name pdName,p.pddetail pddetail,a.info,
        i.unit, i.sku, i.sku_name skuName, a.sales_mode salesMode, a.limited_quantity limitedQuantity,c.type,
        p.picture_path picturePath,i.sku_pic skuPic, i.pack,i.weight, i.introduction,
        p.category_id categoryId, i.maturity, i.production_date productionDate, i.base_sale_quantity baseSaleQuantity,
        i.base_sale_unit baseSaleUnit,b.name,i.origin,p.quality_time qualityTime, p.quality_time_unit qualityTimeUnit,
        a.ladder_price ladderPrice,p.storage_method storageMethod,a.info,i.ext_type extType,
        a.price originalPrice,p.detail_picture detailPicture,IFNULL(a.price,i.sale_price) salePrice,p.slogan,p.other_slogan otherSlogan, i.average_price_flag averagePriceFlag
        <if test="adminId != null">
            ,mp.price price ,mp.direct direct, a.price activityOriginPrice
        </if>
        FROM inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN brand b ON p.brand_id=b.brand_id
        LEFT JOIN category c on c.id=p.category_id
        LEFT JOIN area_sku a ON i.sku = a.sku
        <if test="adminId != null">
            LEFT JOIN major_price mp on mp.sku=a.sku and mp.area_no=a.area_no and mp.admin_id=#{adminId} and mp.direct=#{direct}
            AND mp.valid_time <![CDATA[<=]]> now() AND mp.invalid_time <![CDATA[>]]> now()
        </if>
        WHERE i.pd_id = #{pdId}
        AND a.area_no = #{areaNo}
        AND a.on_sale = 1 AND i.outdated = 0
        <if test="skuShow == null">
            AND a.m_type = 0
            <if test="skuList !=null">
                AND i.sku in
                <foreach collection="skuList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="skuShow!=null and skuShow==1">
            <if test="skuList !=null">
                AND i.sku in
                <foreach collection="skuList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>

        <if test="skuShow!=null and skuShow==2">
          and (a.m_type=0
<!--                     <if test="adminId != null">-->
<!--          or (a.m_type = 1-->
<!--          and i.admin_id = #{adminId})-->
<!--        </if>-->
            <if test="skuList !=null">
                OR i.sku in
                <foreach collection="skuList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            )
        </if>


        order by a.show desc
    </select>

    <select id="selectProductInvInfo" resultType="net.summerfarm.mall.model.dto.product.ProductInventoryInfoDTO" >
        SELECT
        p.pd_name pdName,p.pddetail pddetail,p.detail_picture detailPicture,
        p.slogan,p.other_slogan otherSlogan,p.storage_method storageMethod,p.brand_id brandId,
        p.quality_time qualityTime, p.quality_time_unit qualityTimeUnit,
        a.info,p.pd_id pdId,a.sales_mode salesMode, a.limited_quantity limitedQuantity,
        a.on_sale onSale,a.ladder_price ladderPriceStr,a.price originalPrice,
        i.unit,i.origin,i.sku, i.maturity,i.pack,i.weight,i.introduction,i.base_sale_unit baseSaleUnit,
        i.base_sale_quantity baseSaleQuantity,i.average_price_flag averagePriceFlag,
        ifnull(i.sku_pic, p.picture_path) picturePath,
        IFNULL(a.price,i.sale_price) salePrice
        ,i.net_weight_num netWeightNum,i.weight_num weightNum,i.net_weight_unit netWeightUnit,i.buyer_name buyerName,i.buyer_id buyerId, i.quote_type quoteType,
        i.sub_type subType
        FROM inventory i
        INNER JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN area_sku a  on a.sku = i.sku AND a.area_no = #{areaNo}
        WHERE
         i.outdated = 0
        <if test="skus != null and skus.size > 0">
            and i.sku in
            <foreach collection="skus" open="(" separator="," close=")" item="sku">
                #{sku}
            </foreach>
        </if>
    </select>

    <select id="selectTimingSku" resultType="net.summerfarm.mall.model.vo.TimingProductVO" >
        SELECT p.pd_name pdName,p.pddetail pddetail,p.detail_picture detailPicture,a.info,ar.online_quantity onlineQuantity,p.pd_id pdId,
        ar.online_quantity quantity,ar.reserve_use_quantity reserveUseQuantity, ar.reserve_min_quantity reserveMinQuantity
        , ar.reserve_max_quantity  reserveMaxQuantity,i.unit,i.origin,i.sku, a.sales_mode salesMode, a.limited_quantity limitedQuantity,
        ifnull(i.sku_pic, p.picture_path) picture_path,i.pack,i.weight,a.on_sale, i.introduction,ar.cost_price costPrice,
        tr.id ruleId, tr.`name` ruleName, tr.start_time startTime, tr.end_time endTime, tr.delivery_start deliveryStart,tr.threshold,
        tr.delivery_end deliveryEnd, tr.rule_information ruleInformation, i.maturity,IFNULL(a.price,i.sale_price) salePrice,
        a.ladder_price ladderPriceStr,i.base_sale_unit baseSaleUnit,i.base_sale_quantity baseSaleQuantity,p.slogan,p.other_slogan otherSlogan,tr.delivery_period deliveryPeriod
        FROM inventory i
        INNER JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN timing_rule tr ON i.sku = tr.timing_sku
        LEFT JOIN area_sku a  on a.sku = i.sku AND a.area_no = #{areaNo}
        LEFT JOIN area area on a.area_no=area.area_no
        LEFT join (
            select area_no,store_no from fence where area_no = #{areaNo} and status = 0 limit  1
            ) f on f.area_no = area.area_no
        INNER JOIN warehouse_inventory_mapping wim on wim.store_no = f.store_no and i.sku = wim.sku
        INNER JOIN area_store ar on ar.sku=wim.sku and wim.warehouse_no = ar.area_no
        WHERE
            tr.display = 1 and tr.type=#{type}
           AND tr.area_no = #{areaNo}
          and i.outdated = 0 and a.on_sale = 1
        <if test="pdId != null">
            AND p.pd_id = #{pdId}
        </if>
        <if test="productName != null">
            AND p.pd_name like CONCAT('%',#{productName,jdbcType=VARCHAR},'%')
        </if>
        <if test="categoryIds != null and categoryIds.size > 0">
          and p.category_id in
          <foreach collection="categoryIds" open="(" separator="," close=")" item="categoryId">
            #{categoryId}
          </foreach>
        </if>
       <if test="skus != null and skus.size > 0">
          and tr.timing_sku in
          <foreach collection="skus" open="(" separator="," close=")" item="sku">
            #{sku}
          </foreach>
       </if>
        order by tr.priority DESC
  </select>

    <select id="selectTimingSkuDetail" resultType="net.summerfarm.mall.model.vo.TimingProductVO">
        SELECT p.pd_id pdId,p.pd_name pdName,p.pddetail pddetail,a.info, if(i.sku_pic is null, i.sku_pic, p.picture_path)  picturePath,
        ar.online_quantity onlineQuantity,
            ar.online_quantity quantity,ar.reserve_use_quantity reserveUseQuantity, ar.reserve_min_quantity reserveMinQuantity
            , ar.reserve_max_quantity  reserveMaxQuantity,
        i.unit,i.origin,i.sku, a.sales_mode salesMode, a.limited_quantity limitedQuantity,
        p.detail_picture detailPicture,p.storage_method storageMethod,b.name,
        i.pack,i.weight, i.maturity, a.on_sale, i.introduction,ar.cost_price costPrice,
        tr.id ruleId, tr.`name` ruleName, tr.start_time startTime, tr.end_time endTime, tr.delivery_start deliveryStart,tr.delivery_start_type deliveryStartType,
        tr.delivery_end deliveryEnd, tr.rule_information ruleInformation,tr.delivery_unit deliveryUnit, tr.delivery_upper_limit deliveryUpperLimit,tr.type,IFNULL(a.price,i.sale_price) salePrice,
        a.ladder_price ladderPriceStr,i.base_sale_unit baseSaleUnit,i.base_sale_quantity baseSaleQuantity,p.slogan,p.other_slogan otherSlogan, tr.auto_calculate autoCalculate,tr.delivery_period deliveryPeriod
        ,tr.threshold, tr.plus_day plusDay, p.category_id categoryId
        FROM inventory i
        INNER JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN brand b ON p.brand_id=b.brand_id
        LEFT JOIN timing_rule tr ON i.sku = tr.timing_sku
        LEFT JOIN area_sku a  on a.sku = i.sku  AND a.area_no = #{areaNo}
        LEFT JOIN area area on a.area_no = area.area_no
         LEFT join (
            select area_no,store_no from fence where area_no = #{areaNo} and status = 0 limit  1
            ) f on f.area_no = area.area_no
        INNER JOIN warehouse_inventory_mapping wim on wim.store_no = f.store_no and i.sku = wim.sku
        INNER JOIN area_store ar on ar.sku=wim.sku and wim.warehouse_no = ar.area_no
        WHERE i.sku =#{sku}
        AND tr.id = #{ruleId}
        AND tr.display = 1
  </select>


    <select id="selectCouponProductVO" resultType="net.summerfarm.mall.model.vo.ProductInfoVO">
        SELECT i.pd_id pdId,p.pd_name pdName, p.pddetail pddetail,a.info, p.picture_path picturePath,
        p.category_id categoryId, a.pd_priority priority,c.type,
        a.ladder_price ladderPrice, a.sales_mode salesMode,
        a.limited_quantity limitedQuantity, i.weight,i.unit,i.sku, i.sku_name skuName,a.price originalPrice,IFNULL(a.price,i.sale_price)
        salePrice, i.base_sale_quantity baseSaleQuantity,p.slogan,p.other_slogan otherSlogan,
        i.base_sale_unit baseSaleUnit,
        if(a.pd_priority >= 0, if(a.fix_flag = 1, 0, 1), 1)                                    sort1,
        if(if(a.pd_priority >= 0, if(a.fix_flag = 1, 0, 1), 1) = 0, a.fix_num * -1, a.pd_priority) sort2,
        irc.interest_rate sort3
        <choose>
            <when test="  adminId != null">
                ,mp.price price,mp.direct direct
            </when>
            <otherwise>
                ,a.show_advance showAdvance,a.advance
            </otherwise>
        </choose>
        <choose>
            <when test="coreCustomer == true">
                ,if(a.share = 0,a.quantity, ar.online_quantity) quantity
            </when>
            <otherwise>
                ,if(ar.reserve_max_quantity > ar.reserve_use_quantity + ar.reserve_min_quantity ,if(ar.online_quantity +
                ar.reserve_use_quantity > ar.reserve_max_quantity,ar.online_quantity - (ar.reserve_max_quantity -
                ar.reserve_use_quantity),0),if(ar.online_quantity > ar.reserve_min_quantity , ar.online_quantity -
                ar.reserve_min_quantity,0)) quantity
            </otherwise>
        </choose>

        FROM inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN category c on c.id=p.category_id
        LEFT JOIN area_sku a ON i.sku = a.sku
        LEFT JOIN area area on a.area_no=area.area_no
        LEFT join (
          select area_no,store_no from fence where area_no = #{areaNo} and status = 0 limit  1
        ) f on f.area_no = area.area_no
        INNER JOIN warehouse_inventory_mapping wim on wim.store_no = f.store_no and i.sku = wim.sku
        INNER JOIN area_store ar on ar.sku=wim.sku and wim.warehouse_no = ar.area_no
        left join interest_rate_config irc on a.area_no = irc.area_no and a.sku = irc.sku
        <if test="adminId != null">
            LEFT JOIN major_price mp on mp.sku=a.sku and mp.area_no=a.area_no and mp.admin_id=#{adminId} and mp.direct=#{direct}
        </if>
        WHERE a.on_sale = 1 AND i.outdated = 0
        AND a.area_no = #{areaNo}
        <if test="show !=null">
            AND a.show = #{show}
        </if>
        <if test="adminId != null">
            AND mp.valid_time <![CDATA[<=]]> now()
            AND mp.invalid_time <![CDATA[>]]> now()
        </if>
        <if test="skuShow == null">
            AND a.m_type = 0
            <if test="skuList !=null and skuList.size > 0">
                AND i.sku in
                <foreach collection="skuList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>

        <if test="'大客户' == msize">
            <if test="skuShow!=null and skuShow==1">
                <if test="skuList !=null and skuList.size > 0">
                    AND i.sku in
                    <foreach collection="skuList" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                </if>
            </if>

            <if test="skuShow!=null and skuShow==2">
                and (a.m_type=0
                <if test="skuList !=null and skuList.size > 0">
                    OR i.sku in
                    <foreach collection="skuList" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                </if>
                )
                <if test="queryList !=null and queryList.size > 0">
                    and i.sku in
                    <foreach collection="queryList" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                </if>
            </if>
        </if>

        <if test="pdName != null">
            AND p.pd_name like CONCAT('%',#{pdName},'%')
        </if>
        <if test="blackSkus != null and blackSkus.size > 0">
            AND i.sku not in
            <foreach collection="blackSkus" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="categoryIds !=null and categoryIds.size > 0">
            AND p.category_id in
            <foreach collection="categoryIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        order by sort1, sort2 desc, sort3 desc
    </select>
    <select id="queryBySku" resultType="net.summerfarm.mall.model.vo.ProductVO">
        SELECT i.sku,p.pd_name pdName,i.weight,c.type,p.category_id categoryId,
        c.type categoryType,i.volume,i.weight_num weightNum,ad.name_remakes nameRemakes,i.pd_id pdId
        ,i.origin,storage_location storageLocation,i.type type,ifnull(i.sku_pic, p.picture_path) picture_path
        FROM inventory i
        LEFT JOIN products p ON i.pd_id=p.pd_id
        LEFT JOIN category c ON p.category_id=c.id
        LEFT JOIN admin ad on ad.admin_id = i.admin_id
        WHERE i.sku = #{sku}
    </select>
    <select id="selectRecommendProductVO" resultType="net.summerfarm.mall.model.vo.ProductInfoVO">
      SELECT i.pd_id              pdId,
        p.pd_name            pdName,
        p.pddetail           pddetail,
        a.info,
        p.picture_path       picturePath,
        p.category_id        categoryId,
        a.pd_priority        priority,
        c.type,
        a.ladder_price       ladderPrice,
        a.sales_mode         salesMode,
        a.limited_quantity   limitedQuantity,
        i.weight,
        i.unit,
        i.sku,
        i.sku_pic skuPic,
        i.sku_name skuName,
        a.price     originalPrice,
        IFNULL(a.price, i.sale_price) salePrice,
        i.base_sale_quantity baseSaleQuantity,
        p.slogan,
        p.other_slogan       otherSlogan,
        i.base_sale_unit     baseSaleUnit,
        a.show_advance       showAdvance,
        a.advance,
        a.fix_num fixNum,
        ar.online_quantity onlineQuantity,
        ar.online_quantity quantity,
        ar.reserve_use_quantity reserveUseQuantity,
        ar.reserve_min_quantity reserveMinQuantity,
        ar.reserve_max_quantity reserveMaxQuantity,
        i.ext_type extType,
        p.quality_time qualityTime,
        p.quality_time_unit qualityTimeUnit
        ,i.net_weight_num netWeightNum,i.weight_num weightNum,i.net_weight_unit netWeightUnit,i.buyer_name buyerName,i.buyer_id buyerId
        FROM inventory i
      LEFT JOIN products p ON i.pd_id = p.pd_id
      LEFT JOIN category c on c.id = p.category_id
      LEFT JOIN area_sku a ON i.sku = a.sku
      INNER JOIN warehouse_inventory_mapping wim on wim.store_no = #{storeNo} and i.sku = wim.sku
      INNER JOIN area_store ar on ar.sku = wim.sku and wim.warehouse_no = ar.area_no
      WHERE a.on_sale = 1
      AND i.outdated = 0
      and p.outdated = 0
      and c.outdated = 0
      AND a.m_type = 0
      AND a.show = 1
      AND a.area_no = #{areaNo}
      <if test="skuList != null and skuList.size > 0">
        AND i.sku in
        <foreach collection="skuList" open="(" close=")" item="item" separator=",">
          #{item}
        </foreach>
      </if>
      order by ABS(a.pd_priority) desc
    </select>


    <select id="selectBySpuList" resultType="java.lang.String">
    select distinct i.sku from inventory i
        left join products p on p.pd_id = i.pd_id
        LEFT JOIN area_sku a ON i.sku = a.sku
        left join products_property_value ppv on ppv.sku = i.sku
    where i.ext_type = 0 and i.outdated = 0
        and i.type = 0 AND m_type = 0
        and a.on_sale = 1
        AND a.area_no = #{areaNo}
        <if test="spuList != null and spuList.size > 0">
            AND p.pd_id in
            <foreach collection="spuList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectHomeProductVOByCateGoryId" resultType="net.summerfarm.mall.model.vo.ProductInfoVO">
        SELECT i.pd_id pdId,p.pd_name pdName, p.pddetail pddetail,a.info,p.picture_path picturePath,
        p.category_id categoryId, a.pd_priority priority,c.type,
        a.ladder_price ladderPrice, a.sales_mode salesMode,
        a.limited_quantity limitedQuantity, i.weight,i.unit,i.sku, a.price
        originalPrice,IFNULL(a.price,i.sale_price)
        salePrice, i.base_sale_quantity baseSaleQuantity,p.slogan,p.other_slogan otherSlogan,
        i.base_sale_unit baseSaleUnit,a.show_advance showAdvance,a.advance,
        ar.online_quantity onlineQuantity,
        ar.online_quantity quantity,
        ar.reserve_use_quantity reserveUseQuantity,
        ar.reserve_min_quantity reserveMinQuantity,
        ar.reserve_max_quantity reserveMaxQuantity
        FROM inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN category c on c.id=p.category_id
        LEFT JOIN area_sku a ON i.sku = a.sku
        LEFT JOIN area area on a.area_no = area.area_no
        LEFT join (
          select area_no,store_no from fence where area_no = #{areaNo} and status = 0 limit  1
        ) f on f.area_no = area.area_no
        INNER JOIN warehouse_inventory_mapping wim on wim.store_no = f.store_no and i.sku = wim.sku
        INNER JOIN area_store ar on ar.sku=wim.sku and wim.warehouse_no = ar.area_no
        left join interest_rate_config irc on a.area_no = irc.area_no and a.sku = irc.sku

        WHERE a.on_sale = 1 AND i.outdated = 0 AND a.area_no = #{areaNo} and a.price > 0.01
        AND a.m_type = 0 and i.type = 0
        <if test="invId != null">
            and i.inv_id > #{invId}
        </if>
        <if test="categoryIdList != null and categoryIdList.size > 0">
            AND c.id in
            <foreach collection="categoryIdList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        order by i.inv_id desc
    </select>

    <select id="selectHomeProductVOBySkus" resultType="net.summerfarm.mall.model.vo.ProductInfoVO">
        SELECT i.pd_id pdId,p.pd_name pdName, p.pddetail pddetail,a.info,p.picture_path picturePath,
        p.category_id categoryId, a.pd_priority priority,c.type,
        a.ladder_price ladderPrice, a.sales_mode salesMode,
        a.limited_quantity limitedQuantity, i.weight,i.unit,i.sku, a.price
        originalPrice,IFNULL(a.price,i.sale_price)
        salePrice, i.base_sale_quantity baseSaleQuantity,p.slogan,p.other_slogan otherSlogan,
        i.base_sale_unit baseSaleUnit,a.show_advance showAdvance,a.advance,
        ar.online_quantity onlineQuantity,
        ar.online_quantity quantity,
        ar.reserve_use_quantity reserveUseQuantity,
        ar.reserve_min_quantity reserveMinQuantity,
        ar.reserve_max_quantity reserveMaxQuantity

        FROM inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN category c on c.id=p.category_id
        LEFT JOIN area_sku a ON i.sku = a.sku
        LEFT JOIN area area on a.area_no=area.area_no
        LEFT join (
           select area_no,store_no from fence where area_no = #{areaNo} and status = 0 limit  1
        ) f on f.area_no = area.area_no
        INNER JOIN warehouse_inventory_mapping wim on wim.store_no = f.store_no and i.sku = wim.sku
        INNER JOIN area_store ar on ar.sku=wim.sku and wim.warehouse_no = ar.area_no
        left join interest_rate_config irc on a.area_no = irc.area_no and a.sku = irc.sku

        WHERE a.on_sale = 1 AND i.outdated = 0 AND a.area_no = #{areaNo}
        AND a.m_type = 0 and i.type = 0
        <if test="skuList != null and skuList.size > 0">
            AND i.sku  in
            <foreach collection="skuList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        order by i.inv_id desc
    </select>

    <select id="selectHomeProductVOByBrandNames" resultType="net.summerfarm.mall.model.vo.ProductInfoVO">
        SELECT dsr.brand_name brandName, dsr.rank_id rankId,
        i.pd_id pdId,p.pd_name pdName, p.pddetail pddetail,a.info,p.picture_path picturePath,
        p.category_id categoryId, a.pd_priority priority,c.type,
        a.ladder_price ladderPrice, a.sales_mode salesMode,
        a.limited_quantity limitedQuantity, i.weight,i.unit,i.sku, a.price
        originalPrice,IFNULL(a.price,i.sale_price)
        salePrice, i.base_sale_quantity baseSaleQuantity,p.slogan,p.other_slogan otherSlogan,
        i.base_sale_unit baseSaleUnit,a.show_advance showAdvance,a.advance,
        ar.online_quantity onlineQuantity,
        ar.online_quantity quantity,
        ar.reserve_use_quantity reserveUseQuantity,
        ar.reserve_min_quantity reserveMinQuantity,
        ar.reserve_max_quantity reserveMaxQuantity
        FROM inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN category c on c.id=p.category_id
        LEFT JOIN area_sku a ON i.sku = a.sku
        LEFT JOIN area area on a.area_no=area.area_no
        LEFT join (
            select area_no,store_no from fence where area_no = #{areaNo} and status = 0 limit  1
        ) f on f.area_no = area.area_no
        INNER JOIN warehouse_inventory_mapping wim on wim.store_no = f.store_no and i.sku = wim.sku
        INNER JOIN area_store ar on ar.sku = wim.sku and wim.warehouse_no = ar.area_no
        left join interest_rate_config irc on a.area_no = irc.area_no and a.sku = irc.sku
		inner join day_sale_rank dsr on i.sku = dsr.sku
        WHERE a.on_sale = 1 AND i.outdated = 0 AND a.area_no = #{areaNo}  AND a.m_type = 0  AND a.price > 0.01 AND i.type = 0
              AND dsr.area_no = #{areaNo}
              <if test="brandName != null ">
                AND dsr.brand_name = #{brandName}
              </if>
        </select>
    <select id="selectLandPageFormulaProductVO" resultType="net.summerfarm.mall.model.vo.ProductInfoVO">
        SELECT i.pd_id pdId,p.pd_name pdName, p.pddetail pddetail,a.info,p.picture_path picturePath,
            p.category_id categoryId, a.pd_priority priority,c.type,
            a.ladder_price ladderPrice, a.sales_mode salesMode,
            a.limited_quantity limitedQuantity, i.weight,i.unit,i.sku, i.sku_name skuName,i.sku_pic skuPic, a.price
            originalPrice,IFNULL(a.price,i.sale_price)
            salePrice, i.base_sale_quantity baseSaleQuantity,p.slogan,p.other_slogan otherSlogan,
            i.base_sale_unit baseSaleUnit,a.show_advance showAdvance,a.advance
            <choose>
                <when test="coreCustomer == true">
                    ,if(a.share = 0,a.quantity, ar.online_quantity) quantity,
                    if(a.share = 0,a.quantity, ar.online_quantity) sortQuantity

                </when>
                <otherwise>
                     ,if(ar.reserve_max_quantity  > ar.reserve_use_quantity + ar.reserve_min_quantity,
                    if(ar.online_quantity + ar.reserve_use_quantity > ar.reserve_max_quantity,ar.online_quantity-(ar.reserve_max_quantity - ar.reserve_use_quantity),0),
                    if(ar.online_quantity > ar.reserve_min_quantity,ar.online_quantity - ar.reserve_min_quantity,0)) quantity,
                    if(ar.reserve_max_quantity  > ar.reserve_use_quantity + ar.reserve_min_quantity,
                    if(ar.online_quantity + ar.reserve_use_quantity > ar.reserve_max_quantity,ar.online_quantity-(ar.reserve_max_quantity - ar.reserve_use_quantity),0),
                    if(ar.online_quantity > ar.reserve_min_quantity,ar.online_quantity - ar.reserve_min_quantity,0)) sortQuantity

                </otherwise>
            </choose>
        FROM inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN category c on c.id=p.category_id
        LEFT JOIN area_sku a ON i.sku = a.sku
        LEFT JOIN area area on a.area_no=area.area_no
        LEFT join (
            select area_no,store_no from fence where area_no = #{areaNo} and status = 0 limit  1
        ) f on f.area_no = area.area_no
        INNER join warehouse_inventory_mapping wim on wim.store_no = f.store_no and i.sku = wim.sku
        INNER JOIN area_store ar on ar.sku=wim.sku and wim.warehouse_no = ar.area_no
        left join interest_rate_config irc on a.area_no = irc.area_no and a.sku = irc.sku
		left join land_page_node lpn on lpn.sku = i.sku and lpn.node_type = 0

        WHERE a.on_sale = 1 AND i.outdated = 0 AND a.area_no = #{areaNo} AND i.type = 0
        AND a.m_type = 0 and i.type = 0 AND lpn.outdated = 0 and lpn.item_parent_id = #{formulaItemId}

		order by sortQuantity desc , lpn.sort asc, i.inv_id desc

    </select>
    <select id="selectLandPageSkuProductVO" resultType="net.summerfarm.mall.model.vo.ProductInfoVO">
        SELECT i.pd_id pdId,p.pd_name pdName, p.pddetail pddetail,a.info,p.picture_path picturePath,
            p.category_id categoryId, a.pd_priority priority,c.type,
            a.ladder_price ladderPrice, a.sales_mode salesMode,
            a.limited_quantity limitedQuantity, i.weight,i.unit,i.sku, i.sku_name skuName, i.sku_pic skuPic, a.price
            originalPrice,IFNULL(a.price,i.sale_price)
            salePrice, i.base_sale_quantity baseSaleQuantity,p.slogan,p.other_slogan otherSlogan,
            i.base_sale_unit baseSaleUnit,a.show_advance showAdvance,a.advance,
            ar.online_quantity onlineQuantity,
            ar.online_quantity quantity,
            ar.reserve_use_quantity reserveUseQuantity,
            ar.reserve_min_quantity reserveMinQuantity,
            ar.reserve_max_quantity reserveMaxQuantity
        FROM inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN category c on c.id=p.category_id
        LEFT JOIN area_sku a ON i.sku = a.sku
        LEFT JOIN area area on a.area_no=area.area_no
        LEFT join (
          select area_no,store_no from fence where area_no = #{areaNo} and status = 0 limit  1
        ) f on f.area_no = area.area_no
        INNER join warehouse_inventory_mapping wim on wim.store_no = f.store_no and i.sku = wim.sku
        INNER JOIN area_store ar on ar.sku=wim.sku and wim.warehouse_no = ar.area_no
        left join interest_rate_config irc on a.area_no = irc.area_no and a.sku = irc.sku
		left join land_page_node lpn on lpn.sku = i.sku and lpn.node_type = 0
        WHERE a.on_sale = 1 AND i.outdated = 0 AND a.area_no = #{areaNo} AND i.type = 0
        AND a.m_type = 0 and i.type = 0 AND lpn.outdated = 0 and lpn.item_parent_id = #{productId}
        order by lpn.sort desc, i.inv_id desc
    </select>
    <select id="selectLandPageSkuAutoSortProductVO" resultType="net.summerfarm.mall.model.vo.ProductInfoVO">
        SELECT IFNULL(dsr.sales, -1) sales, lpn.sort, i.pd_id pdId,p.pd_name pdName, p.pddetail pddetail,a.info,p.picture_path picturePath,
        p.category_id categoryId, a.pd_priority priority,c.type,
        a.ladder_price ladderPrice, a.sales_mode salesMode,
        a.limited_quantity limitedQuantity, i.weight,i.unit,i.sku, i.sku_name skuName, i.sku_pic skuPic, a.price
        originalPrice,IFNULL(a.price,i.sale_price)
        salePrice, i.base_sale_quantity baseSaleQuantity,p.slogan,p.other_slogan otherSlogan,
        i.base_sale_unit baseSaleUnit,a.show_advance showAdvance,a.advance
        <choose>
            <when test="coreCustomer == true">
                ,if(a.share = 0,a.quantity, ar.online_quantity) quantity,
                if(a.share = 0,a.quantity, ar.online_quantity) sortQuantity

            </when>
            <otherwise>
                ,if(ar.reserve_max_quantity  > ar.reserve_use_quantity + ar.reserve_min_quantity,
                if(ar.online_quantity + ar.reserve_use_quantity > ar.reserve_max_quantity,ar.online_quantity-(ar.reserve_max_quantity - ar.reserve_use_quantity),0),
                if(ar.online_quantity > ar.reserve_min_quantity,ar.online_quantity - ar.reserve_min_quantity,0)) quantity,
                if(ar.reserve_max_quantity  > ar.reserve_use_quantity + ar.reserve_min_quantity,
                if(ar.online_quantity + ar.reserve_use_quantity > ar.reserve_max_quantity,ar.online_quantity-(ar.reserve_max_quantity - ar.reserve_use_quantity),0),
                if(ar.online_quantity > ar.reserve_min_quantity,ar.online_quantity - ar.reserve_min_quantity,0)) sortQuantity

            </otherwise>
        </choose>

        FROM inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN category c on c.id=p.category_id
        LEFT JOIN area_sku a ON i.sku = a.sku
        LEFT JOIN area area on a.area_no=area.area_no
        LEFT join (
          select area_no,store_no from fence where area_no = #{areaNo} and status = 0 limit  1
        ) f on f.area_no = area.area_no
        INNER join warehouse_inventory_mapping wim on wim.store_no = f.store_no and i.sku = wim.sku
        INNER JOIN area_store ar on ar.sku=wim.sku and wim.warehouse_no = ar.area_no
        left join interest_rate_config irc on a.area_no = irc.area_no and a.sku = irc.sku
		inner join land_page_node lpn on lpn.sku = i.sku and lpn.node_type = 0
		left  join  day_sale_rank dsr on dsr.sku = lpn.sku and dsr.area_no = #{areaNo}

        WHERE a.on_sale = 1 AND i.outdated = 0 AND a.area_no = #{areaNo}
        AND a.m_type = 0 and i.type = 0 AND lpn.outdated = 0 and lpn.item_parent_id = #{productId}

		order by sortQuantity desc , dsr.sales desc, lpn.sort desc, i.inv_id desc
    </select>

    <select id="checkIsFruit" resultType="java.lang.Boolean">
        select count(1) from inventory i left join products p on i.pd_id=p.pd_id left join category c on p.category_id = c.id where c.type=4 and sku=#{sku}
    </select>
    <select id="selectExpiredSkuAutoSortProductVO" resultType="net.summerfarm.mall.model.vo.ProductInfoVO">
        SELECT IFNULL(dsr.sales, -1)                                                            sales,
               i.pd_id                                                                          pdId,
               p.pd_name                                                                        pdName,
               p.pddetail                                                                       pddetail,
               a.info,
               p.picture_path                                                                   picturePath,
               p.category_id                                                                    categoryId,
               a.pd_priority                                                                    priority,
               c.type,
               a.ladder_price                                                                   ladderPrice,
               a.sales_mode                                                                     salesMode,
               a.limited_quantity                                                               limitedQuantity,
               i.weight,
               i.unit,
               i.sku,
               i.sku_pic skuPic,
               i.sku_name skuName,
               a.price                                                                          originalPrice,
               IFNULL(a.price, i.sale_price)                                                    salePrice,
               i.base_sale_quantity                                                             baseSaleQuantity,
               p.slogan,
               p.other_slogan                                                                   otherSlogan,
               i.base_sale_unit                                                                 baseSaleUnit,
               a.show_advance                                                                   showAdvance,
               a.advance
                <choose>
                    <when test="coreCustomer == true">
                        ,if(a.share = 0,a.quantity, ar.online_quantity) quantity,
                        if(a.share = 0,a.quantity, ar.online_quantity) sortQuantity

                    </when>
                    <otherwise>
                        ,a.show_advance showAdvance,a.advance,
                        if(ar.reserve_max_quantity  > ar.reserve_use_quantity + ar.reserve_min_quantity,
                        if(ar.online_quantity + ar.reserve_use_quantity > ar.reserve_max_quantity,ar.online_quantity-(ar.reserve_max_quantity - ar.reserve_use_quantity),0),
                        if(ar.online_quantity > ar.reserve_min_quantity,ar.online_quantity - ar.reserve_min_quantity,0)) quantity,
                        if(ar.reserve_max_quantity  > ar.reserve_use_quantity + ar.reserve_min_quantity,
                        if(ar.online_quantity + ar.reserve_use_quantity > ar.reserve_max_quantity,ar.online_quantity-(ar.reserve_max_quantity - ar.reserve_use_quantity),0),
                        if(ar.online_quantity > ar.reserve_min_quantity,ar.online_quantity - ar.reserve_min_quantity,0)) sortQuantity

                    </otherwise>
                </choose>

        FROM inventory i
                 LEFT JOIN products p ON i.pd_id = p.pd_id
                 LEFT JOIN category c on c.id = p.category_id
                 LEFT JOIN area_sku a ON i.sku = a.sku
                 LEFT join (
            select area_no, store_no
            from fence
            where area_no = #{areaNo} and status = 0 limit  1
        ) f on f.area_no = a.area_no
                 INNER join warehouse_inventory_mapping wim on wim.store_no = f.store_no and i.sku = wim.sku
                 INNER JOIN area_store ar on ar.sku = wim.sku and wim.warehouse_no = ar.area_no
                 left join day_sale_rank dsr on dsr.sku = i.sku and dsr.area_no = #{areaNo}
        WHERE a.on_sale = 1
          AND i.outdated = 0
          AND a.area_no = #{areaNo}
          AND a.m_type = 0
          and i.type = 0
          and i.ext_type = 2
        order by sortQuantity desc, dsr.sales desc, i.inv_id desc

    </select>

    <select id="selectWarehouseNo" resultType="java.lang.Integer">
        select warehouse_no from warehouse_inventory_mapping where store_no in(
            select * from(select store_no from fence where area_no = #{areaNo} and status = 0 limit 1) as a) and sku = #{sku}
    </select>

    <select id="selectCycleCost" resultType="java.math.BigDecimal">
        select first_cycle_cost
        from cycle_inventory_cost
        <where>
            <if test="warehouseNo!=null">
                and warehouse_no= #{warehouseNo}
            </if>
            <if test="sku!=null">
                and sku = #{sku}
            </if>
        </where>
    </select>

    <!-- 批量根据sku编号查询sku信息-->
    <select id="selectBySkuList" resultType="net.summerfarm.mall.model.vo.InventoryVO">
        select
            i.sku, i.sku_name skuName, i.sku_pic skuPic, p.pd_name pdName, p.category_id categoryId, ifnull(i.sku_pic, p.picture_path) picturePath, p.storage_location storageLocation, p.pd_id pdId, i.sale_price salePrice, i.volume, i.weight_num weightNum, i.type skuType, c.type,
            i.inv_id invId, i.maturity, i.weight, i.base_sale_quantity baseSaleQuantity, i.base_sale_unit baseSaleUnit, i.sale_price salePrice,i.introduction,i.ext_type extType,
               i.pack
        from
            inventory i
            LEFT JOIN products p on p.pd_id =i.pd_id
            LEFT JOIN category c on c.id=p.category_id
        <where>
            <if test="skuList != null and skuList.size > 0">
                AND i.sku  in
                <foreach collection="skuList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select *
        from inventory
        where inv_id = #{id}
    </select>
    <select id="selectByPrimaryKeyList" resultType="net.summerfarm.mall.model.vo.InventoryVO">
        select
        i.sku, p.pd_name pdName, p.category_id categoryId, ifnull(i.sku_pic, p.picture_path) picturePath,
        p.storage_location storageLocation, p.pd_id pdId, i.sale_price salePrice, i.volume, i.weight_num weightNum,
        i.type skuType, c.type, i.inv_id invId, i.maturity, i.weight, i.base_sale_quantity baseSaleQuantity,
        i.base_sale_unit baseSaleUnit,i.unit unit, i.sale_price salePrice,i.introduction, c.category categoryCategory,
        i.sub_type skuSubType
        from inventory i
        LEFT JOIN products p on p.pd_id =i.pd_id
        LEFT JOIN category c on c.id=p.category_id
        <where>
            <if test="ids != null and ids.size > 0">
                AND i.inv_id  in
                <foreach collection="ids" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="skuIds != null and skuIds.size > 0">
                AND i.sku  in
                <foreach collection="skuIds" open="(" close=")" item="sku" separator=",">
                    #{sku}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectSkus" resultType="string">
      select distinct i.sku
        from inventory i
        LEFT JOIN area_sku a ON i.sku = a.sku
        LEFT JOIN products p on p.pd_id =i.pd_id
        where
          a.on_sale = 1 AND i.outdated = 0 and i.ext_type = 0
          and (admin_id is null or admin_id = #{adminId})
          and p.pd_id = #{pdId} and area_no = #{areaNo}
    </select>

  <select id="selectByPdId" resultType="java.lang.String">
    select distinct i.sku from inventory i
    left join products p on p.pd_id = i.pd_id
    LEFT JOIN area_sku a ON i.sku = a.sku
    where i.outdated = 0
    and i.type = 0 AND m_type = 0
    and a.on_sale = 1
    AND a.area_no = #{areaNo}
    AND p.pd_id = #{pdId}
  </select>

    <select id="selectBySkuAndAreaNo" resultType="net.summerfarm.mall.model.vo.InventoryVO">
        SELECT
            i.inv_id invId, i.sku, i.ait_id aitId, p.pd_name productName, i.origin, i.unit, i.pack, i.weight, i.production_date productionDate, i.type,i.sku_pic skuPic,
            if(a.share=0,a.quantity,ar.online_quantity) quantity, i.storage_method storageMethod,
            IFNULL(a.price,i.sale_price) salePrice, a.share,
            i.promotion_price promotionPrice, a.on_sale onSale, a.priority, a.sales_mode salesMode, a.limited_quantity limitedQuantity,a.m_type mType,i.introduction, i.maturity, i.after_sale_quantity afterSaleQuantity,
            p.storage_location storageLocation,p.picture_path picturePath,p.pd_name  productName,p.pd_id pdId, i.ext_type extType,p.quality_time qualityTime,p.quality_time_unit qualityTimeUnit
        FROM inventory i LEFT JOIN products p on i.pd_id = p.pd_id
                         LEFT JOIN area_sku a on i.sku = a.sku AND a.area_no = #{areaNo}
                         LEFT JOIN area area on a.area_no=area.area_no
                         left join (
            select store_no ,area_no from fence where area_no = #{areaNo} and status = 0 limit 1
        ) fe   on fe.area_no = a.area_no
                         Left join warehouse_inventory_mapping wim on wim.sku = i.sku and fe.store_no = wim.store_no
                         LEFT JOIN area_store ar on ar.sku=i.sku and wim.warehouse_no = ar.area_no
        WHERE  i.sku = #{sku,jdbcType=VARCHAR}
          AND i.outdated = 0
    </select>

    <select id="selectSkuType" resultType="net.summerfarm.mall.model.vo.InventoryVO">
        SELECT i.sku,i.create_type createType,p.pd_name pdName, p.real_name realName,i.weight,i.type,p.category_id categoryId,c.type categoryType,i.volume,i.weight_num weightNum,ad.name_remakes nameRemakes,i.pd_id pdId,i.origin,i.is_domestic isDomestic,
               i.base_sale_quantity baseSaleQuantity ,i.base_sale_unit baseSaleUnit,p.storage_location storageLocation,i.sample_pool samplePool,i.ext_type extType,
               p.quality_time qualityTime,p.quality_time_unit qualityTimeUnit,i.sku_pic skuPic,p.picture_path picturePath,c.category categoryName,i.pack,i.unit,p.quality_time_type qualityTimeType
        FROM inventory i
                 LEFT JOIN products p ON i.pd_id=p.pd_id
                 LEFT JOIN category c ON p.category_id=c.id
                 LEFT JOIN admin ad on ad.admin_id = i.admin_id
        WHERE i.outdated = 0 and i.sku = #{sku}
    </select>

    <select id="selectInventoryVOBySku" resultType="net.summerfarm.mall.model.vo.InventoryVO">
        SELECT i.sku,p.pd_name pdName,i.weight,i.type,p.category_id categoryId,i.volume,i.weight_num weightNum,p.pd_no pdNo,
               ad.name_remakes nameRemakes,i.pd_id pdId,i.origin, p.warn_time warnTime, i.ext_type extType,i.is_domestic isDomestic,
               p.quality_time qualityTime,p.quality_time_unit qualityTimeUnit,c.type categoryType,p.storage_location storageLocation, i.inv_id invId,i.unit
        FROM inventory i
                 LEFT JOIN products p ON i.pd_id=p.pd_id
                 LEFT JOIN category c ON p.category_id=c.id
                 LEFT JOIN admin ad on ad.admin_id = i.admin_id
        WHERE i.sku = #{sku}
    </select>

    <select id="querySkusBySameSpuSku" resultType="java.lang.String">
    SELECT DISTINCT
        ( `sku` )
    FROM
        inventory
    WHERE
        pd_id IN (
        SELECT
            p.`pd_id`
        FROM
            inventory i
            LEFT JOIN products p ON i.pd_id = p.pd_id
        WHERE
            i.outdated = 0
            AND i.sku IN
        <foreach collection="skus" open="(" close=")" item="sku" separator=",">
            #{sku}
        </foreach>
        )
        AND outdated = 0
    </select>

    <select id="selectRecommendProductVOForPOP" resultType="net.summerfarm.mall.model.vo.ProductInfoVO">
        SELECT i.pd_id              pdId,
        p.pd_name            pdName,
        p.pddetail           pddetail,
        a.info,
        p.picture_path       picturePath,
        p.category_id        categoryId,
        a.pd_priority        priority,
        c.type,
        a.ladder_price       ladderPrice,
        a.sales_mode         salesMode,
        a.limited_quantity   limitedQuantity,
        i.weight,
        i.unit,
        i.sku,
        i.sku_pic skuPic,
        i.sku_name skuName,
        a.price     originalPrice,
        IFNULL(a.price, i.sale_price) salePrice,
        i.base_sale_quantity baseSaleQuantity,
        p.slogan,
        p.other_slogan       otherSlogan,
        i.base_sale_unit     baseSaleUnit,
        a.show_advance       showAdvance,
        a.advance,
        a.fix_num fixNum,
        ar.online_quantity onlineQuantity,
        ar.online_quantity quantity,
        ar.reserve_use_quantity reserveUseQuantity,
        ar.reserve_min_quantity reserveMinQuantity,
        ar.reserve_max_quantity reserveMaxQuantity,
        i.ext_type extType,
        p.quality_time qualityTime,
        p.quality_time_unit qualityTimeUnit
        ,i.net_weight_num netWeightNum,i.weight_num weightNum,i.net_weight_unit netWeightUnit,i.buyer_name buyerName,i.buyer_id buyerId
        FROM inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN category c on c.id = p.category_id
        LEFT JOIN area_sku a ON i.sku = a.sku
        INNER JOIN warehouse_inventory_mapping wim on wim.store_no = #{storeNo} and i.sku = wim.sku
        INNER JOIN area_store ar on ar.sku = wim.sku and wim.warehouse_no = ar.area_no
        WHERE a.on_sale = 1
        AND i.outdated = 0
        and p.outdated = 0
        and c.outdated = 0
        AND a.show = 1
        AND a.area_no = #{areaNo}
        <if test="skuList != null and skuList.size > 0">
            AND i.sku in
            <foreach collection="skuList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        order by ABS(a.pd_priority) desc
    </select>
    <select id="selectPdIdsSetBySkus" resultType="java.lang.Long">
        SELECT DISTINCT
        ( `pd_id` )
        FROM
        inventory
        WHERE sku IN
        <foreach collection="skus" open="(" close=")" item="sku" separator=",">
            #{sku}
        </foreach>
        AND outdated = 0
    </select>
</mapper>
