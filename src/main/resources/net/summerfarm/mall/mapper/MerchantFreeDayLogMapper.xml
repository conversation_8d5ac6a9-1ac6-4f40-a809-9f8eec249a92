<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MerchantFreeDayLogMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MerchantFreeDayLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="click_flag" jdbcType="TINYINT" property="clickFlag" />
    <result column="free_day" jdbcType="VARCHAR" property="freeDay" />
    <result column="effect_flag" jdbcType="TINYINT" property="effectFlag" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, click_flag, free_day, effect_flag, delete_flag, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_free_day_log
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByClickMid" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from merchant_free_day_log
      where m_id = #{mId} and click_flag = 0 and effect_flag = 0
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_free_day_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MerchantFreeDayLog" useGeneratedKeys="true">
    insert into merchant_free_day_log (m_id, click_flag, free_day, 
      effect_flag, delete_flag, creator, 
      create_time)
    values (#{mId,jdbcType=BIGINT}, #{clickFlag,jdbcType=TINYINT}, #{freeDay,jdbcType=VARCHAR}, 
      #{effectFlag,jdbcType=TINYINT}, #{deleteFlag,jdbcType=TINYINT}, #{creator,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MerchantFreeDayLog" useGeneratedKeys="true">
    insert into merchant_free_day_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="clickFlag != null">
        click_flag,
      </if>
      <if test="freeDay != null">
        free_day,
      </if>
      <if test="effectFlag != null">
        effect_flag,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="clickFlag != null">
        #{clickFlag,jdbcType=TINYINT},
      </if>
      <if test="freeDay != null">
        #{freeDay,jdbcType=VARCHAR},
      </if>
      <if test="effectFlag != null">
        #{effectFlag,jdbcType=TINYINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MerchantFreeDayLog">
    update merchant_free_day_log
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="clickFlag != null">
        click_flag = #{clickFlag,jdbcType=TINYINT},
      </if>
      <if test="freeDay != null">
        free_day = #{freeDay,jdbcType=VARCHAR},
      </if>
      <if test="effectFlag != null">
        effect_flag = #{effectFlag,jdbcType=TINYINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.MerchantFreeDayLog">
    update merchant_free_day_log
    set m_id = #{mId,jdbcType=BIGINT},
      click_flag = #{clickFlag,jdbcType=TINYINT},
      free_day = #{freeDay,jdbcType=VARCHAR},
      effect_flag = #{effectFlag,jdbcType=TINYINT},
      delete_flag = #{deleteFlag,jdbcType=TINYINT},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>