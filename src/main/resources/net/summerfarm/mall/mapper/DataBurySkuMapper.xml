<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.DataBurySkuMapper">
    <insert id="insertBatch" parameterType="list">
        insert into data_bury_sku
            (type, add_time, m_id, account_id, sku, ext1, ext2, ext3, ext4, ext5)
        values
            <foreach collection="list" separator="," item="el">
                (#{el.type}, #{el.addTime}, #{el.mId}, #{el.accountId}, #{el.sku}, #{el.ext1}, #{el.ext2}, #{el.ext3}, #{el.ext4}, #{el.ext5})
            </foreach>
    </insert>
</mapper>