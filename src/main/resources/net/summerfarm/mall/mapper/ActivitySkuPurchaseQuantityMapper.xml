<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.ActivitySkuPurchaseQuantityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.ActivitySkuPurchaseQuantity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="activity_id" jdbcType="INTEGER" property="activityId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_item_id" jdbcType="BIGINT" property="orderItemId" />
    <result column="purchase_quantity" jdbcType="INTEGER" property="purchaseQuantity" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="new_activity_id" jdbcType="BIGINT" property="newActivityId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, sku, activity_id, order_no, order_item_id, purchase_quantity, create_time, 
    update_time, new_activity_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from active_sku_purchase_quantity
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectPurchaseQuantity" resultMap="BaseResultMap">
    select
    a.id, a.m_id, a.sku, a.order_no, a.order_item_id, a.purchase_quantity, a.create_time,
    a.update_time,a.new_activity_id
    from active_sku_purchase_quantity a
    left join order_item o on a.order_item_id = o.id
    where  o.status in (1,2,3,6) and a.m_id = #{mId} and a.new_activity_id = #{newActivityId} and a.sku = #{sku}
  </select>

  <select id="selectPurchaseQuantityByActivityId" resultMap="BaseResultMap">
    select
      a.id, a.m_id, a.sku, a.order_no, a.order_item_id, a.purchase_quantity, a.create_time,
      a.update_time,a.new_activity_id
    from active_sku_purchase_quantity a
           left join order_item o on a.order_item_id = o.id
    where  o.status in (1,2,3,6) and a.m_id = #{mId} and a.new_activity_id = #{newActivityId}
  </select>

  <select id="selectPurchaseQuantityToday" resultMap="BaseResultMap">
    select
      a.id, a.m_id, a.sku, a.order_no, a.order_item_id, a.purchase_quantity, a.create_time,
      a.update_time, a.new_activity_id
    from active_sku_purchase_quantity a
           left join order_item o on a.order_item_id = o.id
    where  o.status in (1,2,3,6) and a.m_id = #{mId} and a.new_activity_id = #{newActivityId} and a.sku = #{sku}
      and a.create_time between concat(curdate(),' 00:00:00') and now()
  </select>

  <select id="selectPurchaseQuantityTodayByActivityId" resultMap="BaseResultMap">
    select
      a.id, a.m_id, a.sku, a.order_no, a.order_item_id, a.purchase_quantity, a.create_time,
      a.update_time, a.new_activity_id
    from active_sku_purchase_quantity a
           left join order_item o on a.order_item_id = o.id
    where  o.status in (1,2,3,6) and a.m_id = #{mId} and a.new_activity_id = #{newActivityId}
      and a.create_time between concat(curdate(),' 00:00:00') and now()
  </select>

  <select id="selectByOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from active_sku_purchase_quantity
    where order_no = #{orderNo,jdbcType=VARCHAR} and sku = #{sku,jdbcType=VARCHAR}
  </select>

  <select id="batchSelectPurchaseQuantityToday" resultMap="BaseResultMap">
    select
      a.id, a.m_id, a.sku, a.order_no, a.order_item_id, a.purchase_quantity, a.create_time,
      a.update_time, a.new_activity_id
    from active_sku_purchase_quantity a
           left join order_item o on a.order_item_id = o.id
    where  o.status in (1,2,3,6) and a.m_id = #{mId} and a.new_activity_id in
      <foreach collection="activityIds" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
      and a.sku in
      <foreach collection="skus" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
      and a.create_time between concat(curdate(),' 00:00:00') and now()
  </select>
  <select id="batchSelectPurchaseQuantity" resultMap="BaseResultMap">
    select
      a.id, a.m_id, a.sku, a.order_no, a.order_item_id, a.purchase_quantity, a.create_time,
      a.update_time,a.new_activity_id
    from active_sku_purchase_quantity a
           left join order_item o on a.order_item_id = o.id
    where  o.status in (1,2,3,6) and a.m_id = #{mId} and a.new_activity_id in
    <foreach collection="activityIds" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and a.sku in
    <foreach collection="skus" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from active_sku_purchase_quantity
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.ActivitySkuPurchaseQuantity" useGeneratedKeys="true">
    insert into active_sku_purchase_quantity (m_id, sku, activity_id, 
      order_no, order_item_id, purchase_quantity, 
      create_time, update_time, new_activity_id)
    values (#{mId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{activityId,jdbcType=INTEGER}, 
      #{orderNo,jdbcType=VARCHAR}, #{orderItemId,jdbcType=BIGINT}, #{purchaseQuantity,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{newActivityId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.ActivitySkuPurchaseQuantity" useGeneratedKeys="true">
    insert into active_sku_purchase_quantity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderItemId != null">
        order_item_id,
      </if>
      <if test="purchaseQuantity != null">
        purchase_quantity,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="newActivityId != null">
        new_activity_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderItemId != null">
        #{orderItemId,jdbcType=BIGINT},
      </if>
      <if test="purchaseQuantity != null">
        #{purchaseQuantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="newActivityId != null">
        #{newActivityId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
    <insert id="insertBatch">
      INSERT INTO active_sku_purchase_quantity (m_id,sku,new_activity_id,order_no,order_item_id,purchase_quantity)
      values
      <foreach collection="list"  item="item" separator=",">
        (#{item.mId},#{item.sku},#{item.newActivityId},#{item.orderNo},#{item.orderItemId},#{item.purchaseQuantity})
      </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.ActivitySkuPurchaseQuantity">
    update active_sku_purchase_quantity
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderItemId != null">
        order_item_id = #{orderItemId,jdbcType=BIGINT},
      </if>
      <if test="purchaseQuantity != null">
        purchase_quantity = #{purchaseQuantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="newActivityId != null">
        new_activity_id = #{newActivityId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.ActivitySkuPurchaseQuantity">
    update active_sku_purchase_quantity
    set m_id = #{mId,jdbcType=BIGINT},
      sku = #{sku,jdbcType=VARCHAR},
      new_activity_id = #{newActivityId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_item_id = #{orderItemId,jdbcType=BIGINT},
      purchase_quantity = #{purchaseQuantity,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>