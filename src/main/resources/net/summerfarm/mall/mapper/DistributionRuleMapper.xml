<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.DistributionRuleMapper">


  <resultMap id="ds" type="net.summerfarm.mall.model.domain.DistributionFreeRule">
    <id column="rule_id" property="id" jdbcType="INTEGER"/>
    <result column="rf_status" property="status" jdbcType="INTEGER"/>
    <result column="rule" property="rule" jdbcType="VARCHAR"/>
    <result column="distribution_id" property="distributionId" jdbcType="INTEGER"/>
  </resultMap>

  <resultMap id="ResultMap" type="net.summerfarm.mall.model.vo.DistributionRuleVO">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="status" property="status" jdbcType="INTEGER"/>
    <result column="area_no" property="areaNo" jdbcType="VARCHAR"/>
    <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL"/>
    <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
    <collection property="distributionFreeRules" resultMap="ds"/>
  </resultMap>


  <select id="queryByAdminId" resultMap="ResultMap">
    select df.id,
           df.status,
           df.area_no,
           df.admin_id,
           df.delivery_fee,
           rf.status rf_status,
           rf.rule,
           rf.id     rule_id,
           rf.distribution_id
    from distribution_fee df
           left join rule_distribution_free rf on df.id = rf.distribution_id
    where df.status = 0
      AND df.admin_id = #{adminId}
      AND df.area_no = #{areaNo}
  </select>


  <select id="queryAreaNO" resultType="net.summerfarm.mall.model.domain.DistributionRule">
    select *
    from distribution_fee
    where admin_id = #{adminId}
      and status = 0
      and area_no = 0
  </select>

  <select id="selectValid" resultType="net.summerfarm.mall.model.domain.DistributionRule">
    select id,
           area_no      areaNo,
           delivery_fee deliveryFee,
           status,
           admin_id     adminId
    from distribution_fee
    where status = 0
      and admin_id = #{adminId}
      and area_no = #{areaNo}
  </select>

  <select id="selectValidRule" resultType="net.summerfarm.mall.model.domain.RuleDistributionFree">
    select
           id, status, distribution_id distributionId, rule
    from rule_distribution_free
    where status = 0 and distribution_id = #{distributionId}
  </select>
</mapper>