<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.ActionMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Action" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="action_name" property="actionName" jdbcType="VARCHAR" />
    <result column="instruction" property="instruction" jdbcType="VARCHAR" />
    <result column="on_watch" property="onWatch" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, action_name, instruction, on_watch, update_time
  </sql>

  <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from action
    where action_name = #{actionName}
  </select>

  <!--auto code-->
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from action
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from action
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.Action" >
    insert into action (id, action_name, instruction, 
      on_watch, update_time)
    values (#{id,jdbcType=INTEGER}, #{actionName,jdbcType=VARCHAR}, #{instruction,jdbcType=VARCHAR}, 
      #{onWatch,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.Action" >
    insert into action
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="actionName != null" >
        action_name,
      </if>
      <if test="instruction != null" >
        instruction,
      </if>
      <if test="onWatch != null" >
        on_watch,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="actionName != null" >
        #{actionName,jdbcType=VARCHAR},
      </if>
      <if test="instruction != null" >
        #{instruction,jdbcType=VARCHAR},
      </if>
      <if test="onWatch != null" >
        #{onWatch,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.Action" >
    update action
    <set >
      <if test="actionName != null" >
        action_name = #{actionName,jdbcType=VARCHAR},
      </if>
      <if test="instruction != null" >
        instruction = #{instruction,jdbcType=VARCHAR},
      </if>
      <if test="onWatch != null" >
        on_watch = #{onWatch,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.Action" >
    update action
    set action_name = #{actionName,jdbcType=VARCHAR},
      instruction = #{instruction,jdbcType=VARCHAR},
      on_watch = #{onWatch,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>