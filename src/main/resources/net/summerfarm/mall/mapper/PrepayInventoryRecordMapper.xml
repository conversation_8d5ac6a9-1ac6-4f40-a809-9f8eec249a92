<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.PrepayInventoryRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.PrepayInventoryRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="prepay_inventory_id" jdbcType="INTEGER" property="prepayInventoryId" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="valid" jdbcType="BOOLEAN" property="valid" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, admin_id, prepay_inventory_id, amount, `valid`, order_no, sku, updater, update_time, 
    creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from prepay_inventory_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from prepay_inventory_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.PrepayInventoryRecord" useGeneratedKeys="true">
    insert into prepay_inventory_record (admin_id, prepay_inventory_id, amount, 
      `valid`, order_no, sku, 
      updater, update_time, creator, 
      create_time)
    values (#{adminId,jdbcType=INTEGER}, #{prepayInventoryId,jdbcType=INTEGER}, #{amount,jdbcType=INTEGER}, 
      #{valid,jdbcType=BOOLEAN}, #{orderNo,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, 
      #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.PrepayInventoryRecord" useGeneratedKeys="true">
    insert into prepay_inventory_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="prepayInventoryId != null">
        prepay_inventory_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="valid != null">
        `valid`,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="prepayInventoryId != null">
        #{prepayInventoryId,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=BOOLEAN},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.PrepayInventoryRecord">
    update prepay_inventory_record
    <set>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="prepayInventoryId != null">
        prepay_inventory_id = #{prepayInventoryId,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="valid != null">
        `valid` = #{valid,jdbcType=BOOLEAN},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.PrepayInventoryRecord">
    update prepay_inventory_record
    set admin_id = #{adminId,jdbcType=INTEGER},
      prepay_inventory_id = #{prepayInventoryId,jdbcType=INTEGER},
      amount = #{amount,jdbcType=INTEGER},
      `valid` = #{valid,jdbcType=BOOLEAN},
      order_no = #{orderNo,jdbcType=VARCHAR},
      sku = #{sku,jdbcType=VARCHAR},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectRecordList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from prepay_inventory_record
    where `valid` = true and admin_id = #{adminId} and order_no = #{orderNo} and  sku = #{sku}
    order by id desc
  </select>

  <select id="selectRecordListByOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from prepay_inventory_record
    where `valid` = true and order_no = #{orderNo}
    order by id desc
  </select>

  <select id="selectByOrderNoAndSku" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from prepay_inventory_record
    where `valid` = true and order_no = #{orderNo} and sku =#{sku}
    order by id desc
  </select>

  <select id="selectRecordListNew" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from prepay_inventory_record
    where `valid` = #{valid} and order_no = #{orderNo} and sku = #{sku}
    </select>
</mapper>