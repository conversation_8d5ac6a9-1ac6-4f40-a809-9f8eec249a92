<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.WarehouseInventoryMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="sale_lock_quantity" jdbcType="INTEGER" property="saleLockQuantity"/>
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, warehouse_no, store_no, sku, sale_lock_quantity, updater, update_time, creator, create_time
  </sql>

  <select id="listByStoreNoAndSku" resultMap="BaseResultMap">
    select warehouse_no, store_no, sku
    from warehouse_inventory_mapping
    where store_no = #{storeNo,jdbcType=INTEGER}
    and sku in
    <foreach collection="skuList" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>
</mapper>