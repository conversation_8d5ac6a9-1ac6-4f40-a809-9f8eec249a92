<?xml version="1.0" encoding="UTF-8" ?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MerchantOuterMapper" >
    <select id="queryByOuterNo" resultType="net.summerfarm.mall.model.domain.MerchantOuterDO">
        select m_id mId,outer_no outerNo from merchant_outer
        where outer_no = #{outerNo} and outer_platform_id = #{outerPlatformId}
    </select>
    <select id="selectByMidReceiptNotice" resultType="java.lang.Integer">
        SELECT count(1) FROM merchant_outer m
        LEFT JOIN outer_platform p on p.outer_platform_id = m.outer_platform_id
        WHERE m.m_id = #{mId} and p.push_order_switch=1
    </select>
</mapper>