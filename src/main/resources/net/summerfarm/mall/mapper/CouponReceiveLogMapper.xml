<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.CouponReceiveLogMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.CouponReceiveLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="coupon_id" jdbcType="INTEGER" property="couponId" />
    <result column="coupon_sender_id" jdbcType="INTEGER" property="couponSenderId" />
    <result column="m_id" jdbcType="VARCHAR" property="mId" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, coupon_id, coupon_sender_id, m_id, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from coupon_receive_log
    where id = #{id,jdbcType=INTEGER}
  </select>
    <select id="selectByCoupon" resultType="java.lang.Integer">
        select count(1)
        from coupon_receive_log
        where coupon_sender_id = #{couponSenderId} and m_id = #{mId}
        <if test="id != null">
          and coupon_id = #{id}
        </if>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from coupon_receive_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CouponReceiveLog" useGeneratedKeys="true">
    insert into coupon_receive_log (coupon_id, coupon_sender_id, m_id, 
      creator, create_time)
    values (#{couponId,jdbcType=INTEGER}, #{couponSenderId,jdbcType=INTEGER}, #{mId,jdbcType=VARCHAR}, 
      #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.CouponReceiveLog" useGeneratedKeys="true">
    insert into coupon_receive_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="couponSenderId != null">
        coupon_sender_id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="couponId != null">
        #{couponId,jdbcType=INTEGER},
      </if>
      <if test="couponSenderId != null">
        #{couponSenderId,jdbcType=INTEGER},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.CouponReceiveLog">
    update coupon_receive_log
    <set>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=INTEGER},
      </if>
      <if test="couponSenderId != null">
        coupon_sender_id = #{couponSenderId,jdbcType=INTEGER},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.CouponReceiveLog">
    update coupon_receive_log
    set coupon_id = #{couponId,jdbcType=INTEGER},
      coupon_sender_id = #{couponSenderId,jdbcType=INTEGER},
      m_id = #{mId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="insertList">
    INSERT INTO coupon_receive_log(
    coupon_id,
    coupon_sender_id,
    m_id,
    creator,
    create_time
    )VALUES
    <foreach collection="couponReceiveLogs" item="couponReceiveLog" index="index" separator=",">
      (
      #{couponReceiveLog.couponId,jdbcType=INTEGER},
      #{couponReceiveLog.couponSenderId,jdbcType=INTEGER},
      #{couponReceiveLog.mId,jdbcType=VARCHAR},
      #{couponReceiveLog.creator,jdbcType=INTEGER},
      #{couponReceiveLog.createTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>

  <select id="countBySenderSetupIdAndMId" resultType="net.summerfarm.mall.model.bo.coupon.CouponReceiveCountBO">
    select coupon_sender_id senderSetupId, coupon_id couponId, count(*) num
    from coupon_receive_log
    where m_id = #{mId} and coupon_sender_id in <foreach collection="senderSetupIds" item="senderSetUpId" open="(" close=")" separator=",">
                                #{senderSetUpId}
                              </foreach>
    group by coupon_sender_id, coupon_id
  </select>
</mapper>