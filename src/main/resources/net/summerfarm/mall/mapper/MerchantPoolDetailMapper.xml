<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MerchantPoolDetailMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MerchantPoolDetail">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="pool_info_id" jdbcType="BIGINT" property="poolInfoId"/>
    <result column="m_id" jdbcType="BIGINT" property="mId"/>
    <result column="size" jdbcType="VARCHAR" property="size"/>
    <result column="area_no" jdbcType="INTEGER" property="areaNo"/>
    <result column="version" jdbcType="INTEGER" property="version"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
    , `pool_info_id`, `m_id`, `size`, `area_no`, `version`, `create_time`, `update_time`
  </sql>

  <select id="selectByMId" resultType="java.lang.Long">
    select
    pool_info_id
    from merchant_pool_detail
    <where>
      m_id = #{mId}
      and (`pool_info_id`,`version`) in
      <foreach collection="list" open="(" separator="," close=")" item="item">
        (#{item.poolInfoId},#{item.maxVersion})
      </foreach>
    </where>
  </select>

  <select id="getDetailByMId" resultMap="BaseResultMap">
    select mpd.*
        from merchant_pool_detail mpd right join merchant_pool_info mpi on mpd.pool_info_id = mpi.id and mpd.version = mpi.version
    where m_id = #{mId}
  </select>

  <select id="checkMerchantInMerchantPool" resultType="int">
    select count(1)
    from merchant_pool_detail
    where m_id = #{mId}
    and `pool_info_id` = #{poolInfoId}
    and `version` = #{version}
  </select>


  <select id="countByMidPoolInfoID" resultType="int">
    select count(1)
    from merchant_pool_detail
    where m_id = #{mId}
    and `pool_info_id` = #{poolInfoId}
  </select>
</mapper>