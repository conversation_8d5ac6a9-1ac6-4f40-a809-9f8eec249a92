<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.FinancialInvoiceMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.FinancialInvoice">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="invoice_id" jdbcType="BIGINT" property="invoiceId" />
        <result column="invoice_type" jdbcType="TINYINT" property="invoiceType" />
        <result column="amount_money" jdbcType="DECIMAL" property="amountMoney" />
        <result column="invoice_result" jdbcType="TINYINT" property="invoiceResult" />
        <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
        <result column="handler_id" jdbcType="INTEGER" property="handlerId" />
        <result column="creator_remark" jdbcType="VARCHAR" property="creatorRemark" />
        <result column="handler_remark" jdbcType="VARCHAR" property="handlerRemark" />
        <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="express" jdbcType="VARCHAR" property="express" />
        <result column="finance_order_id" jdbcType="BIGINT" property="financeOrderId" />
        <result column="invoice_status" jdbcType="TINYINT" property="invoiceStatus" />
        <result column="serial_number"  property="serialNumber" />
        <result column="pdf_url" jdbcType="VARCHAR" property="pdfUrl" />
        <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
        <result column="duty_free_good" jdbcType="TINYINT" property="dutyFreeGood" />
        <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode" />
        <result column="invoice_number" jdbcType="VARCHAR" property="invoiceNumber" />
        <result column="belong_type" jdbcType="TINYINT" property="belongType" />
        <result column="mail_address" jdbcType="VARCHAR" property="mailAddress" />
        <result column="title" jdbcType="VARCHAR" property="title" />
    </resultMap>

    <resultMap id="VOResultMap" type="net.summerfarm.mall.model.vo.invoice.FinancialInvoiceVO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="invoice_id" jdbcType="BIGINT" property="invoiceId" />
        <result column="invoice_type" jdbcType="TINYINT" property="invoiceType" />
        <result column="amount_money" jdbcType="DECIMAL" property="amountMoney" />
        <result column="invoice_result" jdbcType="TINYINT" property="invoiceResult" />
        <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
        <result column="handler_id" jdbcType="INTEGER" property="handlerId" />
        <result column="creator_remark" jdbcType="VARCHAR" property="creatorRemark" />
        <result column="handler_remark" jdbcType="VARCHAR" property="handlerRemark" />
        <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="express" jdbcType="VARCHAR" property="express" />
        <result column="finance_order_id" jdbcType="BIGINT" property="financeOrderId" />
        <result column="invoice_status" jdbcType="TINYINT" property="invoiceStatus" />
        <result column="serial_number"  property="serialNumber" />
        <result column="pdf_url" jdbcType="VARCHAR" property="pdfUrl" />
        <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
        <result column="duty_free_good" jdbcType="TINYINT" property="dutyFreeGood" />
        <result column="handle_time"  property="handleTime" />
        <result column="invoice_code" property="invoiceCode" />
        <result column="invoice_number"  property="invoiceNumber" />
        <result column="belong_type" jdbcType="TINYINT" property="belongType" />
        <result column="mail_address" jdbcType="VARCHAR" property="mailAddress" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="order_item_id" jdbcType="BIGINT" property="orderItemId" />
        <result column="title" jdbcType="VARCHAR" property="title" />
    </resultMap>
    <select id="selectByMId" resultType="net.summerfarm.mall.model.vo.invoice.FinancialInvoiceVO">
        select fin.id id, IFNULL(a3.realName, m.mname) customerName, IFNULL(fin.title,ico.invoice_title) invoiceTitle, ico.tax_number taxNumber,
               fin.invoice_type invoiceType, fin.amount_money amountMoney, fin.invoice_result invoiceResult, fin.create_time createTime,fin.mail_address mailAddress,
               fin.belong_type belongType,fin.invoice_id invoiceId,fin.invoice_status invoiceStatus,a1.realname creatorName,if(fin.amount_money <![CDATA[ < ]]> 0,0,1) invoiceColor
        FROM financial_invoice fin
                 left join invoice_config ico on fin.invoice_id = ico.id
                 left join `admin` a1 on fin.creator_id = a1.admin_id
                 left join merchant m on ico.merchant_id = m.m_id
                 left join `admin` a3 on ico.admin_id = a3.admin_id
        where fin.m_id=#{mId}
        order by fin.id DESC
    </select>

</mapper>