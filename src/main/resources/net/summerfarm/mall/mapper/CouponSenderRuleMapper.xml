<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.CouponSenderRuleMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.CouponSenderRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="coupon_sender_id" jdbcType="INTEGER" property="couponSenderId" />
    <result column="scope_id" jdbcType="BIGINT" property="scopeId" />
    <result column="scope_type" jdbcType="TINYINT" property="scopeType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, coupon_sender_id, scope_id, scope_type, create_time, update_time
  </sql>


  <select id="getListBySendIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from coupon_sender_rule
    where scope_type = #{scopeType,jdbcType=INTEGER}
    and coupon_sender_id in
    <foreach item="item" index="index" collection="list"
             open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="getListBySendId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from coupon_sender_rule
    where scope_type = #{scopeType,jdbcType=INTEGER}
    and coupon_sender_id = #{couponSendId,jdbcType=INTEGER}
  </select>

  <select id="getListBySendIdsAndScopeTypeAndScopeId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from coupon_sender_rule
    where scope_type = #{scopeType,jdbcType=INTEGER}
    and scope_id = #{scopeId,jdbcType=BIGINT}
    and coupon_sender_id in
    <foreach item="item" index="index" collection="list"
             open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
</mapper>