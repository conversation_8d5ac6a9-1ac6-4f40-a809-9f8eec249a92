<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MerchantCluePoolMapper" >

    <select id="queryMerchantClue" resultType = "net.summerfarm.mall.model.domain.MerchantCluePool">
        select
        id,
        es_id esId,
        m_name mName,
        address address,
        phone phone
        from merchant_clue_pool
        where status = 0
        <if test="mId != null">
            ANd m_id = #{mId}
        </if>
        <if test="mlId != null">
            AND ml_id = #{mlId}
        </if>
    </select>

    <update id="updateMerchantCluePool" parameterType="net.summerfarm.mall.model.domain.MerchantCluePool">
        update    merchant_clue_pool set status = #{status}
        where es_id =#{esId}
    </update>

    <update id="updateCluePool" parameterType="net.summerfarm.mall.model.domain.MerchantCluePool">
        update    merchant_clue_pool
        <set>
            <if test="mName != null">
                m_name = #{mName},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="esId != null">
                es_id = #{esid}
            </if>
            <if test="mId != null">
                m_id = #{mId}
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>