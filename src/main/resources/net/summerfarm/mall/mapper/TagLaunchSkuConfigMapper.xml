<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.TagLaunchSkuConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.market.TagLaunchSkuConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="info_id" jdbcType="BIGINT" property="infoId" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `info_id`, `biz_id`, `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tag_launch_sku_config
    where `id` = #{id,jdbcType=BIGINT}
  </select>
</mapper>