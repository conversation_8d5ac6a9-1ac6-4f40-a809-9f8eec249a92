<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.DeliveryPlanExtendMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.DeliveryPlanExtend">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="delivery_plan_id" jdbcType="INTEGER" property="deliveryPlanId" />
    <result column="operator_type" jdbcType="INTEGER" property="operatorType" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, delivery_plan_id, operator_type, `operator`, `type`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from delivery_plan_extend
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from delivery_plan_extend
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.DeliveryPlanExtend" useGeneratedKeys="true">
    insert into delivery_plan_extend (order_no, delivery_plan_id, operator_type, 
      `operator`, `type`, create_time, 
      update_time)
    values (#{orderNo,jdbcType=VARCHAR}, #{deliveryPlanId,jdbcType=INTEGER}, #{operatorType,jdbcType=INTEGER}, 
      #{operator,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.DeliveryPlanExtend" useGeneratedKeys="true">
    insert into delivery_plan_extend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="deliveryPlanId != null">
        delivery_plan_id,
      </if>
      <if test="operatorType != null">
        operator_type,
      </if>
      <if test="operator != null">
        `operator`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPlanId != null">
        #{deliveryPlanId,jdbcType=INTEGER},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch">
    insert into delivery_plan_extend (order_no, delivery_plan_id, operator_type,
      `operator`, `type`, create_time,
      update_time)
    values
    <foreach collection="list" separator="," item="item">
      (#{item.orderNo,jdbcType=VARCHAR}, #{item.deliveryPlanId,jdbcType=INTEGER}, #{item.operatorType,jdbcType=INTEGER},
      #{item.operator,jdbcType=VARCHAR}, #{item.type,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.DeliveryPlanExtend">
    update delivery_plan_extend
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPlanId != null">
        delivery_plan_id = #{deliveryPlanId,jdbcType=INTEGER},
      </if>
      <if test="operatorType != null">
        operator_type = #{operatorType,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.DeliveryPlanExtend">
    update delivery_plan_extend
    set order_no = #{orderNo,jdbcType=VARCHAR},
      delivery_plan_id = #{deliveryPlanId,jdbcType=INTEGER},
      operator_type = #{operatorType,jdbcType=INTEGER},
      `operator` = #{operator,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>