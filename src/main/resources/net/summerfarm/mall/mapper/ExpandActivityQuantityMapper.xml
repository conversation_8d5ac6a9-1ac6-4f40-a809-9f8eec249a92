<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.ExpandActivityQuantityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.ExpandActivityQuantity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="expand_activity_id" jdbcType="BIGINT" property="expandActivityId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_item_id" jdbcType="BIGINT" property="orderItemId" />
    <result column="purchase_quantity" jdbcType="INTEGER" property="purchaseQuantity" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, expand_activity_id, sku, m_id, order_no, order_item_id, purchase_quantity, create_time, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from expand_activity_quantity
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectPurchaseQuantity" resultMap="BaseResultMap">
    select
    a.id, a.m_id, a.sku, a.expand_activity_id, a.order_no, a.order_item_id, a.purchase_quantity, a.create_time,
    a.update_time
    from expand_activity_quantity a
    left join orders o on a.order_no = o.order_no
    where  o.status in (1,2,3,6) and a.m_id = #{mId} and a.expand_activity_id = #{expandId} and a.sku = #{sku}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from expand_activity_quantity
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.ExpandActivityQuantity" useGeneratedKeys="true">
    insert into expand_activity_quantity (expand_activity_id, sku, m_id, 
      order_no, order_item_id, purchase_quantity, 
      create_time, update_time)
    values (#{expandActivityId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{mId,jdbcType=BIGINT}, 
      #{orderNo,jdbcType=VARCHAR}, #{orderItemId,jdbcType=BIGINT}, #{purchaseQuantity,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.ExpandActivityQuantity" useGeneratedKeys="true">
    insert into expand_activity_quantity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expandActivityId != null">
        expand_activity_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderItemId != null">
        order_item_id,
      </if>
      <if test="purchaseQuantity != null">
        purchase_quantity,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="expandActivityId != null">
        #{expandActivityId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderItemId != null">
        #{orderItemId,jdbcType=BIGINT},
      </if>
      <if test="purchaseQuantity != null">
        #{purchaseQuantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch">
    INSERT INTO expand_activity_quantity (m_id,sku,expand_activity_id,order_no,order_item_id,purchase_quantity)
    values
    <foreach collection="list"  item="item" separator=",">
      (#{item.mId},#{item.sku},#{item.expandActivityId},#{item.orderNo},#{item.orderItemId},#{item.purchaseQuantity})
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.ExpandActivityQuantity">
    update expand_activity_quantity
    <set>
      <if test="expandActivityId != null">
        expand_activity_id = #{expandActivityId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderItemId != null">
        order_item_id = #{orderItemId,jdbcType=BIGINT},
      </if>
      <if test="purchaseQuantity != null">
        purchase_quantity = #{purchaseQuantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.ExpandActivityQuantity">
    update expand_activity_quantity
    set expand_activity_id = #{expandActivityId,jdbcType=BIGINT},
      sku = #{sku,jdbcType=VARCHAR},
      m_id = #{mId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_item_id = #{orderItemId,jdbcType=BIGINT},
      purchase_quantity = #{purchaseQuantity,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>