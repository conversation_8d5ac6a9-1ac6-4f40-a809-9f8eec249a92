<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.OutsideContactMapper">


    <select id="selectOutsideContactById" parameterType="java.lang.Integer"
            resultType="net.summerfarm.mall.model.domain.OutsideContact">
        select id,tenant_id tenantId,store_id storeId,province,city,area,address,poi,distance,`name`,mname,phone
         from outside_contact
        where id = #{id}

    </select>

</mapper>