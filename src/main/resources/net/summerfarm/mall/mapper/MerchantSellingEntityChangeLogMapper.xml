<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MerchantSellingEntityChangeLogMapper">
    <!-- 结果集映射 -->
    <resultMap id="merchantSellingEntityChangeLogResultMap" type="net.summerfarm.mall.model.domain.sellingEntity.MerchantSellingEntityChangeLog">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="m_id" property="mId" jdbcType="NUMERIC"/>
		<result column="selling_entity_name" property="sellingEntityName" jdbcType="VARCHAR"/>
		<result column="agree" property="agree" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="merchantSellingEntityChangeLogColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.m_id,
          t.selling_entity_name,
          t.agree
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="mId != null">
                AND t.m_id = #{mId}
            </if>
			<if test="sellingEntityName != null and sellingEntityName !=''">
                AND t.selling_entity_name = #{sellingEntityName}
            </if>
			<if test="agree != null">
                AND t.agree = #{agree}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="mId != null">
                    t.m_id = #{mId},
                </if>
                <if test="sellingEntityName != null">
                    t.selling_entity_name = #{sellingEntityName},
                </if>
                <if test="agree != null">
                    t.agree = #{agree},
                </if>
        </trim>
    </sql>


	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.sellingEntity.MerchantSellingEntityChangeLog" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO merchant_selling_entity_change_log
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="mId != null">
				  m_id,
              </if>
              <if test="sellingEntityName != null">
				  selling_entity_name,
              </if>
              <if test="agree != null">
				  agree,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="mId != null">
				#{mId,jdbcType=NUMERIC},
              </if>
              <if test="sellingEntityName != null">
				#{sellingEntityName,jdbcType=VARCHAR},
              </if>
              <if test="agree != null">
				#{agree,jdbcType=INTEGER},
              </if>
        </trim>
    </insert>

	<!-- 根据mID获取数据 -->
	<select id="selectLatestByMId" parameterType="java.lang.Long" resultMap="merchantSellingEntityChangeLogResultMap" >
        SELECT <include refid="merchantSellingEntityChangeLogColumns" />
        FROM merchant_selling_entity_change_log t
		WHERE t.m_id = #{mId}
		order by t.id desc
		limit 1
    </select>

    <update id="updateByPrimaryKeySelective">
    update merchant_selling_entity_change_log
    set agree = #{agree,jdbcType=INTEGER}
    where id = #{id,jdbcType=NUMERIC}
</update>




</mapper>