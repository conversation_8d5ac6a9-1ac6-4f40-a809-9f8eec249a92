<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MarketRuleMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MarketRule">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="name" property="name" jdbcType="VARCHAR"/>
    <result column="type" property="type" jdbcType="INTEGER"/>
    <result column="detail" property="detail" jdbcType="VARCHAR"/>
    <result column="show_name" property="showName" jdbcType="VARCHAR"/>
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
    <result column="rule_detail" property="ruleDetail" jdbcType="VARCHAR"/>
    <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
    <result column="support_type" property="supportType" jdbcType="INTEGER"/>
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    <result column="coupon_rule" property="couponRule" jdbcType="INTEGER"/>
    <collection property="marketRuleDetailList" ofType="net.summerfarm.mall.model.domain.MarketRuleDetail"
                javaType="list">
      <id column="detailId" property="id"/>
      <result column="rule_id" property="ruleId"/>
      <result column="category_id" property="categoryId"/>
      <result column="category_name" property="categoryName"/>
      <result column="sku" property="sku"/>
      <result column="pd_name" property="pdName"/>
      <result column="weight" property="weight"/>
    </collection>
  </resultMap>


  <select id="select" resultMap="BaseResultMap" parameterType="net.summerfarm.mall.model.domain.MarketRule">
    select
    mr.id,mr.name,mr.type,mr.detail,mr.show_name,mr.start_time,mr.end_time,mr.rule_detail,amr.area_no,mr.update_time,mrd.id
    detailId,mrd.rule_id,mrd.category_id,mrd.category_name,mrd.sku,mrd.pd_name,mrd.weight
    from market_rule mr
    left join market_rule_detail mrd on mr.id=mrd.rule_id
    left join area_market_rule amr on amr.rule_id = mr.id
    <where>
      <if test="name != null and name != '' ">
        AND mr.name like concat('%',#{name},'%')
      </if>
      <if test="type != null ">
        AND mr.type =#{type}
      </if>
      <if test="sku != null ">
        AND mrd.sku =#{sku}
      </if>
      <if test="skus != null and skus.size > 0" >
        AND mrd.sku in
        <foreach collection="skus" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="areaNo != null ">
        AND amr.area_no =#{areaNo}
      </if>
      <if test="startTime !=null">
        and mr.start_time <![CDATA[>]]> #{startTime}
      </if>
      <if test="endTime !=null">
        and mr.end_time <![CDATA[<=]]> #{endTime}
      </if>
      <if test="status !=null and  status==0">
        and mr.end_time <![CDATA[>=]]> now() and  mr.start_time <![CDATA[<=]]> now()
      </if>
      <if test="status !=null and  status==1">
        and mr.start_time <![CDATA[>]]> now()
      </if>
      <if test="status !=null and  status==2">
        and mr.end_time <![CDATA[<=]]> now()
      </if>
      <if test="status !=null and  status==3">
        and mr.start_time <![CDATA[>=]]> '2021-06-18' and  mr.end_time <![CDATA[<=]]> '2021-06-22'
      </if>
    </where>
    order by mr.type desc,mrd.category_id
  </select>
  <select id="selectTiming" resultMap="BaseResultMap" >
    select
    mr.id,mr.name,mr.type,mr.detail,mr.show_name,mr.start_time,mr.end_time,mr.rule_detail,mr.area_no,mr.support_type,mr.update_time,mrd.id
    detailId,mrd.rule_id,mrd.category_id,mrd.category_name,mrd.sku,mrd.pd_name,mrd.weight
    from market_rule mr
    left join market_rule_detail mrd on mr.id=mrd.rule_id
    <where>
      <if test="status !=null and  status==0">
        and mr.end_time <![CDATA[>=]]> now() and  mr.start_time <![CDATA[<=]]> now()
      </if>
      <if test="status !=null and  status==1">
        and mr.start_time <![CDATA[>]]> now()
      </if>
      <if test="status !=null and  status==2">
        and mr.end_time <![CDATA[<=]]> now()
      </if>
      and mr.type =#{type}
      AND mrd.sku =#{sku}
      AND mr.area_no =#{areaNo}
    </where>
    order by mr.type desc
  </select>

  <select id="selectMark" resultMap="BaseResultMap" >
    select
    mr.id,mr.name,mr.type,mr.detail,mr.show_name,mr.start_time,mr.end_time,mr.rule_detail,mr.area_no,mr.support_type,mr.update_time,mrd.id
    detailId,mrd.rule_id,mrd.category_id,mrd.category_name,mrd.sku,mrd.pd_name,mrd.weight
    from market_rule mr
    inner join market_rule_detail mrd on mr.id=mrd.rule_id
      AND mrd.sku =#{sku}
      AND mr.area_no =#{areaNo}
        AND mr.end_time <![CDATA[>=]]> now() AND  mr.start_time <![CDATA[<=]]> now()
    order by mr.type desc
  </select>

  <select id="selectOne" resultMap="BaseResultMap">
    select
      id
    from market_rule
    where id = #{id}
  </select>

  <select id="selectValidRule" resultType="net.summerfarm.mall.model.domain.MarketRule">
    select mr.id,
           mr.name,
           mr.type,
           mr.detail,
           mr.show_name showName,
           mr.rule_detail ruleDetail,
           mr.support_type supportType,
           mr.coupon_rule couponRule,
           mr.start_time startTime,
           mr.end_time endTime
    from market_rule mr
    left join area_market_rule amr on mr.id = amr.rule_id
    where start_time &lt;= now()
        and now() &lt; end_time
        and type = #{type}
        and amr.area_no = #{areaNo}
  </select>
    <select id="getInfoByIds" resultType="net.summerfarm.mall.model.domain.MarketRule">
      select
      id,
      name,
      type,
      detail,
      show_name showName,
      rule_detail ruleDetail,
      support_type supportType,
      coupon_rule couponRule
      from market_rule
      where id in
      <foreach collection="marketRuleIds" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </select>

    <select id="selectValidRuleByAreaNo" resultType="net.summerfarm.mall.model.domain.MarketRule">
      select mr.id,
             mr.name,
             mr.type,
             mr.detail,
             mr.show_name showName,
             mr.rule_detail ruleDetail,
             mr.support_type supportType,
             mr.coupon_rule couponRule,
             amr.area_no areaNo,
             mr.start_time startTime,
             mr.end_time endTime
      from market_rule mr
             left join area_market_rule amr on mr.id = amr.rule_id
      where start_time &lt;= now()
        and now() &lt; end_time
        and amr.area_no = #{areaNo}
    </select>
</mapper>