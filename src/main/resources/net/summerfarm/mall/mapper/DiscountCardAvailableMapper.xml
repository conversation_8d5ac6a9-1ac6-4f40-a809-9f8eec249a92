<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.DiscountCardAvailableMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.DiscountCardAvailable">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="discount_card_id" jdbcType="INTEGER" property="discountCardId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, discount_card_id, sku, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from discount_card_available
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from discount_card_available
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.DiscountCardAvailable" useGeneratedKeys="true">
    insert into discount_card_available (discount_card_id, sku, creator, 
      create_time)
    values (#{discountCardId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.DiscountCardAvailable" useGeneratedKeys="true">
    insert into discount_card_available
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="discountCardId != null">
        discount_card_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="discountCardId != null">
        #{discountCardId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.DiscountCardAvailable">
    update discount_card_available
    <set>
      <if test="discountCardId != null">
        discount_card_id = #{discountCardId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.DiscountCardAvailable">
    update discount_card_available
    set discount_card_id = #{discountCardId,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByCardId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from discount_card_available
    where discount_card_id = #{id,jdbcType=INTEGER}

  </select>
  <select id="slelectSkuByCardId" resultType="string">
    select sku
    from discount_card_available
    where discount_card_id = #{cardId} and
          <choose>
            <when test="skuSet != null and skuSet.size != 0">
              sku IN
              <foreach collection="skuSet" open="(" close=")" separator="," item="item">
                #{item}
              </foreach>
            </when>
            <otherwise>
              discount_card_id = -1
            </otherwise>
          </choose>
  </select>

  <select id="listByCardIdsAndSkus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from discount_card_available
    where discount_card_id in
        <foreach collection="cardIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
      and sku IN
        <foreach collection="skus" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
  </select>
  <select id="getSkusByCardId" resultType="java.lang.String">
    select sku
    from discount_card_available
    where discount_card_id = #{discountCardId}
  </select>

  <select id="getAllDiscountCardAvailable" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from discount_card_available
  </select>
</mapper>