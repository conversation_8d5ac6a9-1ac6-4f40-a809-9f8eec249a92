<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.ExpandActivityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.ExpandActivity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="validity_period" jdbcType="INTEGER" property="validityPeriod" />
    <result column="trigger_times" jdbcType="TINYINT" property="triggerTimes" />
    <result column="pending_delivery" jdbcType="TINYINT" property="pendingDelivery" />
    <result column="churn_risk" jdbcType="TINYINT" property="churnRisk" />
    <result column="recall" jdbcType="TINYINT" property="recall" />
    <result column="pull_new" jdbcType="TINYINT" property="pullNew" />
    <result column="purchase_limit" jdbcType="INTEGER" property="purchaseLimit" />
    <result column="discount_percentage" jdbcType="DECIMAL" property="discountPercentage" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `name`, `status`, `type`, start_time, end_time, validity_period, trigger_times,
    pending_delivery, churn_risk, recall, pull_new, purchase_limit, discount_percentage, 
    discount, creator, remark, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from expand_activity
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectEnableActivity" resultType="net.summerfarm.mall.model.vo.ExpandActivityVO">
      select
      ea.id,ea.name,ea.status,ea.type,ea.start_time as startTime,ea.end_time as endTime,ea.validity_period as validityPeriod,ea.trigger_times as triggerTimes,ea.pending_delivery as pendingDelivery,
      ea.churn_risk as churnRisk ,ea.recall,ea.pull_new as pullNew,ea.purchase_limit as purchaseLimit,ea.discount_percentage as discountPercentage,ea.discount,ea.creator,ea.remark
      from expand_activity ea left join expand_area earea on ea.id = earea.expand_id
      where ea.status = 1 and ea.del_flag = 0 and ((ea.type = 1) or (ea.type = 0 and start_time <![CDATA[ <= ]]> now() and end_time > now()))
        <if test="largeAreaNo != null">
          and earea.large_area_no = #{largeAreaNo}
        </if>
          group by earea.expand_id order by ea.id desc
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from expand_activity
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.ExpandActivity" useGeneratedKeys="true">
    insert into expand_activity (`name`, `status`, `type`, 
      start_time, end_time, validity_period, 
      trigger_times, pending_delivery,
      churn_risk, recall, pull_new, 
      purchase_limit, discount_percentage, discount, 
      creator, remark, create_time, 
      update_time)
    values (#{name,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{type,jdbcType=TINYINT}, 
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{validityPeriod,jdbcType=INTEGER}, 
      #{triggerTimes,jdbcType=TINYINT}, #{pendingDelivery,jdbcType=TINYINT},
      #{churnRisk,jdbcType=TINYINT}, #{recall,jdbcType=TINYINT}, #{pullNew,jdbcType=TINYINT}, 
      #{purchaseLimit,jdbcType=INTEGER}, #{discountPercentage,jdbcType=DECIMAL}, #{discount,jdbcType=DECIMAL},
      #{creator,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.ExpandActivity" useGeneratedKeys="true">
    insert into expand_activity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="validityPeriod != null">
        validity_period,
      </if>
      <if test="triggerTimes != null">
        trigger_times,
      </if>
      <if test="pendingDelivery != null">
        pending_delivery,
      </if>
      <if test="churnRisk != null">
        churn_risk,
      </if>
      <if test="recall != null">
        recall,
      </if>
      <if test="pullNew != null">
        pull_new,
      </if>
      <if test="purchaseLimit != null">
        purchase_limit,
      </if>
      <if test="discountPercentage != null">
        discount_percentage,
      </if>
      <if test="discount != null">
        discount,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="validityPeriod != null">
        #{validityPeriod,jdbcType=INTEGER},
      </if>
      <if test="triggerTimes != null">
        #{triggerTimes,jdbcType=TINYINT},
      </if>
      <if test="pendingDelivery != null">
        #{pendingDelivery,jdbcType=TINYINT},
      </if>
      <if test="churnRisk != null">
        #{churnRisk,jdbcType=TINYINT},
      </if>
      <if test="recall != null">
        #{recall,jdbcType=TINYINT},
      </if>
      <if test="pullNew != null">
        #{pullNew,jdbcType=TINYINT},
      </if>
      <if test="purchaseLimit != null">
        #{purchaseLimit,jdbcType=INTEGER},
      </if>
      <if test="discountPercentage != null">
        #{discountPercentage,jdbcType=DECIMAL},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=DECIMAL},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.ExpandActivity">
    update expand_activity
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="validityPeriod != null">
        validity_period = #{validityPeriod,jdbcType=INTEGER},
      </if>
      <if test="triggerTimes != null">
        trigger_times = #{triggerTimes,jdbcType=TINYINT},
      </if>
      <if test="pendingDelivery != null">
        pending_delivery = #{pendingDelivery,jdbcType=TINYINT},
      </if>
      <if test="churnRisk != null">
        churn_risk = #{churnRisk,jdbcType=TINYINT},
      </if>
      <if test="recall != null">
        recall = #{recall,jdbcType=TINYINT},
      </if>
      <if test="pullNew != null">
        pull_new = #{pullNew,jdbcType=TINYINT},
      </if>
      <if test="purchaseLimit != null">
        purchase_limit = #{purchaseLimit,jdbcType=INTEGER},
      </if>
      <if test="discountPercentage != null">
        discount_percentage = #{discountPercentage,jdbcType=DECIMAL},
      </if>
      <if test="discount != null">
        discount = #{discount,jdbcType=DECIMAL},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.ExpandActivity">
    update expand_activity
    set `name` = #{name,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      `type` = #{type,jdbcType=TINYINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      validity_period = #{validityPeriod,jdbcType=INTEGER},
      trigger_times = #{triggerTimes,jdbcType=TINYINT},
      pending_delivery = #{pendingDelivery,jdbcType=TINYINT},
      churn_risk = #{churnRisk,jdbcType=TINYINT},
      recall = #{recall,jdbcType=TINYINT},
      pull_new = #{pullNew,jdbcType=TINYINT},
      purchase_limit = #{purchaseLimit,jdbcType=INTEGER},
      discount_percentage = #{discountPercentage,jdbcType=DECIMAL},
      discount = #{discount,jdbcType=DECIMAL},
      creator = #{creator,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>