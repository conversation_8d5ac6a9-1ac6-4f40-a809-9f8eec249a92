<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.ExpandActivityTagMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.ExpandActivityTag">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="expand_activity_id" jdbcType="BIGINT" property="expandActivityId" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime" />
    <result column="validity_period" jdbcType="INTEGER" property="validityPeriod" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, expand_activity_id, m_id, order_no, effective_time, validity_period, delivery_time, 
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from expand_activity_tag
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByMid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from expand_activity_tag
    where m_id = #{mId,jdbcType=BIGINT} and expand_activity_id = #{expandId,jdbcType=BIGINT} and order_no is null and delivery_time is not null
    order by id desc limit 1
  </select>
  <select id="selectOrderByMid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from expand_activity_tag
    where m_id = #{mId,jdbcType=BIGINT} and expand_activity_id = #{expandId,jdbcType=BIGINT} and order_no is not null
    order by id desc limit 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from expand_activity_tag
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.ExpandActivityTag" useGeneratedKeys="true">
    insert into expand_activity_tag (expand_activity_id, m_id, order_no, 
      effective_time, validity_period, delivery_time, 
      create_time, update_time)
    values (#{expandActivityId,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, 
      #{effectiveTime,jdbcType=TIMESTAMP}, #{validityPeriod,jdbcType=INTEGER}, #{deliveryTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.ExpandActivityTag" useGeneratedKeys="true">
    insert into expand_activity_tag
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expandActivityId != null">
        expand_activity_id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="effectiveTime != null">
        effective_time,
      </if>
      <if test="validityPeriod != null">
        validity_period,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="expandActivityId != null">
        #{expandActivityId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="effectiveTime != null">
        #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="validityPeriod != null">
        #{validityPeriod,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.ExpandActivityTag">
    update expand_activity_tag
    <set>
      <if test="expandActivityId != null">
        expand_activity_id = #{expandActivityId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="effectiveTime != null">
        effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="validityPeriod != null">
        validity_period = #{validityPeriod,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.ExpandActivityTag">
    update expand_activity_tag
    set expand_activity_id = #{expandActivityId,jdbcType=BIGINT},
      m_id = #{mId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
      validity_period = #{validityPeriod,jdbcType=INTEGER},
      delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>