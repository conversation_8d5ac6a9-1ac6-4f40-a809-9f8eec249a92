<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.ActivityScopeConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.market.ActivityScopeConfig">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="basic_info_id" jdbcType="BIGINT" property="basicInfoId"/>
    <result column="scope_id" jdbcType="BIGINT" property="scopeId"/>
    <result column="scope_type" jdbcType="TINYINT" property="scopeType"/>
    <result column="updater_id" jdbcType="INTEGER" property="updaterId"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    `id`
    , `basic_info_id`, `scope_id`, `scope_type`, `updater_id`, `del_flag`, `create_time`,
    `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_scope_config
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from activity_scope_config
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.market.ActivityScopeConfig">
    insert into activity_scope_config (`id`, `basic_info_id`, `scope_id`,
                                       `scope_type`, `updater_id`, `del_flag`,
                                       `create_time`, `update_time`)
    values (#{id,jdbcType=BIGINT}, #{basicInfoId,jdbcType=BIGINT}, #{scopeId,jdbcType=BIGINT},
            #{scopeType,jdbcType=TINYINT}, #{updaterId,jdbcType=INTEGER},
            #{delFlag,jdbcType=TINYINT},
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.market.ActivityScopeConfig">
    insert into activity_scope_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="basicInfoId != null">
        `basic_info_id`,
      </if>
      <if test="scopeId != null">
        `scope_id`,
      </if>
      <if test="scopeType != null">
        `scope_type`,
      </if>
      <if test="updaterId != null">
        `updater_id`,
      </if>
      <if test="delFlag != null">
        `del_flag`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="basicInfoId != null">
        #{basicInfoId,jdbcType=BIGINT},
      </if>
      <if test="scopeId != null">
        #{scopeId,jdbcType=BIGINT},
      </if>
      <if test="scopeType != null">
        #{scopeType,jdbcType=TINYINT},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.mall.model.domain.market.ActivityScopeConfig">
    update activity_scope_config
    <set>
      <if test="basicInfoId != null">
        `basic_info_id` = #{basicInfoId,jdbcType=BIGINT},
      </if>
      <if test="scopeId != null">
        `scope_id` = #{scopeId,jdbcType=BIGINT},
      </if>
      <if test="scopeType != null">
        `scope_type` = #{scopeType,jdbcType=TINYINT},
      </if>
      <if test="updaterId != null">
        `updater_id` = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        `del_flag` = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.market.ActivityScopeConfig">
    update activity_scope_config
    set `basic_info_id` = #{basicInfoId,jdbcType=BIGINT},
        `scope_id`      = #{scopeId,jdbcType=BIGINT},
        `scope_type`    = #{scopeType,jdbcType=TINYINT},
        `updater_id`    = #{updaterId,jdbcType=INTEGER},
        `del_flag`      = #{delFlag,jdbcType=TINYINT},
        `create_time`   = #{createTime,jdbcType=TIMESTAMP},
        `update_time`   = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateDelFlag">
    update activity_scope_config
    set del_flag = 1, updater_id = #{adminId}
    where del_flag = 0 and basic_info_id = #{basicInfoId}
  </update>
  <insert id="insertBatch" parameterType="net.summerfarm.mall.model.domain.market.ActivityScopeConfig">
    insert into activity_scope_config (`basic_info_id`, `scope_id`,
    `scope_type`, `updater_id`)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.basicInfoId,jdbcType=BIGINT}, #{item.scopeId,jdbcType=BIGINT},
      #{item.scopeType,jdbcType=TINYINT}, #{item.updaterId,jdbcType=INTEGER})
    </foreach>

  </insert>

  <select id="selectByInfoId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_scope_config
    where `basic_info_id` = #{basicInfoId} and del_flag = 0
    order by id desc
    <if test="limit != null">
      limit #{limit}
    </if>
  </select>

  <select id="countByBasicInfoId" resultType="int">
    select count(*)
    from activity_scope_config
    where `basic_info_id` = #{basicInfoId}
      and del_flag = 0
  </select>

  <select id="selectByScopeId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_scope_config
    where `basic_info_id` = #{basicInfoId} and scope_id = #{scopeId} and del_flag = 0
  </select>

  <select id="selectExpiredActivity"
    resultMap="net.summerfarm.mall.mapper.ActivityBasicInfoMapper.BaseResultMap">
    select abi.*
    from activity_basic_info abi
           left join activity_scope_config asco on abi.id = asco.basic_info_id
    where abi.type = 1
      and scope_id = #{scopeId}
      and scope_type = 2
      and del_flag = 0 limit 1
  </select>

  <select id="selectAllExpiredScope" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_basic_info abi left join activity_scope_config asco on abi.id = asco.basic_info_id
    where abi.type = 1
      and scope_type = 2
      and del_flag = 0
  </select>
</mapper>
