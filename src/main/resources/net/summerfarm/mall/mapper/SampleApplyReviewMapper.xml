<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.SampleApplyReviewMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.SampleApplyReview">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="sampleId" column="sample_id" jdbcType="INTEGER"/>
            <result property="reviewId" column="review_id" jdbcType="INTEGER"/>
            <result property="reviewName" column="review_name" jdbcType="VARCHAR"/>
            <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="reviewRemark" column="review_remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,sample_id,review_id,
        review_name,audit_time,status,
        review_remark
    </sql>

    <select id="isReview" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sample_apply_review
        where  sample_id = #{sampleApplyId,jdbcType=INTEGER}
        <if test="status != null">
            AND status = #{status}
        </if>
        LIMIT 1
    </select>
</mapper>
