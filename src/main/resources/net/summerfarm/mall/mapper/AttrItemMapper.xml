<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.AttrItemMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.AttrItem" >
    <id column="ait_id" property="aitId" jdbcType="INTEGER" />
    <result column="attr_id" property="attrId" jdbcType="INTEGER" />
    <result column="specification" property="specification" jdbcType="VARCHAR" />
    <result column="priority" property="priority" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    ait_id, attr_id, specification, priority
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from attr_item
    where ait_id = #{aitId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from attr_item
    where ait_id = #{aitId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.AttrItem" >
    insert into attr_item (ait_id, attr_id, specification, 
      priority)
    values (#{aitId,jdbcType=INTEGER}, #{attrId,jdbcType=INTEGER}, #{specification,jdbcType=VARCHAR}, 
      #{priority,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.AttrItem" >
    insert into attr_item
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="aitId != null" >
        ait_id,
      </if>
      <if test="attrId != null" >
        attr_id,
      </if>
      <if test="specification != null" >
        specification,
      </if>
      <if test="priority != null" >
        priority,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="aitId != null" >
        #{aitId,jdbcType=INTEGER},
      </if>
      <if test="attrId != null" >
        #{attrId,jdbcType=INTEGER},
      </if>
      <if test="specification != null" >
        #{specification,jdbcType=VARCHAR},
      </if>
      <if test="priority != null" >
        #{priority,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.AttrItem" >
    update attr_item
    <set >
      <if test="attrId != null" >
        attr_id = #{attrId,jdbcType=INTEGER},
      </if>
      <if test="specification != null" >
        specification = #{specification,jdbcType=VARCHAR},
      </if>
      <if test="priority != null" >
        priority = #{priority,jdbcType=INTEGER},
      </if>
    </set>
    where ait_id = #{aitId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.AttrItem" >
    update attr_item
    set attr_id = #{attrId,jdbcType=INTEGER},
      specification = #{specification,jdbcType=VARCHAR},
      priority = #{priority,jdbcType=INTEGER}
    where ait_id = #{aitId,jdbcType=INTEGER}
  </update>
</mapper>