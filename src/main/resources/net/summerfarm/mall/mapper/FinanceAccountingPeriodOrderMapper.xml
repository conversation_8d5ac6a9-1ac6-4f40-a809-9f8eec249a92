<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.FinanceAccountingPeriodOrderMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.FinanceAccountingPeriodOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bill_generation_time" jdbcType="TIMESTAMP" property="billGenerationTime" />
    <result column="bill_number" jdbcType="CHAR" property="billNumber" />
    <result column="invoice_id" jdbcType="BIGINT" property="invoiceId" />
    <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle" />
    <result column="name_remakes" jdbcType="VARCHAR" property="nameRemakes" />
    <result column="bill_confirmation_time" jdbcType="TIMESTAMP" property="billConfirmationTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="saler_name" jdbcType="VARCHAR" property="salerName" />
    <result column="after_sale_amount" jdbcType="DECIMAL" property="afterSaleAmount" />
    <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="store_quantity" jdbcType="INTEGER" property="storeQuantity" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="bill_cycle" jdbcType="VARCHAR" property="billCycle" />
    <result column="out_times_fee" jdbcType="DECIMAL" property="outTimesFee" />
    <result column="receipt_status" jdbcType="TINYINT" property="receiptStatus" />
    <result column="customer_confirm_status" jdbcType="TINYINT" property="customerConfirmStatus" />
    <result column="write_off_amount" jdbcType="DECIMAL" property="writeOffAmount" />
    <result column="saler_id" jdbcType="INTEGER" property="salerId" />
    <result column="financial_audit" jdbcType="INTEGER" property="financialAudit" />
    <result column="sales_invoicing" jdbcType="INTEGER" property="salesInvoicing" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="customer_type" jdbcType="TINYINT" property="customerType" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, bill_generation_time, bill_number, invoice_id, invoice_title, name_remakes, bill_confirmation_time, 
    type, admin_id, saler_name, after_sale_amount, delivery_fee, total_price, store_quantity, 
    create_time, creator, update_time, updater, bill_cycle, out_times_fee, receipt_status, 
    customer_confirm_status, write_off_amount, saler_id, financial_audit, sales_invoicing, 
    remarks, customer_type
  </sql>
 
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from finance_accounting_period_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_accounting_period_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
 
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.FinanceAccountingPeriodOrder">
    insert into finance_accounting_period_order (id, bill_generation_time, bill_number, 
      invoice_id, invoice_title, name_remakes, 
      bill_confirmation_time, type, admin_id, 
      saler_name, after_sale_amount, delivery_fee, 
      total_price, store_quantity, create_time, 
      creator, update_time, updater, 
      bill_cycle, out_times_fee, receipt_status, 
      customer_confirm_status, write_off_amount, 
      saler_id, financial_audit, sales_invoicing, 
      remarks, customer_type)
    values (#{id,jdbcType=BIGINT}, #{billGenerationTime,jdbcType=TIMESTAMP}, #{billNumber,jdbcType=CHAR}, 
      #{invoiceId,jdbcType=BIGINT}, #{invoiceTitle,jdbcType=VARCHAR}, #{nameRemakes,jdbcType=VARCHAR}, 
      #{billConfirmationTime,jdbcType=TIMESTAMP}, #{type,jdbcType=INTEGER}, #{adminId,jdbcType=INTEGER}, 
      #{salerName,jdbcType=VARCHAR}, #{afterSaleAmount,jdbcType=DECIMAL}, #{deliveryFee,jdbcType=DECIMAL}, 
      #{totalPrice,jdbcType=DECIMAL}, #{storeQuantity,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, 
      #{billCycle,jdbcType=VARCHAR}, #{outTimesFee,jdbcType=DECIMAL}, #{receiptStatus,jdbcType=TINYINT}, 
      #{customerConfirmStatus,jdbcType=TINYINT}, #{writeOffAmount,jdbcType=DECIMAL}, 
      #{salerId,jdbcType=INTEGER}, #{financialAudit,jdbcType=INTEGER}, #{salesInvoicing,jdbcType=INTEGER}, 
      #{remarks,jdbcType=VARCHAR}, #{customerType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.FinanceAccountingPeriodOrder">
    insert into finance_accounting_period_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="billGenerationTime != null">
        bill_generation_time,
      </if>
      <if test="billNumber != null">
        bill_number,
      </if>
      <if test="invoiceId != null">
        invoice_id,
      </if>
      <if test="invoiceTitle != null">
        invoice_title,
      </if>
      <if test="nameRemakes != null">
        name_remakes,
      </if>
      <if test="billConfirmationTime != null">
        bill_confirmation_time,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="salerName != null">
        saler_name,
      </if>
      <if test="afterSaleAmount != null">
        after_sale_amount,
      </if>
      <if test="deliveryFee != null">
        delivery_fee,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="storeQuantity != null">
        store_quantity,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="billCycle != null">
        bill_cycle,
      </if>
      <if test="outTimesFee != null">
        out_times_fee,
      </if>
      <if test="receiptStatus != null">
        receipt_status,
      </if>
      <if test="customerConfirmStatus != null">
        customer_confirm_status,
      </if>
      <if test="writeOffAmount != null">
        write_off_amount,
      </if>
      <if test="salerId != null">
        saler_id,
      </if>
      <if test="financialAudit != null">
        financial_audit,
      </if>
      <if test="salesInvoicing != null">
        sales_invoicing,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="customerType != null">
        customer_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="billGenerationTime != null">
        #{billGenerationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billNumber != null">
        #{billNumber,jdbcType=CHAR},
      </if>
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=BIGINT},
      </if>
      <if test="invoiceTitle != null">
        #{invoiceTitle,jdbcType=VARCHAR},
      </if>
      <if test="nameRemakes != null">
        #{nameRemakes,jdbcType=VARCHAR},
      </if>
      <if test="billConfirmationTime != null">
        #{billConfirmationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="salerName != null">
        #{salerName,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleAmount != null">
        #{afterSaleAmount,jdbcType=DECIMAL},
      </if>
      <if test="deliveryFee != null">
        #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="storeQuantity != null">
        #{storeQuantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="billCycle != null">
        #{billCycle,jdbcType=VARCHAR},
      </if>
      <if test="outTimesFee != null">
        #{outTimesFee,jdbcType=DECIMAL},
      </if>
      <if test="receiptStatus != null">
        #{receiptStatus,jdbcType=TINYINT},
      </if>
      <if test="customerConfirmStatus != null">
        #{customerConfirmStatus,jdbcType=TINYINT},
      </if>
      <if test="writeOffAmount != null">
        #{writeOffAmount,jdbcType=DECIMAL},
      </if>
      <if test="salerId != null">
        #{salerId,jdbcType=INTEGER},
      </if>
      <if test="financialAudit != null">
        #{financialAudit,jdbcType=INTEGER},
      </if>
      <if test="salesInvoicing != null">
        #{salesInvoicing,jdbcType=INTEGER},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="customerType != null">
        #{customerType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
 
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.FinanceAccountingPeriodOrder">
    update finance_accounting_period_order
    <set>
      <if test="billGenerationTime != null">
        bill_generation_time = #{billGenerationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billNumber != null">
        bill_number = #{billNumber,jdbcType=CHAR},
      </if>
      <if test="invoiceId != null">
        invoice_id = #{invoiceId,jdbcType=BIGINT},
      </if>
      <if test="invoiceTitle != null">
        invoice_title = #{invoiceTitle,jdbcType=VARCHAR},
      </if>
      <if test="nameRemakes != null">
        name_remakes = #{nameRemakes,jdbcType=VARCHAR},
      </if>
      <if test="billConfirmationTime != null">
        bill_confirmation_time = #{billConfirmationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="salerName != null">
        saler_name = #{salerName,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleAmount != null">
        after_sale_amount = #{afterSaleAmount,jdbcType=DECIMAL},
      </if>
      <if test="deliveryFee != null">
        delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="storeQuantity != null">
        store_quantity = #{storeQuantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="billCycle != null">
        bill_cycle = #{billCycle,jdbcType=VARCHAR},
      </if>
      <if test="outTimesFee != null">
        out_times_fee = #{outTimesFee,jdbcType=DECIMAL},
      </if>
      <if test="receiptStatus != null">
        receipt_status = #{receiptStatus,jdbcType=TINYINT},
      </if>
      <if test="customerConfirmStatus != null">
        customer_confirm_status = #{customerConfirmStatus,jdbcType=TINYINT},
      </if>
      <if test="writeOffAmount != null">
        write_off_amount = #{writeOffAmount,jdbcType=DECIMAL},
      </if>
      <if test="salerId != null">
        saler_id = #{salerId,jdbcType=INTEGER},
      </if>
      <if test="financialAudit != null">
        financial_audit = #{financialAudit,jdbcType=INTEGER},
      </if>
      <if test="salesInvoicing != null">
        sales_invoicing = #{salesInvoicing,jdbcType=INTEGER},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="customerType != null">
        customer_type = #{customerType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.FinanceAccountingPeriodOrder">
    update finance_accounting_period_order
    set bill_generation_time = #{billGenerationTime,jdbcType=TIMESTAMP},
      bill_number = #{billNumber,jdbcType=CHAR},
      invoice_id = #{invoiceId,jdbcType=BIGINT},
      invoice_title = #{invoiceTitle,jdbcType=VARCHAR},
      name_remakes = #{nameRemakes,jdbcType=VARCHAR},
      bill_confirmation_time = #{billConfirmationTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=INTEGER},
      admin_id = #{adminId,jdbcType=INTEGER},
      saler_name = #{salerName,jdbcType=VARCHAR},
      after_sale_amount = #{afterSaleAmount,jdbcType=DECIMAL},
      delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      total_price = #{totalPrice,jdbcType=DECIMAL},
      store_quantity = #{storeQuantity,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      bill_cycle = #{billCycle,jdbcType=VARCHAR},
      out_times_fee = #{outTimesFee,jdbcType=DECIMAL},
      receipt_status = #{receiptStatus,jdbcType=TINYINT},
      customer_confirm_status = #{customerConfirmStatus,jdbcType=TINYINT},
      write_off_amount = #{writeOffAmount,jdbcType=DECIMAL},
      saler_id = #{salerId,jdbcType=INTEGER},
      financial_audit = #{financialAudit,jdbcType=INTEGER},
      sales_invoicing = #{salesInvoicing,jdbcType=INTEGER},
      remarks = #{remarks,jdbcType=VARCHAR},
      customer_type = #{customerType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getUnFinishedCount" resultType="java.lang.Integer">
    select count(*)
    from finance_accounting_period_order fapo
           left join finance_accounting_store fas on fas.finance_order_id = fapo.id
    where fas.m_id = #{mId} and fapo.`type` = 0
      and fapo.receipt_status != 2
  </select>
</mapper>