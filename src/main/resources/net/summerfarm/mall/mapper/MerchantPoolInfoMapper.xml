<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MerchantPoolInfoMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MerchantPoolInfo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="create_way" jdbcType="TINYINT" property="createWay"/>
    <result column="update_way" jdbcType="TINYINT" property="updateWay"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="version" jdbcType="INTEGER" property="version"/>
    <result column="creator" jdbcType="VARCHAR" property="creator"/>
    <result column="updater" jdbcType="VARCHAR" property="updater"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
    , `name`, `create_way`, `update_way`, `status`, `remark`, `version`,
    `creator`, `updater`, `create_time`, `update_time`
  </sql>

  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_pool_info
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from
    merchant_pool_info
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="name != null">
        and `name` like CONCAT(#{name},'%')
      </if>
      <if test="creator != null">
        and `creator` like CONCAT(#{creator},'%')
      </if>
      <if test="createWay != null">
        and `create_way` = #{createWay}
      </if>
      <if test="updateWay != null">
        and `update_way` = #{updateWay}
      </if>
      <if test="status != null">
        and `status` = #{status}
      </if>
    </where>
    order by id desc
  </select>

  <select id="listCreators" resultType="string">
    select distinct creator
    from merchant_pool_info
    where creator like CONCAT(#{creator}, '%')
  </select>

  <select id="listByIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_pool_info
    where `id` in
    <foreach collection="list" open="(" separator="," close=")" item="id">
      #{id}
    </foreach>
  </select>

</mapper>