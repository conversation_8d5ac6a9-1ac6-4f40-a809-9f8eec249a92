<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.RechargeMapper">

  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Recharge">
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="recharge_no" property="rechargeNo" jdbcType="VARCHAR" />
    <result column="transaction_number" property="transactionNumber" jdbcType="VARCHAR" />
    <result column="m_id" property="mId"  jdbcType="BIGINT" />
    <result column="recharge_num" property="rechargeNum" jdbcType="DECIMAL" />
    <result column="applicant" property="applicant" jdbcType="INTEGER" />
    <result column="handler" property="handler" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="addtime" property="addtime" />
    <result column="updatetime" property="updatetime" />
    <result column="remark" property="remark" jdbcType="VARCHAR"/>
  </resultMap>

  <sql id="base_cloumn">
        id,recharge_no,transaction_number,m_id,recharge_num,recharge_type,applicant,handler,status,addtime,updatetime
    </sql>

  <select id="selectVO" resultType="net.summerfarm.mall.model.vo.RechargeVO">
    SELECT r.id,r.recharge_no rechargeNo,r.m_id mId,r.recharge_num rechargeNum,r.recharge_type rechargeType,r.applicant,r.`handler`,
    r.`status`,r.addtime,r.updatetime,r.remark,m.mname,a1.realname applicantName,a2.realname handlerName
    FROM recharge r
    INNER JOIN merchant m ON r.m_id=m.m_id
    LEFT JOIN admin a1 ON r.applicant = a1.admin_id
    LEFT JOIN admin a2 ON r.handler = a2.admin_id
    <where>
      <if test="rechargeNo != null">
        AND r.recharge_no = #{rechargeNo}
      </if>
    </where>
  </select>

    <insert id="insert" parameterType="net.summerfarm.mall.model.domain.Recharge">
      INSERT INTO
        recharge(recharge_no,m_id,recharge_num,transaction_number,recharge_type,applicant,handler,status,addtime,updatetime,send_coupon,fund_type,level,finance_bank_flowing_water_id)
      VALUES (#{rechargeNo} ,#{mId} ,#{rechargeNum} ,#{transactionNumber} ,#{rechargeType}  ,#{applicant} ,#{handler} ,#{status} ,#{addtime} ,#{updatetime} ,#{sendCoupon},#{fundType},#{level},#{financeBankFlowingWaterId})
    </insert>

  <select id="selectWater" parameterType="long" resultType="decimal">
    select ifnull(sum(recharge_num), 0)
    FROM recharge
    where finance_bank_flowing_water_id = #{financeBankFlowingWaterId}
      and status in (0, 1)
  </select>
</mapper>