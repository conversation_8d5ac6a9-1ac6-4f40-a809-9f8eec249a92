<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.PurchasesConfigMapper">


    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.PurchasesConfig">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER" />
        <result column="area_manage_id" property="areaManageId" jdbcType="INTEGER"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="safe_level" property="safeLevel" jdbcType="INTEGER" />
        <result column="lead_time" property="leadTime" jdbcType="DECIMAL" />
        <result column="supplier_id" property="supplierId" jdbcType="INTEGER"/>
        <result column="stock_rate" property="stockRate" jdbcType="DECIMAL" />
        <result column="cc_admin_id" property="ccAdminId" jdbcType="VARCHAR" />
        <result column="addtime" property="addtime"/>
    </resultMap>

    <sql id="BaseColumn">
        id,type,area_no,area_manage_id,sku,status,safe_level,lead_time,supplier_id,stock_rate,cc_admin_id,addtime,updatetime
    </sql>

    <select id="selectOne" parameterType="net.summerfarm.mall.model.domain.PurchasesConfig" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM purchases_config
        <where>
            <if test="type != null">
                AND type = #{type}
            </if>
            <if test="areaNo != null">
                AND area_no = #{areaNo}
            </if>
            <if test="areaManageId != null">
                AND area_manage_id = #{areaManageId}
            </if>
            <if test="sku != null">
                AND sku = #{sku}
            </if>
        </where>
    </select>

</mapper>