<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.OrderItemMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.OrderItem">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
        <result column="category_id" property="categoryId" jdbcType="INTEGER"/>
        <result column="price" property="price" jdbcType="DECIMAL"/>
        <result column="amount" property="amount" jdbcType="INTEGER"/>
        <result column="maturity" property="maturity" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="picture_path" property="picturePath" jdbcType="VARCHAR"/>
        <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
        <result column="original_price" property="originalPrice"/>
        <result column="suit_id" property="suitId"/>
        <result column="suit_name" property="suitName"/>
        <result column="suit_amount" property="suitAmount"/>
        <result column="_share" property="_share"/>
        <result column="product_type" property="productType"/>
        <result column="actual_total_price" property="actualTotalPrice"/>
    </resultMap>

    <resultMap id="BaseResultMapVO" type="net.summerfarm.mall.model.vo.OrderItemVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="maturity" property="maturity" jdbcType="VARCHAR"/>
        <result column="category_id" property="categoryId" jdbcType="INTEGER"/>
        <result column="price" property="price" jdbcType="DECIMAL"/>
        <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
        <result column="mPrice" property="mPrice"/>
        <result column="type" property="categoryType"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        sku,
        sku_name,
        order_no,
        pd_name,
        category_id,
        price,
        original_price,
        amount,
        maturity,
        weight,
        picture_path,
        add_time,
        suit_id,
        suit_name,
        suit_amount,
        product_type
    </sql>

    <select id="selectOne" resultMap="BaseResultMap">
        select
        t.id, t.sku, t.order_no, t.pd_name, t.category_id, MAX(t.price) price, t.original_price, SUM(t.amount) amount,
        t.maturity, t.weight, t.picture_path, t.add_time, t.suit_id, t.suit_name, t.suit_amount,t.status,product_type,t.actual_total_price
        <if test="areaNo != null">
            ,ak.share _share
        </if>
        FROM order_item t

        <if test="areaNo != null">
            left join area_sku ak on t.sku=ak.sku and ak.area_no=#{areaNo}
        </if>
        where t.order_no =#{orderNo} AND t.sku = #{sku}
        <if test="suitId != null">
            and t.suit_id =#{suitId}
        </if>
        limit 1
    </select>

    <select id="selectTimingOrderQuantity" parameterType="java.lang.String" resultType="java.lang.Integer">
        select IFNULL(SUM(oi.amount), 0) quantity
        FROM order_item oi
        WHERE oi.category_id <![CDATA[<>]]> 3
          AND oi.order_no = #{orderNo}
    </select>

    <select id="selectDayLimiteds" resultType="net.summerfarm.mall.model.vo.LimitedSaleVO">
        SELECT c.sku,SUM(IFNULL(c.amount, 0)) quantity, ak.sales_mode salesMode,ak.limited_quantity limitedQuantity
        from order_item c
                 INNER JOIN orders o on c.order_no = o.order_no
                and o.order_time >= #{startTime}
                AND o.order_time  <![CDATA[<]]> #{endTime}
                AND o.status in (1, 3, 6)
                AND o.m_id = #{mId}
                 left join area_sku ak on ak.sku = c.sku
        where ak.area_no = #{areaNo}
          and ak.sales_mode = 2
          and c.status in (2, 3, 6)
          and o.type in (0,1,3,30)
        GROUP BY c.sku
    </select>

    <select id="selectTimingItem" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM order_item oi
        WHERE oi.order_no = #{orderNo}
    </select>

    <select id="selectOrderItemId" resultMap="BaseResultMap" parameterType="long">
        SELECT
            oi.id,
            oi.sku,
            oi.order_no,
            oi.pd_name,
            oi.category_id,
            oi.price,
            oi.amount,
            oi.maturity,
            oi.weight,
            p.picture_path,
            oi.add_time,
            oi.status,
            oi.suit_id,
            oi.original_price
        FROM order_item oi
                 LEFT JOIN inventory i on i.sku = oi.sku
                 LEFT JOIN products p on i.pd_id = p.pd_id
        WHERE oi.id = #{orderItemId}
    </select>

    <select id="selectOrderItem" resultType="net.summerfarm.mall.model.vo.OrderItemVO" parameterType="java.lang.String">
        SELECT oi.id,
               oi.sku,
               oi.order_no       orderNo,
               oi.pd_name        pdName,
               oi.category_id    categoryId,
               oi.price,
               oi.original_price originalPrice,
               oi.amount,
               oi.maturity,
               oi.weight,
               oi.picture_path   picturePath,
               oi.add_time       addTime,
               oi.suit_id        suitId,
               oi.suit_name      suitName,
               oi.suit_amount    suitAmount,
               c.type,
               oi.status,
               oi.product_type   productType,
               oi.weight_num weightNum,
               oi.actual_total_price actualTotalPrice
        FROM order_item oi
                 LEFT JOIN category c on oi.category_id = c.id
        WHERE oi.order_no = #{orderNo}
    </select>


    <select id="selectOrderItemUsable" resultType="net.summerfarm.mall.model.domain.OrderItem"
            parameterType="java.lang.String">
        SELECT oi.id,
               oi.sku,
               oi.order_no       orderNo,
               oi.pd_name        pdName,
               oi.category_id    categoryId,
               oi.price,
               oi.original_price originalPrice,
               oi.amount,
               oi.maturity,
               oi.weight,
               oi.picture_path   picturePath,
               oi.add_time       addTime,
               oi.suit_id        suitId,
               oi.suit_name      suitName,
               oi.suit_amount    suitAmount,
               c.type,
               oi.status,
               oi.product_type   productType
        FROM order_item oi
                 LEFT JOIN category c on oi.category_id = c.id
                 INNER JOIN inventory i on oi.sku = i.sku

        WHERE oi.order_no = #{orderNo}
          and oi.status in (2, 3, 6)
          and oi.sku !='DF001TF0001'
    </select>

    <select id="selectOrderItemSuitId" resultType="net.summerfarm.mall.model.vo.OrderItemVO">
        SELECT oi.id, oi.sku, oi.order_no orderNo, oi.pd_name pdName, oi.category_id categoryId, oi.price,
        oi.original_price originalPrice, oi.amount, oi.maturity, oi.weight, oi.picture_path picturePath, oi.add_time
        addTime, oi.suit_id suitId, oi.suit_name suitName, oi.suit_amount suitAmount,c.type,oi.status,oi.use_coupon useCoupon,
        oi.product_type   productType, oi.actual_total_price actualTotalPrice

        FROM order_item oi
        LEFT JOIN category c on oi.category_id=c.id
        WHERE oi.order_no = #{orderNo}
        <if test="suitId != null">
            and oi.suit_id=#{suitId}
        </if>
        <if test="sku != null">
            AND oi.sku=#{sku}
        </if>
        <if test="productType != null">
            and oi.product_type=#{productType}
        </if>
    </select>

    <select id="selectOrderItemByArea" resultType="net.summerfarm.mall.model.domain.OrderItem">
        SELECT oi.amount,
               oi.sku,
               oi.suit_id suitId,
               oi.id,
               a.share    _share,oi.status,
               oi.product_type productType
    FROM
               order_item oi
               LEFT
               JOIN
               area_sku
               a
        on a
           .
           sku =
           oi
           .
           sku
           AND
           a
           .
           area_no =
           #{areaNo}
        WHERE oi.order_no = #{orderNo}
    </select>
    <!--批量插入-->
    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into order_item (sku, sku_name, pd_name, category_id, order_no, amount, price, original_price, maturity,
        weight,volume,weight_num, picture_path, add_time,storage_location, suit_id, suit_name, suit_amount,
        rebate_type,rebate_number,m_price,use_coupon,max_threshold,product_type,actual_total_price,status,info
        ) values
        <foreach collection="list" separator="," item="item">
            (#{item.sku,jdbcType=VARCHAR}, #{item.skuName,jdbcType=VARCHAR}, #{item.pdName,jdbcType=VARCHAR}, #{item.categoryId},
            #{item.orderNo,jdbcType=VARCHAR}, #{item.amount,jdbcType=INTEGER},
            #{item.price,jdbcType=DECIMAL}, #{item.originalPrice,jdbcType=DECIMAL}, #{item.maturity},
            #{item.weight},#{item.volume},#{item.weightNum}, #{item.picturePath}, now(), #{item.storageLocation},
            #{item.suitId}, #{item.suitName}, #{item.suitAmount}, #{item.rebateType}, #{item.rebateNumber},
            #{item.mPrice},#{item.useCoupon},#{item.maxThreshold},#{item.productType},#{item.actualTotalPrice},#{item.status},#{item.info}
            )
        </foreach>
    </insert>


    <update id="updateStatusById">
        update order_item
        set status = #{status}
        where id = #{id}
    </update>

    <update id="updateStatusByOrderNo">
        update order_item
        set status = #{status}
        where order_no = #{orderNo}
    </update>


    <update id="updateStatusByOrderNoDelivery">
        update order_item
        set status = #{status}
        where order_no = #{orderNo}
          and status = 3
    </update>

    <select id="selectOrderItemVO" resultType="net.summerfarm.mall.model.vo.OrderItemVO" parameterType="java.lang.String">
        SELECT oi.id,
               oi.sku,
               oi.order_no       orderNo,
               oi.pd_name        pdName,
               oi.category_id    categoryId,
               oi.price,
               oi.original_price originalPrice,
               oi.amount,
               oi.maturity,
               oi.weight,
               oi.picture_path   picturePath,
               oi.add_time       addTime,
               oi.suit_id        suitId,
               oi.suit_name      suitName,
               oi.suit_amount    suitAmount,
               c.type,
               oi.status,
               i.type skuType,
               i.pd_id           pdId,
               oi.product_type  productType,
               oi.total_price    totalPrice,
               oi.actual_total_price actualTotalPrice,
               t.tax_rate_value taxRateValue,
               oi.actual_total_price actualTotalPrice,
               c.category itemCategory,
                oi.volume volume,
               oi.info info,
        oi.weight_num weightNum,
        oi.storage_location storageLocation,
        i.unit unit, i.sub_type subType,
        oie.buyer_name buyerName,
        oie.supplier_id supplierId,
        oie.cost skuCost,
        oie.specifications skuPickUpSpecification,
        oie.auto_after_sale_flag autoAfterSaleFlag,
        oie.auto_after_sale_amount autoAfterSaleAmount,
        oie.auto_after_sale_quantity autoAfterSaleQuantity,
        i.after_sale_unit afterSaleUnit
        FROM order_item oi
                 LEFT JOIN category c on oi.category_id = c.id
                 LEFT JOIN inventory i on oi.sku = i.sku
                 LEFT JOIN tax_rate_config t on t.pd_id = i.pd_id
                 LEFT JOIN order_item_extra oie on oi.id = oie.order_item_id
        WHERE oi.order_no = #{orderNo}
    </select>

    <select id="selectOrderItemVOByMaster" resultType="net.summerfarm.mall.model.vo.OrderItemVO"
            parameterType="java.lang.String">
        /*FORCE_MASTER*/
        SELECT oi.id,
               oi.sku,
               oi.order_no                  orderNo,
               oi.pd_name                   pdName,
               oi.category_id               categoryId,
               oi.price,
               oi.original_price            originalPrice,
               oi.amount,
               oi.maturity,
               oi.weight,
               oi.picture_path              picturePath,
               oi.add_time                  addTime,
               oi.suit_id                   suitId,
               oi.suit_name                 suitName,
               oi.suit_amount               suitAmount,
               c.type,
               oi.status,
               i.type                       skuType,
               i.pd_id                      pdId,
               oi.product_type              productType,
               oi.total_price               totalPrice,
               oi.actual_total_price        actualTotalPrice,
               t.tax_rate_value             taxRateValue,
               oi.actual_total_price        actualTotalPrice,
               c.category                   itemCategory,
               oi.volume                    volume,
               oi.info                      info,
               oi.weight_num                weightNum,
               oi.storage_location          storageLocation,
               i.unit                       unit,
               i.sub_type                   subType,
               oie.buyer_name               buyerName,
               oie.supplier_id              supplierId,
               oie.cost                     skuCost,
               oie.specifications           skuPickUpSpecification,
               oie.auto_after_sale_flag     autoAfterSaleFlag,
               oie.auto_after_sale_amount   autoAfterSaleAmount,
               oie.auto_after_sale_quantity autoAfterSaleQuantity,
               i.after_sale_unit            afterSaleUnit
        FROM order_item oi
                 LEFT JOIN category c on oi.category_id = c.id
                 LEFT JOIN inventory i on oi.sku = i.sku
                 LEFT JOIN tax_rate_config t on t.pd_id = i.pd_id
                 LEFT JOIN order_item_extra oie on oi.id = oie.order_item_id
        WHERE oi.order_no = #{orderNo}
    </select>

    <select id="selectOrderItemActingUsable" resultType="java.lang.Integer"
            parameterType="java.lang.String">
        select sum(amount) from order_item where
         order_no = #{orderNo} and  sku !='DF001TF0001'
        <if test="useCoupon != null">
            and use_coupon =#{useCoupon}
        </if>

    </select>

    <select id="selectOrderItemActing" resultType="java.lang.Integer"
            parameterType="java.lang.String">
        select sum(amount) from order_item where
        order_no = #{orderNo} and  sku !='DF001TF0001' and status = 8 and suit_id != 0
        <if test="useCoupon != null">
            and use_coupon =#{useCoupon}
        </if>

    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        id, sku, order_no, pd_name, category_id,  price, original_price,  amount, maturity, weight,
        picture_path, add_time, suit_id, suit_name, suit_amount,product_type
        FROM order_item t where t.order_no =#{orderNo}
        <if test="sku !=null">
            AND t.sku = #{sku}
        </if>
        and t.suit_id=#{suitId}
    </select>

    <select id="selectMaxSku" resultType="net.summerfarm.mall.model.domain.OrderItem">
    select sum(oi.amount) amount,oi.sku,i.pd_id pdId from orders o
      inner join order_item oi on o.order_no = oi.order_no
      inner join inventory i on i.sku = oi.sku
      where oi.status in(2,3,6)  and m_id = #{mId}  and o.area_no = #{areaNo}
    and o.order_time <![CDATA[>=]]> #{startTime} and order_time <![CDATA[<]]> #{endTime}
    group by sku
    order by amount desc
  </select>
  <select id="selectAvgAmount" resultType="int">
    select ifnull(ceil(sum(oi.amount) / count(distinct o.order_no)), 0)
    from order_item oi
      left join orders o on oi.order_no = o.order_no
      left join area a on o.area_no = a.area_no
      left join area_store `as` on `as`.sku = oi.sku and a.parent_no = `as`.area_no
    where o.status in (2, 3, 6)
      and `as`.area_no = #{storeNo}
      and oi.sku = #{sku}
      and o.order_time &gt;= #{startTime}
      and o.order_time &lt; #{endTime}
  </select>
    <select id="selectActivityGmv" resultType="java.math.BigDecimal">
        select sum(totalGmv)
        from (
                 select sum(oi.amount * oi.price) + o.delivery_fee as totalGmv
                 from orders o
                 left join order_item oi on o.order_no = oi.order_no
                 where oi.status in (2, 3, 6) and o.status != 12
                   and order_time between #{startTime}
                     and #{endTime}
                   and m_id = #{mId}
                 group by oi.order_no
             ) a
    </select>

    <select id="selectListByOrderNos" resultType="net.summerfarm.mall.model.domain.OrderItem" parameterType="java.lang.String">
        select <include refid="Base_Column_List"></include>
        from order_item where order_no in
        <foreach collection="list" item="item" index="" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateOrderItem" parameterType="net.summerfarm.mall.model.domain.OrderItem">
        update order_item
        <set>
            <if test="price != null">
                price =#{price},
            </if>
            <if test="originalPrice != null">
                original_price =#{originalPrice},
            </if>
            <if test="useCoupon != null">
                use_coupon = #{useCoupon}
            </if>
        </set>
         where id =#{id}
    </update>
    <update id="updateByOrderNoSelective">
        update order_item set price = #{price},actual_total_price = #{actualTotalPrice} where order_no = #{orderNo}
    </update>


    <!--批量插入-->
    <insert id="insertBatchAOL">
        insert into order_item (sku, pd_name, category_id, order_no, amount, price, original_price, maturity,
        weight,volume,weight_num, picture_path, add_time,
        storage_location, suit_id, suit_name, suit_amount,rebate_type,rebate_number,m_price,use_coupon,max_threshold,status,product_type
        ) values
        <foreach collection="list" separator="," item="item">
            (#{item.sku,jdbcType=VARCHAR}, #{item.pdName,jdbcType=VARCHAR}, #{item.categoryId},
            #{item.orderNo,jdbcType=VARCHAR}, #{item.amount,jdbcType=INTEGER},
            #{item.price,jdbcType=DECIMAL}, #{item.originalPrice,jdbcType=DECIMAL}, #{item.maturity},
            #{item.weight},#{item.volume},#{item.weightNum}, #{item.picturePath}, now(), #{item.storageLocation},
            #{item.suitId}, #{item.suitName}, #{item.suitAmount}, #{item.rebateType}, #{item.rebateNumber},
            #{item.mPrice},#{item.useCoupon},#{item.maxThreshold},#{item.status},#{item.productType}
            )
        </foreach>
    </insert>
  <insert id="insertSingle" keyProperty="id" keyColumn="id" parameterType="net.summerfarm.mall.model.domain.OrderItem" useGeneratedKeys="true">
    insert into order_item (sku, pd_name, category_id, order_no, amount, price, original_price, maturity,
    weight,volume,weight_num, picture_path, add_time,
    storage_location, suit_id, suit_name, suit_amount,rebate_type,rebate_number,
    m_price,use_coupon,max_threshold,product_type,actual_total_price,info
    ) value
      (#{sku,jdbcType=VARCHAR}, #{pdName,jdbcType=VARCHAR}, #{categoryId},
      #{orderNo,jdbcType=VARCHAR}, #{amount,jdbcType=INTEGER},
      #{price,jdbcType=DECIMAL}, #{originalPrice,jdbcType=DECIMAL}, #{maturity},
      #{weight},#{volume},#{weightNum}, #{picturePath}, now(), #{storageLocation},
      #{suitId}, #{suitName}, #{suitAmount}, #{rebateType}, #{rebateNumber},
      #{mPrice},#{useCoupon},#{maxThreshold},#{productType},#{actualTotalPrice},#{info}
      )
  </insert>

  <select id="selectByOrderNos" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from order_item
    where order_no in
            <foreach item="item" index="index" collection="orderNos"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
  </select>

    <select id="selectOrderItemByAfter" resultType="net.summerfarm.mall.model.domain.OrderItem">
        SELECT oi.id,
               oi.sku,
               oi.order_no       orderNo,
               oi.pd_name        pdName,
               oi.category_id    categoryId,
               oi.price,
               oi.original_price originalPrice,
               oi.amount,
               oi.maturity,
               oi.weight,
               oi.picture_path   picturePath,
               oi.add_time       addTime,
               oi.suit_id        suitId,
               oi.suit_name      suitName,
               oi.suit_amount    suitAmount,
               c.type,
               oi.status,
               oi.product_type   productType,
               oi.actual_total_price actualTotalPrice
        FROM order_item oi
                 LEFT JOIN category c on oi.category_id = c.id
                 INNER JOIN inventory i on oi.sku = i.sku
        WHERE oi.order_no = #{orderNo}
          and oi.status in (2, 3, 6)
          and oi.sku !='DF001TF0001'
        <if test="sku != null">
            and oi.sku = #{sku}
        </if>
        <if test="suitId != null">
            and oi.suit_id = #{suitId}
        </if>
        <if test="productType != null">
            and oi.product_type = #{productType}
        </if>
        limit 1
    </select>
    <select id="selectSkuListToday" resultType="java.lang.String">
    select distinct sku from orders o
      inner join order_item oi on o.order_no = oi.order_no
      where  m_id = #{mId}
    and o.order_time BETWEEN CONCAT(CURDATE(),' 00:00:00') AND CONCAT(CURDATE(),' 23:59:59')
    </select>
    <select id="selectAccurateDelivery" resultType="java.lang.Integer">
       select  count(1) from  order_item where sku = 'DF001TF0001' and order_no = #{orderNo}
    </select>

    <select id="listDataByDeliveryDateAndStatusList" resultMap="BaseResultMap">
        SELECT sku, order_no, amount
        FROM order_item
        where order_no in
        <foreach collection="orderNoList" open="(" separator="," close=")" item="orderNo">
            #{orderNo}
        </foreach>
    </select>
    <select id="selectByOrderNoNew" resultType="net.summerfarm.mall.model.vo.OrderItemVO">
        select
        t.id, t.sku, t.order_no as orderNo, t.pd_name, t.category_id, MAX(t.price) price, t.original_price, SUM(t.amount) amount,
        t.maturity, t.weight, t.picture_path, t.add_time, t.suit_id, t.suit_name, t.suit_amount,t.status,product_type,t.actual_total_price,
        o.m_id mId, o.account_id accountId,t.product_type productType, o.type
        <if test="areaNo != null">
            ,ak.share _share
        </if>
        FROM order_item t
        LEFT JOIN orders o ON t.order_no = o.order_no
        <if test="areaNo != null">
            left join area_sku ak on t.sku=ak.sku and ak.area_no=#{areaNo}
        </if>
        where t.order_no =#{orderNo} AND t.sku = #{sku}
        <if test="suitId != null">
            and t.suit_id =#{suitId}
        </if>
        limit 1
    </select>

    <select id="selectTiming" resultMap="BaseResultMap">
        SELECT
        oi.id,
        oi.sku,
        oi.order_no,
        oi.pd_name,
        oi.category_id,
        oi.price,
        oi.amount,
        oi.maturity,
        oi.weight,
        p.picture_path,
        oi.add_time,
        oi.status,
        oi.suit_id,
        i.sub_type subType
        FROM order_item oi
        LEFT JOIN inventory i on i.sku = oi.sku
        LEFT JOIN products p on i.pd_id = p.pd_id
        WHERE oi.order_no = #{orderNo}
        AND oi.category_id <![CDATA[<>]]> 3
    </select>

    <select id="getCommonOrderExportList" resultType="net.summerfarm.mall.model.vo.OrderItemVO">
        SELECT oi.order_no orderNo, o.order_time orderTime, o.m_id mId, m.mname mName, oi.status, oi.sku,
               oi.pd_name pdName, oi.weight, oi.amount, oi.price, oi.actual_total_price actualTotalPrice, o.delivery_fee deliveryFee
        FROM order_item oi
        LEFT JOIN orders o on oi.order_no = o.order_no
        LEFT JOIN merchant m on o.m_id = m.m_id
        where o.m_id = #{mId} and oi.status in (3, 6) AND o.`type` in (0, 2, 3)
        and o.order_pay_type = #{orderPayType} and o.order_time >= #{startTime}
        and #{endTime} > o.order_time
    </select>

    <select id="getTimingOrderExportList" resultType="net.summerfarm.mall.model.vo.OrderItemVO">
        SELECT oi.order_no orderNo, o.order_time orderTime, o.m_id mId, m.mname mName, oi.status, oi.sku, c.province, c.city, c.area, c.address, c.house_number houseNumber,
               oi.pd_name pdName, oi.weight, oi.amount, oi.price, oi.actual_total_price actualTotalPrice, o.delivery_fee deliveryFee,
               dp.delivery_time deliveryTime, dp.status deliveryStatus, dp.quantity, dp.id deliveryId
        FROM `delivery_plan`  dp LEFT JOIN `order_item` oi on oi.`order_no` = dp.`order_no`
        LEFT JOIN contact c on dp.`contact_id` = c.`contact_id`
        LEFT JOIN orders o on dp.`order_no` = o.`order_no` LEFT JOIN `merchant` m on m.`m_id` = o.`m_id`
        where o.m_id = #{mId} and oi.status in (3, 6) AND o.`type` = 1 and dp.status in (3, 6)
        and o.order_time >= #{startTime} and #{endTime} > o.order_time
    </select>

    <select id="getCommonOrderExportListCount" resultType="int">
        SELECT count(0)
        FROM order_item oi
                 LEFT JOIN orders o on oi.order_no = o.order_no
                 LEFT JOIN merchant m on o.m_id = m.m_id
        where o.m_id = #{mId} and oi.status in (3, 6) AND o.`type` in (0, 2, 3)
          and o.order_pay_type = #{orderPayType} and o.order_time >= #{startTime}
          and #{endTime} > o.order_time
    </select>

    <select id="getTimingOrderExportListCount" resultType="int">
        SELECT count(0)
        FROM `delivery_plan`  dp LEFT JOIN `order_item` oi on oi.`order_no` = dp.`order_no`
                                 LEFT JOIN contact c on dp.`contact_id` = c.`contact_id`
                                 LEFT JOIN orders o on dp.`order_no` = o.`order_no` LEFT JOIN `merchant` m on m.`m_id` = o.`m_id`
        where o.m_id = #{mId} and oi.status in (3, 6) AND o.`type` = 1 and dp.status in (3, 6)
          and o.order_time >= #{startTime} and #{endTime} > o.order_time
    </select>

    <select id="selectByOrderNo" resultMap="BaseResultMapVO">
        select oi.sku,oi.amount,oi.weight,oi.pd_name,oi.id,oi.add_time,amount*price mPrice,c.type type,oi.product_type productType,oi.suit_id suitId
        from order_item oi
                 LEFT JOIN orders o ON oi.order_no = o.order_no
                 LEFT JOIN category c ON c.id = oi.category_id
        where o.`status` IN (2, 3, 6)  AND oi.status != 8 and oi.order_no =#{orderNo} and oi.sku != 'DF001TD0001'
    </select>

    <select id="selectByEntity" resultType="net.summerfarm.mall.model.vo.OrderItemVO">
        SELECT oi.order_no orderNo, oi.sku, oi.suit_id suitId, o.m_id mId, o.account_id accountId, oi.product_type productType
        FROM order_item oi
        LEFT JOIN orders o on oi.order_no = o.order_no
        where oi.order_no = #{orderNo} and oi.sku = #{sku} limit 1
    </select>
    <select id="getItemListBySkus" resultType="net.summerfarm.mall.model.domain.OrderItem">
        SELECT oi.id, oi.sku, oi.order_no orderNo, oi.pd_name pdName, oi.category_id categoryId, oi.price,
        oi.original_price originalPrice, oi.amount, oi.maturity, oi.weight, oi.picture_path picturePath, oi.add_time
        addTime, oi.suit_id suitId, oi.suit_name suitName, oi.suit_amount suitAmount,oi.status,oi.use_coupon useCoupon,
        oi.product_type   productType, oi.actual_total_price actualTotalPrice
        FROM order_item oi
        where oi.order_no = #{orderNo} and oi.sku in
        <foreach collection="skuList" open="(" separator="," close=")" item="sku">
            #{sku}
        </foreach>
    </select>
    <select id="selectOrderItemVOForList" resultType="net.summerfarm.mall.model.vo.OrderItemVO">
        SELECT oi.id,
               oi.sku,
               oi.order_no       orderNo,
               oi.pd_name        pdName,
               oi.category_id    categoryId,
               oi.price,
               oi.original_price originalPrice,
               oi.amount,
               oi.maturity,
               oi.weight,
               oi.picture_path   picturePath,
               oi.add_time       addTime,
               oi.suit_id        suitId,
               oi.suit_name      suitName,
               oi.suit_amount    suitAmount,
               c.type,
               oi.status,
               i.type skuType,
               i.pd_id           pdId,
               oi.product_type  productType,
               oi.total_price    totalPrice,
               oi.actual_total_price actualTotalPrice,
               t.tax_rate_value taxRateValue,
               oi.actual_total_price actualTotalPrice,
               c.category itemCategory,
                oi.volume volume,
        oi.weight_num weightNum,
        oi.storage_location storageLocation,
        i.unit unit,
        oi.add_time addTime
        FROM order_item oi
                 LEFT JOIN category c on oi.category_id = c.id
                 LEFT JOIN inventory i on oi.sku = i.sku
                 LEFT JOIN tax_rate_config t on t.pd_id = i.pd_id
        WHERE oi.order_no in
        <foreach item="item" index="index" collection="orderNos"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectByOrderNoNewPro" resultType="net.summerfarm.mall.model.vo.OrderItemVO">
        select oi.sku,oi.amount,oi.weight,oi.pd_name pdName,i.unit ,o.account_id accountId,oi.suit_id suitId,
               o.m_id mId, o.account_id accountId,oi.order_no orderNo,i.type skuType,ad.name_remakes nameRemakes,
               p.picture_path skuPic,c.type categoryType,oi.add_time addTime,oi.id,oi.product_type productType
        from order_item oi
                 LEFT JOIN orders o ON oi.order_no = o.order_no
                 LEFT JOIN inventory i on i.sku = oi.sku
                 left join products p on p.pd_id = i.pd_id
                 LEFT JOIN admin ad on i.admin_id = ad.admin_id
                 left join category c on c.id = p.category_id
        where o.`status` IN (2, 3, 6)  AND oi.status != 8 and oi.order_no = #{orderNo} and oi.sku != 'DF001TD0001'
    </select>
    <select id="selectOrderItemList" resultType="net.summerfarm.mall.model.vo.OrderItemVO">
        SELECT
            oi.id,
            oi.sku,
            oi.order_no orderNo,
            oi.pd_name  pdName,
            oi.category_id categoryId,
            oi.price,
            oi.amount,
            oi.maturity,
            oi.weight,
            p.picture_path picturePath,
            oi.add_time addTime,
            oi.status,
            oi.suit_id suitId,
            i.sub_type subType
        FROM order_item oi
                 LEFT JOIN inventory i on i.sku = oi.sku
                 LEFT JOIN products p on i.pd_id = p.pd_id
        WHERE oi.category_id <![CDATA[<>]]> 3 AND
        oi.order_no in
        <foreach item="item" index="index" collection="orderNos"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectListByItemIds" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select <include refid="Base_Column_List"></include>
        from order_item where id in
        <foreach collection="list" item="item" index="" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
