<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.DeliveryPlanMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.DeliveryPlan">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="delivery_time" property="deliveryTime" jdbcType="DATE"/>
        <result column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="master_order_no" property="masterOrderNo" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="contact_id" property="contactId" jdbcType="BIGINT"/>
        <result column="deliverytype" property="deliverytype" jdbcType="BIT"/>
        <result column="time_frame" property="timeFrame" jdbcType="VARCHAR"/>
        <result column="order_store_no" property="orderStoreNo" jdbcType="INTEGER"/>
        <result column="intercept_flag" property="interceptFlag" jdbcType="INTEGER"/>
        <result column="old_delivery_time" property="oldDeliveryTime" jdbcType="DATE"/>
        <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        order_no,
        status,
        delivery_time,
        quantity,
        master_order_no,
        update_time,
        contact_id,
        deliverytype,
        time_frame,
        order_store_no,
        intercept_flag,
        old_delivery_time,
        add_time
    </sql>

    <select id="countWaitPay" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT count(1)
        FROM delivery_plan dp
        WHERE dp.master_order_no is not null
          AND dp.status = 1
          AND dp.order_no = #{orderNo}
    </select>

    <select id="selectByMasterOrderNo" resultType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delivery_plan dp
        WHERE dp.master_order_no = #{masterOrderNo}
    </select>

    <select id="selectMonthly" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        select dp.id,
               dp.delivery_time   deliveryTime,
               dp.master_order_no masterOrderNo,
               dp.order_no        orderNo,
               dp.quantity,
               dp.`status`,
               dp.contact_id      contactId,
               c.address ,
               c.province,
               c.city,
               c.area
        from delivery_plan dp
                 left join orders o on dp.order_no = o.order_no
                 left join contact c on dp.contact_id = c.contact_id
        where
            o.m_id = #{mId}
          and DATE_FORMAT(dp.delivery_time, '%Y-%m') = DATE_FORMAT(#{date}, '%Y-%m')
          and o.type = 1 and o.status != 8 and dp.status != 11
    </select>

    <update id="updateStatus" parameterType="net.summerfarm.mall.model.domain.DeliveryPlan">
        update delivery_plan
        set status = #{status},
        update_time= #{updateTime}
        <if test="deliveryTime !=null">
            ,delivery_time = #{deliveryTime}
        </if>
        where
        order_no = #{orderNo}
        <if test="masterOrderNo !=null">
            or master_order_no = #{masterOrderNo}
        </if>
    </update>

    <update id="updateTimingPlan" parameterType="net.summerfarm.mall.model.domain.DeliveryPlan">
        UPDATE `delivery_plan`
        SET `status` = (
            CASE `delivery_time`
                WHEN #{deliveryTime} THEN 3
                else 2
                END)
        where order_no = #{orderNo}
    </update>


    <update id="deliveryedById" parameterType="net.summerfarm.mall.model.domain.DeliveryPlan">
        update delivery_plan
        set status     = 6,
            update_time= now()
        where
            id = #{id}
    </update>


    <select id="selectAutoConfirm" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT o.order_no orderNo,o.m_id mId,o.type,dp.quantity,dp.id,dp.deliverytype,dp.intercept_flag interceptFlag,dp.order_store_no orderStoreNo,
        dp.delivery_time deliveryTime,dp.contact_id contactId
        from delivery_plan dp
                 LEFT JOIN orders o on o.order_no = dp.order_no
        where dp.delivery_time <![CDATA[<]]> #{dateTime}
          and o.status = 3
          AND dp.status = 3
    </select>


    <select id="selectDeliveredQuantity4Follow" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT IFNULL(sum(quantity), 0)
        FROM delivery_plan dp
        WHERE dp.order_no = #{orderNo}
          AND dp.status in (2, 3, 6)
    </select>


    <select id="selectDeliveredByOrderNo" resultType="java.lang.Integer">
        select IFNULL(sum(quantity), 0) quantity
        from delivery_plan
        where status = 6
          and order_no = #{orderNo}
    </select>

    <insert id="insertBatch" parameterType="net.summerfarm.mall.model.domain.DeliveryPlan" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into delivery_plan (order_no, delivery_time, quantity, status, update_time,contact_id, account_id, order_store_no)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.orderNo,jdbcType=VARCHAR}, #{item.deliveryTime,jdbcType=DATE}, #{item.quantity,jdbcType=INTEGER},
            #{item.status}, now(),#{item.contactId}, #{item.accountId}, #{item.orderStoreNo})
        </foreach>
    </insert>

    <select id="countByDeliveryTime" resultType="java.lang.Integer">
        SELECT count(1) FROM delivery_plan dp
        LEFT JOIN orders o ON dp.order_no = o.order_no
        WHERE o.m_id = #{mId}
        <if test="contactId != null">
            AND dp.contact_id=#{contactId}
        </if>
        AND DATE (dp.delivery_time)= #{deliveryDate} AND dp.status in (2,3) and o.status in (2,3)
    </select>

    <select id="count" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1)
        FROM delivery_plan dp
        WHERE dp.order_no = #{orderNo}
          AND status in (2, 3, 6)
    </select>


    <select id="selectByOrderNo" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT
            dp.id,
            dp.order_no        orderNo,
            dp.status,
            dp.delivery_time   deliveryTime,
            dp.quantity,
            dp.master_order_no masterOrderNo,
            dp.update_time     updateTime,
            dp.contact_id      contactId,
            dp.order_store_no  orderStoreNo,
            c.address          address,
            c.province         province,
            c.city             city,
            c.area             area,
            c.house_number     houseNumber,
            c.contact,
            c.phone,
            dp.contact_id contactId,
            dp.deliverytype    deliverytype,
            dp.intercept_flag interceptFlag
        FROM delivery_plan dp
                 left join contact c on dp.contact_id = c.contact_id
        WHERE dp.order_no = #{orderNo}
          AND dp.status in (2, 3, 6)
    </select>

    <select id="selectByOrderNoNoStatus" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT
            dp.id,
            dp.order_no        orderNo,
            dp.status,
            dp.delivery_time   deliveryTime,
            dp.quantity,
            dp.master_order_no masterOrderNo,
            dp.update_time     updateTime,
            dp.contact_id      contactId,
            dp.order_store_no  orderStoreNo,
            c.address          address
        FROM delivery_plan dp
        left join contact c on dp.contact_id = c.contact_id
        WHERE dp.order_no = #{orderNo}
    </select>

    <insert id="insert" parameterType="net.summerfarm.mall.model.domain.DeliveryPlan" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into delivery_plan (order_no, status,
                                   delivery_time, quantity, update_time, master_order_no, contact_id, time_frame,
                                   deliverytype, account_id, order_store_no, admin_id)
        values (#{orderNo,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
                #{deliveryTime,jdbcType=DATE}, #{quantity,jdbcType=INTEGER}, #{updateTime,jdbcType=DATE},
                #{masterOrderNo}, #{contactId}, #{timeFrame}, #{deliverytype}, #{accountId}, #{orderStoreNo}, #{adminId})
    </insert>

    <select id="getUnLockOrder" resultType="net.summerfarm.mall.model.domain.DeliveryPlan">
      select dp.id,dp.order_no orderNo,dp.delivery_time deliveryTime,quantity
        from delivery_plan dp
        LEFT JOIN  orders o on dp.order_no=o.order_no
        left join merchant m on m.m_id=o.m_id
        LEFT join order_item oi on o.order_no=oi.order_no
        left join area a on m.area_no =a.area_no
        LEFT join (
            select area_no,store_no from fence where   status = 0
            group by area_no, store_no
        ) f on f.area_no = a.area_no
        WHERE  dp.delivery_time = #{deliveryDate}
            and f.store_no = #{storeNo}
            and dp.status=2
            AND oi.sku=#{sku}
        AND o.type=1
    </select>


    <select id="queryById" resultType="net.summerfarm.mall.model.domain.DeliveryPlan">
      select id,order_no orderNo,delivery_time deliveryTime,quantity,contact_id contactId,status
        from delivery_plan
        where id = #{id}

      </select>

    <select id="noticeList" resultType="net.summerfarm.mall.model.domain.DeliveryNotice">
        SELECT msa.openid, c.contact, c.phone, oi.pd_name pdName, dp.quantity, dp.delivery_time deliveryTime, o.order_no
        FROM delivery_plan dp
        INNER JOIN orders o ON dp.order_no = o.order_no
            AND o.type = 1
            AND dp.delivery_time = #{delivertTime}
            AND dp.status IN (2,3,6)
            AND o.status IN (2,3,6)
        INNER JOIN order_item oi ON o.order_no = oi.order_no
        INNER JOIN contact c ON dp.contact_id = c.contact_id
        INNER JOIN merchant_sub_account msa ON o.m_id = msa.m_id
        where msa.status = 1 and msa.delete_flag = 1
    </select>

    <select id="timingOrderPlans" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT dp.id,dp.delivery_time deliveryTime, dp.status, dp.quantity,concat(c.province, c.city, c.area, c.address) address, msa.contact, msa.phone
             ,dp.old_delivery_time oldDeliveryTime,dp.contact_id contactId ,dp.order_store_no orderStoreNo,dp.delivery_evaluation_status deliveryEvaluationStatus
        FROM delivery_plan dp
        LEFT JOIN contact c ON dp.contact_id = c.contact_id
        LEFT JOIN merchant_sub_account msa ON dp.account_id = msa.account_id
        WHERE dp.order_no = #{orderNo}
        AND dp.status IN (1,2,3,6)
    </select>

    <delete id="deleteById" parameterType="java.lang.Integer">
        DELETE
        FROM delivery_plan
        WHERE id = #{id}
    </delete>

    <update id="updateQuantity" parameterType="net.summerfarm.mall.model.domain.DeliveryPlan">
        UPDATE delivery_plan
        SET quantity = #{quantity}
        WHERE id = #{id}
    </update>

    <update id="updateStatusById" parameterType="net.summerfarm.mall.model.domain.DeliveryPlan">
        UPDATE delivery_plan
        SET status = #{status}
        WHERE id = #{id}
    </update>

    <update id="updateContactIdById" parameterType="net.summerfarm.mall.model.domain.DeliveryPlan">
        UPDATE delivery_plan
        SET contact_id = #{contactId,jdbcType=BIGINT}
        WHERE id = #{id}
    </update>

    <select id="noticeLists" resultType="net.summerfarm.mall.model.domain.DeliveryNotice">
        SELECT msa.openid, concat(c.province,c.city,c.area,c.address) contact, c.phone, dp.quantity,
               dp.delivery_time deliveryTime, o.order_no orderNo,dp.order_store_no orderStoreNo
        FROM delivery_plan dp
        INNER JOIN orders o ON dp.order_no = o.order_no
        AND dp.delivery_time = #{delivertTime}
        AND dp.status IN (2,3,6)
        AND o.status IN (2,3,6)
        AND o.type != 3
        INNER JOIN contact c ON dp.contact_id = c.contact_id
        INNER JOIN merchant_sub_account msa ON o.m_id = msa.m_id and msa.delete_flag = 1 and msa.status = 1
        INNER JOIN merchant m on m.m_id = o.m_id and m.area_no not in (2836,2250,4722,7524,11031,15655,16536,6001,13351)
    </select>

    <select id="countByMasterOrderNo" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1)
        FROM delivery_plan dp
        WHERE dp.master_order_no = #{masterOrderNo}
          AND status in (2, 3, 6)
    </select>

    <select id="selectById"  resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM delivery_plan
        WHERE id = #{id}
    </select>
    <update id="updateTimingPlanLock" parameterType="net.summerfarm.mall.model.domain.DeliveryPlan">
        UPDATE `delivery_plan`
        SET `status` = (
            CASE
                WHEN `delivery_time` <![CDATA[<= ]]>#{deliveryTime} THEN 3
                else 2
                END)
        where order_no = #{orderNo}
    </update>

    <update id="updateDeliveryDate">
        update delivery_plan set delivery_time = #{deliveryDate} where id = #{id}
    </update>
    <update id="updateById" parameterType="net.summerfarm.mall.model.domain.DeliveryPlan" >
        update delivery_plan
        <set >
            <if test="status != null" >
                status = #{status},
            </if>
            <if test="deliveryTime != null" >
                delivery_time = #{deliveryTime},
            </if>
            <if test="quantity != null" >
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="contactId !=null">
                contact_id=#{contactId},
            </if>
            <if test="orderStoreNo != null">
                order_store_no=#{orderStoreNo},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByMasterOrderNoVO" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT
            dp.id,
            dp.order_no        orderNo,
            dp.status,
            dp.delivery_time   deliveryTime,
            dp.quantity,
            dp.master_order_no masterOrderNo,
            dp.update_time     updateTime,
            dp.contact_id      contactId,
            dp.order_store_no  orderStoreNo,
            c.address          address,
            dp.contact_id contactId
        FROM delivery_plan dp
                 left join contact c on dp.contact_id = c.contact_id
        WHERE dp.master_order_no = #{orderNo}
          AND dp.status in (2, 3, 6)
    </select>

    <select id="selectByMasterOrderNoVOByNoStatus" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT
            dp.id,
            dp.order_no        orderNo,
            dp.status,
            dp.delivery_time   deliveryTime,
            dp.quantity,
            dp.master_order_no masterOrderNo,
            dp.update_time     updateTime,
            dp.contact_id      contactId,
            dp.order_store_no  orderStoreNo,
            c.address          address,
            dp.contact_id contactId
        FROM delivery_plan dp
                 left join contact c on dp.contact_id = c.contact_id
        WHERE dp.master_order_no = #{orderNo}
    </select>

    <select id="selectNoStatusByOrderNo" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT
            dp.id,
            dp.order_no        orderNo,
            dp.status,
            dp.delivery_time   deliveryTime,
            dp.quantity,
            dp.master_order_no masterOrderNo,
            dp.update_time     updateTime,
            dp.contact_id      contactId,
            dp.order_store_no  orderStoreNo,
            c.address          address,
            dp.contact_id contactId
        FROM delivery_plan dp
                 left join contact c on dp.contact_id = c.contact_id
        WHERE dp.order_no = #{orderNo}
    </select>

<!--auto generated by MybatisCodeHelper on 2022-02-25-->
    <select id="selectWaitByContact" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from delivery_plan
        where `status` in (1, 2, 3, 12, 6) and delivery_time <![CDATA[>]]>  NOW() and contact_id = #{contactId}
    </select>

    <select id="countByOrderNo" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*)
        from delivery_plan
        where order_no = #{orderNo} and status =3
    </select>

    <select id="selectByDeliveryPlan" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT o.order_no orderNo,o.m_id mId,o.type,dp.quantity,dp.id,dp.deliverytype
        from delivery_plan dp
        LEFT JOIN orders o on o.order_no = dp.order_no
        where dp.order_no = #{orderNo}
          and o.status = 3
          AND dp.status = 3
    </select>
    <select id="selectByOrderNoIntercept" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
         SELECT
            dp.id,
            dp.order_no        orderNo,
            dp.status,
            dp.delivery_time   deliveryTime,
            dp.quantity,
            dp.master_order_no masterOrderNo,
            dp.update_time     updateTime,
            dp.contact_id      contactId,
            dp.order_store_no  orderStoreNo,
            c.address          address,
            dp.intercept_flag interceptFlag,
            dp.delivery_evaluation_status deliveryEvaluationStatus
        FROM delivery_plan dp
                 left join contact c on dp.contact_id = c.contact_id
        WHERE dp.order_no = #{orderNo}
          AND dp.status in (2, 3, 6,8)
    </select>

    <select id="selectByOrderNoAndContactId" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT
        dp.id,dp.intercept_flag InterceptFlag, dp.order_no orderNo, dp.status, dp.delivery_time deliveryTime, dp.quantity, dp.contact_id contactId, dp.order_store_no orderStoreNo,
        con.contact mcontact,con.phone mphone, concat(con.province,con.city,con.area,con.address,ifnull(con.house_number,'')) address,dp.time_frame timeFrame,dp.deliverytype
        ,dp.old_delivery_time as oldDeliveryTime
        FROM delivery_plan dp
        LEFT JOIN contact con on dp.contact_id=con.contact_id
        WHERE dp.order_no = #{orderNo} and dp.status in (2,3,6)
        <if test="contactId != null">
            and dp.contact_id = #{contactId}
        </if>
    </select>
    <select id="selectByDeliveryTimeAndContactId" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT
            dp.id,
            dp.order_no        orderNo,
            dp.status,
            dp.delivery_time   deliveryTime,
            dp.quantity,
            dp.master_order_no masterOrderNo,
            dp.update_time     updateTime,
            dp.contact_id      contactId,
            dp.order_store_no  orderStoreNo,
            dp.contact_id contactId
        FROM delivery_plan dp
        where dp.delivery_time = #{deliveryTime} and dp.contact_id = #{contactId} and dp.id != #{id} and dp.status in (2,3,6)
    </select>

    <select id="selectDeliveryPlanOnlyByOrderNo" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT
        dp.id,
        dp.order_no        orderNo,
        dp.status,
        dp.delivery_time   deliveryTime,
        dp.quantity,
        dp.master_order_no masterOrderNo,
        dp.update_time     updateTime,
        dp.contact_id      contactId,
        dp.order_store_no  orderStoreNo,
        dp.contact_id contactId,
        dp.deliverytype    deliverytype,
        dp.intercept_flag interceptFlag,
        dp.old_delivery_time oldDeliveryTime
        FROM delivery_plan dp
        WHERE dp.order_no = #{orderNo}
    </select>

    <select id="listDataByDeliveryDateAndStatusList" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT
        dp.order_no        orderNo,
        dp.delivery_time   deliveryTime,
        dp.contact_id      contactId,
        dp.status,
        dp.deliverytype,
        dp.order_store_no  orderStoreNo
        FROM delivery_plan dp
        where dp.delivery_time = #{deliveryTime}
        and dp.status in
        <foreach collection="statusList" open="(" separator="," close=")" item="status">
            #{status}
        </foreach>
        limit #{pageIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <select id="countDataByDeliveryDateAndStatusList" resultType="int">
        SELECT count(*)
        FROM delivery_plan dp
        where dp.delivery_time = #{deliveryTime}
        and dp.status in
        <foreach collection="statusList" open="(" separator="," close=")" item="status">
            #{status}
        </foreach>
    </select>
    <select id="selectByDeliverTime" resultType="net.summerfarm.mall.model.domain.DeliveryPlan">
        select
        id, order_no orderNo, status, delivery_time deliveryTime, quantity,contact_id contactId
    from delivery_plan
    where order_no =#{orderNo} and delivery_time = #{deliveryTime} and contact_id = #{contactId}
    </select>
    <update id="updateDeliveryEvalutionStatus" parameterType="net.summerfarm.mall.model.domain.DeliveryPlan">
        update delivery_plan
        set delivery_evaluation_status = #{deliveryEvaluationStatus},
        update_time= now()
        where id in
        <foreach collection="updateDeliveryPlans" open="(" separator="," close=")" item="plan">
           #{plan.id}
        </foreach>
    </update>
    <update id="orderSelfPickup">
        update delivery_plan
        set status = 6,deliverytype = 1,delivery_time= #{deliveryDate}
        where
        order_no = #{orderNo} and status = 3
        <if test="oldDeliveryDate !=null">
            and delivery_time = #{oldDeliveryDate}
        </if>
        <if test="contactId !=null">
            and contact_id = #{contactId}
        </if>
    </update>
    <select id="countByDeliveryStatus" resultType="java.lang.Integer">
        SELECT count(1) FROM delivery_plan dp
        where dp.contact_id=#{contactId}
        AND  dp.status in (3)
    </select>

    <select id="listByOrderNos" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT
            dp.id,
            dp.order_no        orderNo,
            dp.status,
            dp.delivery_time   deliveryTime,
            dp.quantity,
            dp.master_order_no masterOrderNo,
            dp.update_time     updateTime,
            dp.contact_id      contactId,
            dp.order_store_no  orderStoreNo,
            dp.deliverytype    deliverytype,
            dp.intercept_flag interceptFlag
        FROM delivery_plan dp
        WHERE dp.order_no in
              <foreach collection="orderNos" open="(" item="orderNo" close=")" separator=",">
                  #{orderNo}
              </foreach>
          AND dp.status in (2, 3, 6)
    </select>

    <update id="updateStatusBatch">
        update delivery_plan
        set status = #{status}
        where id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="getSomeTimeUnLockPlan" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        select dp.id,oi.sku, dp.quantity,o.order_no orderNo,m.mname,oi.pd_name pdName,dp.contact_id contactId,
        o.type,oi.weight,dp.order_store_no storeNo,dp.delivery_time deliveryTime,o.area_no areaNo,dp.order_store_no orderStoreNo
        from delivery_plan dp
        LEFT JOIN orders o on dp.order_no=o.order_no
        left join merchant m on m.m_id=o.m_id
        LEFT join order_item oi on o.order_no=oi.order_no
        left join area a on m.area_no =a.area_no
        left join warehouse_inventory_mapping wim on wim.store_no = dp.order_store_no and wim.sku = oi.sku
        WHERE dp.delivery_time <![CDATA[>]]> #{startDate}
        AND dp.delivery_time <![CDATA[<=]]> #{endDate}
        AND dp.status= #{status}
        AND o.type=1
        <if test="sku != null">
            AND oi.sku=#{sku}
        </if>
        <if test="storeNo != null">
            AND dp.order_store_no = #{storeNo}
        </if>
        <if test="trustStoreNo != null">
            AND wim.warehouse_no = #{trustStoreNo}
        </if>
        order by dp.delivery_time, dp.id
    </select>

    <select id="queryDeliveryTimeByOrderNo" resultMap="BaseResultMap">
        select dp.delivery_time deliveryTime,dp.order_no orderNo,dp.quantity,dp.deliverytype from delivery_plan dp
        inner join orders o on o.order_no = dp.order_no and o.type in (0,3,12,30) and dp.order_no = #{orderNo}
    </select>

    <select id="selectDeliveryPlanBeforeOrderNo" resultType="net.summerfarm.mall.model.domain.DeliveryPlan">
    select
        dp.id, dp.order_no orderNo, dp.status, dp.delivery_time deliveryTime, dp.quantity, dp.contact_id contactId
    from delivery_plan dp
    left join orders o on dp.order_no=o.order_no
    where o.type in (0, 30) and  dp.order_no != #{orderNo} and dp.delivery_time = #{deliveryDate} and dp.contact_id = #{contactId}
    </select>
    <select id="selectTimingByOrderNoAndDateAndContactId"
            resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">

        select dp.id,
            dp.order_no orderNo,
            dp.status,
            delivery_time deliveryTime,
            quantity,
            deliverytype,
            p.pd_name pdName,
            i.weight
        from delivery_plan dp
            left join orders o on dp.order_no = o.order_no
            left join order_item oi on o.order_no = oi.order_no
            left join inventory i on oi.sku = i.sku
            left join products p on i.pd_id = p.pd_id
        where o.type = 1
            and o.status in (2, 3, 6)
            and dp.status in (2, 3, 6)
            and dp.order_no = #{orderNo}
            and dp.delivery_time = #{deliveryTime}
            and dp.contact_id = #{contactId}
    </select>

    <select id="selectDeliveryEvaluation" resultType="net.summerfarm.mall.model.domain.DeliveryPlan">
        select
        dp.id, dp.order_no orderNo, dp.status, dp.delivery_time deliveryTime, dp.quantity,dp.contact_id contactId,dp.delivery_evaluation_status deliveryEvaluationStatus
        from delivery_plan dp
        where
        dp.delivery_evaluation_status = 0 and dp.status = 6
        <if test="deliveryDate != null">
            AND dp.delivery_time = #{deliveryDate}
        </if>
        <if test="contactId != null">
            AND dp.contact_id = #{contactId}
        </if>
        <if test="deliveryId != null">
            AND dp.id != #{deliveryId}
        </if>
    </select>

    <select id="selectDeliveryEvaluationList" resultType="net.summerfarm.mall.model.domain.DeliveryPlan">
        select
        dp.id, dp.order_no orderNo, dp.status, dp.delivery_time deliveryTime, dp.quantity,dp.contact_id contactId,dp.delivery_evaluation_status deliveryEvaluationStatus
        from orders o
        left join delivery_plan dp on dp.order_no = o.order_no
        where
        dp.delivery_evaluation_status = 0 and dp.status = 6
        <if test="deliveryDate != null">
            AND dp.delivery_time = #{deliveryDate}
        </if>
        <if test="contactId != null">
            AND dp.contact_id = #{contactId}
        </if>
    </select>
    <select id="selectListByMinDeliveryTime" resultType="net.summerfarm.mall.model.dto.delivery.DeliveryPlanQueryDTO" parameterType="net.summerfarm.mall.client.req.DeliveryPlanQueryReq">
        select
            dp.id, dp.order_no orderNo, dp.status, dp.delivery_time deliveryTime, dp.quantity, dp.contact_id contactId,
            c.m_id mId, dp.order_store_no orderStoreNo, dp.add_time addTime
        from delivery_plan dp
            left join contact c on dp.contact_id = c.contact_id
            left join merchant m on c.m_id = m.m_id
        where dp.delivery_time >= #{minDeliveryTime}
            and c.city = #{city}
            <if test="areaList != null and areaList.size > 0">
                and c.area in
                <foreach collection="areaList" open="(" separator="," close=")" item="area">
                    #{area}
                 </foreach>
            </if>
            <if test="storeNo != null">
                and dp.order_store_no = #{storeNo}
            </if>
            and dp.status = #{status}
            and dp.quantity > 0
            and dp.deliverytype = 0
    </select>
    <select id="selectDeliveryPlanForFulfillmentFinish"
            resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">

        SELECT o.order_no orderNo,o.m_id mId,o.type,dp.quantity,dp.id,dp.deliverytype,dp.intercept_flag interceptFlag,dp.order_store_no orderStoreNo,
        dp.delivery_time deliveryTime,dp.contact_id contactId
        from delivery_plan dp
                 LEFT JOIN orders o on o.order_no = dp.order_no
        where dp.order_no = #{orderNo} AND o.status = 3 AND dp.status = 3
        <if test="deliveryTime != null">
            AND dp.delivery_time = #{deliveryTime}
        </if>
        <if test="contactId != null">
            AND dp.contact_id = #{contactId}
        </if>
    </select>
    <update id="updateStatusBatchByOrderNo">
        update delivery_plan
        set status = #{status}
        where order_no in
        <foreach collection="orderNoList" item="orderNo" separator="," open="(" close=")">
            #{orderNo}
        </foreach>
    </update>
    <select id="selectByOrderNoBatch" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delivery_plan
        where order_no in
        <foreach collection="orderNoList" item="orderNo" separator="," open="(" close=")">
            #{orderNo}
        </foreach>
    </select>

    <select id="queryRecentTwoDeliveryPlan" resultType="net.summerfarm.mall.client.resp.RecentDeliveryPlanResp">
        select dp.order_store_no storeNo,dp.contact_id contactId,dp.delivery_time deliveryTime,dp.order_no orderNo,os.type orderType
        from delivery_plan dp
                 join orders os on dp.order_no = os.order_no
        where os.m_id = #{mId} and DATE_FORMAT(#{orderTime}, '%Y-%m-%d') >= dp.delivery_time and dp.status=6
        limit 2
    </select>
    <select id="selectDeliveredQuantityWithOutUpdate" resultType="java.lang.Integer">
        SELECT IFNULL(sum(quantity), 0)
        FROM delivery_plan dp
        WHERE dp.order_no = #{orderNo}
          AND dp.status in (2, 3, 6)
        <if test="ids != null">
              AND dp.id not in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectByDeliveryPlanIds" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT
            dp.id,
            dp.order_no        orderNo,
            dp.status,
            dp.delivery_time   deliveryTime,
            dp.quantity,
            dp.master_order_no masterOrderNo,
            dp.update_time     updateTime,
            dp.contact_id      contactId,
            dp.order_store_no  orderStoreNo,
            c.address          address,
            c.province         province,
            c.city             city,
            c.area             area,
            c.house_number     houseNumber,
            dp.contact_id contactId,
            dp.deliverytype    deliverytype,
            dp.intercept_flag interceptFlag
        FROM delivery_plan dp
                 left join contact c on dp.contact_id = c.contact_id
        WHERE dp.order_no = #{orderNo}
          AND dp.status in (2, 3, 6)
          AND dp.id in
            <foreach collection="planIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
    </select>

    <select id="selectOne" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM delivery_plan
        WHERE status in (2,3)
        AND order_no=#{orderNo}
        <if test="deliveryTime != null">
            and delivery_time = #{deliveryTime}
        </if>
        <if test="contactId != null">
            AND contact_id=#{contactId}
        </if>
        limit 1
    </select>

    <update id="updateDeliveryTimeAndOldDate">
        UPDATE delivery_plan dp SET dp.delivery_time=#{deliveryTime},dp.contact_id=#{contactId},dp.old_delivery_time = #{oldDeliveryTime}
        WHERE dp.status in (2,3) AND dp.order_no=#{orderNo}
    </update>

    <update id="updateDeliveryTime">
        UPDATE delivery_plan dp SET dp.delivery_time=#{deliveryTime},dp.contact_id=#{contactId}
        WHERE dp.status in (2,3) AND dp.order_no=#{orderNo}
    </update>

    <select id="getDeliveryPlanByMIds" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT
               dp.contact_id contactId,
               c.m_id mId,
               c.contact contact,
               c.phone phone,
               dp.order_no orderNo,
               dp.order_store_no  orderStoreNo,
               dp.delivery_time deliveryTime
        FROM delivery_plan dp left join contact c on c.contact_id = dp.contact_id
        WHERE dp.delivery_time = #{deliveryTime} and dp.status = 3 and dp.deliverytype  = 0 and c.m_id in
            <foreach collection="mIdList" item="mId" separator="," open="(" close=")">
                #{mId}
            </foreach>
        order by dp.contact_id
    </select>

    <select id="getAllDeliveryPlanByMIdAndContactId" resultType="net.summerfarm.mall.model.vo.DeliveryPlanVO">
        SELECT
            dp.contact_id contactId,
            dp.order_no orderNo,
            dp.order_store_no  orderStoreNo,
            dp.delivery_time deliveryTime
        FROM delivery_plan dp
        LEFT JOIN orders o ON dp.order_no = o.order_no
        WHERE o.m_id = #{mId}
        <if test="contactId != null">
            AND dp.contact_id=#{contactId}
        </if>
        AND dp.status in (2,3) and o.status in (2,3)
    </select>
</mapper>
