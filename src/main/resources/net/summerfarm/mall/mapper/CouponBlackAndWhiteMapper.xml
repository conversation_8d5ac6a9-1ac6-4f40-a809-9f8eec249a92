<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.CouponBlackAndWhiteMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.CouponBlackAndWhite">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="coupon_id" jdbcType="BIGINT" property="couponId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, coupon_id, sku, type, creator, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from coupon_black_and_white
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from coupon_black_and_white
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.CouponBlackAndWhite">
    insert into coupon_black_and_white (id, coupon_id, sku, 
      type, creator, create_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{couponId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR},
      #{type,jdbcType=TINYINT}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.CouponBlackAndWhite">
    insert into coupon_black_and_white
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.CouponBlackAndWhite">
    update coupon_black_and_white
    <set>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.CouponBlackAndWhite">
    update coupon_black_and_white
    set coupon_id = #{couponId,jdbcType=BIGINT},
      sku = #{sku,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getAllByEntity" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from coupon_black_and_white
    <where>
      <if test="couponId != null">
        and coupon_id = #{couponId,jdbcType=BIGINT}
      </if>
      <if test="sku != null">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="type != null">
        and type = #{type,jdbcType=TINYINT}
      </if>
      <if test="couponIds != null and couponIds.size > 0">
        and coupon_id in
        <foreach item="item" collection="couponIds"
                 open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="pageCouponIds" resultType="java.lang.Integer">
    select distinct (coupon_id)
    from coupon_black_and_white bw left join coupon c
    on c.id = bw.coupon_id
    where c.status = 1
  </select>

  <select id="findByCouponIds" resultMap="BaseResultMap">
    SELECT
        coupon_id,
        type,
        GROUP_CONCAT(sku) as sku
    FROM
        coupon_black_and_white
    WHERE
        coupon_id IN
        <foreach item="couponId" collection="list" open="(" separator="," close=")">
            #{couponId}
        </foreach>
    GROUP BY
        coupon_id, type
  </select>

</mapper>
