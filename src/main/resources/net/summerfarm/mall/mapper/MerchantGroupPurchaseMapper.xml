<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MerchantGroupPurchaseMapper" >


    <select id="selectGroupPurchase"  resultType="net.summerfarm.mall.model.domain.MerchantGroupPurchase">
    select m_id mId,m_name mName ,add_time addTime,phone,admin_id adminId,admin_name adminName
    from merchant_group_purchase
    where m_id =#{mId}
  </select>

    <insert id="insertGroupPurchase" parameterType="net.summerfarm.mall.model.domain.MerchantGroupPurchase" >
        insert into merchant_group_purchase
        <trim prefix="(" suffix=")" suffixOverrides="," >

            <if test="addTime != null" >
                add_time,
            </if>
            <if test="mId != null" >
                m_id,
            </if>
            <if test="mName != null" >
                m_name,
            </if>
            <if test="phone != null" >
                phone,
            </if>
            <if test="adminId != null" >
                admin_id,
            </if>
            <if test="adminName != null">
                admin_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="addTime != null" >
                #{addTime},
            </if>
            <if test="mId != null" >
                #{mId},
            </if>
            <if test="mName != null" >
                #{mName},
            </if>
            <if test="phone != null" >
                #{phone},
            </if>
            <if test="adminId != null" >
                #{adminId},
            </if>
            <if test="adminName != null">
                #{adminName} ,
            </if>
        </trim>
    </insert>

</mapper>
































































































