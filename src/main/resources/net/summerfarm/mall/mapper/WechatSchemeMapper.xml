<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.WechatSchemeMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.WechatScheme">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="host_ip" jdbcType="VARCHAR" property="hostIp" />
    <result column="scheme" jdbcType="VARCHAR" property="scheme" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, host_ip, scheme, expire_time, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wechat_scheme
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByIp" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wechat_scheme
    where host_ip = #{ip,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wechat_scheme
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.WechatScheme" useGeneratedKeys="true">
    insert into wechat_scheme (host_ip, scheme, expire_time, 
      create_time, update_time)
    values (#{hostIp,jdbcType=VARCHAR}, #{scheme,jdbcType=VARCHAR}, #{expireTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.WechatScheme" useGeneratedKeys="true">
    insert into wechat_scheme
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hostIp != null">
        host_ip,
      </if>
      <if test="scheme != null">
        scheme,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hostIp != null">
        #{hostIp,jdbcType=VARCHAR},
      </if>
      <if test="scheme != null">
        #{scheme,jdbcType=VARCHAR},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.WechatScheme">
    update wechat_scheme
    <set>
      <if test="hostIp != null">
        host_ip = #{hostIp,jdbcType=VARCHAR},
      </if>
      <if test="scheme != null">
        scheme = #{scheme,jdbcType=VARCHAR},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.WechatScheme">
    update wechat_scheme
    set host_ip = #{hostIp,jdbcType=VARCHAR},
      scheme = #{scheme,jdbcType=VARCHAR},
      expire_time = #{expireTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>