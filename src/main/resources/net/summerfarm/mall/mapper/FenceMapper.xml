<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.FenceMapper">

    <sql id="Base_Column_List">
        id,fence_name,store_no,area_no,`status`,add_time,update_time,admin_id,pack_id,`type`
    </sql>
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Fence">
        <id column="id" property="id"/>
        <result column="fence_name" property="fenceName"/>
        <result column="add_time" property="addTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="area_no" property="areaNo"/>
        <result column="store_no" property="storeNo"/>
        <result column="status" property="status"/>
        <result column="admin_id" property="adminId"/>
        <result column="pack_id" property="packId"/>
    </resultMap>
    <insert id="insertFence" parameterType="net.summerfarm.mall.model.domain.Fence" useGeneratedKeys="true" keyProperty="id">
        insert into fence (add_time,update_time,status,area_no,store_no,fence_name,admin_id,pack_id)
          values  (now(),now(),#{status},#{areaNo},#{storeNo},#{fenceName},#{adminId},#{packId})
    </insert>

    <update id="updateFence">
        update fence
        <set>
            <if test="fenceName != null">
                fence_name = #{fenceName},
            </if>
            <if test="adminId != null">
                admin_id = #{adminId},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="deleteFence">
        update fence
        set status = 1
        where id = #{id}
    </update>

    <select id="selectByFence" resultType="net.summerfarm.mall.model.vo.FenceVO" parameterType="net.summerfarm.mall.model.vo.FenceVO">
        select
            f.id,
            f.fence_name fenceName,
            a.area_name areaName ,
            wlc.store_name storeName,
            f.status,
            ad.realname adminName ,
            f.update_time updateTime
        from fence f
        left join area a on f.area_no = a.area_no
        left join admin ad on ad.admin_id = f.admin_id
        left join warehouse_logistics_center wlc on wlc.store_no = f.store_no
        <where>
            <if test="areaNo != null">
                AND  f.area_no = #{areaNo}
            </if>
            <if test="storeNo != null">
                AND f.store_no = #{storeNo}
            </if>
            <if test="fenceName != null">
                AND f.fence_name like CONCAT('%', #{fenceName},'%')
            </if>
            <if test="status != null">
                AND f.status = #{status}
            </if>
        </where>
        order by f.id desc
    </select>

    <select id="selectFenceById" resultType ="net.summerfarm.mall.model.vo.FenceVO">
       select
            f.id,
            f.fence_name fenceName,
            a.area_name areaName ,
            wlc.store_name storeName,
            f.status,
            f.area_no areaNO,
            f.store_no storeNo
        from fence f
        left join area a on f.area_no = a.area_no
        left join admin ad on ad.admin_id = f.admin_id
        left join warehouse_logistics_center wlc on wlc.store_no = f.store_no
      where f.id = #{id}
    </select>
    <select id="selectFenceByGdId" resultType="net.summerfarm.mall.model.domain.Fence">
        select
          f.id,
          f.area_no areaNo,
          f.store_no storeNo
        from fence f
        left join ad_code_msg acm on f.id = acm.fence_id
        where acm.gd_id =#{gdId}
    </select>

    <select id="selectFence" resultType="net.summerfarm.mall.model.domain.Fence" parameterType="net.summerfarm.mall.model.domain.Fence">
        select
          id,area_no areaNo ,store_no storeNo
        from fence
        <where>
            status = 0
            <if test="areaNo != null">
                And area_no = #{areaNo}
            </if>
            <if test="storeNo != null">
                And store_no = #{storeNo}
            </if>
            <if test="fenceName != null">
                AND fence_name = #{fenceName}
            </if>
            <if test="id != null">
                AND id = #{id}
            </if>
        </where>
    </select>

    <select id="selectFenceByName" resultType="net.summerfarm.mall.model.domain.Fence">
        select
        id,area_no areaNo ,store_no storeNo
        from fence
        where  fence_name = #{fenceName}
    </select>

    <select id="selectFenceArea" parameterType="net.summerfarm.mall.model.domain.Fence"  resultType="net.summerfarm.mall.model.domain.Fence">
         select
        id,area_no areaNo ,store_no storeNo
        from fence
        where status = 0 and ( store_no = #{storeNo} or area_no =#{areaNo} )
    </select>
    <select id="selectMaxPackId" resultType="java.lang.Integer" >
      select pack_id from fence order by pack_id desc limit 1
    </select>

    <select id="selectStoreNoByPickId" resultType="java.lang.Integer">
        select distinct store_no from fence  where pack_id  = #{pickId} and status = 1
    </select>

    <select id="selectByCity"  resultType="net.summerfarm.mall.model.domain.AdCodeMsg" parameterType="net.summerfarm.mall.model.domain.Contact">
        select
        id,
        ad_code adCode,
        province,
        city,
        area,
        `level`,
        gd_id gdId,
        fence_id fenceId
        from ad_code_msg
        where
        status = #{status} AND  city = #{city}
        <if test="area != null">
            and  area = #{area}
        </if>
    </select>
    <select id="selectFenceVoByAreaCity" resultType="net.summerfarm.mall.model.vo.FenceVO">
        select acm.fence_id fenceId,fd.delivery_frequent deliveryFrequent,fd.next_delivery_date nextDeliveryDate, f.area_no areaNo from ad_code_msg acm
        left join fence_delivery fd on acm.fence_id = fd.fence_id and fd.delete_flag = 0
        left join fence f on f.id = acm.fence_id and f.status = 0
        where acm.city =#{city}
        <if test="area!=null and area != ''">
            and acm.area = #{area}
        </if>

        and acm.status = 0
    </select>

    <select id="selectOneByAreaNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from fence
        where area_no = #{areaNo} and status = 0
        limit 1
    </select>

    <select id="getBatchFencesById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from fence
        where id in
        <foreach item="item" index="index" collection="fenceIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
