<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MerchantOrderRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MerchantOrderRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, sku
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_order_record
    where id = #{id,jdbcType=INTEGER}
  </select>
    <select id="selectByMid" resultType="java.lang.String">
      select sku from merchant_order_record where m_id =#{mId}

    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from merchant_order_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MerchantOrderRecord" useGeneratedKeys="true">
    insert into merchant_order_record (m_id, sku)
    values (#{mId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MerchantOrderRecord" useGeneratedKeys="true">
    insert into merchant_order_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MerchantOrderRecord">
    update merchant_order_record
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.MerchantOrderRecord">
    update merchant_order_record
    set m_id = #{mId,jdbcType=BIGINT},
      sku = #{sku,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>