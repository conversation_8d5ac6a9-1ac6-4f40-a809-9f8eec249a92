<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.StockTaskItemMapper">


    <select id="queryNumberBySku" resultType="net.summerfarm.mall.model.domain.StockTaskItem">
        select sti.sku,sti.quantity,sti.actual_quantity actualQuantity from stock_task st
        inner join stock_task_item sti where sti.stock_task_id = st.id and sku = #{sku}
        and date(expect_time) = #{date} and st.type = 51;
    </select>

    <select id="queryNumberByOrderNo" resultType="net.summerfarm.mall.model.domain.StockTaskItem">
        select sti.quantity,sti.actual_quantity actualQuantity,sti.sku from stock_task st
        inner join stock_task_item sti on st.id = sti.stock_task_id and sti.sku=#{sku} and st.task_no =#{orderNo} and type = 51
    </select>


</mapper>