<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MerchantBusinessSwitchMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MerchantBusinessSwitch">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="enabled" jdbcType="TINYINT" property="enabled" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `type`, m_id, enabled, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_business_switch
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByBusinessType" resultType="net.summerfarm.mall.model.domain.MerchantBusinessSwitch">
      select
      <include refid="Base_Column_List" />
      from merchant_business_switch
      where m_id = #{mId,jdbcType=BIGINT} and `type` = #{type,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_business_switch
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MerchantBusinessSwitch" useGeneratedKeys="true">
    insert into merchant_business_switch (`type`, m_id, enabled, 
      create_time, update_time)
    values (#{type,jdbcType=INTEGER}, #{mId,jdbcType=BIGINT}, #{enabled,jdbcType=TINYINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MerchantBusinessSwitch" useGeneratedKeys="true">
    insert into merchant_business_switch
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        `type`,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MerchantBusinessSwitch">
    update merchant_business_switch
    <set>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.MerchantBusinessSwitch">
    update merchant_business_switch
    set `type` = #{type,jdbcType=INTEGER},
      m_id = #{mId,jdbcType=BIGINT},
      enabled = #{enabled,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>