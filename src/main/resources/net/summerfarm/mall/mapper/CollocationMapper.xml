<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.CollocationMapper" >

    <select id="skuInValidCollectionForMap" resultType="java.lang.String">
        select ci.sku
        from collocation_item ci
                 inner join collocation c on ci.collocation_id = c.id
                 inner join area_collocation ac on c.id = ac.collocation_id
        where c.status = 1 and ac.area_no = #{areaNo}  and ci.sku in
        <foreach collection="list" open="(" close=") " separator="," item="item">
            #{item}
        </foreach>
        group by ci.sku having count(*) > 0
    </select>
</mapper>