<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.PopupTypeMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.PopupType">
    <!--@mbg.generated-->
    <!--@Table popup_type-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="popup_interval" jdbcType="INTEGER" property="popupInterval" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, type_name, popup_interval
  </sql>

<!--auto generated by MybatisCodeHelper on 2024-06-20-->
  <select id="selectOneByTypeName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from popup_type
        where type_name=#{typeName,jdbcType=VARCHAR}
    </select>

</mapper>