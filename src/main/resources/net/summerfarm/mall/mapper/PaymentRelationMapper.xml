<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.PaymentRelationMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.PaymentRelation">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="masterPaymentId" column="master_payment_id" jdbcType="BIGINT"/>
            <result property="paymentId" column="payment_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,master_payment_id,payment_id,
        create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from payment_relation
        where  id = #{id,jdbcType=BIGINT} 
    </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from payment_relation
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.PaymentRelation" useGeneratedKeys="true">
        insert into payment_relation
        ( id,master_payment_id,payment_id
        ,create_time,update_time)
        values (#{id,jdbcType=BIGINT},#{masterPaymentId,jdbcType=BIGINT},#{paymentId,jdbcType=BIGINT}
        ,#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.PaymentRelation" useGeneratedKeys="true">
        insert into payment_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="masterPaymentId != null">master_payment_id,</if>
                <if test="paymentId != null">payment_id,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="masterPaymentId != null">#{masterPaymentId,jdbcType=BIGINT},</if>
                <if test="paymentId != null">#{paymentId,jdbcType=BIGINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.PaymentRelation">
        update payment_relation
        <set>
                <if test="masterPaymentId != null">
                    master_payment_id = #{masterPaymentId,jdbcType=BIGINT},
                </if>
                <if test="paymentId != null">
                    payment_id = #{paymentId,jdbcType=BIGINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.PaymentRelation">
        update payment_relation
        set 
            master_payment_id =  #{masterPaymentId,jdbcType=BIGINT},
            payment_id =  #{paymentId,jdbcType=BIGINT},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <select id="selectByMasterPaymentId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select payment_id from payment_relation where master_payment_id = #{masterPaymentId,jdbcType=BIGINT}
    </select>
</mapper>
