<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.VisitorLogMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.VisitorLog" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="action_id" property="actionId" jdbcType="INTEGER" />
    <result column="visitor" property="visitor" jdbcType="VARCHAR" />
    <result column="channel_code" property="channelCode" jdbcType="VARCHAR" />
    <result column="url" property="url" jdbcType="VARCHAR" />
    <result column="params" property="params" jdbcType="VARCHAR" />
    <result column="visit_time" property="visitTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, action_id, visitor, channel_code, url, params, visit_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from visitor_log
    where id = #{id,jdbcType=INTEGER}
  </select>

  <insert id="insertBatch" parameterType="net.summerfarm.mall.model.domain.VisitorLog">
    INSERT INTO visitor_log ( visitor, url, params, visit_time)
    values
    <foreach collection="list" separator="," item="item">
      (#{item.visitor}, #{item.url}, #{item.params}, #{item.visitTime})
    </foreach>
  </insert>

  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.VisitorLog" >
    insert into visitor_log (id, action_id, visitor,
      channel_code, visit_time)
    values (#{id,jdbcType=INTEGER}, #{actionId,jdbcType=INTEGER}, #{visitor,jdbcType=VARCHAR},
      #{channelCode,jdbcType=VARCHAR}, #{visitTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.VisitorLog" >
    insert into visitor_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="actionId != null" >
        action_id,
      </if>
      <if test="visitor != null" >
        visitor,
      </if>
      <if test="channelCode != null" >
        channel_code,
      </if>
      <if test="visitTime != null" >
        visit_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="actionId != null" >
        #{actionId,jdbcType=INTEGER},
      </if>
      <if test="visitor != null" >
        #{visitor,jdbcType=VARCHAR},
      </if>
      <if test="channelCode != null" >
        #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="visitTime != null" >
        #{visitTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
</mapper>