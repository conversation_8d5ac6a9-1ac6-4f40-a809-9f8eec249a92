<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.SkuPriceFluctuationMapper">

    <sql id="base_sql">
        id,price,recode_time,area_no,sku,activity_price,add_time
    </sql>

    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.SkuPriceFluctuation" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="price" property="price" jdbcType="VARCHAR" />
        <result column="recode_time" property="recodeTime" jdbcType="VARCHAR" />
        <result column="area_no" property="areaNo" jdbcType="VARCHAR" />
        <result column="activity_price" property="activityPrice" jdbcType="INTEGER" />
        <result column="add_time" property="addTime" jdbcType="VARCHAR" />
    </resultMap>

    <insert id="insertBatch" parameterType="net.summerfarm.mall.model.domain.SkuPriceFluctuation" >
        insert into sku_price_fluctuation (price,recode_time,area_no,sku,activity_price,add_time) values
        <foreach collection="list" separator="," item="item">
            (#{item.price},#{item.recodeTime},#{item.areaNo},#{item.sku},#{item.activityPrice},#{item.addTime}
            )
        </foreach>
    </insert>

    <select id="selectBySkuAndAreaAndAddTime"
            resultMap="BaseResultMap">
        select <include refid="base_sql"></include>
        from sku_price_fluctuation
        where sku = #{sku}
            and area_no = #{areaNo}
            and
            DATE_FORMAT(add_time, '%Y-%m-%d %H' ) = DATE_FORMAT( #{addTime} , '%Y-%m-%d %H'  )
    </select>
</mapper>