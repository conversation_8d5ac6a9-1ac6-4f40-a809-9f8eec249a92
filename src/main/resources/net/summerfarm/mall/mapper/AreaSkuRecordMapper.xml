<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.AreaSkuRecordMapper">
    <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.AreaSkuRecord">
        INSERT INTO area_sku_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recorder != null">
                recorder,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="areaNo != null">
                area_no,
            </if>
            <if test="typeName != null">
                type_name,
            </if>
            <if test="resultStatus != null">
                result_status,
            </if>
            <if test="addtime != null">
                addtime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recorder != null">
                #{recorder},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
            <if test="areaNo">
                #{areaNo},
            </if>
            <if test="typeName != null">
                #{typeName},
            </if>
            <if test="resultStatus != null">
                #{resultStatus,jdbcType=BIT},
            </if>
            <if test="addtime != null">
                #{addtime},
            </if>
        </trim>
    </insert>
    <select id="selectFirstOnSale" resultType="java.time.LocalDateTime">
        select addtime from area_sku_record
        where type_name = '上下架'
          and result_status = 1
          and area_no = #{areaNo}
          and sku = #{sku}
        order by id desc limit 1
    </select>

    <select id="listFirstOnSaleBySkus" resultType="net.summerfarm.mall.model.domain.AreaSkuRecord">
        select sku,addtime from area_sku_record
        where type_name = '上下架'
          and result_status = 1
          and area_no = #{areaNo}
          and sku in
        <foreach collection="list" open="(" separator="," close=")" item="sku">
            #{sku}
        </foreach>
        group by sku
        order by id asc
    </select>
</mapper>