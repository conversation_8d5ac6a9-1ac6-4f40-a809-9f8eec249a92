<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MerchantCardMapper">

    <sql id="BaseColumn">
        id,m_id,card_id,card_rule_id,vaild_date,sender,add_time
    </sql>

    <insert id="insert" parameterType="net.summerfarm.mall.model.domain.MerchantCard">
        INSERT INTO merchant_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                m_id,
            </if>
            <if test="cardId != null">
                card_id,
            </if>
            <if test="cardRuleId != null">
                card_rule_id,
            </if>
            <if test="vaildDate != null">
                vaild_date,
            </if>
            <if test="sender != null">
                sender,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                #{mId} ,
            </if>
            <if test="cardId != null">
                #{cardId} ,
            </if>
            <if test="cardRuleId != null">
                #{cardRuleId} ,
            </if>
            <if test="vaildDate != null">
                #{vaildDate} ,
            </if>
            <if test="sender != null">
                #{sender} ,
            </if>
            <if test="addTime != null">
                #{addTime} ,
            </if>
        </trim>
    </insert>

    <select id="selectVaildCard" parameterType="net.summerfarm.mall.model.vo.MerchantCardVO"
            resultType="net.summerfarm.mall.model.vo.MerchantCardVO">
        SELECT mc.id,mc.m_id mId,mc.card_id cardId,mc.card_rule_id cardRuleId,mc.vaild_date vaildDate,mc.sender,mc.add_time addTime,c.`name`,
	      c.card_type cardType,c.money,c.threshold,c.type,c.grouping,c.times,c.remark,c.`status`
        FROM merchant_card mc
        INNER JOIN card c ON mc.card_id=c.id
        WHERE mc.m_id = #{mId}
        AND mc.vaild_date <![CDATA[>=]]> #{vaildDate}
        AND c.card_type = #{cardType}
    </select>

    <select id="countCards" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM merchant_card
        WHERE m_id = #{mId}
        AND vaild_date <![CDATA[>]]> #{date}
    </select>

</mapper>