<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MerchantCancelMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MerchantCancel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="resource" jdbcType="TINYINT" property="resource" />
    <result column="remake" jdbcType="VARCHAR" property="remake" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="mname" jdbcType="VARCHAR" property="MName" />
    <result column="certificate" jdbcType="VARCHAR" property="certificate" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="updater" jdbcType="BIGINT" property="updater" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, m_id, status, phone, mname, remake, certificate, resource, creator, updater, create_time, update_time
  </sql>
 
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_cancel
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_cancel
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.MerchantCancel">
    insert into merchant_cancel (id, m_id, status, 
      remake, certificate, creator, 
      create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, 
      #{remake,jdbcType=VARCHAR}, #{certificate,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.MerchantCancel">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchant_cancel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="mName != null">
        mname,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="resource != null">
        resource,
      </if>
      <if test="remake != null">
        remake,
      </if>
      <if test="certificate != null">
        certificate,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="mName != null">
        #{mName,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="resource != null">
        #{resource,jdbcType=TINYINT},
      </if>
      <if test="remake != null">
        #{remake,jdbcType=VARCHAR},
      </if>
      <if test="certificate != null">
        #{certificate,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MerchantCancel">
    update merchant_cancel
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="remake != null">
        remake = #{remake,jdbcType=VARCHAR},
      </if>
      <if test="certificate != null">
        certificate = #{certificate,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.MerchantCancel">
    update merchant_cancel
    set m_id = #{mId,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      remake = #{remake,jdbcType=VARCHAR},
      certificate = #{certificate,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectInfoByMid" resultMap="BaseResultMap" parameterType="net.summerfarm.mall.model.domain.MerchantCancel">
    select
    <include refid="Base_Column_List" />
    from merchant_cancel
    where m_id = #{mId,jdbcType=BIGINT}
    and status = #{status,jdbcType=TINYINT}
    limit 1
    </select>
</mapper>