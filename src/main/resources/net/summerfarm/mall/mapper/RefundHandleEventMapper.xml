<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="net.summerfarm.mall.mapper.RefundHandleEventMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.RefundHandleEvent">
    <!--@mbg.generated-->
    <!--@Table refund_handle_event-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="refund_no" jdbcType="VARCHAR" property="refundNo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="retry_count" jdbcType="INTEGER" property="retryCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, refund_no, `status`, retry_count, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from refund_handle_event
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from refund_handle_event
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.RefundHandleEvent" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into refund_handle_event (refund_no, `status`, retry_count, 
      create_time, update_time)
    values (#{refundNo,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{retryCount,jdbcType=INTEGER}, 
     NOW(), NOW())
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.RefundHandleEvent" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into refund_handle_event
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="refundNo != null">
        refund_no,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="retryCount != null">
        retry_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="refundNo != null">
        #{refundNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="retryCount != null">
        #{retryCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.RefundHandleEvent">
    <!--@mbg.generated-->
    update refund_handle_event
    <set>
      <if test="refundNo != null">
        refund_no = #{refundNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="retryCount != null">
        retry_count = #{retryCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.RefundHandleEvent">
    <!--@mbg.generated-->
    update refund_handle_event
    set refund_no = #{refundNo,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      retry_count = #{retryCount,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2022-01-20-->
  <select id="selectAllByStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from refund_handle_event
    where `status` in
    <foreach collection="list" separator="," open="(" close=")" item="status" >
      #{status}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2022-01-20-->
  <update id="updateStatusById">
    update refund_handle_event
    set `status`=#{updatedStatus,jdbcType=TINYINT}
    where id=#{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2022-01-20-->
  <update id="updateStatusByRefundNo">
    update refund_handle_event
    set `status`=#{updatedStatus,jdbcType=TINYINT}
    where refund_no=#{refundNo,jdbcType=VARCHAR}
  </update>
</mapper>