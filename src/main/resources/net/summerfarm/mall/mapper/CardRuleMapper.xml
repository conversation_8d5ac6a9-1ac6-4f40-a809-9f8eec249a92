<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.CardRuleMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.CardRule">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="card_id" property="cardId" jdbcType="INTEGER"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="merchant_type" property="merchantType" jdbcType="INTEGER"/>
        <result column="add_time" property="addTime"/>
    </resultMap>

    <sql id="BaseColumn">
        id,card_id,start_time,end_time,area_no,merchant_type,add_time
    </sql>

    <select id="selectValidRule" parameterType="net.summerfarm.mall.model.domain.CardRule" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM card_rule
        WHERE start_time <![CDATA[<=]]> NOW()
        AND end_time <![CDATA[>=]]> NOW()
        <if test="areaNo != null">
            AND area_no = #{areaNo}
        </if>
        <if test="merchantType != null">
            AND merchant_type = #{merchantType}
        </if>
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM card_rule
        WHERE id = #{id}
    </select>

    <select id="select" parameterType="java.lang.Integer" resultType="net.summerfarm.mall.model.vo.CardRuleVO">
        SELECT cr.id,cr.card_id cardId,c.`name`,c.type,c.vaild_date vaildDate,c.vaild_time vaildTime,c.times,c.card_type cardType
        FROM card_rule cr
        INNER JOIN card c ON cr.card_id=c.id
        WHERE cr.id = #{id}
    </select>

</mapper>