<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.TemporaryActivityConfigMapper">


    <select id="selectByTableName" resultType="net.summerfarm.mall.model.domain.TemporaryActivityConfig" parameterType="java.lang.String">
        select
        `table_name` tableName ,
        skus,
        ruler_id rulerId,
        desction
        from temporary_activity_config
        where `table_name` = #{tableName}
    </select>

</mapper>