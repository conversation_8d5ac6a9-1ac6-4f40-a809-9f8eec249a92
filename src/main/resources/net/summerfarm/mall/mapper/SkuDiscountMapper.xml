<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.SkuDiscountMapper" >

    <select id="selectBySkus" resultType="net.summerfarm.mall.model.domain.SkuDiscount">
        select sku,discount,overlap from sku_discount where sku in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectBySku" resultType="net.summerfarm.mall.model.domain.SkuDiscount">
        select sku, overlap
        from sku_discount
        where sku = #{sku}
    </select>
</mapper>