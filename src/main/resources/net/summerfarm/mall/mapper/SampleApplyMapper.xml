<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.SampleApplyMapper">

    <resultMap id="baseResultMap" type="net.summerfarm.mall.model.domain.SampleApply">
        <id column="sample_id" property="sampleId"/>
        <result column="add_time" property="addTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_id" property="createId"/>
        <result column="create_name" property="createName"/>
        <result column="m_id" property="mId"/>
        <result column="m_name" property="mName"/>
        <result column="grade" property="grade"/>
        <result column="m_size" property="mSize"/>
        <result column="m_phone" property ="mPhone"/>
        <result column="m_contact" property="mContact"/>
        <result column="area_no" property="areaNo"/>
        <result column="contact_id" property="contactId"/>
        <result column="bd_id" property="bdId"/>
        <result column="bd_name" property="bdName"/>
        <result column="status" property="status"/>
        <result column="satisfaction" property="satisfaction"/>
        <result column="purchase_intention" property="purchaseIntention"/>
        <result column="remark" property="remark"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="store_no" property="storeNo"/>
        <result column="selling_entity_name" property="sellingEntityName"/>
    </resultMap>

    <select id="selectSampleById" resultMap="baseResultMap">
         select * from sample_apply
         where sample_id =#{sampleId}
    </select>

    <select id="countDataByDeliveryDateAndStatusList" resultType="int">
        select count(*)
        from sample_apply
        where delivery_time = #{deliveryTime}
        and status in
        <foreach collection="statusList" open="(" separator="," close=")" item="status">
            #{status}
        </foreach>
    </select>

    <select id="listDataByDeliveryDateAndStatusList" resultMap="baseResultMap">
        select sample_id,contact_id,delivery_time,status,store_no
        from sample_apply
        where delivery_time = #{deliveryTime}
        and status in
        <foreach collection="statusList" open="(" separator="," close=")" item="status">
            #{status}
        </foreach>
        limit #{pageIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <update id="sampleApplySelfPickup">
        update sample_apply
        set status = 0,delivery_time= #{deliveryDate}
        where
        sample_id = #{sampleId}
    </update>
  <update id="updateById" parameterType="net.summerfarm.mall.model.domain.SampleApply">
      update sample_apply
      <set>
          <if test="status != null">
              `status` = #{status},
          </if>
          <if test="satisfaction != null">
              satisfaction = #{satisfaction},
          </if>
          <if test="purchaseIntention != null">
              purchase_intention = #{purchaseIntention},
          </if>
          <if test="remark != null">
              remark = #{remark},
          </if>
          <if test="updateTime != null">
              update_time = #{updateTime},
          </if>
          <if test="deliveryTime != null">
              delivery_time = #{deliveryTime},
          </if>
          <if test="storeNo != null">
              store_no = #{storeNo},
          </if>
      </set>
      where sample_id =#{sampleId}
  </update>
</mapper>