<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.TimingRuleMapper" >
  <update id="batchUpdateByPrimaryKey">
    update timing_rule set delivery_period = 91
    where
    `id` in
    <foreach collection="list" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </update>

  <select id="selectByPrimaryKey" resultType="net.summerfarm.mall.model.vo.TimingRuleVO" parameterType="java.lang.Integer" >
    select 
    tr.id, tr.name, tr.timing_sku timingSku, tr.start_time startTime, tr.end_time endTime, tr.delivery_start deliveryStart,
    tr.delivery_end deliveryEnd, tr.rule_information ruleInformation, tr.delivery_unit deliveryUnit, tr.delivery_upper_limit deliveryUpperLimit,
    tr.update_time updateTime, tr.area_no areaNo,tr.type,IFNULL(a.price,i.sale_price) salePrice, a.ladder_price ladderPriceStr,
    i.base_sale_unit baseSaleUnit,i.base_sale_quantity baseSaleQuantity,p.category_id categoryId, tr.display,tr.threshold, p.pd_id pdId,
    tr.auto_calculate autoCalculate,tr.delivery_period deliveryPeriod,tr.delivery_start_type deliveryStartType, tr.plus_day plusDay, i.inv_id invId
    from timing_rule tr
    left join area_sku a on a.sku=tr.timing_sku and a.area_no=tr.area_no
    LEFT join inventory i on i.sku=a.sku
    LEFT JOIN products p on i.pd_id=p.pd_id
    where tr.id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectTimingRuleBySku" resultType="net.summerfarm.mall.model.domain.TimingRule">
    SELECT id,name,timing_sku timingSku,area_no areaNo,delivery_unit deliveryUnit,delivery_period deliveryPeriod,threshold,
    delivery_upper_limit deliveryUpperLimit,auto_calculate autoCalculate, delivery_start_type deliveryStartType, plus_day plusDay,
    delivery_start deliveryStart
    FROM timing_rule
    WHERE timing_sku = #{sku}
    AND area_no = #{areaNo}
    AND display = 1
    <if test="type != null">
      AND type = #{type}
    </if>
    order by id desc limit 1
  </select>

  <select id="listBySkus" resultType="net.summerfarm.mall.model.vo.TimingRuleVO">
    SELECT id,name,timing_sku timingSku,area_no areaNo,delivery_unit deliveryUnit,delivery_period deliveryPeriod,threshold,
    delivery_upper_limit deliveryUpperLimit,auto_calculate autoCalculate, delivery_start_type deliveryStartType, plus_day plusDay,
    delivery_start deliveryStart
    FROM timing_rule
    WHERE timing_sku in
          <foreach collection="skus" open="(" separator="," close=")" item="item">
            #{item}
          </foreach>
    AND area_no = #{areaNo}
    AND display = 1
    <if test="type != null">
      AND type = #{type}
    </if>
  </select>

  <select id="listTimingCategory" resultType="integer">
    select distinct p.category_id
    from timing_rule tr
           left join area_sku a on a.sku=tr.timing_sku and a.area_no=tr.area_no
           left join inventory i on i.sku=a.sku
           left JOIN products p on i.pd_id=p.pd_id
    where tr.area_no = #{areaNo}
      and tr.display = 1
      and tr.type = 0
      and i.outdated = 0
      and a.on_sale = 1
  </select>

  <select id="listBySkusPriority" resultType="net.summerfarm.mall.model.domain.TimingRule">
    SELECT tr.id ,
           tr.`name` ,
           tr.timing_sku timingSku,
           tr.threshold,
           tr.type,
           tr.start_time startTime,
           tr.end_time endTime,
           tr.delivery_start deliveryStart,
           tr.delivery_end deliveryEnd,
           tr.delivery_start_type deliveryStartType,
           tr.delivery_period deliveryPeriod,
           tr.delivery_unit deliveryUnit,
           tr.delivery_upper_limit deliveryUpperLimit,
           tr.rule_information ruleInformation,
           tr.auto_calculate autoCalculate,
           tr.plus_day plusDay
    FROM timing_rule tr
    WHERE
    tr.display = 1 and tr.type=#{type}
    AND tr.area_no = #{areaNo}
    <if test="skus != null and skus.size > 0">
      and tr.timing_sku in
      <foreach collection="skus" open="(" separator="," close=")" item="sku">
        #{sku}
      </foreach>
    </if>
    order by tr.priority DESC,id desc
  </select>

    <select id="listAllTimingRule" resultType="net.summerfarm.mall.model.domain.TimingRule">
      SELECT  id,delivery_period deliveryPeriod
      FROM  `timing_rule`
      where `type`  = 0 and `display`  = 1 and `delivery_period`  is not null
    </select>
    <select id="getTimingInfoByAreaNo" resultType="net.summerfarm.mall.model.vo.TimingRuleVO">
        SELECT id,name,timing_sku timingSku,area_no areaNo,delivery_unit deliveryUnit,delivery_period deliveryPeriod,threshold,
        delivery_upper_limit deliveryUpperLimit,auto_calculate autoCalculate, delivery_start_type deliveryStartType, plus_day plusDay,
        delivery_start deliveryStart
        FROM timing_rule
        WHERE area_no = #{areaNo}
        AND display = 1
        AND type = 0
    </select>
</mapper>