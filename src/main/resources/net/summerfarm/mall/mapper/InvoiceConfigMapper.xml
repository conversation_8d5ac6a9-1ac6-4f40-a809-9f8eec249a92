<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.InvoiceConfigMapper">
    <delete id="deleteById">
        delete from invoice_config where id = #{id}
    </delete>


    <select id="selectBusiness" parameterType="long" resultType="net.summerfarm.mall.model.domain.InvoiceConfig">
        select id,
               merchant_id     merchantId,
               admin_id        adminId,
               type,
               invoice_title   invoiceTitle,
               tax_number      taxNumber,
               open_account    openAccount,
               open_bank       openBank,
               company_address companyAddress,
               company_phone   companyPhone,
               business_license_address businessLicenseAddress
        from invoice_config
        where merchant_id = #{id}
          and valid_status = 0
        limit 1
    </select>

    <select id="selectAdminId" parameterType="net.summerfarm.mall.model.domain.InvoiceConfig" resultType="net.summerfarm.mall.model.domain.InvoiceConfig">
        select id,
               admin_id      adminId,
               invoice_title invoiceTitle,
               tax_number    taxNumber
        from invoice_config
        where admin_id = #{adminId}
          and valid_status = 0
          and type = 1
          and invoice_title = #{invoiceTitle}
          and tax_number = #{taxNumber}
    </select>

    <insert id="insertAdmin" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.InvoiceConfig" useGeneratedKeys="true">
        insert into invoice_config (admin_id, `type`,
                                    invoice_title, tax_number, open_account,
                                    open_bank, company_address, company_phone,
                                    mail_address, company_receiver, company_email,
                                    link_method, create_time,business_license_address)
        values (#{adminId,jdbcType=INTEGER}, 1,
                #{invoiceTitle,jdbcType=VARCHAR}, #{taxNumber,jdbcType=VARCHAR}, #{openAccount,jdbcType=VARCHAR},
                #{openBank,jdbcType=VARCHAR}, #{companyAddress,jdbcType=VARCHAR}, #{companyPhone,jdbcType=VARCHAR},
                #{mailAddress,jdbcType=VARCHAR}, #{companyReceiver,jdbcType=VARCHAR}, #{companyEmail,jdbcType=VARCHAR},
                #{linkMethod,jdbcType=VARCHAR}, now(),#{businessLicenseAddress})
    </insert>

    <insert id="insertMerchant" parameterType="net.summerfarm.mall.model.domain.InvoiceConfig">
        insert into invoice_config (merchant_id, `type`,
                                    invoice_title, tax_number, open_account,
                                    open_bank, company_address, company_phone,
                                    mail_address, company_receiver, company_email,
                                    link_method, create_time,business_license_address,default_flag)
        values (#{merchantId}, 0,
                #{invoiceTitle,jdbcType=VARCHAR}, #{taxNumber,jdbcType=VARCHAR}, #{openAccount,jdbcType=VARCHAR},
                #{openBank,jdbcType=VARCHAR}, #{companyAddress,jdbcType=VARCHAR}, #{companyPhone,jdbcType=VARCHAR},
                #{mailAddress,jdbcType=VARCHAR}, #{companyReceiver,jdbcType=VARCHAR}, #{companyEmail,jdbcType=VARCHAR},
                #{linkMethod,jdbcType=VARCHAR}, now(),#{businessLicenseAddress},#{defaultFlag})
    </insert>

    <update id="updateMerchant" parameterType="net.summerfarm.mall.model.domain.InvoiceConfig">
        update invoice_config
        set invoice_title            = #{invoiceTitle,jdbcType=VARCHAR},
            tax_number               = #{taxNumber,jdbcType=VARCHAR},
            link_method              = #{linkMethod,jdbcType=VARCHAR},
            mail_address             = #{mailAddress,jdbcType=VARCHAR},
            open_account             = #{openAccount,jdbcType=VARCHAR},
            company_receiver         = #{companyReceiver,jdbcType=VARCHAR},
            open_bank                = #{openBank,jdbcType=VARCHAR},
            company_email            = #{companyEmail,jdbcType=VARCHAR},
            company_address          = #{companyAddress,jdbcType=VARCHAR},
            company_phone            = #{companyPhone,jdbcType=VARCHAR},
            updater                  = #{merchantId},
            business_license_address = #{businessLicenseAddress},
            update_time              = now(),
            default_flag             =#{defaultFlag}
        where id = #{id}
    </update>

    <update id="update" parameterType="net.summerfarm.mall.model.domain.InvoiceConfig">
        update invoice_config
        set open_account = #{openAccount,jdbcType=VARCHAR},
            open_bank = #{openBank,jdbcType=VARCHAR},
            company_address = #{companyAddress,jdbcType=VARCHAR},
            company_phone = #{companyPhone,jdbcType=VARCHAR},
            business_license_address = #{businessLicenseAddress},
            updater = #{merchantId},
            update_time = now()
        where merchant_id = #{merchantId}
          and valid_status = 0
    </update>
    <update id="resetDefaultFlag">
        update invoice_config
        set default_flag=1
        where merchant_id = #{mId} and type = 0
    </update>
    <update id="updateDefaultFlag">
        update invoice_config set default_flag=#{defaultFlag} where id=#{id}
    </update>

    <select id="selectByType" resultType="net.summerfarm.mall.model.domain.InvoiceConfig">
        select c.id, c.merchant_id merchantId, c.admin_id adminId, c.`type`, c.invoice_title invoiceTitle, c.tax_number taxNumber,
        c.open_account openAccount,c.open_bank openBank,c.company_address companyAddress, c.company_phone companyPhone,c.mail_address mailAddress,
        c.company_receiver companyReceiver,c.company_email companyEmail, c.valid_status vaildStatus,c.update_time updateTime,
        c.create_time createTime, c.link_method linkMethod,c.business_license_address businessLicenseAddress,c.creator creator ,c.updater updater,
        if(r.id is not null,0,if(#{adminId} is not null,1,default_flag)) defaultFlag
        FROM invoice_config c
        left join invoice_merchant_relation r on c.id= r.invoice_id and r.`merchant_id` = #{mId} AND status=0
        where valid_status = 0
        <if test="mId != null and adminId == null">
            AND c.merchant_id = #{mId} and type =0
        </if>
        <if test="adminId != null">
            AND c.admin_id = #{adminId} and type = 1
        </if>
        <if test="invoiceTitle !=null">
            AND c.invoice_title=#{invoiceTitle}
        </if>
        <if test="taxNumber !=null">
            AND c.tax_number=#{taxNumber}
        </if>
        order by c.create_time desc
    </select>
    <select id="selectById" resultType="net.summerfarm.mall.model.domain.InvoiceConfig">
        select id,
               merchant_id     merchantId,
               admin_id        adminId,
               type,
               invoice_title   invoiceTitle,
               tax_number      taxNumber,
               open_account    openAccount,
               open_bank       openBank,
               company_address companyAddress,
               company_phone   companyPhone,
               business_license_address businessLicenseAddress
        from invoice_config
        where id=#{id}
    </select>

</mapper>