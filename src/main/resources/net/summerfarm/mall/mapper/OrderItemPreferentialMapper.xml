<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.OrderItemPreferentialMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.OrderItemPreferential">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="order_item_id" jdbcType="BIGINT" property="orderItemId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="related_id" jdbcType="BIGINT" property="relatedId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, amount, order_item_id, `type`, related_id, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_item_preferential
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_item_preferential
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.OrderItemPreferential" useGeneratedKeys="true">
    insert into order_item_preferential (order_no, amount, order_item_id, 
      `type`, related_id, create_time, 
      update_time)
    values (#{orderNo,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, #{orderItemId,jdbcType=BIGINT}, 
      #{type,jdbcType=INTEGER}, #{relatedId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.OrderItemPreferential" useGeneratedKeys="true">
    insert into order_item_preferential
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="orderItemId != null">
        order_item_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="relatedId != null">
        related_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orderItemId != null">
        #{orderItemId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch">
    INSERT INTO order_item_preferential (order_no,amount,order_item_id,type,related_id,create_time,discounts_detail_snapshot)
    values
    <foreach collection="list"  item="item" separator=",">
      (#{item.orderNo},#{item.amount},#{item.orderItemId},#{item.type},#{item.relatedId},#{item.createTime},#{item.discountsDetailSnapshot})
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.OrderItemPreferential">
    update order_item_preferential
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="orderItemId != null">
        order_item_id = #{orderItemId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null">
        related_id = #{relatedId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.OrderItemPreferential">
    update order_item_preferential
    set order_no = #{orderNo,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DECIMAL},
      order_item_id = #{orderItemId,jdbcType=BIGINT},
      `type` = #{type,jdbcType=INTEGER},
      related_id = #{relatedId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="queryByOrderNo" resultMap="BaseResultMap">
    select
    id, order_no, amount, order_item_id, type, related_id, create_time, update_time
    from order_item_preferential
    where order_no = #{orderNo}
  </select>
  <select id="selectSelective" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from order_item_preferential
    <where>
      <if test="orderNo != null">
        and order_no = #{orderNo}
      </if>
      <if test="orderItemId != null">
        and order_item_id = #{orderItemId}
      </if>
      <if test="type != null">
        and `type` = #{type}
      </if>
      <if test="relatedId != null">
        and related_id = #{relatedId}
      </if>
    </where>
  </select>

    <select id="queryByOrderNos" resultType="net.summerfarm.mall.model.domain.OrderItemPreferential">
    select
    id, order_no orderNo, amount, order_item_id orderItemId, type, related_id relatedId
    from order_item_preferential
    where order_no in
      <foreach collection="orderNos" close=")" open="(" separator="," item="orderNo">
        #{orderNo,jdbcType=VARCHAR}
      </foreach>
    and type = #{type}
    </select>
</mapper>