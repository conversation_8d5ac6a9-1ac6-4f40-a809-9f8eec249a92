<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.OrderItemExtraMapper">
    <!-- 结果集映射 -->
    <resultMap id="orderItemExtraResultMap" type="net.summerfarm.mall.model.domain.OrderItemExtra">
        <id column="id" property="id" jdbcType="NUMERIC"/>
        <result column="order_item_id" property="orderItemId" jdbcType="NUMERIC"/>
        <result column="buyer_id" property="buyerId" jdbcType="NUMERIC"/>
        <result column="buyer_admin_id" property="buyerAdminId" jdbcType="NUMERIC"/>
        <result column="buyer_name" property="buyerName" jdbcType="VARCHAR"/>
        <result column="supplier_id" property="supplierId" jdbcType="NUMERIC"/>
        <result column="platform_commission_ratio" property="platformCommissionRatio" jdbcType="DOUBLE"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="cost" property="cost" jdbcType="DOUBLE"/>
        <result column="specifications" property="specifications" jdbcType="DOUBLE"/>
        <result column="quote_type" property="quoteType" jdbcType="INTEGER"/>
        <result column="min_auto_after_sale_threshold" property="minAutoAfterSaleThreshold" jdbcType="INTEGER"/>
        <result column="auto_after_sale_flag" property="autoAfterSaleFlag" jdbcType="INTEGER"/>
        <result column="help_order_item_id" property="helpOrderItemId" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="orderItemExtraColumns">
          t.id,
          t.order_item_id,
          t.buyer_id,
          t.buyer_admin_id,
          t.buyer_name,
          t.supplier_id,
          t.platform_commission_ratio,
          t.create_time,
          t.update_time,
          t.cost,
          t.specifications,
          t.quote_type,
          t.min_auto_after_sale_threshold,
          t.auto_after_sale_flag,
          t.help_order_item_id
    </sql>


    <!-- 修改字段SQL -->
    <sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderItemId != null">
                t.order_item_id = #{orderItemId},
            </if>
            <if test="buyerId != null">
                t.buyer_id = #{buyerId},
            </if>
            <if test="buyerAdminId != null">
                t.buyer_admin_id = #{buyerAdminId},
            </if>
            <if test="buyerName != null">
                t.buyer_name = #{buyerName},
            </if>
            <if test="supplierId != null">
                t.supplier_id = #{supplierId},
            </if>
            <if test="platformCommissionRatio != null">
                t.platform_commission_ratio = #{platformCommissionRatio},
            </if>
            <if test="createTime != null">
                t.create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                t.update_time = #{updateTime},
            </if>
            <if test="autoAfterSaleAmount != null">
                t.auto_after_sale_amount = #{autoAfterSaleAmount},
            </if>
            <if test="skuTotalWeight != null">
                t.sku_total_weight = #{skuTotalWeight},
            </if>
            <if test="autoAfterSaleQuantity != null">
                t.auto_after_sale_quantity = #{autoAfterSaleQuantity},
            </if>
            <if test="autoAfterSaleFlag != null">
                t.auto_after_sale_flag = #{autoAfterSaleFlag},
            </if>
            <if test="helpOrderItemId != null">
                t.help_order_item_id = #{helpOrderItemId},
            </if>
        </trim>
    </sql>

    <!-- 根据主键ID获取数据 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="orderItemExtraResultMap">
        SELECT
        <include refid="orderItemExtraColumns"/>
        FROM order_item_extra t
        WHERE t.id = #{id}
    </select>


    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
    <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.OrderItemExtra" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO order_item_extra
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="orderItemId != null">
                order_item_id,
            </if>
            <if test="buyerId != null">
                buyer_id,
            </if>
            <if test="buyerAdminId != null">
                buyer_admin_id,
            </if>
            <if test="buyerName != null">
                buyer_name,
            </if>
            <if test="supplierId != null">
                supplier_id,
            </if>
            <if test="platformCommissionRatio != null">
                platform_commission_ratio,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=NUMERIC},
            </if>
            <if test="orderItemId != null">
                #{orderItemId,jdbcType=NUMERIC},
            </if>
            <if test="buyerId != null">
                #{buyerId,jdbcType=NUMERIC},
            </if>
            <if test="buyerAdminId != null">
                #{buyerAdminId,jdbcType=NUMERIC},
            </if>
            <if test="buyerName != null">
                #{buyerName,jdbcType=VARCHAR},
            </if>
            <if test="supplierId != null">
                #{supplierId,jdbcType=NUMERIC},
            </if>
            <if test="platformCommissionRatio != null">
                #{platformCommissionRatio,jdbcType=DOUBLE},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>



    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
    <insert id="insertBatch" parameterType="net.summerfarm.mall.model.domain.OrderItemExtra" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO order_item_extra
            (order_item_id,
            buyer_id,
            buyer_admin_id,
            buyer_name,
            supplier_id,
            platform_commission_ratio,
            create_time,
            update_time,
            cost,
            specifications,
            quote_type,
            min_auto_after_sale_threshold,
            total_unit_cost,
            markup_amount
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.orderItemId,jdbcType=NUMERIC},
            #{item.buyerId,jdbcType=NUMERIC},
            #{item.buyerAdminId,jdbcType=NUMERIC},
            #{item.buyerName,jdbcType=VARCHAR},
            #{item.supplierId,jdbcType=NUMERIC},
            #{item.platformCommissionRatio,jdbcType=DOUBLE},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.cost},
            #{item.specifications},
            #{item.quoteType,jdbcType=INTEGER},
            #{item.minAutoAfterSaleThreshold,jdbcType=INTEGER},
            #{item.totalUnitCost},
            #{item.markupAmount}
            )
        </foreach>
    </insert>


    <!-- 根据主键ID进行修改 -->
    <update id="updateSelectiveById" parameterType="net.summerfarm.mall.model.domain.OrderItemExtra">
        UPDATE order_item_extra t
        <include refid="whereColumnByUpdate"></include>
        <where>
            t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>


    <!-- 根据主键ID进行物理删除 -->
    <delete id="remove" parameterType="net.summerfarm.mall.model.domain.OrderItemExtra">
        DELETE
        FROM order_item_extra
        WHERE id = #{id,jdbcType=NUMERIC}
    </delete>

    <select id="selectByOrderItemIds" resultMap="orderItemExtraResultMap">
        SELECT
        <include refid="orderItemExtraColumns"/>
        FROM order_item_extra t
        WHERE t.order_item_id in
        <foreach collection="orderItemIds" item="orderItemId" open="(" separator="," close=")">
            #{orderItemId}
        </foreach>
    </select>

    <update id="updateSelectiveByOrderItemId">
        UPDATE order_item_extra t
        <include refid="whereColumnByUpdate"></include>
        <where>
            t.order_item_id = #{orderItemId,jdbcType=NUMERIC}
        </where>
    </update>
</mapper>