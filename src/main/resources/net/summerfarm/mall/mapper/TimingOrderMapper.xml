<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.TimingOrderMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.TimingOrder">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="delivery_start_time" jdbcType="TIMESTAMP" property="deliveryStartTime" />
    <result column="delivery_end_time" jdbcType="TIMESTAMP" property="deliveryEndTime" />
    <result column="price_ladder" jdbcType="INTEGER" property="priceLadder" />
    <result column="discount_delivery_times" jdbcType="INTEGER" property="discountDeliveryTimes" />
    <result column="delivery_times" jdbcType="INTEGER" property="deliveryTimes" />
    <result column="delivery_unit" property="deliveryUnit" jdbcType="INTEGER" />
    <result column="delivery_upper_limit" property="deliveryUpperLimit" jdbcType="INTEGER" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, delivery_start_time, delivery_end_time, price_ladder, discount_delivery_times, delivery_unit, delivery_upper_limit,
    delivery_times, add_time
  </sql>

  <select id="selectByOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from timing_order
    where order_no = #{orderNo}
  </select>
  
  <update id="updateDeliveryTimes">
    update timing_order t set t.delivery_times =t.delivery_times+ #{quantity}
    WHERE t.order_no = #{orderNo}
  </update>

  <!--auto code-->
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from timing_order
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from timing_order
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.TimingOrder">
    insert into timing_order (id, order_no, delivery_start_time,
    delivery_end_time, price_ladder, discount_delivery_times,
    delivery_times, add_time,delivery_unit, delivery_upper_limit)
    values (#{id,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR}, #{deliveryStartTime,jdbcType=TIMESTAMP},
    #{deliveryEndTime,jdbcType=TIMESTAMP}, #{priceLadder,jdbcType=INTEGER}, #{discountDeliveryTimes,jdbcType=INTEGER},
    #{deliveryTimes,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{deliveryUnit}, #{deliveryUpperLimit})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.TimingOrder">
    insert into timing_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="deliveryStartTime != null">
        delivery_start_time,
      </if>
      <if test="deliveryEndTime != null">
        delivery_end_time,
      </if>
      <if test="priceLadder != null">
        price_ladder,
      </if>
      <if test="discountDeliveryTimes != null">
        discount_delivery_times,
      </if>
      <if test="deliveryTimes != null">
        delivery_times,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="deliveryUnit != null" >
        delivery_unit,
      </if>
      <if test="deliveryUpperLimit != null" >
        delivery_upper_limit,
      </if>
      <if test="type != null" >
        type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryStartTime != null">
        #{deliveryStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryEndTime != null">
        #{deliveryEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="priceLadder != null">
        #{priceLadder,jdbcType=INTEGER},
      </if>
      <if test="discountDeliveryTimes != null">
        #{discountDeliveryTimes,jdbcType=INTEGER},
      </if>
      <if test="deliveryTimes != null">
        #{deliveryTimes,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryUnit != null" >
        #{deliveryUnit},
      </if>
      <if test="deliveryUpperLimit != null" >
        #{deliveryUpperLimit},
      </if>
      <if test="type != null" >
        #{type},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.TimingOrder">
    update timing_order
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryStartTime != null">
        delivery_start_time = #{deliveryStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryEndTime != null">
        delivery_end_time = #{deliveryEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="priceLadder != null">
        price_ladder = #{priceLadder,jdbcType=INTEGER},
      </if>
      <if test="discountDeliveryTimes != null">
        discount_delivery_times = #{discountDeliveryTimes,jdbcType=INTEGER},
      </if>
      <if test="deliveryTimes != null">
        delivery_times = #{deliveryTimes,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.TimingOrder">
    update timing_order
    set order_no = #{orderNo,jdbcType=VARCHAR},
    delivery_start_time = #{deliveryStartTime,jdbcType=TIMESTAMP},
    delivery_end_time = #{deliveryEndTime,jdbcType=TIMESTAMP},
    price_ladder = #{priceLadder,jdbcType=INTEGER},
    discount_delivery_times = #{discountDeliveryTimes,jdbcType=INTEGER},
    delivery_times = #{deliveryTimes,jdbcType=INTEGER},
    add_time = #{addTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="batchUpdateByPrimaryKey">
    update timing_order set delivery_end_time = #{deliveryEndTime}
    where
    `id` in
    <foreach collection="list" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </update>

  <select id="selectDetail" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.vo.TimingOrderVO">
      SELECT o.order_no orderNo, msa.contact accountContact, o.order_time orderTime, p.end_time endTime, p.pay_type payType,
             o.total_price total, origin_price originPrice, o.type, o.status, o.account_id accountId,
        rp.status redPackStatus, rp.red_pack_amount redPackAmount, msa.phone accountPhone,o.order_sale_type orderSaleType,o.area_no areaNo
      FROM orders o
      LEFT JOIN merchant_sub_account msa ON o.account_id = msa.account_id
      LEFT JOIN payment p ON o.order_no = p.order_no
      LEFT JOIN red_pack rp ON o.order_no = rp.order_no
      WHERE o.order_no = #{orderNo}
  </select>

    <select id="listTimingOrders" resultType="java.lang.Integer">
      select t.id FROM  `timing_order` t
      LEFT JOIN  `orders` o on t.`order_no`  = o.`order_no`
      where o.`status`  in (2,3) and o.type = 1
    </select>
  <select id="selectByOrderNoList" resultType="net.summerfarm.mall.model.domain.TimingOrder">
    select id, order_no orderNo, delivery_start_time deliveryStartTime, delivery_end_time deliveryEndTime, price_ladder PriceLadder, discount_delivery_times discountDeliveryTimes, delivery_unit deliveryUnit, delivery_upper_limit deliveryUpperLimit,
    delivery_times deliveryTimes, add_time addTime
    from timing_order
    where order_no in
    <foreach collection="orderNoList" item="orderNo" open="(" close=")" separator=",">
      #{orderNo}
    </foreach>
  </select>

</mapper>