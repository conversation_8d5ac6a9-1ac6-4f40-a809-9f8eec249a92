<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.PCMaxThresholdMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.PCMaxThreshold">
        <result column="id" jdbcType="INTEGER" property="id" />
        <result column="store_no" jdbcType="INTEGER" property="storeNo" />
        <result column="calc_store" jdbcType="VARCHAR" property="calcStore" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="median" jdbcType="REAL" property="median" />
        <result column="mad" jdbcType="REAL" property="mad" />
        <result column="mul" jdbcType="INTEGER" property="mul" />
        <result column="threshold" jdbcType="INTEGER" property="threshold" />
        <result column="create_time" jdbcType="DATE" property="createTime" />
    </resultMap>

    <select id="selectOneBySoreNo" resultMap="BaseResultMap">
    select id,
       store_no,
       calc_store,
       sku,
       median,
       mad,
       mul,
       threshold,
       create_time
    from pc_max_threshold
    where store_no = #{storeNo} and sku = #{sku}
  </select>
</mapper>