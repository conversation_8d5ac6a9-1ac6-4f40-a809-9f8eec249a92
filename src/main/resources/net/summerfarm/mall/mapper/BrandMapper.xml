<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.BrandMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Brand" >
    <id column="brand_id" property="brandId" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="alias" property="alias" jdbcType="VARCHAR" />
    <result column="logo_path" property="logoPath" jdbcType="VARCHAR" />
    <result column="priority" property="priority" jdbcType="INTEGER" />
    <result column="firstcharacter" property="firstcharacter" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    brand_id, name, alias, logo_path, priority, firstcharacter
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from brand
    where brand_id = #{brandId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from brand
    where brand_id = #{brandId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.Brand" >
    insert into brand (brand_id, name, alias, 
      logo_path, priority, firstcharacter
      )
    values (#{brandId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{alias,jdbcType=VARCHAR}, 
      #{logoPath,jdbcType=VARCHAR}, #{priority,jdbcType=INTEGER}, #{firstcharacter,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.Brand" >
    insert into brand
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="brandId != null" >
        brand_id,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="alias != null" >
        alias,
      </if>
      <if test="logoPath != null" >
        logo_path,
      </if>
      <if test="priority != null" >
        priority,
      </if>
      <if test="firstcharacter != null" >
        firstcharacter,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="brandId != null" >
        #{brandId,jdbcType=INTEGER},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="alias != null" >
        #{alias,jdbcType=VARCHAR},
      </if>
      <if test="logoPath != null" >
        #{logoPath,jdbcType=VARCHAR},
      </if>
      <if test="priority != null" >
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="firstcharacter != null" >
        #{firstcharacter,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.Brand" >
    update brand
    <set >
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="alias != null" >
        alias = #{alias,jdbcType=VARCHAR},
      </if>
      <if test="logoPath != null" >
        logo_path = #{logoPath,jdbcType=VARCHAR},
      </if>
      <if test="priority != null" >
        priority = #{priority,jdbcType=INTEGER},
      </if>
      <if test="firstcharacter != null" >
        firstcharacter = #{firstcharacter,jdbcType=VARCHAR},
      </if>
    </set>
    where brand_id = #{brandId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.Brand" >
    update brand
    set name = #{name,jdbcType=VARCHAR},
      alias = #{alias,jdbcType=VARCHAR},
      logo_path = #{logoPath,jdbcType=VARCHAR},
      priority = #{priority,jdbcType=INTEGER},
      firstcharacter = #{firstcharacter,jdbcType=VARCHAR}
    where brand_id = #{brandId,jdbcType=INTEGER}
  </update>

  <select id="selectByIds" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from brand
    where brand_id in
    <foreach collection="ids" open="(" close=")" item="item" separator=",">
      #{item}
    </foreach>
  </select>
</mapper>