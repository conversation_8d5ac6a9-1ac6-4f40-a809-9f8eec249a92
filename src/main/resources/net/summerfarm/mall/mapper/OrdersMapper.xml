<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.OrdersMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Orders" >
    <id column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="m_id" property="mId" jdbcType="BIGINT" />
    <result column="account_id" property="accountId" jdbcType="BIGINT"/>
    <result column="order_time" property="orderTime"/>
    <result column="status" property="status" jdbcType="SMALLINT" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL" />
    <result column="total_price" property="totalPrice" jdbcType="DECIMAL" />
    <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
    <result column="out_times" property="outTimes" jdbcType="INTEGER" />
    <result column="out_times_fee" property="outTimesFee" jdbcType="DECIMAL" />
    <result column="origin_price" property="originPrice" jdbcType="DECIMAL" />
    <result column="discount_card_id" property="discountCardId" jdbcType="INTEGER" />
    <result column="order_sale_type" property="orderSaleType" jdbcType="INTEGER"/>
    <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
    <result column="operate_id" property="operateId" jdbcType="INTEGER"/>
  </resultMap>

    <resultMap id="VOMap" type="net.summerfarm.mall.model.vo.OrderVO">
        <id column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="account_id" property="accountId" jdbcType="BIGINT"/>
        <result column="order_time" property="orderTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="SMALLINT"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="delivery_plan_id" property="deliveryPlanId" jdbcType="INTEGER"/>
        <result column="delivery_status" property="deliveryStatus" jdbcType="INTEGER"/>
        <result column="delivery_time" property="deliveryTime" jdbcType="DATE"/>
        <result column="old_delivery_time" property="oldDeliveryTime" jdbcType="DATE"/>
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL"/>
        <result column="delivery_evaluation_status" property="deliveryEvaluationStatus" jdbcType="INTEGER"/>
        <result column="time_frame" property="timeFrame" jdbcType="VARCHAR"/>
        <result column="total_price" property="total" jdbcType="DECIMAL"/>
        <result column="mcontact" property="mcontact" jdbcType="VARCHAR"/>
        <result column="mphone" property="mphone" jdbcType="VARCHAR"/>
        <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP"/>
        <result column="pay_type" property="payType" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="out_times" property="outTimes" jdbcType="VARCHAR"/>
        <result column="out_times_fee" property="outTimesFee" jdbcType="DECIMAL"/>
        <result column="red_pack_amount" property="redPackAmount" jdbcType="DECIMAL"/>
        <result column="redPackStatus" property="redPackStatus" jdbcType="INTEGER"/>
        <result column="accountId" property="accountId" jdbcType="BIGINT"/>
        <result column="accountContact" property="accountContact" jdbcType="VARCHAR"/>
        <result column="accountPhone" property="accountPhone" jdbcType="VARCHAR"/>
        <result column="mSize" property="mSize" jdbcType="VARCHAR"/>
        <result column="orderSaleType" property="orderSaleType" jdbcType="VARCHAR"/>
        <result column="origin_price" property="originPrice" jdbcType="DECIMAL"/>
        <result column="invoice_status" property="invoiceStatus" jdbcType="INTEGER"/>
        <result column="address_remark" property="addressRemark" jdbcType="VARCHAR"/>
        <result column="master_order_no" property="masterOrderNo" jdbcType="VARCHAR"/>
        <collection property="orderItems" resultMap="net.summerfarm.mall.mapper.OrderItemMapper.BaseResultMap"/>
    </resultMap>

    <select id="unpaidOrders" resultMap="BaseResultMap">
        SELECT * FROM orders
        <where>
            AND type != 10
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="outTimes != null">
                AND out_times = #{outTimes}
            </if>
        </where>
    </select>

    <select id="selectOrders" parameterType="hashmap" resultMap="VOMap">
        SELECT o.order_time, o.order_no, o.status, o.total_price,IFNULL(o.delivery_fee,0) delivery_fee ,
        o.confirm_time, o.type,o.out_times,o.out_times_fee,o.account_id accountId
        FROM orders o
        <where>
            <if test="mId != null">
                AND o.m_id= #{mId}
            </if>
            <if test="status != null">
                AND o.status = #{status}
            </if>
            <if test="type != null">
                AND o.type = #{type}
            </if>
        </where>

    </select>

    <select id="selectTimingOrder" resultType="net.summerfarm.mall.model.vo.TimingDeliveryVO">
        SELECT o.order_no orderNo, o.status orderStatus,o.order_sale_type orderSaleType
        FROM orders o
        WHERE o.type = 1
        <if test="mId != null">
            AND o.m_id= #{mId}
        </if>
        <if test="status != null">
            AND o.status = #{status}
        </if>
    </select>

    <select id="selectOrderList" resultType="net.summerfarm.mall.model.vo.OrderVO">
        SELECT o.order_no orderNo, o.total_price total, o.type, o.status, o.order_time orderTime,o.order_sale_type
        orderSaleType,o.area_no areaNo
        FROM orders o
            <if test="orderStatus != null and orderStatus == 3">
                left join market_partnership_buy_order mpbo on o.order_no = mpbo.order_no
                left join market_partnership_buy mpb on mpbo.partnership_buy_id = mpb.id
            </if>
        WHERE o.m_id = #{mId} and o.type != 3 and o.type != 10 and o.type != 11
        <choose>
            <when test="orderStatus != null and orderStatus == 1">
                AND o.status in(1,12)
            </when>
            <when test="orderStatus != null and orderStatus == 3">
                AND o.status = #{orderStatus} AND (mpb.status is null or mpb.status = 2)
            </when>
            <when test="orderStatus != null">
                AND o.status = #{orderStatus}
            </when>
        </choose>
    </select>

    <select id="selectOrder" parameterType="java.lang.String" resultMap="VOMap">
        SELECT o.order_time,
               o.order_no,
               o.status,
               o.total_price,
               IFNULL(o.delivery_fee, 0)                                                           delivery_fee,
               o.order_sale_type                                                                   orderSaleType,
               o.confirm_time,
               con.contact                                                                         mcontact,
               con.phone                                                                           mphone,
               o.type,
               o.area_no,
               dp.time_frame,
               dp.delivery_time,
               dp.status delivery_status,
               dp.id as delivery_plan_id,
               dp.old_delivery_time,
               dp.delivery_evaluation_status,
               p.end_time,
               CONCAT(con.province, con.city, con.area, con.address, ifnull(con.house_number, '')) address,
               o.out_times,
               o.out_times_fee,
               p.pay_type,
               rp.red_pack_amount,
               rp.`status`                                                                         redPackStatus,
               o.account_id                                                                        accountId,
               msa.contact                                                                         accountContact,
               msa.phone                                                                           accountPhone,
               o.m_size                                                                            mSize,
               o.invoice_status                                                 ,
               o.origin_price,
               con.address_remark
        FROM orders o
                 LEFT JOIN payment p on o.order_no = p.order_no
                 left join delivery_plan dp on dp.order_no = o.order_no
                 left join contact con on dp.contact_id = con.contact_id
                 LEFT JOIN red_pack rp ON rp.order_no = o.order_no
                 left join merchant_sub_account msa
                           on o.m_id = msa.m_id and o.account_id = msa.account_id and msa.delete_flag = 1
        WHERE o.order_no = #{orderNo}
    </select>

    <select id="selectOrderVO" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.vo.OrderVO">
        SELECT o.order_no                 orderNo,
               t.deliveryTime,
               t.mcontact,
               t.mphone,
               o.total_price              total,
               t.address,
               IFNULL(o.delivery_fee, 0)  deliveryFee,
               o.status                   status,
               o.type,
               t.timeFrame,
               o.out_times                outTimes,
               IFNULL(o.out_times_fee, 0) outTimesFee,
               o.red_pack_amount          redPackAmount,
               o.card_rule_id             cardRuleId,
               msa.contact                accountContact,
               msa.phone                  accountPhone,
               o.origin_price             originPrice,
               o.order_sale_type          orderSaleType,
               o.area_no                  areaNo,
               o.order_time               orderTime,
               o.m_size               mSize,
               o.m_id                  mId,
               o.admin_id adminId,
               o.out_stock outStock,
               o.selling_entity_name as sellingEntityName
        FROM orders o
                 LEFT JOIN (
            select dp.order_no,
                   dp.delivery_time                              deliveryTime,
                   dp.time_frame                                 timeFrame,
                   c.contact                                     mcontact,
                   c.phone                                       mphone,
                   CONCAT(c.province, c.city, c.area, c.address) address
            from delivery_plan dp
                     LEFT JOIN contact c ON dp.contact_id = c.contact_id
            WHERE dp.order_no = #{orderNo} limit 1) t ON o.order_no = t.order_no
                 left join merchant_sub_account msa on o.m_id = msa.m_id and o.account_id = msa.account_id
        WHERE o.order_no = #{orderNo}
    </select>

    <select id="countOrders" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT count(1) totalOrders
        from orders t
        WHERE t.status not in (10, 11)
          and t.type != 10 and  t.m_id= #{mId}
    </select>

    <select id="countOrderByDate" resultType="java.lang.Integer">
        SELECT count(1) totalOrders
        FROM orders t
        WHERE t.status IN (1, 2, 3, 6, 8)
          AND t.type != 10
          AND t.m_id = #{mId}
          AND t.order_time >= #{startDate}
          AND t.order_time <![CDATA[<]]> DATE_ADD(#{endDate}, INTERVAL 1 DAY)
    </select>

    <select id="countOrderByMid" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT count(1) totalOrders
        from orders t
        WHERE t.type != 10 and t.status = 6 and t.m_id= #{mId}
    </select>

    <select id="selectOne" resultType="net.summerfarm.mall.model.domain.Orders">
        SELECT m_id mId, account_id accountId, `type`, order_no orderNo,invoice_status invoiceStatus,area_no areaNo,status
        FROM orders
        where order_no = #{orderNo}
    </select>

    <select id="selectByOrderNo" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.domain.Orders">
        select t.order_no         orderNo,
               t.m_id             mId,
               t.account_id       accountId,
               t.status,
               t.total_price      totalPrice,
               t.origin_price     originPrice,
               t.order_time       orderTime,
               t.type,
               t.confirm_time     confirmTime,
               t.out_times        outTimes,
               t.delivery_fee     deliveryFee,
               t.m_size           mSize,
               t.area_no          areaNo,
               t.red_pack_amount  redPackAmount,
               t.card_rule_id     cardRuleId,
               t.discount_card_id discountCardId,
               t.admin_id          adminId,
               msa.contact        accountContact,
               msa.phone          accountPhone,
               t.order_sale_type  orderSaleType,
               t.out_stock        outStock,
               t.order_sale_type  orderSaleType,
               t.order_pay_type   orderPayType,
               t.direct           direct
        from orders t
                 left join merchant_sub_account msa on t.account_id = msa.account_id
        where t.order_no = #{orderNo}
    </select>

    <select id="selectByOrderNoForUpdate" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.domain.Orders">
        select t.order_no         orderNo,
        t.m_id             mId,
        t.account_id       accountId,
        t.status,
        t.total_price      totalPrice,
        t.origin_price     originPrice,
        t.order_time       orderTime,
        t.type,
        t.confirm_time     confirmTime,
        t.out_times        outTimes,
        t.delivery_fee     deliveryFee,
        t.m_size           mSize,
        t.area_no          areaNo,
        t.red_pack_amount  redPackAmount,
        t.card_rule_id     cardRuleId,
        t.discount_card_id discountCardId,
        t.admin_id          adminId,
        t.order_sale_type  orderSaleType,
        t.out_stock        outStock,
        t.order_pay_type   orderPayType
        from orders t
        where t.order_no = #{orderNo}
        for update
    </select>


    <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.Orders">
        insert into orders
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                order_id,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="mId != null">
                m_id,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="orderTime != null">
                order_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="deliveryFee != null">
                delivery_fee,
            </if>
            <if test="totalPrice != null">
                total_price,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="confirmTime != null">
                confirm_time,
            </if>
            <if test="outTimes != null">
                out_times,
            </if>
            <if test="outTimesFee != null">
                out_times_fee,
            </if>
            <if test="areaNo != null">
                area_no,
            </if>
            <if test="mSize != null">
                m_size,
            </if>
            <if test="direct != null">
                direct,
            </if>
            <if test="skuShow != null">
                sku_show,
            </if>
            <if test="redPackAmount != null">
                red_pack_amount,
            </if>
            <if test="cardRuleId != null">
                card_rule_id,
            </if>
            <if test="originPrice != null">
                origin_price,
            </if>
            <if test="discountCardId != null">
                discount_card_id,
            </if>
            <if test="orderSaleType != null">
                order_sale_type,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="operateId != null">
                operate_id,
            </if>
            <if test="orderPayType !=null">
                order_pay_type,
            </if>
            <if test="sellingEntityName !=null">
                selling_entity_name
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="mId != null">
                #{mId,jdbcType=BIGINT},
            </if>
            <if test="accountId != null">
                #{accountId,jdbcType=BIGINT},
            </if>
            <if test="orderTime != null">
                #{orderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="deliveryFee != null">
                #{deliveryFee,jdbcType=DECIMAL},
            </if>
            <if test="totalPrice != null">
                #{totalPrice,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="confirmTime != null">
                #{confirmTime,jdbcType=TIMESTAMP},
            </if>
            <if test="outTimes != null">
                #{outTimes},
            </if>
            <if test="outTimesFee != null">
                #{outTimesFee},
            </if>
            <if test="areaNo != null">
                #{areaNo},
            </if>
            <if test="mSize != null">
                #{mSize},
            </if>
            <if test="direct != null">
                #{direct},
            </if>
            <if test="skuShow != null">
                #{skuShow},
            </if>
            <if test="redPackAmount != null">
                #{redPackAmount},
            </if>
            <if test="cardRuleId != null">
                #{cardRuleId},
            </if>
            <if test="originPrice != null">
                #{originPrice},
            </if>
            <if test="discountCardId != null">
                #{discountCardId},
            </if>
            <if test="orderSaleType != null">
                #{orderSaleType},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
            <if test="operateId != null">
                #{operateId},
            </if>
            <if test="orderPayType !=null">
                #{orderPayType},
            </if>
            <if test="sellingEntityName !=null">
                #{sellingEntityName}
            </if>
        </trim>
    </insert>
    <update id="updateByOrderNoSelective" parameterType="net.summerfarm.mall.model.domain.Orders">
        update orders
        <set>
            <if test="mId != null">
                m_id = #{mId,jdbcType=BIGINT},
            </if>
            <if test="accountId != null">
                account_id = #{accountId,jdbcType=BIGINT},
            </if>
            <if test="orderTime != null">
                order_time = #{orderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=SMALLINT},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="deliveryFee != null">
                delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
            </if>
            <if test="totalPrice != null">
                total_price = #{totalPrice,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="originPrice != null">
                origin_price = #{originPrice,jdbcType=VARCHAR},
            </if>
            <if test="confirmTime != null">
                confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
            </if>
            <if test="outTimes != null">
                out_times = #{outTimes},
            </if>
        </set>
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </update>

    <update id="updateStatus">
        update orders
        set status = #{aimStatus}
        where order_no = #{orderNo}
          and status = #{originalStatus}
    </update>
    <update id="updateOutStock">
        update orders
        set out_stock = 1
        where order_no = #{orderNo}
    </update>

    <select id="countOrder" resultType="java.lang.Integer">
        SELECT count(*)
        FROM orders o
        WHERE o.m_id = #{mId}
          and o.type = #{type}
          and o.status = #{status};
    </select>


    <select id="countOutTimes" resultType="java.lang.Integer">
        SELECT count(*)
        FROM orders o
                 LEFT JOIN payment p on o.order_no = p.order_no
        WHERE o.type != 10 and o.m_id=#{mId}
          and o.status in (2
            , 3
            , 6)
          and out_times=2
          AND p.end_time
            > #{monthStart}
          AND o.out_times_fee=0
    </select>

    <select id="selectTotalPriceByMonth" resultType="java.math.BigDecimal">
        SELECT SUM(total_price) totalPrice
        FROM orders
        WHERE confirm_time <![CDATA[>=]]> #{beginTime}
          AND confirm_time <![CDATA[<]]> #{endTime}
          AND `status` = 6
          AND m_id = #{mId};
    </select>



    <select id="selectSumTotalPrice" resultType="java.math.BigDecimal">
        SELECT SUM(origin_price) totalPrice
        FROM orders
        WHERE order_time <![CDATA[>=]]> #{beginTime}
        AND order_time <![CDATA[<]]> #{endTime}
        AND `status`in (2,3,6)
        and `type` = 1
        <if test="mSize != null">
            AND m_size = #{mSize}
        </if>
        AND m_id=#{mId};
    </select>

    <select id="selectOrdersByMid" resultType="java.lang.String">
        select order_no orderNo
        from orders
        where status in (2,3,6) and m_id = #{mid}
            and order_time <![CDATA[>=]]> #{startDate}
            and order_time <![CDATA[<=]]> #{endDate}
        group by order_no
    </select>

    <select id="selectByMid" resultType="java.lang.String">
        select o.order_no orderNo
        from orders o
        inner join delivery_plan dp on dp.order_no = o.order_no
        <if test='startDate != null'>
            and dp.delivery_time <![CDATA[>=]]> #{startDate}
        </if>
        <if test="endDate != null">
            and dp.delivery_time <![CDATA[<=]]> #{endDate}
        </if>
        inner join order_item oi on o.order_no = oi.order_no and oi.status in (2,3,6)
        inner join contact con on dp.contact_id=con.contact_id
        where o.status in (2,3,6) and o.m_id = #{mid} and o.type = #{type}
        group by o.order_no
    </select>

    <select id="queryOrderByContactId" resultMap="VOMap">
        select *
        from orders o
                 inner join delivery_plan dp on dp.order_no = o.order_no and dp.contact_id = #{contactId} and
                                                dp.delivery_time = #{deliveryTime}
                 inner join order_item oi on o.order_no = oi.order_no
        where o.type != 10
    </select>



    <select id="selectByOrderyNo" parameterType="string" resultType="net.summerfarm.mall.model.vo.OrderVO">
        SELECT o.m_id          mId,
               o.account_id    accountId,
               o.order_no      orderNo,
               m.mname,
               o.order_time    orderTime,
               p.money,
               o.status,
               o.remark,
               o.type,
               o.total_price   totalPrice,
               p.end_time      endTime,
               m.admin_id      majorAdminId,
               m.direct,
               m.size,
               o.out_times     outTimes,
               o.out_times_fee outTimesFee,
               o.delivery_fee  deliveryFee,
               o.out_stock     outStock,
               o.area_no       areaNo,
               msa.contact     subAccountContact,
               msa.phone       subAccountPhone,
               msa.openid      subAccountOpenid,
               o.m_size        mSize,
               o.order_sale_type orderSaleType,
               m.phone         mphone
        FROM (SELECT * FROM orders t WHERE t.order_no = #{orderNo,jdbcType=VARCHAR}) o
                 LEFT JOIN merchant m ON m.m_id = o.m_id
                 LEFT JOIN payment p ON p.order_no = o.order_no
                 INNER JOIN merchant_sub_account msa ON o.account_id = msa.account_id
    </select>

    <select id="selectOrderByMid" resultMap="BaseResultMap">
        select *
        from orders
        where status in (2, 3, 6)
                  and m_id = #{mId}
                  and area_no = #{areaNo}
                  and year (order_time) = #{year}
        order by total_price;
    </select>

    <select id="selectOrderByCategory" resultType="net.summerfarm.mall.model.domain.CategoryOrder">
        select c.category, c.id, sum(oi.price * oi.amount) categoryPrice
        from orders o
                 inner join order_item oi on o.order_no = oi.order_no
                 inner join inventory i on i.sku = oi.sku
                 inner join products p on i.pd_id = p.pd_id
                 inner join category c on p.category_id = c.id
        where oi.status in (2, 3, 6)
          and m_id = #{mId}
          and c.outdated = 0
          and parent_id is not null
          and o.area_no = #{areaNo}
          and o.order_time <![CDATA[>=]]> #{startTime}
          and o.order_time <![CDATA[<]]> #{endTime}
          and o.status in (2, 3, 6)
        group by c.id
        order by categoryPrice desc;
    </select>


    <select id="selectSumAfterPrice" resultType="java.math.BigDecimal">
        SELECT SUM(oi.price * oi.amount) totalPrice
        FROM orders o
        inner join order_item oi on o.order_no = oi.order_no and oi.status = 8
        WHERE o.order_time <![CDATA[>=]]> #{beginTime}
        AND o.order_time <![CDATA[<]]> #{endTime}
        AND o.`status`in (2,3,6) and o.type = 1
        <if test="mSize != null">
            AND o.m_size = #{mSize}
        </if>
        AND o.m_id=#{mId}
    </select>

    <select id="selectUnPayDiscountOrder" resultMap="BaseResultMap">
        SELECT *
        FROM orders
        where status = 1
          and type = 10
          and order_sale_type = 0
          and m_id = #{mId}
          and discount_card_id = #{discountCardId}
    </select>

    <select id="selectSumOrders" resultType="java.lang.Integer">
        SELECT count(*)
        FROM orders o
            <if test="status == 3">
                left join market_partnership_buy_order mpbo on o.order_no = mpbo.order_no
                left join market_partnership_buy mpb on mpbo.partnership_buy_id = mpb.id
            </if>
        WHERE o.m_id = #{mId}
          <choose>
              <when test="status == 3">
                  and o.status = #{status} and (mpb.status is null or mpb.status = 2)
              </when>
              <otherwise>
                  and o.status = #{status}
              </otherwise>
          </choose>
          and `type` in (0, 1, 30)
    </select>
    <select id="selectPeriodMid" resultType="java.lang.Long">
        select m_id
        from orders
        where status in (2, 3, 6)
          and DATE (order_time) between #{sTime}
          and #{eTime}
        group by m_id
        having count (1)>0

    </select>

    <select id="selectIsFruit" resultType="net.summerfarm.mall.model.domain.FruitSales">
        select oi.id orderItemId, oi.sku sku, oi.amount sales
        from orders o
                 left join order_item oi on o.order_no = oi.order_no
                 left join inventory i on oi.sku = i.sku
                 left join products p on i.pd_id = p.pd_id
                 inner join category c on p.category_id = c.id
        where c.type = 4 and o.order_no = #{orderNo};
    </select>
    <select id="selectIsFruitByItemId" resultType="net.summerfarm.mall.model.domain.FruitSales">
        select oi.id orderItemId, oi.sku sku, oi.amount * (-1) sales
                 from order_item oi
                 left join orders o on o.order_no=oi.order_no
                 left join inventory i on oi.sku = i.sku
                 left join products p on i.pd_id = p.pd_id
                 inner join category c on p.category_id = c.id
        where c.type = 4 and oi.id = #{orderItemId}
    </select>

    <select id="selectUnPayRechargeOrder" resultMap="BaseResultMap">
      SELECT order_id,
             order_no,
             m_id,
             order_time,
             type,
             status,
             delivery_fee,
             total_price,
             remark,
             confirm_time,
             area_name,
             out_times,
             discount_type,
             out_times_fee,
             area_no,
             m_size,
             account_id,
             origin_price,
             out_stock,
             discount_card_id,
             order_sale_type,
             receivable_status,
             admin_id,
             update_time,
             operate_id
      FROM orders
      where status = 1
        and type = 10
        and order_sale_type = 1
        and m_id = #{mId}
        and (discount_card_id = #{configId} or (discount_card_id is null and total_price = #{customAmount}))
    </select>
    <select id="unpaidVirtualOrders" resultMap="BaseResultMap">
      select order_id,
             order_no,
             m_id,
             order_time,
             type,
             status
      from orders
      where type = 10 and status = 1
    </select>

    <select id="selectIsFirstConsume" resultType="java.lang.String">
       SELECT DISTINCT(oi.sku) FROM order_item oi LEFT JOIN orders o on oi.order_no = o.order_no LEFT JOIN merchant m ON o.m_id = m.m_id
          WHERE o.status > 1 and m.m_id = #{mId} and  oi.sku in
        <foreach collection="list" open="(" close=") " separator="," item="item">
            #{item}
        </foreach>
            and o.order_time <![CDATA[>=]]>  #{recentConsumeTime};
    </select>
    <select id="selectSkuOrderd" resultType="java.lang.String">
        select distinct sku from
                orders o left join order_item oi on o.order_no = oi.order_no
            where oi.sku in
              <foreach collection="productSkuInfoVOS" item="item" open="(" close=")" separator=",">
                  #{item.sku}
              </foreach>
              and o.m_id = #{mId} and oi.status in (2,3,6)
    </select>

    <select id="checkIsCBD" resultType="java.lang.Boolean">
        select count(1)
        from orders o
        left join order_item oi on o.order_no = oi.order_no
        left join merchant m on o.m_id = m.m_id
        where o.order_no = #{orderNo} and oi.sku = #{sku}
        <if test="adminIds != null">
            and m.admin_id in
            <foreach collection="adminIds.split(',')" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="hasOrderInPeriod" resultType="boolean">
      select if(count(*) > 0, false, true)  from order_item oi
        left join orders o on oi.order_no = o.order_no
      where o.status in (1,2,3,6,7,8,10,12,13,14)
        and o.m_id = #{mId}
        and oi.sku = #{sku}
        and o.order_time &gt;= #{startTime}
        and o.order_time &lt; #{endTime}
    </select>

    <select id="hasOrderByMid" resultType="java.lang.Boolean">
        select if(count(*) > 0, false, true)  from  orders o
        where o.status in (2,3,6,7,8,10,12,13,14)
        and o.m_id = #{mId}
        and o.order_no != #{orderNo}
        and o.order_time &gt;= #{startTime}
        and o.order_time &lt; #{endTime}
    </select>
    <select id="hasDeliveryPlanByTime" resultType="java.lang.Boolean">
       SELECT if(count(*) > 0, true, false) FROM delivery_plan a
       LEFT JOIN orders o on  a.order_no = o.order_no
       where o.type = 1 and a.delivery_time = #{localDate}
        <if test="contactIds != null">
            and contact_id in
            <foreach collection="contactIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

  <select id="listTimeOutOrders" resultMap="BaseResultMap">
    SELECT * FROM orders
              where `status` = 1 and order_time &lt;= DATE_ADD(NOW(), INTERVAL -30 MINUTE)
  </select>
  
  <select id="countOrdersByType" resultType="int">
    select count(distinct o.order_no) from orders o
        left join order_preferential op
            on o.order_no = op.order_no where m_id = #{mId} and op.type = 15
  </select>

    <select id="orderDeliveryList" resultType="net.summerfarm.mall.model.dto.order.OrderDeliveryDTO">
       SELECT
       d.order_no as orderNo,
       d.contact_id as contactId,
       o.order_time as orderTime,
       o.status as status,
       o.order_sale_type as orderSaleType
       FROM
        orders o
       LEFT JOIN delivery_plan d on o.order_no = d.order_no
       where
       o.status = 3
       and o.m_id =  #{mId}
       and d.delivery_time =  #{deliveryTime}
       ORDER BY o.order_time desc
    </select>

    <select id="selectAreaNoByOrderNo" resultType="java.lang.Integer">
        select area_no
        from orders
        where order_no = #{orderNo}
    </select>

  <select id="listOrderByOrderNoList" resultType="net.summerfarm.mall.model.domain.Orders">
    select order_no         orderNo,
          m_id             mId,
          account_id       accountId,
          status,
          total_price      totalPrice,
          origin_price     originPrice,
          order_time       orderTime,
          type,
          confirm_time     confirmTime,
          out_times        outTimes,
          delivery_fee     deliveryFee,
          m_size           mSize,
          area_no          areaNo,
          red_pack_amount  redPackAmount,
          card_rule_id     cardRuleId,
          discount_card_id discountCardId,
          admin_id          adminId,
          order_sale_type  orderSaleType,
          out_stock        outStock,
          order_sale_type  orderSaleType,
          order_pay_type   orderPayType,
          direct,
      selling_entity_name sellingEntityName
      FROM orders
      where order_no in
      <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
          #{item}
      </foreach>
    </select>

  <select id="getUnfilledOrderByMid" resultType="java.lang.Integer" parameterType="java.lang.Long">
      SELECT count(*)
      FROM orders
      WHERE m_id = #{mId}
        and status in (1, 2, 3)
    </select>
    <select id="getNeedReturnTimingOrder" resultType="java.lang.String">
        select order_no as orderNo
        from orders
        where status in (2,3)
            and type = 1
            and order_time <![CDATA[>=]]> #{startTime}
            and order_time <![CDATA[<=]]> #{endTime}
    </select>
    <select id="hasOrderInPeriodForSkuList" resultType="java.lang.String">
        select oi.sku
        from order_item oi
        left join orders o on oi.order_no = o.order_no
      where o.status in (1,2,3,6,7,8,10,12,13,14)
        and o.m_id = #{mId}
        and o.order_time &gt;= #{startTime}
        and o.order_time &lt; #{endTime}
        and oi.sku in
        <foreach collection="list" open="(" close=") " separator="," item="item">
            #{item}
        </foreach>
         group by oi.sku having count(*) > 0
    </select>

  <select id="listWarningTimingOrder" resultType="net.summerfarm.mall.model.vo.TimingDeliveryVO">
    select
      o.order_time orderTime,
      o.order_no orderNo,
      o.total_price totalPrice,
      o.account_id accountId,
      oi.sku,
      oi.pd_name pdName,
      oi.amount quantity
    from orders o
        left join order_item oi on o.order_no = oi.order_no
    where o.status =3 and o.type = 1
    and o.order_time between #{startTime} and #{endTime}
  </select>

  <select id="listTimingOrder" resultType="net.summerfarm.mall.model.vo.TimingDeliveryVO">
    SELECT o.order_no orderNo,
           o.status orderStatus,
           o.order_time orderTime,
    oi.sku,
    oi.pd_name pdName,
    oi.amount quantity,
    oi.picture_path picPath
    FROM orders o
        left join order_item oi on o.order_no = oi.order_no
    WHERE o.type = 1
    <if test="mId != null">
      AND o.m_id= #{mId}
    </if>
    <if test="status != null">
      AND o.status = #{status}
    </if>
  </select>

    <select id="selectDeliveryEvaluationList" resultType="net.summerfarm.mall.model.dto.order.DeliveryEvaluationDTO">
        SELECT o.order_no orderNo, o.total_price totalPrice, o.delivery_fee deliveryFee, o.type, o.status, o.order_time orderTime,o.order_sale_type
        orderSaleType,o.area_no areaNo,dp.id deliveryPlanId
        FROM orders o
        left join delivery_plan dp on o.order_no = dp.order_no
        WHERE o.m_id = #{mId} and o.type != 3 and o.type != 10 and o.type != 11 and dp.status = 6
        <choose>
            <when test="deliveryEvaluationStatus != null and deliveryEvaluationStatus == 0">
                AND dp.delivery_evaluation_status = 0 and dp.delivery_time >= #{deliveryDate}
            </when>
            <when test="deliveryEvaluationStatus != null and deliveryEvaluationStatus == 1">
                AND dp.delivery_evaluation_status = 1
            </when>
        </choose>
        group by o.order_no
        order by dp.delivery_time desc , o.order_time desc
    </select>
    <select id="getNeedReturnTimingOrderForView" resultType="java.lang.String">
        select order_no as orderNo
        from orders
        where status in (2,3)
            and type = 1
            and order_time <![CDATA[<=]]> #{oldOrderTime}
            and m_id = #{mId}
    </select>
    <select id="getNeedReturnTimingOrderForSms" resultType="java.lang.String">
        select order_no as orderNo
        from orders
        where status in (2,3)
            and type = 1
            and order_time = #{orderTime}
    </select>

    <select id="selectAllUncompletedOrders" resultType="net.summerfarm.mall.model.domain.Orders">
    select order_no as orderNo, m_id as mId
        from orders
        where status in (2,3)
            and type = 1
    </select>
    <select id="selectUnscheduledOrders" resultType="java.lang.String">
        SELECT o.order_no
        FROM orders o
        LEFT JOIN delivery_plan dp ON o.order_no = dp.order_no
        WHERE o.m_id = #{mId} and o.type = 1 and o.status in (2,3)
        AND o.order_time <![CDATA[<]]> NOW() - INTERVAL 48 HOUR
        AND dp.delivery_time IS NULL
    </select>

  <select id="listAllTimeOutError" resultMap="VOMap">
    SELECT orl.master_order_no,o.* FROM order_relation orl left join orders o on o.order_no = orl.order_no
    where `status` = 1 and order_time &lt;= #{orderTime} group by master_order_no
  </select>


    <select id="exitByMidSkuOder" resultType="net.summerfarm.mall.model.vo.OrderVO">
        select
        o.order_id as orderId,
        o.order_no as orderNo,
        o.m_id,
        o.order_time
        from orders o
        join order_item  oi on o.order_no =  oi.order_no
        where o.status in (2, 3, 6)
        and o.m_id = #{mId}
        and  oi.`sku`  in
        <foreach collection="skus" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        LIMIT 1

    </select>
    <select id="getOrderRecordBySpu" resultType="java.lang.Long">
        SELECT o.m_id FROM orders o
                          LEFT JOIN order_item oi  on o.order_no = oi.order_no
                          left join inventory i on oi.sku = i.sku
                          LEFT JOIN products p on p.pd_id = i.pd_id
        WHERE
        o.`status` in (2, 3, 6)
        and o.order_time BETWEEN DATE_ADD(CURRENT_DATE(), INTERVAL -1 DAY) AND CURRENT_TIME
        and oi.status in (2, 3, 6) and p.pd_no = #{pdNo} and o.m_id in
        <foreach collection="mIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY o.`m_id`
    </select>

    <select id="selectContactIdListByOrderTimeAndMId" resultType="java.lang.Long">
        select DISTINCT(dp.contact_id)
        from orders o left join delivery_plan dp on o.order_no = dp.order_no
        where o.order_time >= #{startOrderTime,jdbcType=TIMESTAMP}
        and o.order_time &lt;= #{endOrderTime,jdbcType=TIMESTAMP}
        and o.m_id = #{mId,jdbcType=BIGINT}
    </select>

    <select id="querySellingEntityByNos" resultType="java.lang.String">
        SELECT
        distinct o.selling_entity_name
        FROM
        orders o
        WHERE
        o.order_no IN
        <foreach collection="orderNos" item="orderNo" separator="," open="(" close=")">
            #{orderNo}
        </foreach>
    </select>
</mapper>
