<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.FinanceBankFlowingWaterMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.FinanceBankFlowingWater">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="trading_day" jdbcType="VARCHAR" property="tradingDay" />
    <result column="trading_time" jdbcType="VARCHAR" property="tradingTime" />
    <result column="value_date" jdbcType="VARCHAR" property="valueDate" />
    <result column="trading_type" jdbcType="VARCHAR" property="tradingType" />
    <result column="abstract_text" jdbcType="VARCHAR" property="abstractText" />
    <result column="transaction_amount" jdbcType="DECIMAL" property="transactionAmount" />
    <result column="mark" jdbcType="VARCHAR" property="mark" />
    <result column="serial_number" jdbcType="VARCHAR" property="serialNumber" />
    <result column="process_instance_number" jdbcType="VARCHAR" property="processInstanceNumber" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="purpose" jdbcType="VARCHAR" property="purpose" />
    <result column="business_reference_number" jdbcType="VARCHAR" property="businessReferenceNumber" />
    <result column="business_summary" jdbcType="VARCHAR" property="businessSummary" />
    <result column="other_summaries" jdbcType="VARCHAR" property="otherSummaries" />
    <result column="bank_area_no" jdbcType="VARCHAR" property="bankAreaNo" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="account_number" jdbcType="VARCHAR" property="accountNumber" />
    <result column="bank_no" jdbcType="VARCHAR" property="bankNo" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_address" jdbcType="VARCHAR" property="bankAddress" />
    <result column="company_division" jdbcType="VARCHAR" property="companyDivision" />
    <result column="company_account" jdbcType="VARCHAR" property="companyAccount" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="information_signs" jdbcType="VARCHAR" property="informationSigns" />
    <result column="information_existence" jdbcType="VARCHAR" property="informationExistence" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="reversal_flag" jdbcType="VARCHAR" property="reversalFlag" />
    <result column="extended_summary" jdbcType="VARCHAR" property="extendedSummary" />
    <result column="transaction_analysis_code" jdbcType="VARCHAR" property="transactionAnalysisCode" />
    <result column="payment_order_no" jdbcType="VARCHAR" property="paymentOrderNo" />
    <result column="enterprise_identification_code" jdbcType="VARCHAR" property="enterpriseIdentificationCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="claim_status" jdbcType="INTEGER" property="claimStatus" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, trading_day, trading_time, value_date, trading_type, abstract_text, transaction_amount,
    mark, serial_number, process_instance_number, business_name, purpose, business_reference_number, 
    business_summary, other_summaries, bank_area_no, user_name, account_number, bank_no, 
    bank_name, bank_address, company_division, company_account, company_name, information_signs, 
    information_existence, bill_no, reversal_flag, extended_summary, transaction_analysis_code, 
    payment_order_no, enterprise_identification_code, create_time, update_time, updater, 
    claim_status,pay_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_bank_flowing_water
    where id = #{id,jdbcType=BIGINT} and mark = 'C'
  </select>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.FinanceBankFlowingWater">
    update finance_bank_flowing_water
    <set>
      <if test="tradingDay != null">
        trading_day = #{tradingDay,jdbcType=VARCHAR},
      </if>
      <if test="tradingTime != null">
        trading_time = #{tradingTime,jdbcType=VARCHAR},
      </if>
      <if test="valueDate != null">
        value_date = #{valueDate,jdbcType=VARCHAR},
      </if>
      <if test="tradingType != null">
        trading_type = #{tradingType,jdbcType=VARCHAR},
      </if>
      <if test="abstractText != null">
        abstract_text = #{abstractText,jdbcType=VARCHAR},
      </if>
      <if test="transactionAmount != null">
        transaction_amount = #{transactionAmount,jdbcType=DECIMAL},
      </if>
      <if test="mark != null">
        mark = #{mark,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        serial_number = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="processInstanceNumber != null">
        process_instance_number = #{processInstanceNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="purpose != null">
        purpose = #{purpose,jdbcType=VARCHAR},
      </if>
      <if test="businessReferenceNumber != null">
        business_reference_number = #{businessReferenceNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessSummary != null">
        business_summary = #{businessSummary,jdbcType=VARCHAR},
      </if>
      <if test="otherSummaries != null">
        other_summaries = #{otherSummaries,jdbcType=VARCHAR},
      </if>
      <if test="bankAreaNo != null">
        bank_area_no = #{bankAreaNo,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null">
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="bankNo != null">
        bank_no = #{bankNo,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAddress != null">
        bank_address = #{bankAddress,jdbcType=VARCHAR},
      </if>
      <if test="companyDivision != null">
        company_division = #{companyDivision,jdbcType=VARCHAR},
      </if>
      <if test="companyAccount != null">
        company_account = #{companyAccount,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="informationSigns != null">
        information_signs = #{informationSigns,jdbcType=VARCHAR},
      </if>
      <if test="informationExistence != null">
        information_existence = #{informationExistence,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="reversalFlag != null">
        reversal_flag = #{reversalFlag,jdbcType=VARCHAR},
      </if>
      <if test="extendedSummary != null">
        extended_summary = #{extendedSummary,jdbcType=VARCHAR},
      </if>
      <if test="transactionAnalysisCode != null">
        transaction_analysis_code = #{transactionAnalysisCode,jdbcType=VARCHAR},
      </if>
      <if test="paymentOrderNo != null">
        payment_order_no = #{paymentOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseIdentificationCode != null">
        enterprise_identification_code = #{enterpriseIdentificationCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="claimStatus != null">
        claim_status = #{claimStatus},
      </if>
      <if test="payType != null">
        pay_type = #{payType},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>