<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.LuckyDrawActivityPrizeMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.LuckyDrawActivityPrize">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="equity_package_id" jdbcType="BIGINT" property="equityPackageId" />
    <result column="coupon_id" jdbcType="BIGINT" property="couponId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
 
  <sql id="Base_Column_List">
    id, activity_id, equity_package_id, coupon_id, create_time,
    update_time
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lucky_draw_activity_prize
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lucky_draw_activity_prize
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.LuckyDrawActivityPrize">
    insert into lucky_draw_activity_prize (id, activity_id, equity_package_id, 
      coupon_id, create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{activityId,jdbcType=BIGINT}, #{equityPackageId,jdbcType=BIGINT}, 
        #{couponId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.LuckyDrawActivityPrize">
    insert into lucky_draw_activity_prize
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="equityPackageId != null">
        equity_package_id,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="equityPackageId != null">
        #{equityPackageId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
 
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.LuckyDrawActivityPrize">
    update lucky_draw_activity_prize
    <set>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="equityPackageId != null">
        equity_package_id = #{equityPackageId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.LuckyDrawActivityPrize">
    update lucky_draw_activity_prize
    set activity_id = #{activityId,jdbcType=BIGINT},
      equity_package_id = #{equityPackageId,jdbcType=BIGINT},
      coupon_id = #{couponId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByActivityId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lucky_draw_activity_prize
    where activity_id = #{activityId,jdbcType=BIGINT}
    </select>

  <select id="selectByEquityPackageId" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List" />
    from lucky_draw_activity_prize
    where equity_package_id = #{equityPackageId,jdbcType=BIGINT}
  </select>
</mapper>