<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.TimingOrderRefundRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.TimingOrderRefundRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, type, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from timing_order_refund_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByOrderNoList" resultType="java.lang.String">
    select order_no
    from timing_order_refund_record
    where order_no in
    <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
     and type = #{type}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from timing_order_refund_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.TimingOrderRefundRecord" useGeneratedKeys="true">
    insert into timing_order_refund_record (order_no, create_time, update_time
      )
    values (#{orderNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.TimingOrderRefundRecord" useGeneratedKeys="true">
    insert into timing_order_refund_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
    <insert id="insertBatch">
      insert into timing_order_refund_record (order_no, type, create_time, update_time) values
      <foreach collection="list" separator="," item="item">
        (#{item.orderNo,jdbcType=VARCHAR}, #{item.type,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
      </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.TimingOrderRefundRecord">
    update timing_order_refund_record
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.TimingOrderRefundRecord">
    update timing_order_refund_record
    set order_no = #{orderNo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>