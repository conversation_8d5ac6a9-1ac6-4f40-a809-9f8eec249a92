<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.ShoppingCartMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.ShoppingCart">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="parent_sku" jdbcType="VARCHAR" property="parentSku" />
    <result column="product_type" jdbcType="TINYINT" property="productType" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, m_id, account_id, biz_id, sku, parent_sku, product_type, quantity, create_time, 
    update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from shopping_cart
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from shopping_cart
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.ShoppingCart">
    insert into shopping_cart (id, m_id, account_id, 
      biz_id, sku, parent_sku, 
      product_type, quantity, create_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, 
      #{bizId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{parentSku,jdbcType=VARCHAR}, 
      #{productType,jdbcType=TINYINT}, #{quantity,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" keyColumn="id" parameterType="net.summerfarm.mall.model.domain.ShoppingCart">
    <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into shopping_cart
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="parentSku != null">
        parent_sku,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="parentSku != null">
        #{parentSku,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=TINYINT},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.ShoppingCart">
    update shopping_cart
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="parentSku != null">
        parent_sku = #{parentSku,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=TINYINT},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.ShoppingCart">
    update shopping_cart
    set m_id = #{mId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=BIGINT},
      biz_id = #{bizId,jdbcType=BIGINT},
      sku = #{sku,jdbcType=VARCHAR},
      parent_sku = #{parentSku,jdbcType=VARCHAR},
      product_type = #{productType,jdbcType=TINYINT},
      quantity = #{quantity,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByEntity" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from shopping_cart
    <where>
      <if test="mId != null">
        and m_id = #{mId,jdbcType=BIGINT}
      </if>
      <if test="accountId != null">
        and account_id = #{accountId,jdbcType=BIGINT}
      </if>
      <if test="bizId != null">
        and biz_id = #{bizId,jdbcType=BIGINT}
      </if>
      <if test="sku != null">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="parentSku != null">
        and parent_sku = #{parentSku,jdbcType=VARCHAR}
      </if>
      <if test="productType != null">
        and product_type = #{productType,jdbcType=TINYINT}
      </if>
    </where>
    limit 1
  </select>

  <select id="getSumQuantity" resultType="java.lang.Integer">
    select sum(quantity) quantity
    from shopping_cart
    where m_id = #{mId,jdbcType=BIGINT} and account_id = #{accountId,jdbcType=BIGINT}
      and sku = #{sku,jdbcType=VARCHAR} and parent_sku = #{parentSku,jdbcType=VARCHAR}
      and product_type in(0,2)
  </select>

  <update id="updateQuantityByEntity">
    update shopping_cart
    <set>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <where>
      <if test="mId != null">
        and m_id = #{mId,jdbcType=BIGINT}
      </if>
      <if test="accountId != null">
        and account_id = #{accountId,jdbcType=BIGINT}
      </if>
      <if test="bizId != null">
        and biz_id = #{bizId,jdbcType=BIGINT}
      </if>
      <if test="sku != null">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="parentSku != null">
        and parent_sku = #{parentSku,jdbcType=VARCHAR}
      </if>
      <if test="productType != null">
        and product_type = #{productType,jdbcType=TINYINT}
      </if>
    </where>
  </update>

  <delete id="batchDelete" parameterType="java.util.List">
    delete from shopping_cart
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
  </delete>

  <select id="getShoppingCarts" resultType="net.summerfarm.mall.model.vo.ShoppingCartVO">
    select sc.sku, p.category_id categoryId, sc.id, IFNULL(a.price,i.sale_price) salePrice, a.on_sale onSale, ifnull(i.sku_pic, p.picture_path) picturePath,
    i.sku_name skuName, sc.biz_id bizId, sc.parent_sku parentSku, sc.product_type productType, sc.quantity, p.pd_name pdName, a.limited_quantity limitedQuantity,
    i.weight, i.sku_pic skuPic, a.price originalPrice, a.price mallPrice, i.base_sale_quantity baseSaleQuantity, i.base_sale_unit baseSaleUnit, a.sales_mode salesMode, a.ladder_price ladderPrice,
    a.m_type mType, p.pd_id pdId, i.sub_type subType
    ,i.net_weight_num netWeightNum,i.video_url videoUrl,i.after_sale_rule_detail afterSaleRuleDetail,i.buyer_name buyerName,i.buyer_id buyerId,
    i.video_upload_user videoUploadUser,i.video_upload_time videoUploadTime
    ,i.weight_num weightNum
    <if test="adminId != null">
      ,mp.price price ,mp.direct, mp.pay_method payMethod, mp.price_type priceType,mp.price_adjustment_value priceAdjustmentValue, a.price activityOriginPrice
    </if>
    from shopping_cart sc
    LEFT JOIN area_sku a on sc.sku = a.sku
    LEFT JOIN inventory i on sc.sku = i.sku
    LEFT JOIN products p on p.pd_id =i.pd_id
    <if test="adminId != null">
      LEFT JOIN major_price mp on mp.sku = a.sku and mp.area_no = a.area_no and mp.admin_id = #{adminId} and mp.direct = #{direct}
      and mp.valid_time <![CDATA[<=]]> now() and  mp.invalid_time <![CDATA[>]]> now()
    </if>
    where sc.m_id = #{mId,jdbcType=BIGINT} and sc.account_id = #{accountId,jdbcType=BIGINT}
      and i.outdated = 0 and a.area_no = #{areaNo} and sc.quantity > 0
    order by sc.id desc
  </select>

  <delete id="deleteByEntity">
    delete from shopping_cart
    <where>
      <if test="mId != null">
        and m_id = #{mId,jdbcType=BIGINT}
      </if>
      <if test="accountId != null">
        and account_id = #{accountId,jdbcType=BIGINT}
      </if>
      <if test="sku != null">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="productType != null">
        and product_type = #{productType,jdbcType=TINYINT}
      </if>
      <if test="parentSku != null">
        and parent_sku = #{parentSku,jdbcType=VARCHAR}
      </if>
    </where>
  </delete>

  <delete id="batchDeleteByEntity">
    delete from shopping_cart
    where (`m_id`, `account_id`, `sku`, `product_type`, `parent_sku`) in
    <foreach collection="list" open="(" separator="," close=")" item="item">
      (#{item.mId}, #{item.accountId}, #{item.sku}, #{item.productType}, #{item.parentSku})
    </foreach>
  </delete>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from shopping_cart
    where m_id = #{mId,jdbcType=BIGINT} and account_id = #{accountId,jdbcType=BIGINT}
  </select>

  <update id="batchUpdateQuantity">
    update shopping_cart
    set `quantity` = CASE `id`
    <foreach collection="list" item="item">
      when #{item.id} then #{item.quantity}
    </foreach>
    END
    WHERE `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=",">
      #{item.id}
    </foreach>
  </update>

  <update id="updateQuantityById">
    update shopping_cart
    set `quantity` = #{quantity,jdbcType=INTEGER}
    WHERE `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="getAll" resultType="net.summerfarm.mall.model.vo.ShoppingCartVO">
    select sc.sku, sc.biz_id bizId, sc.parent_sku parentSku, sc.product_type productType, sc.quantity, p.pd_name pdName,
    i.weight, p.pd_id pdId, sc.m_id mId, sc.account_id accountId, sc.id
    from shopping_cart sc
    inner JOIN inventory i on sc.sku = i.sku
    inner JOIN products p on p.pd_id =i.pd_id
    where sc.m_id = #{mId,jdbcType=BIGINT}
    <if test="accountId != null">
      and sc.account_id = #{accountId,jdbcType=BIGINT}
    </if>
    order by sc.id desc
  </select>
</mapper>