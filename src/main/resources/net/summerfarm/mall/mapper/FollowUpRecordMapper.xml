<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.FollowUpRecordMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.FollowUpRecord" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="m_id" property="mId" jdbcType="BIGINT" />
    <result column="admin_id" property="adminId" jdbcType="BIGINT" />
    <result column="admin_name" property="adminName" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="m_lifecycle" property="mLifecycle" jdbcType="INTEGER" />
    <result column="m_tag" property="mTag" jdbcType="VARCHAR" />
    <result column="m_last_order_time" property="mLastOrderTime" jdbcType="TIMESTAMP" />
    <result column="follow_up_way" property="followUpWay" jdbcType="VARCHAR" />
    <result column="follow_up_pic" property="followUpPic" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="priority" property="priority" jdbcType="INTEGER" />
    <result column="condition" property="condition" jdbcType="VARCHAR" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="contact_id" property="contactId"  />
    <result column="next_follow_time" property="nextFollowTime"/>
    <result column="expected_content" property="expectedContent"/>
    <result column="visit_objective" property="visitObjective"/>
    <result column="whether_remark" property="whetherRemark"/>
    <result column="visit_type" property="visitType"/>
    <result column="location" property="location"/>
    <result column="kp_id" property="kpId"/>
    <result column="poi_note" property="poiNote"/>
    <result column="escort_admin_id" property="escortAdminId"/>
    <result column="account_id" property="accountId"/>
    <result column="feedback" property="feedback"/>
    <result column="feedback_time" property="feedbackTime"/>

  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, admin_id, admin_name, creator, m_lifecycle, m_tag, m_last_order_time, follow_up_way, follow_up_pic, status,
    priority, `condition`, add_time,contact_id,next_follow_time,expected_content,visit_objective,whether_remark,visit_type,location,
    kp_id,poi_note,escort_admin_id,account_id,feedback,feedback_time
  </sql>



  <update id="updateById" parameterType="net.summerfarm.mall.model.domain.FollowUpRecord">
    UPDATE follow_up_record t
    <set>
      <if test="followUpPic != null" >
        t.follow_up_pic = #{followUpPic},
      </if>
      <if test="followUpWay != null" >
        t.follow_up_way = #{followUpWay},
      </if>
      <if test="status != null" >
        t.status =#{status},
      </if>
      <if test="condition != null" >
        t.condition = #{condition,jdbcType=VARCHAR},
      </if>
      <if test=" addTime !=null">
        t.add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedContent != null">
        t.expected_content = #{expectedContent},
      </if>
      <if test="visitObjective != null">
          t.visit_objective = #{visitObjective},
      </if>
      <if test="feedback != null">
        t.feedback = #{feedback},
      </if>
      <if test="feedbackTime != null">
        t.feedback_time = #{feedbackTime}
      </if>
    </set>
    where t.id = #{id,jdbcType=INTEGER}
  </update>



  <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from follow_up_record
    where id=#{id}
  </select>


</mapper>