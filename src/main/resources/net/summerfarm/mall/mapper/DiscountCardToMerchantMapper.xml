<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.DiscountCardToMerchantMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.DiscountCardToMerchant">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="discount_card_id" jdbcType="INTEGER" property="discountCardId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="total_times" jdbcType="INTEGER" property="totalTimes" />
    <result column="used_times" jdbcType="INTEGER" property="usedTimes" />
    <result column="deadline" jdbcType="DATE" property="deadline" />
    <result column="updator" jdbcType="VARCHAR" property="updator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, discount_card_id, `status`, total_times, used_times, deadline, updator, 
    update_time, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from discount_card_to_merchant
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from discount_card_to_merchant
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.DiscountCardToMerchant" useGeneratedKeys="true">
    insert into discount_card_to_merchant (m_id, discount_card_id, `status`, 
      total_times, used_times, deadline, 
      updator, update_time, creator, 
      create_time)
    values (#{mId,jdbcType=BIGINT}, #{discountCardId,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{totalTimes,jdbcType=INTEGER}, #{usedTimes,jdbcType=INTEGER}, #{deadline,jdbcType=DATE}, 
      #{updator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.DiscountCardToMerchant" useGeneratedKeys="true">
    insert into discount_card_to_merchant
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="discountCardId != null">
        discount_card_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="totalTimes != null">
        total_times,
      </if>
      <if test="usedTimes != null">
        used_times,
      </if>
      <if test="deadline != null">
        deadline,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="discountCardId != null">
        #{discountCardId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="totalTimes != null">
        #{totalTimes,jdbcType=INTEGER},
      </if>
      <if test="usedTimes != null">
        #{usedTimes,jdbcType=INTEGER},
      </if>
      <if test="deadline != null">
        #{deadline,jdbcType=DATE},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.DiscountCardToMerchant">
    update discount_card_to_merchant
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="discountCardId != null">
        discount_card_id = #{discountCardId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="totalTimes != null">
        total_times = #{totalTimes,jdbcType=INTEGER},
      </if>
      <if test="usedTimes != null">
        used_times = #{usedTimes,jdbcType=INTEGER},
      </if>
      <if test="deadline != null">
        deadline = #{deadline,jdbcType=DATE},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.DiscountCardToMerchant">
    update discount_card_to_merchant
    set m_id = #{mId,jdbcType=BIGINT},
      discount_card_id = #{discountCardId,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      total_times = #{totalTimes,jdbcType=INTEGER},
      used_times = #{usedTimes,jdbcType=INTEGER},
      deadline = #{deadline,jdbcType=DATE},
      updator = #{updator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectUsableDiscountCard" resultMap="BaseResultMap">
    select dctm.* from discount_card_to_merchant dctm
    where dctm.status = 1
      and dctm.discount_card_id = #{discountCardId}
      and dctm.m_id = #{mId}
      and dctm.deadline > #{now} limit 1
  </select>
  <select id="selectAllDiscountCard" resultType="net.summerfarm.mall.model.vo.DiscountCardToMerchantVO">
     select dctm.id,
       m_id mId,
       discount_card_id discountCardId,
       status,
       total_times totalTimes,
       used_times usedTimes,
       deadline,
       dc.name,
       dc.discount
     from discount_card_to_merchant dctm
     left join discount_card dc on dctm.discount_card_id = dc.id
     where dctm.m_id = #{mId}
  </select>

  <select id="selectDiscountCardByCondition" resultType="net.summerfarm.mall.model.vo.DiscountCardToMerchantVO">
    select dctm.id,
    m_id mId,
    discount_card_id discountCardId,
    status,
    total_times totalTimes,
    used_times usedTimes,
    deadline,
    dc.name,
    dc.discount
    from discount_card_to_merchant dctm
    left join discount_card dc on dctm.discount_card_id = dc.id
    <where>
      dctm.m_id = #{mId}
      <if test="status != null">
        <if test="status == 1">
          and status = 1 and total_times <![CDATA[>]]> used_times and deadline <![CDATA[>=]]> now()
        </if>
        <if test="status == 0">
          and (status = 0 or total_times <![CDATA[<=]]> used_times or deadline <![CDATA[<]]> now())
         </if>
      </if>
    </where>
  </select>

  <select id="countUsable" resultType="int">
    select
     count(1)
    from discount_card_to_merchant
    where status = 1 and total_times &gt; used_times and m_id = #{mId} and deadline &gt;= #{now}
  </select>
  <select id="selectCard" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from discount_card_to_merchant where status = 1 and total_times &gt; used_times and m_id = #{mId}  and deadline &gt;= now()
  </select>
  <update id="increaseUsedTimes">
    update discount_card_to_merchant
    set used_times = used_times + #{addUsedTimes}
    where id = #{id}
      and total_times >=  used_times + #{addUsedTimes}
  </update>
</mapper>