<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.BannerMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Banner" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="show_rule" property="showRule" jdbcType="VARCHAR" />
    <result column="url" property="url" jdbcType="VARCHAR" />
    <result column="link" property="link" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="BIT" />
    <result column="position" property="position" jdbcType="INTEGER" />
    <result column="updateTime" property="updateTime" jdbcType="TIMESTAMP" />

    <result column="word" property="word" jdbcType="VARCHAR" />
    <result column="color" property="color" jdbcType="VARCHAR" />
    <result column="link_type" property="linkType" jdbcType="TINYINT" />
    <result column="link_biz_id" property="linkBizId" jdbcType="INTEGER" />
    <result column="weight" property="weight" jdbcType="INTEGER" />
    <result column="old_link_biz_id" property="oldLinkBizId" jdbcType="INTEGER" />
    <result column="display_format" property="displayFormat" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, name, type,show_rule, url, link, status, position, updateTime, word, color, link_type, link_biz_id, weight, old_link_biz_id, display_format
  </sql>

  <sql id="New_Base_Column_List" >
    id, type, show_rule, url, word, color, link_type, link_biz_id, weight, old_link_biz_id, display_format
  </sql>

  <select id="select" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from banner
    where
     status = 1
    <if test="areaNo!=null">
     and area_no = #{areaNo}
    </if>
    and now() <![CDATA[>]]> start_time
    and now() <![CDATA[<]]> end_time
    and show_rule = #{showRule}
    <if test="type != null">
        and type = #{type,jdbcType=VARCHAR}
    </if>
    ORDER BY `position` desc,updateTime
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from banner
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeys" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from banner
    where id in
          <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=INTEGER}
          </foreach>

  </select>
    <select id="selectFirstByIds" resultType="net.summerfarm.mall.model.domain.Banner">
        select id
        from banner
      where id in
      <foreach collection="ids" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
      order by updateTime asc limit 1


    </select>

  <select id="listByQuery" resultMap="BaseResultMap">
    select
    <include refid="New_Base_Column_List" />
    from banner
    where
    status = 1
    <if test="areaNo!=null">
      and area_no = #{areaNo}
    </if>
    and now() <![CDATA[>]]> start_time
    and now() <![CDATA[<]]> end_time
    and show_rule = #{showRule}
    and tenant_id = 1
  </select>
</mapper>