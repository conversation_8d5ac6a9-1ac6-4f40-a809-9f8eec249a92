<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.InvitecodeMapper" >

  <select id="select" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.domain.Invitecode">
    SELECT invitecode, admin_id adminId FROM invitecode WHERE invitecode = #{invitecode}
  </select>

  <select id="selectMajor" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.domain.Invitecode">
    SELECT i.invitecode, i.admin_id adminId FROM invitecode  i
     WHERE i.invitecode = #{invitecode}
  </select>

  <select id="selectSale" resultType="java.lang.String">
    SELECT invitecode FROM invitecode WHERE admin_id=#{adminId}
  </select>

</mapper>