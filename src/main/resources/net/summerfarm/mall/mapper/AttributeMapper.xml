<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.AttributeMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Attribute" >
    <id column="attr_id" property="attrId" jdbcType="INTEGER" />
    <result column="attribute" property="attribute" jdbcType="VARCHAR" />
    <result column="field" property="field" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    attr_id, attribute, field
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from attribute
    where attr_id = #{attrId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from attribute
    where attr_id = #{attrId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.Attribute" >
    insert into attribute (attr_id, attribute, field
      )
    values (#{attrId,jdbcType=INTEGER}, #{attribute,jdbcType=VARCHAR}, #{field,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.Attribute" >
    insert into attribute
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="attrId != null" >
        attr_id,
      </if>
      <if test="attribute != null" >
        attribute,
      </if>
      <if test="field != null" >
        field,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="attrId != null" >
        #{attrId,jdbcType=INTEGER},
      </if>
      <if test="attribute != null" >
        #{attribute,jdbcType=VARCHAR},
      </if>
      <if test="field != null" >
        #{field,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.Attribute" >
    update attribute
    <set >
      <if test="attribute != null" >
        attribute = #{attribute,jdbcType=VARCHAR},
      </if>
      <if test="field != null" >
        field = #{field,jdbcType=VARCHAR},
      </if>
    </set>
    where attr_id = #{attrId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.Attribute" >
    update attribute
    set attribute = #{attribute,jdbcType=VARCHAR},
      field = #{field,jdbcType=VARCHAR}
    where attr_id = #{attrId,jdbcType=INTEGER}
  </update>
</mapper>