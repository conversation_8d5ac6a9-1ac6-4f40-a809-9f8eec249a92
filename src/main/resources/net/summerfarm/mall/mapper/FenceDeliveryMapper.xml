<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.FenceDeliveryMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.FenceDelivery">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fence_id" jdbcType="INTEGER" property="fenceId" />
    <result column="next_delivery_date" jdbcType="DATE" property="nextDeliveryDate" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fence_id, delivery_frequent, next_delivery_date, delete_flag, creator, create_time, 
    updater, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fence_delivery
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByFenceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fence_delivery
    where fence_id = #{fenceId} and delete_flag = 0

  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from fence_delivery
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.FenceDelivery" useGeneratedKeys="true">
    insert into fence_delivery (fence_id, delivery_frequent, next_delivery_date, 
      delete_flag, creator, create_time, 
      updater, update_time)
    values (#{fenceId,jdbcType=INTEGER}, #{deliveryFrequent,jdbcType=VARCHAR}, #{nextDeliveryDate,jdbcType=DATE}, 
      #{deleteFlag,jdbcType=TINYINT}, #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.FenceDelivery" useGeneratedKeys="true">
    insert into fence_delivery
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fenceId != null">
        fence_id,
      </if>
      <if test="deliveryFrequent != null">
        delivery_frequent,
      </if>
      <if test="nextDeliveryDate != null">
        next_delivery_date,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fenceId != null">
        #{fenceId,jdbcType=INTEGER},
      </if>
      <if test="deliveryFrequent != null">
        #{deliveryFrequent,jdbcType=VARCHAR},
      </if>
      <if test="nextDeliveryDate != null">
        #{nextDeliveryDate,jdbcType=DATE},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.FenceDelivery">
    update fence_delivery
    <set>
      <if test="fenceId != null">
        fence_id = #{fenceId,jdbcType=INTEGER},
      </if>
      <if test="deliveryFrequent != null">
        delivery_frequent = #{deliveryFrequent,jdbcType=VARCHAR},
      </if>
      <if test="nextDeliveryDate != null">
        next_delivery_date = #{nextDeliveryDate,jdbcType=DATE},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.FenceDelivery">
    update fence_delivery
    set fence_id = #{fenceId,jdbcType=INTEGER},
      delivery_frequent = #{deliveryFrequent,jdbcType=VARCHAR},
      next_delivery_date = #{nextDeliveryDate,jdbcType=DATE},
      delete_flag = #{deleteFlag,jdbcType=TINYINT},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>