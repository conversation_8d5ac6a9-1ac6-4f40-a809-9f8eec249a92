<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.ExchangeItemConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.ExchangeItemConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="scope_config_id" jdbcType="BIGINT" property="scopeConfigId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="adjust_type" jdbcType="TINYINT" property="adjustType" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `scope_config_id`, `sku`, `adjust_type`, `amount`, `priority`, `creator`, `update_time`, 
    `create_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from exchange_item_config
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from exchange_item_config
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.ExchangeItemConfig">
    insert into exchange_item_config (`id`, `scope_config_id`, `sku`, 
      `adjust_type`, `amount`, `priority`, 
      `creator`, `update_time`, `create_time`
      )
    values (#{id,jdbcType=BIGINT}, #{scopeConfigId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, 
      #{adjustType,jdbcType=TINYINT}, #{amount,jdbcType=DECIMAL}, #{priority,jdbcType=INTEGER}, 
      #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.ExchangeItemConfig">
    insert into exchange_item_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="scopeConfigId != null">
        `scope_config_id`,
      </if>
      <if test="sku != null">
        `sku`,
      </if>
      <if test="adjustType != null">
        `adjust_type`,
      </if>
      <if test="amount != null">
        `amount`,
      </if>
      <if test="priority != null">
        `priority`,
      </if>
      <if test="creator != null">
        `creator`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="scopeConfigId != null">
        #{scopeConfigId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="adjustType != null">
        #{adjustType,jdbcType=TINYINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.ExchangeItemConfig">
    update exchange_item_config
    <set>
      <if test="scopeConfigId != null">
        `scope_config_id` = #{scopeConfigId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        `sku` = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="adjustType != null">
        `adjust_type` = #{adjustType,jdbcType=TINYINT},
      </if>
      <if test="amount != null">
        `amount` = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="priority != null">
        `priority` = #{priority,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        `creator` = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="selectByScopeId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from exchange_item_config
    where `scope_config_id` = #{scopeConfigId} order by priority asc
  </select>

  <select id="selectBySku" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from exchange_item_config
    where `scope_config_id` = #{scopeConfigId} and sku = #{sku}
  </select>
</mapper>