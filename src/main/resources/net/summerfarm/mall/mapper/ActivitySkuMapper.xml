<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.ActivitySkuMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.ActivitySku">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="activity_id" property="activityId" jdbcType="INTEGER"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="activity_price" property="activityPrice"/>
        <result column="sale_price" property="salePrice"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="logo" property="logo" jdbcType="VARCHAR"/>
        <result column="activity_stock" jdbcType="INTEGER" property="activityStock"/>
        <result column="limited_quantity" jdbcType="INTEGER" property="limitedQuantity"/>
    </resultMap>

    <update id="updateActivityStock">
        update activity_sku
        <if test="updateTag == true">
            set activity_stock = activity_stock - #{purchaseQuantity}
        </if>
        <if test="updateTag == false">
            set activity_stock = activity_stock + #{purchaseQuantity}
        </if>
        where id = #{id}
    </update>

    <select id="selectByActivityId" parameterType="java.lang.Integer" resultType="java.lang.String">
        select
        sku
        from activity_sku
        where activity_id=#{activityId} and sku_status = 1;
    </select>

    <select id="selectSkuValidActivity" resultType="net.summerfarm.mall.model.vo.ActivitySkuVO">
        select ak.id,
               ak.activity_stock activityStock,
               ak.limited_quantity limitedQuantity,
               activity_id activityId,
               sku,
               sku_name skuName,
               activity_price activityPrice,
               sale_price salePrice,
               ladder_price ladderPrice,
               a.name activityName
        from activity_sku ak
                 left join activity a on ak.activity_id = a.id
        where a.status = 2
          and ak.sku_status = 1
          and a.area_no = #{areaNo}
          and ak.sku = #{sku}
          and start_time <![CDATA[ <= ]]> now() and end_time >= now()
    </select>

    <select id="discountZoneSkuList" resultType="net.summerfarm.mall.model.vo.ActivitySkuInfoVO">
        select ak.id,
               activity_id activityId,
               sku,
               sku_name skuName,
               activity_price activityPrice,
               sale_price salePrice,
               ladder_price ladderPrice,
               a.name activityName
        from activity_sku ak
                 left join activity a on ak.activity_id = a.id
        where a.status = 2
          and ak.sku_status = 1
          and a.area_no = #{areaNo}
          and start_time <![CDATA[ <= ]]> now() and end_time >= now()

    </select>
    <select id="selectSkuActivity" resultType="net.summerfarm.mall.model.vo.ActivitySkuVO">
        select ak.id,
               ak.activity_stock activityStock,
               ak.limited_quantity limitedQuantity,
               activity_id activityId,
               sku,
               sku_name skuName,
               activity_price activityPrice,
               sale_price salePrice,
               ladder_price ladderPrice,
               a.name activityName
        from activity_sku ak
                 left join activity a on ak.activity_id = a.id
        where a.id = #{activityId,jdbcType=INTEGER} and ak.sku_status = 1 and ak.sku = #{sku,jdbcType=VARCHAR}
    </select>
</mapper>
