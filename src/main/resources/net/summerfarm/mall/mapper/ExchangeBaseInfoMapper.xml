<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.ExchangeBaseInfoMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.ExchangeBaseInfo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="trigger_num" jdbcType="INTEGER" property="triggerNum"/>
    <result column="purchase_limit" jdbcType="INTEGER" property="purchaseLimit"/>
    <result column="discount_percentage" jdbcType="INTEGER" property="discountPercentage"/>
    <result column="discount" jdbcType="DECIMAL" property="discount"/>
    <result column="type" jdbcType="TINYINT" property="type"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="effect_time_type" jdbcType="TINYINT" property="effectTimeType"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="creator" jdbcType="VARCHAR" property="creator"/>
    <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    `id`
    , `name`, `remark`, `trigger_num`, `purchase_limit`, `discount_percentage`, `discount`,
    `type`, `status`, `effect_time_type`, `start_time`, `end_time`, `creator`, `is_delete`, 
    `update_time`, `create_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from exchange_base_info
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from exchange_base_info
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.ExchangeBaseInfo">
    insert into exchange_base_info (`id`, `name`, `remark`,
                                    `trigger_num`, `purchase_limit`, `discount_percentage`,
                                    `discount`, `type`, `status`,
                                    `effect_time_type`, `start_time`, `end_time`,
                                    `creator`, `is_delete`, `update_time`,
                                    `create_time`)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
            #{triggerNum,jdbcType=INTEGER}, #{purchaseLimit,jdbcType=INTEGER},
            #{discountPercentage,jdbcType=INTEGER},
            #{discount,jdbcType=DECIMAL}, #{type,jdbcType=TINYINT}, #{status,jdbcType=TINYINT},
            #{effectTimeType,jdbcType=TINYINT}, #{startTime,jdbcType=TIMESTAMP},
            #{endTime,jdbcType=TIMESTAMP},
            #{creator,jdbcType=VARCHAR}, #{isDelete,jdbcType=TINYINT},
            #{updateTime,jdbcType=TIMESTAMP},
            #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.ExchangeBaseInfo">
    insert into exchange_base_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="remark != null">
        `remark`,
      </if>
      <if test="triggerNum != null">
        `trigger_num`,
      </if>
      <if test="purchaseLimit != null">
        `purchase_limit`,
      </if>
      <if test="discountPercentage != null">
        `discount_percentage`,
      </if>
      <if test="discount != null">
        `discount`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="effectTimeType != null">
        `effect_time_type`,
      </if>
      <if test="startTime != null">
        `start_time`,
      </if>
      <if test="endTime != null">
        `end_time`,
      </if>
      <if test="creator != null">
        `creator`,
      </if>
      <if test="isDelete != null">
        `is_delete`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="triggerNum != null">
        #{triggerNum,jdbcType=INTEGER},
      </if>
      <if test="purchaseLimit != null">
        #{purchaseLimit,jdbcType=INTEGER},
      </if>
      <if test="discountPercentage != null">
        #{discountPercentage,jdbcType=INTEGER},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="effectTimeType != null">
        #{effectTimeType,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.mall.model.domain.ExchangeBaseInfo">
    update exchange_base_info
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        `remark` = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="triggerNum != null">
        `trigger_num` = #{triggerNum,jdbcType=INTEGER},
      </if>
      <if test="purchaseLimit != null">
        `purchase_limit` = #{purchaseLimit,jdbcType=INTEGER},
      </if>
      <if test="discountPercentage != null">
        `discount_percentage` = #{discountPercentage,jdbcType=INTEGER},
      </if>
      <if test="discount != null">
        `discount` = #{discount,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="effectTimeType != null">
        `effect_time_type` = #{effectTimeType,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        `start_time` = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        `end_time` = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        `creator` = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        `is_delete` = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="listAllValid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from exchange_base_info
    where status = 1 and ((start_time &lt;= NOW() and end_time &gt;= NOW()) or effect_time_type = 1)
    and is_delete = 0
  </select>

  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from exchange_base_info
    where id = #{id} and status = 1 and ((start_time &lt;= NOW() and end_time &gt;= NOW()) or
    effect_time_type = 1) and is_delete = 0
  </select>
</mapper>