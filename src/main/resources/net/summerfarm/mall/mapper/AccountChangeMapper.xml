<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.AccountChangeMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.AccountChange">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="m_id" property="mId" jdbcType="VARCHAR"/>
        <result column="openid" property="openid" jdbcType="VARCHAR"/>
        <result column="unionid" property="unionid" jdbcType="VARCHAR"/>
        <result column="old_phone" property="oldPhone" jdbcType="VARCHAR"/>
        <result column="old_contact" property="oldContact" jdbcType="VARCHAR"/>
        <result column="new_phone" property="newPhone" jdbcType="VARCHAR"/>
        <result column="new_contact" property="newContact" jdbcType="VARCHAR"/>
        <result column="mname" property="mname" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="account_id" property="accountId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="select" resultMap="BaseResultMap" parameterType="net.summerfarm.mall.model.domain.AccountChange">
        select
        *
        from account_change
        <where>
            <if test="mId != null">
                AND m_id=#{mId}
            </if>
            <if test="accountId != null">
                AND account=#{accountId}
            </if>
            <if test="openid != null">
                AND openid=#{openid}
            </if>
            <if test="status != null">
                AND status=#{status}
            </if>
        </where>
        ORDER BY id desc
        limit 1

    </select>

    <insert id="insert" parameterType="net.summerfarm.mall.model.domain.AccountChange">
        insert into account_change
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                m_id,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="openid != null">
                openid,
            </if>
            <if test="unionid != null">
                unionid,
            </if>
            <if test="oldPhone != null">
                old_phone,
            </if>
            <if test="oldContact != null">
                old_contact,
            </if>
            <if test="newPhone != null">
                new_phone,
            </if>
            <if test="newContact != null">
                new_contact,
            </if>
            <if test="mname != null">
                mname,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                #{mId},
            </if>
            <if test="accountId != null">
                #{accountId},
            </if>
            <if test="openid != null">
                #{openid},
            </if>
            <if test="unionid != null">
                #{unionid},
            </if>
            <if test="oldPhone != null">
                #{oldPhone},
            </if>
            <if test="oldContact != null">
                #{oldContact},
            </if>
            <if test="newPhone != null">
                #{newPhone},
            </if>
            <if test="newContact != null">
                #{newContact},
            </if>
            <if test="mname != null">
                #{mname},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

</mapper>