<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.StockTaskMapper">

    <resultMap id="withItem" type="net.summerfarm.mall.model.domain.StockTask">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="task_no" property="taskNo" jdbcType="VARCHAR"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="expect_time" property="expectTime"/>
        <result column="state" property="state" jdbcType="INTEGER"/>
        <result column="addtime" property="addtime"/>
        <result column="updatetime" property="updatetime"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="dimension" property="dimension" jdbcType="INTEGER"/>
        <result column="category" property="category" jdbcType="VARCHAR"/>
        <result column="mismatch_reason" property="mismatchReason" jdbcType="VARCHAR"/>
        <result column="option_flag" property="optionFlag" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="baseColumn">
        id
        ,task_no,area_no,type,expect_time,state,addtime,admin_id,remark,dimension,mismatch_reason,category,updatetime
    </sql>

    <select id="selectOne" resultType="net.summerfarm.mall.model.domain.StockTask">
        SELECT id,task_no taskNo,area_no areaNo,type,expect_time expectTime,state,addtime,updatetime
        FROM stock_task
        WHERE task_no = #{taskNo}
        <if test="type != null">
            AND type = #{type}
        </if>
    </select>

</mapper>
