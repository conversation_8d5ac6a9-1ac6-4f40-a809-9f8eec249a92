<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.AfterSaleProofMapper" >


  <select id="select" resultType="net.summerfarm.mall.model.domain.AfterSaleProof"
          parameterType="net.summerfarm.mall.model.domain.AfterSaleProof">

    select p.id,p.after_sale_order_no afterSaleOrderNo,p.handler,p.proof_pic proofPic,p.handle_remark handleRemark,p.quantity,p.after_sale_type afterSaleType,p.auditer,p.handle_num handleNum,
           p.handle_type handleType,p.status,p.apply_remark applyRemark,p.updatetime,p.refund_type refundType,p.create_time createTime from after_sale_proof p
    where p.after_sale_order_no=#{afterSaleOrderNo}

  </select>


  <update id="updateById" parameterType="net.summerfarm.mall.model.domain.AfterSaleProof">
      update after_sale_proof
      <set>
          <if test="status != null">
              status = #{status},
          </if>
          <if test="proofPic != null">
              proof_pic = #{proofPic},
          </if>
          <if test="handleType != null">
              handle_type = #{handleType},
          </if>

          <if test="handleNum != null">
              handle_num = #{handleNum},
          </if>

          <if test="handler != null">
              handler = #{handler},
          </if>

          <if test="handleRemark != null">
              handle_remark = #{handleRemark,jdbcType=VARCHAR},
          </if>

          <if test="extraRemark != null">
              extra_remark = #{extraRemark,jdbcType=VARCHAR},
          </if>

          <if test="auditer != null">
              auditer = #{auditer},
          </if>

          <if test="applyRemark != null">
              apply_remark = #{applyRemark},
          </if>

          <if test="quantity != null">
              quantity = #{quantity,jdbcType=INTEGER},
          </if>

          <if test="afterSaleType != null">
              after_sale_type = #{afterSaleType},
          </if>

          <if test="refundType != null">
              refund_type = #{refundType},
          </if>
          <if test="handletime != null">
              handletime = #{handletime},
          </if>
          <if test="auditeRemark != null">
              audite_remark = #{auditeRemark},
          </if>
          <if test="auditetime != null">
              auditetime = #{auditetime},
          </if>
          <if test="applyer != null">
              applyer = #{applyer},
          </if>
          <if test="recoveryNum != null">
              recovery_num = #{recoveryNum},
          </if>
          <if test="handleSecondaryRemark != null">
              handle_secondary_remark = #{handleSecondaryRemark},
          </if>
      </set>
      where id = #{id}
  </update>



  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.AfterSaleProof" >
    insert into after_sale_proof (after_sale_order_no,quantity,after_sale_type,refund_type,proof_pic,handle_type,status,apply_remark,handle_num,applyer
    ,auditetime,handletime,auditer,recovery_num,handle_remark,extra_remark,handle_secondary_remark,apply_secondary_remark,handler,audite_remark,proof_video)
    values (#{afterSaleOrderNo},#{quantity},#{afterSaleType},#{refundType},#{proofPic},#{handleType},#{status},#{applyRemark},#{handleNum},#{applyer}
    ,#{auditetime},#{handletime},#{auditer},#{recoveryNum},#{handleRemark},#{extraRemark},#{handleSecondaryRemark},#{applySecondaryRemark},#{handler},#{auditeRemark},#{proofVideo})
  </insert>
    <insert id="saveBatchAfterSaleProof">
        insert into after_sale_proof (after_sale_order_no,quantity,after_sale_type,refund_type,proof_pic,handle_type,status,apply_remark,handle_num,applyer
        ,auditetime,handletime,auditer,recovery_num,handle_remark)
        values
        <foreach collection="list"  item="item" separator=",">
            (#{item.afterSaleOrderNo},#{item.quantity},#{item.afterSaleType},#{item.refundType},#{item.proofPic},#{item.handleType},#{item.status},#{item.applyRemark},
            #{item.handleNum},#{item.applyer},#{item.auditetime},#{item.handletime},#{item.auditer},#{item.recoveryNum},#{item.handleRemark})
        </foreach>
    </insert>

    <select id="querySumQuantity" resultType ="java.lang.Integer">
    select sum(af.quantity) from (
              select asp.quantity from after_sale_proof  asp
              inner JOIN after_sale_order aso on asp.after_sale_order_no = aso.after_sale_order_no and order_no = #{orderNo} and aso.status = 2  and aso.suit_id = 0
              inner join order_item oi on oi.order_no = aso.order_no
              <if test="useCoupon != null">
                and oi.use_coupon = #{useCoupon}
              </if>
              <if test="afterSaleOrderNo != null">
                  and asp.after_sale_order_no != #{afterSaleOrderNo}
              </if>
              where asp.status = 2
              group by asp.after_sale_order_no
        ) af;
  </select>
  <select id="selectByOrderNo"  resultType="net.summerfarm.mall.model.domain.AfterSaleProof">
    select asp.after_sale_order_no afterSaleOrderNo,asp.quantity,asp.handle_num handleNum,asp.status,asp.handle_type handleType,
     asp.proof_pic proofPic,asp.updatetime,asp.auditetime,asp.refund_type refundType
    from after_sale_order aso
    left join after_sale_proof asp on aso.after_sale_order_no = asp.after_sale_order_no
    where aso.order_no = #{orderNo}

      <if test="sku != null">
          AND aso.sku = #{sku}
      </if>

  </select>
    <select id="selectDesc" resultType="net.summerfarm.mall.model.domain.AfterSaleProof">
    select p.id,p.after_sale_order_no afterSaleOrderNo,p.handler,p.proof_pic proofPic,p.handle_remark handleRemark,p.quantity,p.after_sale_type afterSaleType,p.auditer,p.handle_num handleNum,
           p.handle_type handleType,p.status,p.apply_remark applyRemark,p.updatetime,p.refund_type refundType,p.proof_video proofVideo from after_sale_proof p
    where p.after_sale_order_no=#{afterSaleOrderNo}
        ORDER BY p.updatetime desc limit 1
    </select>

    <update id="updateByAfterSale"  parameterType="net.summerfarm.mall.model.domain.AfterSaleProof">
        update after_sale_proof
        <set>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="proofPic != null">
                proof_pic = #{proofPic},
            </if>
            <if test="handleType != null">
                handle_type = #{handleType},
            </if>

            <if test="handleNum != null">
                handle_num = #{handleNum},
            </if>

            <if test="handler != null">
                handler = #{handler},
            </if>

            <if test="handleRemark != null">
                handle_remark = #{handleRemark,jdbcType=VARCHAR},
            </if>

            <if test="extraRemark != null">
                extra_remark = #{extraRemark,jdbcType=VARCHAR},
            </if>

            <if test="auditer != null">
                auditer = #{auditer},
            </if>

            <if test="applyRemark != null">
                apply_remark = #{applyRemark},
            </if>

            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>

            <if test="afterSaleType != null">
                after_sale_type = #{afterSaleType},
            </if>

            <if test="refundType != null">
                refund_type = #{refundType},
            </if>
            <if test="handletime != null">
                handletime = #{handletime},
            </if>
            <if test="auditeRemark != null">
                audite_remark = #{auditeRemark},
            </if>
            <if test="auditetime != null">
                auditetime = #{auditetime},
            </if>
            <if test="applyer != null">
                applyer = #{applyer},
            </if>
            <if test="recoveryNum != null">
                recovery_num = #{recoveryNum},
            </if>
        </set>
        where after_sale_order_no = #{afterSaleOrderNo}
    </update>


</mapper>