<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MajorCategoryMapper">
    <select id="selectSpu" resultType="java.lang.String">
        select pd_id from major_category mc
            left join products p on mc.category_id = p.category_id
        where mc.direct = #{direct} and mc.admin_id = #{adminId} and mc.area_no = #{areaNo}
        and mc.status = 1
        <if test="showType != null">
            and mc.type = #{showType}
        </if>
        <choose>
            <when test="pdId != null">
                and pd_id = #{pdId}
            </when>
            <otherwise>
                and pd_id is not null
            </otherwise>
        </choose>
    </select>

    <select id="selectMajorCategory" resultType="java.lang.Boolean">
        select if(count(*) > 0, true, false) from major_category mc
        where mc.direct = #{direct} and mc.admin_id = #{adminId} and mc.area_no = #{areaNo}
        and mc.status = 1 and mc.type = 0
        <if test="categoryId != null">
            and mc.category_id = #{categoryId}
        </if>
    </select>
    <select id="selectMajorCategoryList" resultType="net.summerfarm.mall.model.domain.MajorCategory">
        select mc.category_id categoryId,mc.type type from major_category mc
        where mc.direct = #{direct} and mc.admin_id = #{adminId} and mc.area_no = #{areaNo}
        and mc.status = 1
        <if test="categoryIds !=null">
            AND mc.category_id in
            <foreach collection="categoryIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>