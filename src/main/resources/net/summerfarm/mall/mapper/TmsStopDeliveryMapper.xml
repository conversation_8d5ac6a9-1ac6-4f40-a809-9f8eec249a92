<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.TmsStopDeliveryMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.TmsStopDelivery">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="shutdown_start_time" jdbcType="DATE" property="shutdownStartTime" />
    <result column="shutdown_end_time" jdbcType="DATE" property="shutdownEndTime" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, store_no, shutdown_start_time, shutdown_end_time, delete_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tms_stop_delivery
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByStoreNo" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from tms_stop_delivery
      where store_no = #{storeNo} and delete_flag = 0 and shutdown_end_time >= DATE(now())
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tms_stop_delivery
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.TmsStopDelivery" useGeneratedKeys="true">
    insert into tms_stop_delivery (create_time, update_time, store_no, 
      shutdown_start_time, shutdown_end_time, delete_flag
      )
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{storeNo,jdbcType=INTEGER}, 
      #{shutdownStartTime,jdbcType=DATE}, #{shutdownEndTime,jdbcType=DATE}, #{deleteFlag,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.TmsStopDelivery" useGeneratedKeys="true">
    insert into tms_stop_delivery
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="shutdownStartTime != null">
        shutdown_start_time,
      </if>
      <if test="shutdownEndTime != null">
        shutdown_end_time,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="shutdownStartTime != null">
        #{shutdownStartTime,jdbcType=DATE},
      </if>
      <if test="shutdownEndTime != null">
        #{shutdownEndTime,jdbcType=DATE},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.TmsStopDelivery">
    update tms_stop_delivery
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="shutdownStartTime != null">
        shutdown_start_time = #{shutdownStartTime,jdbcType=DATE},
      </if>
      <if test="shutdownEndTime != null">
        shutdown_end_time = #{shutdownEndTime,jdbcType=DATE},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.TmsStopDelivery">
    update tms_stop_delivery
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      store_no = #{storeNo,jdbcType=INTEGER},
      shutdown_start_time = #{shutdownStartTime,jdbcType=DATE},
      shutdown_end_time = #{shutdownEndTime,jdbcType=DATE},
      delete_flag = #{deleteFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateStatus">
    update tms_stop_delivery
    set delete_flag = 1 where store_no =#{storeNo}
  </update>
</mapper>