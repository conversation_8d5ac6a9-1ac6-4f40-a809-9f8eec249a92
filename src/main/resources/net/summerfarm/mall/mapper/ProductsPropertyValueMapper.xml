<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.ProductsPropertyValueMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.ProductsPropertyValue">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="pd_id" jdbcType="INTEGER" property="pdId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="products_property_id" jdbcType="INTEGER" property="productsPropertyId" />
    <result column="products_property_value" jdbcType="VARCHAR" property="productsPropertyValue" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, pd_id, sku, products_property_id, products_property_value, creator, create_time
  </sql>
  <select id="selectValue" resultType="net.summerfarm.mall.model.vo.ProductsPropertyValueVO">
    select ppv.id,
      pd_id                   pdId,
      sku,
      ppv.products_property_id    productsPropertyId,
      ppv.products_property_value productsPropertyValue,
      name
    from products_property_value ppv
    left join products_property pp on ppv.products_property_id = pp.id
    <where>
      pp.status = 1
      <if test="pdId != null">
        and ppv.pd_id = #{pdId}
      </if>
      <choose>
        <when test="sku != null">
          and ppv.sku = #{sku}
        </when>
        <otherwise>
          and ppv.sku is null
        </otherwise>
      </choose>
    </where>
    order by pp.format_type
  </select>

  <select id="selectByPdIds" resultType="net.summerfarm.mall.model.vo.ProductsPropertyValueVO">
    select ppv.id,
      pd_id                   pdId,
      sku,
      ppv.products_property_id    productsPropertyId,
      ppv.products_property_value productsPropertyValue,
      name
    from products_property_value ppv
    left join products_property pp on ppv.products_property_id = pp.id
    <where>
      pp.status = 1
      and ppv.sku is null
      and pd_id in
      <foreach item="pdId" collection="pdIds" open="(" separator="," close=")">
        #{pdId}
      </foreach>
    </where>
    order by pp.format_type
  </select>
  <!-- 定义结果映射 -->
  <resultMap id="propertyCountMap" type="java.util.HashMap">
    <result property="products_property_value" column="products_property_value" />
    <result property="count" column="count" />
  </resultMap>

  <!-- 查询方法 -->
  <select id="selectProductsPropertyValueNameByPdIdsAndProductsPropertyName" resultMap="propertyCountMap">
    SELECT
    v.products_property_value AS products_property_value,
    COUNT(*) AS count
    FROM
    products_property_value v
    LEFT JOIN
    products_property p ON p.id = v.products_property_id
    WHERE
    p.status = 1
    AND p.name = #{name}
    AND p.type = 0
    AND v.products_property_value IS NOT NULL
    AND v.pd_id IN
    <foreach item="pdId" collection="pdIds" open="(" separator="," close=")">
      #{pdId}
    </foreach>
    GROUP BY  v.products_property_value
    </select>
  <select id="selectProductsPropertyValueNameBySkusAndProductsPropertyName" resultMap="propertyCountMap">
    select
    v.products_property_value AS products_property_value,
    COUNT(*) AS count
    from
    products_property_value v
    LEFT JOIN products_property p on p.id = v.`products_property_id`
    where
    p.status = 1
    AND p.`name` = #{name}
    AND p.`type` = 1
    and v.products_property_value is not null
    and v.`sku` in
    <foreach item="sku" collection="skus" open="(" separator="," close=")">
      #{sku}
    </foreach>
    GROUP BY v.products_property_value
  </select>
</mapper>
