<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.ProductsMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Products" >
    <id column="pd_id" property="pdId" jdbcType="BIGINT" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
    <result column="brand_id" property="brandId" jdbcType="INTEGER" />
    <result column="pd_name" property="pdName" jdbcType="VARCHAR" />
    <result column="after_sale_time" property="afterSaleTime" jdbcType="VARCHAR" />
    <result column="after_sale_type" property="afterSaleType" jdbcType="VARCHAR" />
    <result column="refund_type" property="refundType" jdbcType="VARCHAR" />
    <result column="after_sale_unit" property="afterSaleUnit" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="warn_time" property="warnTime" jdbcType="INTEGER"/>
    <result column="picture_path" property="picturePath" jdbcType="VARCHAR"/>
    <result column="storage_location" property="storageLocation" jdbcType="VARCHAR"/>
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="net.summerfarm.mall.model.domain.Products" extends="BaseResultMap" >
    <result column="pddetail" property="pddetail" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    pd_id, category_id, brand_id, pd_name, create_time, after_sale_time, after_sale_type, after_sale_unit,refund_type,warn_time, picture_path,storage_location
  </sql>
  <sql id="Blob_Column_List" >
    pddetail
  </sql>

  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from products
    where pd_id = #{pdId,jdbcType=BIGINT}
  </select>

  <select id="accessiblePdId" resultType="java.lang.Long">
    SELECT distinct p.pd_id FROM products p
      LEFT JOIN inventory i ON i.pd_id = p.pd_id
      LEFT JOIN area_sku a ON i.sku = a.sku
      <if test="adminId != null">
        INNER JOIN major_price mp on mp.sku = a.sku and mp.area_no = a.area_no and mp.admin_id = #{adminId} and mp.direct = #{direct}
      </if>
    WHERE i.outdated = 0
    AND p.outdated = 0
    AND a.on_sale = 1
    AND a.show = 1
    AND a.area_no = #{areaNo}
    <choose>
      <when test="'大客户' == msize">
        AND (i.type = 0 OR (i.type = 1 AND i.admin_id = #{typeAdminId}))
      </when>
      <otherwise>
        AND i.type = 0
      </otherwise>
    </choose>
  </select>

  <select id="queryBySku" resultType="net.summerfarm.mall.model.domain.Products">
    SELECT p.pd_name pdName,p.category_id categoryId,p.pd_id pdId
    FROM inventory i
           LEFT JOIN products p ON i.pd_id=p.pd_id
           LEFT JOIN category c ON p.category_id=c.id
           LEFT JOIN admin ad on ad.admin_id = i.admin_id
    WHERE i.sku = #{sku}
  </select>

  <select id="selectByPdIds" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from products
    where pd_id in
    <foreach collection="pdIds" item="pdId" open="(" separator="," close=")">
      #{pdId}
    </foreach>
  </select>

  <select id="listBySkuList" resultType="net.summerfarm.mall.model.domain.Products">
    select p.pd_id pdId, p.category_id categoryId, p.brand_id brandId, p.pd_name pdName, p.create_time createTime, p.after_sale_time afterSaleTime, p.after_sale_type afterSaleType, p.after_sale_unit afterSaleUnit,
           p.refund_type refundType,p.warn_time warnTime, p.picture_path picturePath,p.storage_location storageLocation,p.pddetail
    from inventory i LEFT JOIN products p ON i.pd_id = p.pd_id
    <if test="skuList !=null">
      where i.sku in
      <foreach collection="skuList" open="(" close=")" item="item" separator=",">
        #{item}
      </foreach>
    </if>
  </select>
</mapper>
