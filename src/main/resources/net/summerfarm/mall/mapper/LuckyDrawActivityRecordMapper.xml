<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.LuckyDrawActivityRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.LuckyDrawActivityRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="participation_time" jdbcType="DATE" property="participationTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
 
  <sql id="Base_Column_List">
    id, m_id, activity_id, participation_time, create_time, update_time
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lucky_draw_activity_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lucky_draw_activity_record
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.LuckyDrawActivityRecord">
    insert into lucky_draw_activity_record (id, m_id, activity_id, 
      participation_time, create_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT}, #{activityId,jdbcType=BIGINT}, 
      #{participationTime,jdbcType=DATE}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.LuckyDrawActivityRecord">
    insert into lucky_draw_activity_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="participationTime != null">
        participation_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="participationTime != null">
        #{participationTime,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.LuckyDrawActivityRecord">
    update lucky_draw_activity_record
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="participationTime != null">
        participation_time = #{participationTime,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.LuckyDrawActivityRecord">
    update lucky_draw_activity_record
    set m_id = #{mId,jdbcType=BIGINT},
      activity_id = #{activityId,jdbcType=BIGINT},
      participation_time = #{participationTime,jdbcType=DATE},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByEntity" resultMap="BaseResultMap" parameterType="net.summerfarm.mall.model.domain.LuckyDrawActivityRecord">
    select
    <include refid="Base_Column_List" />
    from lucky_draw_activity_record
    <where>
      <if test="mId != null">
        and m_id = #{mId,jdbcType=BIGINT}
      </if>
      <if test="activityId != null">
        and activity_id = #{activityId,jdbcType=BIGINT}
      </if>
      <if test="participationTime != null">
        and participation_time = #{participationTime,jdbcType=DATE}
      </if>
    </where>
  </select>
</mapper>