<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.AreaSkuPriceMarkupConfigMapper">
    <!-- 结果集映射 -->
    <resultMap id="areaSkuPriceMarkupConfigResultMap" type="net.summerfarm.mall.model.domain.AreaSkuPriceMarkupConfig">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="area_no" property="areaNo" jdbcType="INTEGER"/>
		<result column="markup_type" property="markupType" jdbcType="INTEGER"/>
		<result column="markup_value" property="markupValue" jdbcType="DOUBLE"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="updater" property="updater" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="areaSkuPriceMarkupConfigColumns">
          t.id,
          t.sku,
          t.area_no,
          t.markup_type,
          t.markup_value,
          t.update_time,
          t.create_time,
          t.creator,
          t.updater
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="areaNo != null">
                AND t.area_no = #{areaNo}
            </if>
            <if test="areaNos != null and areaNos.size() > 0">
                and t.area_no in
                <foreach collection="areaNos" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="areaNo != null">
                    t.area_no = #{areaNo},
                </if>
                <if test="markupType != null">
                    t.markup_type = #{markupType},
                </if>
                <if test="markupValue != null">
                    t.markup_value = #{markupValue},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="creator != null">
                    t.creator = #{creator},
                </if>
                <if test="updater != null">
                    t.updater = #{updater},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="areaSkuPriceMarkupConfigResultMap" >
        SELECT <include refid="areaSkuPriceMarkupConfigColumns" />
        FROM area_sku_price_markup_config t
		WHERE t.id = #{id}
    </select>


    <!-- 根据主键ID获取数据 -->
    <select id="selectBySkusAndAreaNo"  resultMap="areaSkuPriceMarkupConfigResultMap" >
        SELECT <include refid="areaSkuPriceMarkupConfigColumns" />
        FROM area_sku_price_markup_config t
        WHERE  t.sku in
        <foreach collection="skus" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and t.area_no = #{areaNo}
    </select>



</mapper>