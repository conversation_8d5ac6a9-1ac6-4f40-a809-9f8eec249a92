<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MarketRuleDetailMapper" >

  <select id="selectRuleSku" resultType="java.lang.String">
    select sku
    from market_rule_detail
    where rule_id = #{ruleId}
      <if test="skuSet != null and skuSet.size != 0">
          and sku IN
          <foreach collection="skuSet" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
      </if>
  </select>

  <select id="selectRuleSkuByRuleIdsAndSku" resultType="java.lang.String">
    select rule_id as ruleId,group_concat(distinct sku) as skuList
    from market_rule_detail
    where rule_id in
    <foreach collection="ruleIds" open="(" close=")" separator="," item="ruleId">
    #{ruleId}
    </foreach>
    <if test="skuSet != null and skuSet.size != 0">
      and sku IN
      <foreach collection="skuSet" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
    group by rule_id
  </select>

  <select id="selectRuleDeatilSku" resultType="net.summerfarm.mall.model.domain.MarketRuleDetail">
    select id, sku, pd_name as pdName, weight, rule_id as ruleId, category_id as categoryId, category_name as categoryName
    from market_rule_detail
    where rule_id = #{ruleId} and sku = #{sku}
  </select>

  <select id="selectRuleUsableSkusByRuleIds" resultType="net.summerfarm.mall.model.domain.MarketRuleDetail">
    select id, sku, pd_name as pdName, weight, rule_id as ruleId, category_id as categoryId, category_name as categoryName
    from market_rule_detail
    where rule_id in
      <foreach collection="ruleIds" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </select>
  <select id="selectRuleDeatilByRuleId" resultType="net.summerfarm.mall.model.domain.MarketRuleDetail">
    select id, sku, pd_name as pdName, weight, rule_id as ruleId, category_id as categoryId, category_name as categoryName
    from market_rule_detail
    where rule_id = #{ruleId}
  </select>

  <select id="getAllEfficientRuleDetail" resultType="net.summerfarm.mall.model.domain.MarketRuleDetail">
    select mrd.id, mrd.sku, mrd.pd_name as pdName, mrd.weight, mrd.rule_id as ruleId,
           mrd.category_id as categoryId, mrd.category_name as categoryName, mr.type
    from market_rule_detail mrd
    LEFT JOIN `market_rule` mr on `mrd`.`rule_id` = mr.`id`
    LEFT JOIN area_market_rule amr on mr.id = amr.rule_id
    where  mr.start_time &lt;= now()
      and now() &lt; mr.end_time and amr.area_no = #{areaNo}
  </select>
    <select id="selectRuleDetailBySkusAndRuleIds"
            resultType="net.summerfarm.mall.model.domain.MarketRuleDetail">
      select id, sku, pd_name as pdName, weight, rule_id as ruleId, category_id as categoryId, category_name as categoryName
      from market_rule_detail
      where rule_id in
      <foreach collection="ruleIds" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
        and sku in
      <foreach collection="skuList" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
      order by id desc
    </select>
</mapper>