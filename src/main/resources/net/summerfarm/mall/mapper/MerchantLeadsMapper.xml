<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MerchantLeadsMapper" >
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MerchantLeads" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="mname" property="mname" jdbcType="VARCHAR" />
        <result column="phone" property="phone" jdbcType="VARCHAR" />
        <result column="province" property="province" jdbcType="VARCHAR" />
        <result column="city" property="city" jdbcType="VARCHAR" />
        <result column="area" property="area" jdbcType="VARCHAR" />
        <result column="address" property="address" jdbcType="VARCHAR" />
        <result column="poi_note" property="poiNote" jdbcType="VARCHAR" />
        <result column="area_no" property="areaNo" jdbcType="INTEGER" />
        <result column="area_name" property="areaName" jdbcType="VARCHAR" />
        <result column="size" property="size" jdbcType="VARCHAR" />
        <result column="author" property="author" jdbcType="VARCHAR" />
        <result column="admin_id" property="adminId" jdbcType="INTEGER" />
        <result column="admin_name" property="adminName" jdbcType="VARCHAR" />
        <result column="source" property="source" jdbcType="VARCHAR" />
        <result column="mcontact" property="mcontact" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Base_Column_List" >
        id, mname, phone, province, city, area, address, poi_note, area_no, area_name, size,
        author, admin_id, admin_name, source, mcontact, status, remark
    </sql>


    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MerchantLeads" >
        update merchant_leads
        <set >
            updatetime = now(),
            <if test="mname != null" >
                mname = #{mname,jdbcType=VARCHAR},
            </if>
            <if test="phone != null" >
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="province != null" >
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null" >
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null" >
                area = #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null" >
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="poiNote != null" >
                poi_note = #{poiNote,jdbcType=VARCHAR},
            </if>
            <if test="areaNo != null" >
                area_no = #{areaNo,jdbcType=INTEGER},
            </if>
            <if test="areaName != null" >
                area_name = #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="size != null" >
                size = #{size,jdbcType=VARCHAR},
            </if>
            <if test="author != null" >
                author = #{author,jdbcType=VARCHAR},
            </if>
            <if test="adminId != null" >
                admin_id = #{adminId,jdbcType=INTEGER},
            </if>
            <if test="adminName != null" >
                admin_name = #{adminName,jdbcType=VARCHAR},
            </if>
            <if test="source != null" >
                source = #{source,jdbcType=VARCHAR},
            </if>
            <if test="mcontact != null" >
                mcontact = #{mcontact,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="mId != null">
                m_id = #{mId},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectOne" resultType="net.summerfarm.mall.model.domain.MerchantLeads" parameterType="long">
        select
            id, mname, phone, province, city, area, address, poi_note poiNote, area_no areaNo, area_name areaName, size,
            author, admin_id adminId, admin_name adminName, source, mcontact, status, remark,house_number houseNumber,
            company_brand companyBrand,enterprise_scale enterpriseScale,`type`,door_pic doorPic, merchant_type merchantType
        from merchant_leads
        where id = #{leadsId}
    </select>
</mapper>