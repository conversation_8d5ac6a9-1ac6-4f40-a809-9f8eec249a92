<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.ContactOperateLogMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.ContactOperateLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="contact_id" jdbcType="BIGINT" property="contactId" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="operate_source" jdbcType="TINYINT" property="operateSource" />
    <result column="operate_type" jdbcType="TINYINT" property="operateType" />
    <result column="context" jdbcType="VARCHAR" property="context" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, contact_id, m_id, operate_name, operate_source, operate_type, 
    context
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from contact_operate_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from contact_operate_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.ContactOperateLog" useGeneratedKeys="true">
    insert into contact_operate_log (create_time, update_time, contact_id, 
      m_id, operate_name, operate_source, 
      operate_type, context)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{contactId,jdbcType=BIGINT}, 
      #{mId,jdbcType=BIGINT}, #{operateName,jdbcType=VARCHAR}, #{operateSource,jdbcType=TINYINT}, 
      #{operateType,jdbcType=TINYINT}, #{context,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.ContactOperateLog" useGeneratedKeys="true">
    insert into contact_operate_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="contactId != null">
        contact_id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="operateName != null">
        operate_name,
      </if>
      <if test="operateSource != null">
        operate_source,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="context != null">
        context,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactId != null">
        #{contactId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="operateName != null">
        #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="operateSource != null">
        #{operateSource,jdbcType=TINYINT},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="context != null">
        #{context,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.ContactOperateLog">
    update contact_operate_log
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactId != null">
        contact_id = #{contactId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="operateName != null">
        operate_name = #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="operateSource != null">
        operate_source = #{operateSource,jdbcType=TINYINT},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="context != null">
        context = #{context,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.ContactOperateLog">
    update contact_operate_log
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      contact_id = #{contactId,jdbcType=BIGINT},
      m_id = #{mId,jdbcType=BIGINT},
      operate_name = #{operateName,jdbcType=VARCHAR},
      operate_source = #{operateSource,jdbcType=TINYINT},
      operate_type = #{operateType,jdbcType=TINYINT},
      context = #{context,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByContactId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from contact_operate_log
    contact_id = #{contactId,jdbcType=BIGINT}
  </select>
</mapper>