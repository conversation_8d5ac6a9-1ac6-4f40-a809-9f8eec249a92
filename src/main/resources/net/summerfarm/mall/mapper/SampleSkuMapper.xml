<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.SampleSkuMapper">


    <select id="selectBySampleId" parameterType="integer" resultType="net.summerfarm.mall.model.domain.SampleSku">
        select s.id, s.sample_id sampleId, s.sku, s.pd_name pdName, s.amount, s.weight, i.type as skuType, i.sku_pic as picturePath
        from sample_sku s
        left join  inventory i on s.sku = i.sku
        where s.sample_id = #{sampleId}
    </select>

    <select id="selectBySampleIds" resultType="net.summerfarm.mall.model.domain.SampleSku">
        select sample_id sampleId, sku, amount
        from sample_sku
        where sample_id in
        <foreach collection="sampleIds" open="(" separator="," close=")" item="sampleId">
            #{sampleId}
        </foreach>
    </select>
</mapper>