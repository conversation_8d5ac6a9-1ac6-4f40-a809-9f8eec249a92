<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.DirectPurchaseInfoMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.DirectPurchaseInfo">

    </resultMap>

    <sql id="Base_Column_List">
    id, m_id, purchase_no, order_no, send_time, receive_place, logistics_info, addtime, contact_id, contact_phone, one_click_ship_time, delivery_date
  </sql>

    <select id="selectById" resultType="net.summerfarm.mall.model.domain.DirectPurchaseInfo">

        select
        id, m_id as mId, purchase_no as purchaseNo, order_no as orderNo, send_time as sendTime,
         receive_place as receivePlace, logistics_info as logisticsInfo, addtime, contact_id as contactId, contact_phone as contactPhone,
         one_click_ship_time as oneClickShipTime, delivery_date as deliveryDate
        from direct_purchase_info
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="countDataByDeliveryDate" resultType="int">
        select count(*)
        from direct_purchase_info
        where delivery_date = #{deliveryTime,jdbcType=TIMESTAMP}
    </select>

    <select id="listDataByDeliveryDate" resultType="net.summerfarm.mall.model.domain.DirectPurchaseInfo">
        select order_no as orderNo, contact_id as contactId, delivery_date as deliveryDate
        from direct_purchase_info
        where delivery_date = #{deliveryTime,jdbcType=TIMESTAMP}
    </select>
</mapper>