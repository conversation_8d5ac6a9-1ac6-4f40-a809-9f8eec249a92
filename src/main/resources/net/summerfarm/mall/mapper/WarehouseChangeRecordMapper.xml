<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.WarehouseChangeMapper">

    <insert id="insertBySelect" parameterType="net.summerfarm.mall.model.vo.WarehouseChangeRecordVO">
        INSERT INTO warehouse_change_record(warehouse_no,store_no, sku, recorder, new_sale_lock_quantity, old_sale_lock_quantity, type_name, record_no, addtime)
        SELECT warehouse_no,store_no, sku, #{recorder},ifnull(#{newSaleLockQuantity},sale_lock_quantity ), ifnull(#{oldSaleLockQuantity} ,#{quantity}+ sale_lock_quantity), #{typeName} , #{recordNo} , now()
        FROM warehouse_inventory_mapping
        WHERE sku = #{sku}
        AND warehouse_no = #{warehouseNo}
        AND store_no=#{storeNo}
    </insert>

</mapper>