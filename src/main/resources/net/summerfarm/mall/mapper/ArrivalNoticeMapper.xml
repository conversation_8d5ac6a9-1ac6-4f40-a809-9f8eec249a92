<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.ArrivalNoticeMapper" >

  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.ArrivalNotice">
    insert into arrival_notice(m_id,mname,add_time,sku,pd_name,weight,store_no,store_name,status,type,num)
    values (#{mId},#{mname},#{addTime},#{sku},#{pdName},#{weight},#{storeNo},#{storeName},#{status},#{type},#{num})
  </insert>

  <insert id="batchInsert">
    insert into arrival_notice(m_id,mname,add_time,sku,pd_name,weight,store_no,store_name,status)
    values
    <foreach item="item" index="index" collection="list" separator=",">
      (#{item.mId},#{item.mname},#{item.addTime},#{item.sku},#{item.pdName},#{item.weight},#{item.storeNo},#{item.storeName},#{item.status})
    </foreach>
  </insert>


  <select id="select" resultType="net.summerfarm.mall.model.domain.ArrivalNotice"
          parameterType="net.summerfarm.mall.model.domain.ArrivalNotice">
    select
    an.sku,an.pd_name pdName,an.type
    from arrival_notice an
    where
    an.status=1
    <if test="sku !=null">
      AND an.sku=#{sku}
    </if>
    <if test="mId !=null">
      AND an.m_id=#{mId}
    </if>
    <if test="pdName != null">
      AND an.pdName like concat('%',#{pdName},'%')
    </if>
    <if test="storeNo != null">
      AND an.store_no=#{storeNo}
    </if>
  </select>

  <select id="selectBySku" resultType="net.summerfarm.mall.model.domain.ArrivalNotice"
          parameterType="net.summerfarm.mall.model.domain.ArrivalNotice">
    select an.id,m.m_id mId,an.pd_name pdName,an.weight,an.sku,an.store_no storeNo,m.mname,an.add_time addTime,i.pd_id pdId,m.openid,an.type
    from arrival_notice an
    left join merchant m on m.m_id=an.m_id
    left join inventory i on i.sku=an.sku
    where an.status = 1

    <if test="sku != null">
      and an.sku = #{sku}
    </if>
    <if test="storeNo != null">
      AND an.store_no=#{storeNo}
    </if>
  </select>

  <update id="updateById" >
    update arrival_notice an
    set an.status=2
    where an.id=#{id} and an.status=1
  </update>

  <select id="selectBySkuListAndMidAndStoreNo" resultType="net.summerfarm.mall.model.domain.ArrivalNotice">
    select m_id as mId, store_no as storeNo, sku, max(add_time) as addTime, id
    from arrival_notice
    where sku in
    <foreach item="sku" index="index" collection="skuList" open="(" separator="," close=")">
        #{sku}
    </foreach>
    and m_id = #{mid}
    and store_no = #{storeNo}
    group by m_id, store_no, sku
  </select>

  <delete id="batchDelete">
    delete from arrival_notice
    where id in
    <foreach item="id" index="index" collection="ids" open="(" separator="," close=")">
        #{id}
    </foreach>
  </delete>
</mapper>
