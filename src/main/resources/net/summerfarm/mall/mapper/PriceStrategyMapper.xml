<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.PriceStrategyMapper">

    <select id="select" resultType="net.summerfarm.mall.model.domain.PriceStrategy">
        select id,
               business_id businessId,
               type,
               adjust_type adjustType,
               amount,
               effective_price effectivePrice,
               rounding_mode roundingMode
        from price_strategy
        where business_id = #{businessId} and type = #{type}
    </select>
</mapper>