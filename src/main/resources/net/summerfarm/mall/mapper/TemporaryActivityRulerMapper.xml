<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.TemporaryActivityRulerMapper">


    <select id="selectByRulerId" resultType="net.summerfarm.mall.model.domain.TemporaryActivityRuler" parameterType="java.lang.Integer">
        select
        `ruler_id` rulerId ,
        category_id categoryId,
        brand_name brandName,
        category_time categoryTime
        from temporary_activity_ruler
        where `ruler_id` = #{rulerId}
    </select>

</mapper>