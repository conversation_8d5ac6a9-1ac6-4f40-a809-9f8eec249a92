<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.MajorPriceMapper" >


  <select id="selectSKu" resultType="java.lang.String">
    SELECT sku
    FROM major_price mp
    WHERE  mp.admin_id=#{adminId} AND mp.area_no=#{areaNo} and mp.direct=#{direct}
    AND mp.valid_time <![CDATA[<=]]> now()
    AND mp.invalid_time <![CDATA[>]]> now()
    AND mp.status = 1
    <if test="mallShow != null">
      and mp.mall_show = 0
    </if>
  </select>



  <select id="selectSKuByAdminMsg" resultType="net.summerfarm.mall.model.domain.Inventory">
     SELECT mp.sku,i.pd_id pdId,mp.price salePrice
    FROM major_price mp
    left join inventory i on i.sku = mp.sku
    WHERE  mp.admin_id=#{adminId} AND mp.area_no=#{areaNo} and mp.direct=#{direct} AND mp.status = 1
    <if test="pdId != null">
      AND i.pd_id = #{pdId}
    </if>
    AND mp.valid_time <![CDATA[<=]]> now()
    AND mp.invalid_time <![CDATA[>]]> now()
    <if test="mallShow != null">
      and mp.mall_show = 0
    </if>
  </select>


  <select id="selectSkuNotMallShow" resultType="java.lang.String">
     SELECT sku
    FROM major_price mp
    WHERE  mp.admin_id=#{adminId} AND mp.area_no=#{areaNo} and mp.direct=#{direct} AND mp.status = 1
    AND mp.valid_time <![CDATA[<=]]> now()
    AND mp.invalid_time <![CDATA[>]]> now()
    <if test="mallShow != null">
      and mp.mall_show = 1
    </if>
  </select>
    <select id="selectByMid" resultType="java.lang.Integer">
      select count(1) from major_price mj
      left join admin a on mj.admin_id = a.admin_id
      left join merchant m on a.admin_id = m.admin_id
      where sku = #{sku} and m.m_id= #{mId} and #{now} between mj.valid_time and mj.invalid_time and mj.mall_show = 0 AND mj.status = 1
        and mj.area_no = #{areaNo}
    </select>

  <select id="selectValidMajorPrice" resultType="net.summerfarm.mall.model.domain.MajorPrice">
    select m.id, m.sku, m.area_no areaNo, m.admin_id adminId, m.price ,m.large_area_no largeAreaNo,m.price_adjustment_value priceAdjustmentValue, m.remark,m.status

    from major_price m
           left join area_sku ak ON m.sku = ak.sku AND m.area_no = ak.area_no
    where m.sku = #{sku}
      and m.area_no = #{areaNo}
      and m.admin_id = #{adminId}
      and m.direct = 1
      and ak.on_sale = 1
      and m.valid_time <![CDATA[<=]]> now()
      and m.invalid_time <![CDATA[>]]> now()
  </select>

  <select id="selectMajorPrice" resultType="net.summerfarm.mall.model.domain.MajorPrice">
    select m.id, m.sku, m.area_no areaNo, m.admin_id adminId, m.price, m.price_type priceType, m.pay_method payMethod, m.valid_time validTime, m.invalid_time invalidTime, m.mall_show mallShow
    ,m.large_area_no largeAreaNo,m.price_adjustment_value priceAdjustmentValue, m.remark,m.status
    from major_price m
           left join area_sku ak ON m.sku = ak.sku AND m.area_no = ak.area_no
    where m.sku = #{sku}
      and m.area_no = #{areaNo}
      and m.admin_id = #{adminId}
      and m.direct = #{direct}
      and ak.on_sale = 1
      and m.valid_time <![CDATA[<=]]> now()
      and m.invalid_time <![CDATA[>]]> now()
      AND m.status = 1
      order by m.id desc limit 1
  </select>
  <select id="selectMajorPriceList" resultType="net.summerfarm.mall.model.domain.MajorPrice">
    SELECT
    mp.sku sku,
    mp.price price,
    mp.direct direct,
    mp.mall_show mallShow
   ,mp.large_area_no largeAreaNo,mp.price_adjustment_value priceAdjustmentValue, mp.remark,mp.status
    FROM
    major_price mp
    WHERE
    mp.area_no = #{areaNo}
    AND mp.admin_id=#{adminId}
    AND mp.direct=#{direct}
    AND mp.valid_time <![CDATA[<=]]> now() AND  mp.invalid_time <![CDATA[>]]> now() AND mp.status = 1
      <if test="skuList !=null">
        AND mp.sku in
        <foreach collection="skus" open="(" close=")" item="item" separator=",">
          #{item}
        </foreach>
      </if>
  </select>

  <select id="selectSkusMajorPriceList" resultType="net.summerfarm.mall.model.domain.MajorPrice">
    SELECT
    mp.id, mp.pay_method payMethod, mp.valid_time validTime, mp.invalid_time invalidTime,
    mp.sku sku,
    mp.admin_id adminId,
    mp.area_no areaNo,
    mp.price_type priceType,
    mp.price price,
    mp.direct direct,
    mp.mall_show mallShow
    ,mp.large_area_no largeAreaNo,mp.price_adjustment_value priceAdjustmentValue, mp.remark,mp.status
    FROM
    major_price mp
    WHERE
    mp.area_no = #{areaNo}
    AND mp.admin_id=#{adminId}
    AND mp.direct=#{direct}
    AND mp.valid_time <![CDATA[<=]]> now() AND  mp.invalid_time <![CDATA[>]]> now() AND mp.status = 1
    <if test="skus !=null">
      AND mp.sku in
      <foreach collection="skus" open="(" close=")" item="item" separator=",">
        #{item}
      </foreach>
    </if>
order by mp.id desc
  </select>
</mapper>