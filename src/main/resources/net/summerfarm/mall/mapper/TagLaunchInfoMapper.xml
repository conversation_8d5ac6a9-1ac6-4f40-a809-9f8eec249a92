<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.TagLaunchInfoMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.market.TagLaunchInfo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="type" jdbcType="INTEGER" property="type"/>
    <result column="style" jdbcType="INTEGER" property="style"/>
    <result column="content" jdbcType="VARCHAR" property="content"/>
    <result column="picture" jdbcType="VARCHAR" property="picture"/>
    <result column="weight" jdbcType="INTEGER" property="weight"/>
    <result column="scope_type" jdbcType="INTEGER" property="scopeType"/>
    <result column="item_type" jdbcType="INTEGER" property="itemType"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="creator_id" jdbcType="INTEGER" property="creatorId"/>
    <result column="updater_id" jdbcType="INTEGER" property="updaterId"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
  </resultMap>

  <resultMap id="DTOResultMap" type="net.summerfarm.mall.model.dto.market.malltag.MallTagDTO">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="type" jdbcType="INTEGER" property="type"/>
    <result column="style" jdbcType="INTEGER" property="style"/>
    <result column="content" jdbcType="VARCHAR" property="content"/>
    <result column="picture" jdbcType="VARCHAR" property="picture"/>
    <result column="weight" jdbcType="INTEGER" property="weight"/>
    <result column="scope_type" jdbcType="BIT" property="scopeType"/>
    <result column="item_type" jdbcType="BIT" property="itemType"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="biz_id" jdbcType="VARCHAR" property="bizId"/>
  </resultMap>
  <sql id="Base_Column_List">
    `id`
    , `name`, `type`, `style`, `content`, `picture`, `weight`, `scope_type`, `item_type`,
    `start_time`, `end_time`, `creator_id`, `updater_id`, `update_time`, `create_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tag_launch_info
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <select id="listAll" resultMap="DTOResultMap">
    select distinct tli.*,tls.biz_id
    from tag_launch_info tli
    left join tag_launch_scope_config tlsc on tli.id = tlsc.info_id
    left join tag_launch_sku_config tls on tli.id = tls.info_id
    where (start_time &lt;= now() and end_time &gt;= now())
      and (tli.scope_type = 0 or (tli.scope_type,tlsc.scope_id) in
    <foreach collection="scopes" open="(" separator="," close=")" item="item">
      (#{item.scopeType}, #{item.scopeId})
    </foreach>)
    and (tli.item_type = 0
    or (tli.item_type = 1 and tls.biz_id in
    <foreach collection="skus" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>))
  </select>
</mapper>