<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.LuckyDrawPrizeRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.LuckyDrawPrizeRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="coupon_id" jdbcType="BIGINT" property="couponId" />
    <result column="merchant_coupon_id" jdbcType="BIGINT" property="merchantCouponId" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, activity_id, coupon_id, merchant_coupon_id, m_id, create_time, update_time
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lucky_draw_prize_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lucky_draw_prize_record
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.LuckyDrawPrizeRecord">
    insert into lucky_draw_prize_record (id, activity_id, coupon_id, 
      merchant_coupon_id, m_id, create_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{activityId,jdbcType=BIGINT}, #{couponId,jdbcType=BIGINT}, 
      #{merchantCouponId,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.LuckyDrawPrizeRecord">
    insert into lucky_draw_prize_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="merchantCouponId != null">
        merchant_coupon_id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=BIGINT},
      </if>
      <if test="merchantCouponId != null">
        #{merchantCouponId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.LuckyDrawPrizeRecord">
    update lucky_draw_prize_record
    <set>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=BIGINT},
      </if>
      <if test="merchantCouponId != null">
        merchant_coupon_id = #{merchantCouponId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.LuckyDrawPrizeRecord">
    update lucky_draw_prize_record
    set activity_id = #{activityId,jdbcType=BIGINT},
      coupon_id = #{couponId,jdbcType=BIGINT},
      merchant_coupon_id = #{merchantCouponId,jdbcType=BIGINT},
      m_id = #{mId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByEntity" resultMap="BaseResultMap" parameterType="net.summerfarm.mall.model.domain.LuckyDrawPrizeRecord">
    select
    <include refid="Base_Column_List" />
    from lucky_draw_prize_record
    <where>
      <if test="activityId != null">
        and activity_id = #{activityId,jdbcType=BIGINT}
      </if>
      <if test="mId != null">
        and m_id = #{mId,jdbcType=BIGINT}
      </if>
    </where>
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into lucky_draw_prize_record (activity_id, coupon_id,
                                         merchant_coupon_id, m_id, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.activityId,jdbcType=BIGINT}, #{item.couponId,jdbcType=BIGINT},
      #{item.merchantCouponId,jdbcType=BIGINT}, #{item.mId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <select id="getListByActivityId" resultMap="BaseResultMap" parameterType="java.lang.Long">
    SELECT a.id, a.coupon_id, a.m_id FROM
      (SELECT id, coupon_id, m_id FROM lucky_draw_prize_record
       where activity_id = #{activityId,jdbcType=BIGINT}  ORDER BY id desc) a  GROUP BY a.m_id limit 20
  </select>
</mapper>