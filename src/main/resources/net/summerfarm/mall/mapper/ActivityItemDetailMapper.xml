<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.ActivityItemDetailMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.market.ActivityItemDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="item_config_id" jdbcType="BIGINT" property="itemConfigId" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
    <result column="tag_name" jdbcType="VARCHAR" property="tagName" />
    <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `item_config_id`, `category_id`, `tag_name`, `del_flag`, `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from activity_item_detail
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from activity_item_detail
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.market.ActivityItemDetail">
    insert into activity_item_detail (`id`, `item_config_id`, `category_id`, 
      `tag_name`, `del_flag`, `create_time`, 
      `update_time`)
    values (#{id,jdbcType=BIGINT}, #{itemConfigId,jdbcType=BIGINT}, #{categoryId,jdbcType=INTEGER}, 
      #{tagName,jdbcType=VARCHAR}, #{delFlag,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.market.ActivityItemDetail">
    insert into activity_item_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="itemConfigId != null">
        `item_config_id`,
      </if>
      <if test="categoryId != null">
        `category_id`,
      </if>
      <if test="tagName != null">
        `tag_name`,
      </if>
      <if test="delFlag != null">
        `del_flag`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="itemConfigId != null">
        #{itemConfigId,jdbcType=BIGINT},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="tagName != null">
        #{tagName,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.market.ActivityItemDetail">
    update activity_item_detail
    <set>
      <if test="itemConfigId != null">
        `item_config_id` = #{itemConfigId,jdbcType=BIGINT},
      </if>
      <if test="categoryId != null">
        `category_id` = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="tagName != null">
        `tag_name` = #{tagName,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        `del_flag` = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.market.ActivityItemDetail">
    update activity_item_detail
    set `item_config_id` = #{itemConfigId,jdbcType=BIGINT},
      `category_id` = #{categoryId,jdbcType=INTEGER},
      `tag_name` = #{tagName,jdbcType=VARCHAR},
      `del_flag` = #{delFlag,jdbcType=TINYINT},
      `create_time` = #{createTime,jdbcType=TIMESTAMP},
      `update_time` = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>
</mapper>