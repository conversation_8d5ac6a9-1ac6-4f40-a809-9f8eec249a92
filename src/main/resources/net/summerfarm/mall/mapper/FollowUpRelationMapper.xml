<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.FollowUpRelationMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.FollowUpRelation" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="m_id" property="mId" jdbcType="BIGINT" />
    <result column="admin_id" property="adminId" jdbcType="INTEGER" />
    <result column="admin_name" property="adminName" jdbcType="VARCHAR" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="reassign" property="reassign" jdbcType="BIT" />
    <result column="last_follow_up_time" property="lastFollowUpTime" jdbcType="TIMESTAMP" />
    <result column="reassign_time" property="reassignTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, m_id, admin_id, admin_name, add_time, reassign, last_follow_up_time, reassign_time
  </sql>

  <select id="selectByMId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from follow_up_relation
    where m_id = #{mId}
    AND reassign = 0
  </select>
    <select id="selectOne" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from follow_up_relation
      <where>
        <if test="mId != null">
          AND m_id = #{mId}
        </if>
        <if test="reassign != null">
          AND reassign =#{reassign}
        </if>
        <if test="adminName !=null">
          AND admin_name = #{adminName}
        </if>
      </where>
      order by id desc
      limit 1
    </select>

  <delete id="deleteByMid">
        delete from follow_up_relation where m_id = #{mId}
    </delete>

</mapper>