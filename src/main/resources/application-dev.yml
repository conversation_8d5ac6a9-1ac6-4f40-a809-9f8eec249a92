server:
  tomcat:
    accept-count: 1000
    max-connections: 500
    max-threads: 20
    min-spare-threads: 20
  servlet:
    session:
      timeout: 3600
  port: 80
#mysql
mysql:
  asyncInit: true
  dbType: com.alibaba.druid.pool.DruidDataSource
  driverClassName: com.mysql.jdbc.Driver
  initialSize: 64
  maxActive: 64
  maxWait: 1000
  minIdle: 64
  offline:
    password: xianmu619
    url: **************************************************************************************************
    username: test
  password: xianmu619
  testWhileIdle: true
  url: ******************************************************************************************
  username: dev
rocketmq:
  consumer:
    access-key: ''
    secret-key: ''
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: ''
    group: GID_mall
    secret-key: ''
    sendMsgTimeout: 10000
scheduling:
  local:
    ip: ********
spring:
  application:
    id: summerfarm-mall
    name: summerfarm-mall
  schedulerx2:
    appKey: w489AnHT3rPwJxYBaXZeDA==
    endpoint: acm.aliyun.com
    groupId: mall
    namespace: 0fba89cd-351e-4e9f-86dc-4b4fbc06170e
  redis:
    host: test-redis.summerfarm.net
    password: xianmu619
    port: 6379
    timeout: 5000 # 连接超时时间（毫秒）
    database: 0
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 0
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）        
es:
  port: 80
  url: dev.es.summerfarm.net
  user-name: elastic
  user-pwd: Xianmu619
mall:
  domain:
    name: devh5.summerfarm.net
stmp:
  account: <EMAIL>
  auth: true
  defaultEncoding: utf-8
  host: smtp.feishu.cn
  password: Xianmu619
  port: 465
  socketFactory:
    class: javax.net.ssl.SSLSocketFactory
xianmu:
  mall:
    domain: https://devh5.summerfarm.net
pop:
  mall:
    domain: https://devshunluda.cosfo.cn
wechat:
  app:
    id: wx6569288b28c54ced
    secret: 9adf57dbc754204f08ef67aba718256a
  mp-app:
    id: wx0234b1d4eb212e12
    secret: 7779dbf6349ca85212b05435dfc38716
  pop-app:
    id: wx7b96cacba5e0e9e7
    secret: fad5bc94ebb8e92014225982588e5fc9
  pop-mp-app:
    id: wxefc29048b84a19cb
    secret: 7f2a43f3a9975be1c6f66683c9ca2b03

## Mybatis 配置
mybatis:
  type-aliases-package: net.summerfarm.mall.model.domain
  mapper-locations: classpath:net.summerfarm.mall.mapper/*.xml

# 日志配置
logging:
  pattern:
    console: "%d - %msg%n"
  level:
    root:  INFO
    org.springframework:  INFO
    org.mybatis:  INFO
    net.summerfarm: INFO

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

management:
  health:
    elasticsearch:
      enabled: false
log-path: ${APP_LOG_DIR:../log}

##启用shutdown
#endpoints:
#  shutdown:
#    enabled: true
#    #禁用密码验证
#    sensitive: false
dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    #    address: nacos://************:11000
    address: nacos://test-nacos.summerfarm.net:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: fac8164c-1da8-43d2-bf49-e187bda7fcb4
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 5000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
    timeout: 10000
nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: 19b82444-16f9-4d22-a522-b7ac6495c954
xm:
  log:
    enable: true
    resp: true