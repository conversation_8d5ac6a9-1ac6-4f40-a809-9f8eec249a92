
# mybatis sql 打印
#logging:
#   level:
#     net.summerfarm.mapper: debug
#mybatis:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
server:
  tomcat:
    accept-count: 1000
    max-connections: 10000
    max-threads: 800
    min-spare-threads: 100
  servlet:
    session:
      timeout: 3600
  port: 80
mysql:
  asyncInit: true
  dbType: com.alibaba.druid.pool.DruidDataSource
  driverClassName: com.mysql.jdbc.Driver
  initialSize: 5
  maxActive: 80
  maxWait: 6000
  minIdle: 5
  offline:
    password: xianmu619
    url: **************************************************************************************************
    username: test
  password: xianmu619
  testWhileIdle: true
  url: ******************************************************************************************
  username: qa
rocketmq:
  consumer:
    access-key: ''
    secret-key: ''
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: ''
    group: GID_mall
    secret-key: ''
    sendMsgTimeout: 10000
scheduling:
  local:
    ip: ********
es:
  port: 80
  url: dev.es.summerfarm.net
  user-name: elastic
  user-pwd: Xianmu619
mall:
  domain:
    name: qah5.summerfarm.net
spring:
  application:
    id: summerfarm-mall
    name: summerfarm-mall
  schedulerx2:
    appKey: h2WDM5MU85Lr+tBHwEmBdw==
    endpoint: acm.aliyun.com
    groupId: mall
    namespace: a40f0ca3-5bb6-417f-9df0-fb12c4c464a7
  redis:
    host: test-redis.summerfarm.net
    password: xianmu619
    port: 6379
    timeout: 5000 # 连接超时时间（毫秒）
    database: 5
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 5
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）        
stmp:
  account: <EMAIL>
  auth: true
  defaultEncoding: utf-8
  host: smtp.feishu.cn
  password: Xianmu619
  port: 465
  socketFactory:
    class: javax.net.ssl.SSLSocketFactory
xianmu:
  mall:
    domain: https://qah5.summerfarm.net
pop:
  mall:
    domain: https://qashunluda.cosfo.cn
wechat:
  app:
    id: wx32a0e329197b752b
    secret: e3d4592bb9f437e682334efd37928ee8
  mp-app:
    id: wx674b60a859676717
    secret: 250fd1499c812616fe4b0caab9dffb0f
  pop-app:
    id: wxc6e92d6b2059e052
    secret: a5732570e0dc8370af4764da66372445
  pop-mp-app:
    id: wxefc29048b84a19cb
    secret: 7f2a43f3a9975be1c6f66683c9ca2b03

## Mybatis 配置
mybatis:
  type-aliases-package: net.summerfarm.mall.model.domain
  mapper-locations: classpath:net.summerfarm.mall.mapper/*.xml

# 日志配置
logging:
  pattern:
    console: "%d - %msg%n"
  level:
    root:  ERROR
    org.springframework:  ERROR
    org.mybatis:  ERROR
    net.summerfarm: INFO
    net.xianmu: INFO

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

management:
  health:
    elasticsearch:
      enabled: false
log-path: ${APP_LOG_DIR:../log}

##启用shutdown
#endpoints:
#  shutdown:
#    enabled: true
#    #禁用密码验证
#    sensitive: false
dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    #    address: nacos://************:11000
    address: nacos://test-nacos.summerfarm.net:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: 34792f7a-aaa2-41ee-8a7f-53be483c2533
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 10000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: b781e552-933d-44c5-b642-49dd30c5ba5f
xm:
  log:
    enable: true
    resp: false
  sentinel:
    nacos:
      serverAddr: test-nacos.summerfarm.net:11000
      groupId: sentinel
      namespace: e5ca5c64-a551-4889-b5a1-7bf9c90b4752
  mq:
    listen: false
