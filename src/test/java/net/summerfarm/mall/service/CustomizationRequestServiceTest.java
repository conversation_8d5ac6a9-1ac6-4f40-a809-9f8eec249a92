package net.summerfarm.mall.service;

import net.summerfarm.mall.model.domain.CustomizationRequest;
import net.summerfarm.mall.model.input.CustomizationRequestInput;
import net.xianmu.common.result.CommonResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 定制需求服务测试类
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CustomizationRequestServiceTest {

    @Resource
    private CustomizationRequestService customizationRequestService;

    @Test
    public void testSaveCustomizationRequest() {
        // 创建测试数据
        CustomizationRequestInput input = new CustomizationRequestInput();
        input.setProductName("定制T恤 - 白色 L码");
        input.setSku("CUSTOM_TSHIRT_001");
        input.setSourceSku("TEMPLATE_TSHIRT_001");
        input.setStoreName("测试门店");
        input.setAccountName("design_account_001");
        input.setInitiateOrderNo("ORDER_TEST_001");
        input.setColorCount(2);
        input.setReferenceImage("http://example.com/reference.jpg");
        input.setRemark("测试定制需求");

        // 执行保存
        CommonResult<Long> result = customizationRequestService.saveCustomizationRequest(input);
        
        // 验证结果
        System.out.println("保存结果: " + result);
        if (result.isSuccess()) {
            System.out.println("定制需求保存成功，ID: " + result.getData());
            
            // 查询保存的记录
            CommonResult<CustomizationRequest> queryResult = 
                customizationRequestService.getCustomizationRequestById(result.getData());
            System.out.println("查询结果: " + queryResult);
        }
    }

    @Test
    public void testGetCustomizationRequestByOrderNo() {
        String orderNo = "ORDER_TEST_001";
        CommonResult<CustomizationRequest> result = 
            customizationRequestService.getCustomizationRequestByOrderNo(orderNo);
        System.out.println("根据订单号查询结果: " + result);
    }

    @Test
    public void testUpdateCustomizationRequestStatus() {
        // 假设存在ID为1的记录
        Long id = 1L;
        Integer newStatus = 2; // 设计师发起确认
        
        CommonResult<Boolean> result = 
            customizationRequestService.updateCustomizationRequestStatus(id, newStatus);
        System.out.println("状态更新结果: " + result);
    }

    @Test
    public void testConfirmDesign() {
        // 假设存在ID为1的记录
        Long id = 1L;
        Boolean approved = true;
        String refuseReason = null;
        
        CommonResult<Boolean> result = 
            customizationRequestService.confirmDesign(id, approved, refuseReason);
        System.out.println("设计确认结果: " + result);
    }

    @Test
    public void testAddCommunicationNote() {
        // 假设存在ID为1的记录
        Long id = 1L;
        String note = "客户要求修改颜色为蓝色";
        
        CommonResult<Boolean> result = 
            customizationRequestService.addCommunicationNote(id, note);
        System.out.println("添加沟通记录结果: " + result);
    }
}
