package net.summerfarm.mall.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import net.summerfarm.mall.model.dto.price.BatchTakePriceRequest;
import net.summerfarm.mall.util.PriceApiEncryptUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * PriceController 测试类
 * 
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureWebMvc
public class PriceControllerTest {

    @Resource
    private MockMvc mockMvc;

    @Resource
    private ObjectMapper objectMapper;

    @Test
    public void testBatchTakeActualPrice() throws Exception {
        // 1. 准备测试数据
        Long mId = 12345L;
        Long timestamp = System.currentTimeMillis();
        
        // 创建SKU列表
        BatchTakePriceRequest.BatchTakePriceItem item1 = new BatchTakePriceRequest.BatchTakePriceItem();
        item1.setSku("863080734120");
        item1.setQuantity(2);
        
        BatchTakePriceRequest.BatchTakePriceItem item2 = new BatchTakePriceRequest.BatchTakePriceItem();
        item2.setSku("593305756483");
        item2.setQuantity(1);
        
        List<BatchTakePriceRequest.BatchTakePriceItem> skuList = Arrays.asList(item1, item2);

        // 2. 生成签名
        String skuListStr = skuList.stream()
                .map(item -> item.getSku() + ":" + item.getQuantity())
                .sorted()
                .collect(Collectors.joining(","));
        
        String signature = PriceApiEncryptUtil.generateSignature(mId, skuListStr, timestamp);

        // 3. 构建请求
        BatchTakePriceRequest request = new BatchTakePriceRequest();
        request.setMId(mId);
        request.setSkuList(skuList);
        request.setTimestamp(timestamp);
        request.setSignature(signature);

        // 4. 执行测试
        mockMvc.perform(MockMvcRequestBuilders.post("/price/batch/take-actual-price")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(true))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").exists())
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.mId").value(mId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.skuPrices").isArray());

        System.out.println("批量价格查询API测试通过");
    }

    @Test
    public void testBatchTakeActualPriceWithInvalidSignature() throws Exception {
        // 1. 准备测试数据（使用错误的签名）
        Long mId = 12345L;
        Long timestamp = System.currentTimeMillis();
        
        BatchTakePriceRequest.BatchTakePriceItem item1 = new BatchTakePriceRequest.BatchTakePriceItem();
        item1.setSku("863080734120");
        item1.setQuantity(2);
        
        List<BatchTakePriceRequest.BatchTakePriceItem> skuList = Arrays.asList(item1);

        // 2. 使用错误的签名
        String wrongSignature = "wrong_signature_123456";

        // 3. 构建请求
        BatchTakePriceRequest request = new BatchTakePriceRequest();
        request.setMId(mId);
        request.setSkuList(skuList);
        request.setTimestamp(timestamp);
        request.setSignature(wrongSignature);

        // 4. 执行测试，期望签名验证失败
        mockMvc.perform(MockMvcRequestBuilders.post("/price/batch/take-actual-price")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("签名验证失败"));

        System.out.println("签名验证失败测试通过");
    }

    @Test
    public void testBatchTakeActualPriceWithExpiredTimestamp() throws Exception {
        // 1. 准备测试数据（使用过期的时间戳）
        Long mId = 12345L;
        Long expiredTimestamp = System.currentTimeMillis() - 10 * 60 * 1000L; // 10分钟前
        
        BatchTakePriceRequest.BatchTakePriceItem item1 = new BatchTakePriceRequest.BatchTakePriceItem();
        item1.setSku("863080734120");
        item1.setQuantity(2);
        
        List<BatchTakePriceRequest.BatchTakePriceItem> skuList = Arrays.asList(item1);

        // 2. 生成签名（虽然签名正确，但时间戳过期）
        String skuListStr = skuList.stream()
                .map(item -> item.getSku() + ":" + item.getQuantity())
                .sorted()
                .collect(Collectors.joining(","));
        
        String signature = PriceApiEncryptUtil.generateSignature(mId, skuListStr, expiredTimestamp);

        // 3. 构建请求
        BatchTakePriceRequest request = new BatchTakePriceRequest();
        request.setMId(mId);
        request.setSkuList(skuList);
        request.setTimestamp(expiredTimestamp);
        request.setSignature(signature);

        // 4. 执行测试，期望时间戳验证失败
        mockMvc.perform(MockMvcRequestBuilders.post("/price/batch/take-actual-price")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("签名验证失败"));

        System.out.println("过期时间戳验证失败测试通过");
    }

    @Test
    public void testBatchTakeActualPriceWithTooManySkus() throws Exception {
        // 1. 准备测试数据（超过20个SKU）
        Long mId = 12345L;
        Long timestamp = System.currentTimeMillis();
        
        // 创建21个SKU项目
        List<BatchTakePriceRequest.BatchTakePriceItem> skuList = 
                java.util.stream.IntStream.range(1, 22)
                        .mapToObj(i -> {
                            BatchTakePriceRequest.BatchTakePriceItem item = new BatchTakePriceRequest.BatchTakePriceItem();
                            item.setSku("TEST_SKU_" + String.format("%03d", i));
                            item.setQuantity(1);
                            return item;
                        })
                        .collect(Collectors.toList());

        // 2. 生成签名
        String skuListStr = skuList.stream()
                .map(item -> item.getSku() + ":" + item.getQuantity())
                .sorted()
                .collect(Collectors.joining(","));
        
        String signature = PriceApiEncryptUtil.generateSignature(mId, skuListStr, timestamp);

        // 3. 构建请求
        BatchTakePriceRequest request = new BatchTakePriceRequest();
        request.setMId(mId);
        request.setSkuList(skuList);
        request.setTimestamp(timestamp);
        request.setSignature(signature);

        // 4. 执行测试，期望参数验证失败
        mockMvc.perform(MockMvcRequestBuilders.post("/price/batch/take-actual-price")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(MockMvcResultMatchers.status().isBadRequest()); // 参数验证失败

        System.out.println("SKU数量限制验证测试通过");
    }

    @Test
    public void testGenerateSignatureExample() {
        // 演示如何生成签名
        Long mId = 12345L;
        String skuList = "863080734120:2,593305756483:1";
        Long timestamp = System.currentTimeMillis();
        
        String signature = PriceApiEncryptUtil.generateSignature(mId, skuList, timestamp);
        
        System.out.println("=== 签名生成示例 ===");
        System.out.println("门店ID: " + mId);
        System.out.println("SKU列表: " + skuList);
        System.out.println("时间戳: " + timestamp);
        System.out.println("签名: " + signature);
        
        // 验证签名
        boolean isValid = PriceApiEncryptUtil.verifySignature(mId, skuList, timestamp, signature);
        System.out.println("签名验证结果: " + isValid);
        
        assert isValid : "签名验证应该成功";
    }
}
