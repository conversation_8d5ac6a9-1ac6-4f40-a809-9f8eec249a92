package net.summerfarm.mall.util;

import org.junit.Test;

import static org.junit.Assert.*;

/**
 * PriceApiEncryptUtil 测试类
 * 
 * <AUTHOR>
 */
public class PriceApiEncryptUtilTest {

    @Test
    public void testGenerateAndVerifySignature() {
        // Given
        Long mId = 12345L;
        String skuList = "863080734120:2,593305756483:1";
        Long timestamp = System.currentTimeMillis();

        // When
        String signature = PriceApiEncryptUtil.generateSignature(mId, skuList, timestamp);

        // Then
        assertNotNull("签名不应为空", signature);
        assertEquals("签名长度应为32位", 32, signature.length());

        // 验证签名
        boolean isValid = PriceApiEncryptUtil.verifySignature(mId, skuList, timestamp, signature);
        assertTrue("签名验证应该成功", isValid);
    }

    @Test
    public void testVerifySignatureWithWrongSignature() {
        // Given
        Long mId = 12345L;
        String skuList = "863080734120:2,593305756483:1";
        Long timestamp = System.currentTimeMillis();
        String wrongSignature = "wrong_signature_123456789012345678901234";

        // When
        boolean isValid = PriceApiEncryptUtil.verifySignature(mId, skuList, timestamp, wrongSignature);

        // Then
        assertFalse("错误的签名验证应该失败", isValid);
    }

    @Test
    public void testVerifySignatureWithExpiredTimestamp() {
        // Given
        Long mId = 12345L;
        String skuList = "863080734120:2,593305756483:1";
        Long expiredTimestamp = System.currentTimeMillis() - 10 * 60 * 1000L; // 10分钟前
        String signature = PriceApiEncryptUtil.generateSignature(mId, skuList, expiredTimestamp);

        // When
        boolean isValid = PriceApiEncryptUtil.verifySignature(mId, skuList, expiredTimestamp, signature);

        // Then
        assertFalse("过期时间戳的签名验证应该失败", isValid);
    }

    @Test
    public void testFormatAndParseSkuList() {
        // Given
        java.util.List<PriceApiEncryptUtil.SkuItem> originalList = java.util.Arrays.asList(
                new PriceApiEncryptUtil.SkuItem("SKU001", 2),
                new PriceApiEncryptUtil.SkuItem("SKU002", 1),
                new PriceApiEncryptUtil.SkuItem("SKU003", 3)
        );

        // When
        String formatted = PriceApiEncryptUtil.formatSkuList(originalList);
        java.util.List<PriceApiEncryptUtil.SkuItem> parsed = PriceApiEncryptUtil.parseSkuList(formatted);

        // Then
        assertEquals("格式化后的字符串应该正确", "SKU001:2,SKU002:1,SKU003:3", formatted);
        assertEquals("解析后的列表大小应该一致", originalList.size(), parsed.size());
        
        for (int i = 0; i < originalList.size(); i++) {
            assertEquals("SKU应该一致", originalList.get(i).getSku(), parsed.get(i).getSku());
            assertEquals("数量应该一致", originalList.get(i).getQuantity(), parsed.get(i).getQuantity());
        }
    }

    @Test
    public void testSignatureConsistency() {
        // Given
        Long mId = 12345L;
        String skuList = "863080734120:2,593305756483:1";
        Long timestamp = 1703123456789L; // 固定时间戳

        // When
        String signature1 = PriceApiEncryptUtil.generateSignature(mId, skuList, timestamp);
        String signature2 = PriceApiEncryptUtil.generateSignature(mId, skuList, timestamp);

        // Then
        assertEquals("相同参数应该生成相同的签名", signature1, signature2);
    }

    @Test
    public void testDifferentParametersGenerateDifferentSignatures() {
        // Given
        Long timestamp = System.currentTimeMillis();
        String skuList = "863080734120:2,593305756483:1";

        // When
        String signature1 = PriceApiEncryptUtil.generateSignature(12345L, skuList, timestamp);
        String signature2 = PriceApiEncryptUtil.generateSignature(54321L, skuList, timestamp);

        // Then
        assertNotEquals("不同mId应该生成不同的签名", signature1, signature2);
    }

    @Test
    public void testGenerateSignatureExample() {
        // 生成示例签名用于文档
        Long mId = 12345L;
        String skuList = "863080734120:2,593305756483:1";
        Long timestamp = System.currentTimeMillis(); // 使用当前时间戳

        String signature = PriceApiEncryptUtil.generateSignature(mId, skuList, timestamp);

        System.out.println("=== API调用示例 ===");
        System.out.println("门店ID: " + mId);
        System.out.println("SKU列表: " + skuList);
        System.out.println("时间戳: " + timestamp);
        System.out.println("签名: " + signature);
        System.out.println();
        System.out.println("请求体JSON示例:");
        System.out.println("{");
        System.out.println("  \"mId\": " + mId + ",");
        System.out.println("  \"skuList\": [");
        System.out.println("    {\"sku\": \"863080734120\", \"quantity\": 2},");
        System.out.println("    {\"sku\": \"593305756483\", \"quantity\": 1}");
        System.out.println("  ],");
        System.out.println("  \"timestamp\": " + timestamp + ",");
        System.out.println("  \"signature\": \"" + signature + "\"");
        System.out.println("}");

        // 验证生成的签名
        boolean isValid = PriceApiEncryptUtil.verifySignature(mId, skuList, timestamp, signature);
        assertTrue("生成的示例签名应该有效", isValid);
    }
}
