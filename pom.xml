<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.2.2.RELEASE</version>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<artifactId>summerfarm-mall</artifactId>
	<groupId>net.summerfarm</groupId>
	<packaging>jar</packaging>
	<version>1.0.0</version>
	<name>mallApplication</name>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<!--    基础配置信息    -->
		<!--    依赖版本统一配置、便于管理    -->
		<fastjson.version>1.2.83</fastjson.version>
		<lombok.version>1.18.2</lombok.version>
		<typehandlers.version>1.0.1</typehandlers.version>
		<datatype.version>2.9.2</datatype.version>
		<swagger.version>2.7.0</swagger.version>
		<qiniu.version>[7.2.0, 7.2.99]</qiniu.version>
		<starter.version>2.1.1</starter.version>
		<mysql-connector.version>8.0.23</mysql-connector.version>
		<mybatis.version>3.5.0</mybatis.version>
		<druid.version>1.2.6</druid.version>
		<redis.version>3.1.0</redis.version>
		<commons-math3.version>3.6.1</commons-math3.version>
		<xstream.version>1.4.7</xstream.version>
		<es.version>7.10.0</es.version>
		<gauva.version>28.2-jre</gauva.version>
		<nacos-config.version>0.2.10</nacos-config.version>
		<inventory-client.version>2.0.15-RELEASE</inventory-client.version>
		<usercenter.version>1.2.1</usercenter.version>
		<inventory-sdk.version>2.0.12-RELEASE</inventory-sdk.version>
		<crm-client.version>1.0.7</crm-client.version>
		<item-client.version>1.0.48-RELEASE</item-client.version>
		<mall-engine-client.version>1.0.9-RELEASE</mall-engine-client.version>
		<job-sdk.verison>1.0.2-RELEASE</job-sdk.verison>
		<bms.client.version>1.2.0-RELEASE</bms.client.version>
		<ofc-client.version>1.6.7-RELEASE</ofc-client.version>
	</properties>

    <dependencies>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-mybatis-interceptor-support</artifactId>
            <version>1.0.7-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>3.0.9</version>
            <type>pom</type>
        </dependency>
        <!--    核心依赖模块    -->
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-common</artifactId>
			<version>1.5.14-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.pagehelper</groupId>
                    <artifactId>pagehelper</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-rocketmq-support</artifactId>
            <version>1.2.4</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-sentinel-support</artifactId>
            <version>1.0.2-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-task-support</artifactId>
            <version>1.0.5</version>
        </dependency>
        <!-- 二方库 -->
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-log-support</artifactId>
            <version>1.0.14-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>mall-client</artifactId>
            <version>1.1.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>net.manage.client</groupId>
            <artifactId>manage-client</artifactId>
            <version>1.0.52-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.cosfo.summerfarm</groupId>
            <artifactId>saas-to-summerfarm</artifactId>
            <version>1.6.12-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-redis-support</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-warehouse</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.xianmu.common</groupId>
            <artifactId>xianmu-common</artifactId>
            <!--      根据实际版本修改，线上禁止SNAPSHOT版本     -->
            <version>1.1.9-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>goods-center-client</artifactId>
			<version>1.2.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-pms-client</artifactId>
            <version>1.5.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>tms-client</artifactId>
            <version>1.0.12-RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>xianmu-common</artifactId>
                    <groupId>net.xianmu.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>tms-common</artifactId>
                    <groupId>net.summerfarm</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>summerfarm-inventory-sdk</artifactId>
            <groupId>net.summerfarm</groupId>
            <version>${inventory-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>summerfarm-inventory-client</artifactId>
            <version>${inventory-client.version}</version>
        </dependency>

		<dependency>
			<groupId>net.summerfarm.wms</groupId>
			<artifactId>summerfarm-wms-client</artifactId>
			<version>1.6.5-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>net.summerfarm</groupId>
			<artifactId>summerfarm-wnc-client</artifactId>
			<version>1.3.8-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>net.summerfarm</groupId>
			<artifactId>crm-client</artifactId>
			<version>${crm-client.version}</version>
		</dependency>
		<dependency>
			<groupId>net.summerfarm</groupId>
			<artifactId>ofc-client</artifactId>
			<version>${ofc-client.version}</version>
		</dependency>
		<dependency>
			<groupId>net.summerfarm</groupId>
			<artifactId>mall-engine-client</artifactId>
			<version>${mall-engine-client.version}</version>
		</dependency>
		<dependency>
			<groupId>net.xianmu</groupId>
			<artifactId>marketing-center-client</artifactId>
			<version>1.0.11-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>net.summerfarm</groupId>
			<artifactId>summerfarm-payment-sdk</artifactId>
			<version>1.0.11-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>net.xianmu</groupId>
			<artifactId>bms-service-client</artifactId>
			<version>${bms.client.version}</version>
		</dependency>

		<dependency>
			<groupId>net.xianmu</groupId>
			<artifactId>job-sdk</artifactId>
			<version>${job-sdk.verison}</version>
		</dependency>

		<dependency>
			<groupId>com.cosfo</groupId>
			<artifactId>item-center-client</artifactId>
			<version>${item-client.version}</version>
		</dependency>
		<dependency>
			<groupId>net.xianmu</groupId>
			<artifactId>usercenter-client</artifactId>
			<version>${usercenter.version}</version>
		</dependency>
		<!--  springboot 核心依赖包  -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
			<exclusions>
				<exclusion>
					<groupId>io.lettuce</groupId>
					<artifactId>lettuce-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-databind</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.restdocs</groupId>
			<artifactId>spring-restdocs-mockmvc</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<!--开发环境re热部署-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional>
		</dependency>
		<!--引入java mail 发邮件用-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-mail</artifactId>
		</dependency>
		<!-- alibaba json -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson.version}</version>
		</dependency>
		<!--  lombok  -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
		</dependency>
		<!--mybatis整合spring组件-->
		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
			<version>${starter.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.mybatis</groupId>
					<artifactId>mybatis</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis</artifactId>
			<version>${mybatis.version}</version>
		</dependency>
		<!--数据库组件——mysql连接组件-->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>${mysql-connector.version}</version>
			<scope>runtime</scope>
		</dependency>
		<!--alibaba开源数据库连接池-->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
			<version>${druid.version}</version>
		</dependency>
		<!--  mybatis类型处理  -->
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis-typehandlers-jsr310</artifactId>
			<version>${typehandlers.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
			<version>${datatype.version}</version>
		</dependency>
		<!--redis依赖-->
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>${redis.version}</version>
		</dependency>
		<!--  swagger  -->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>${swagger.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--  七牛上传SDK  -->
		<dependency>
			<groupId>com.qiniu</groupId>
			<artifactId>qiniu-java-sdk</artifactId>
			<version>${qiniu.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.squareup.okhttp3</groupId>
					<artifactId>okhttp</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.squareup.okhttp</groupId>
			<artifactId>okhttp</artifactId>
			<version>2.7.1</version>
			<exclusions>
				<exclusion>
					<artifactId>okio</artifactId>
					<groupId>com.squareup.okio</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--   apache数学工具包     -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-math3</artifactId>
			<version>${commons-math3.version}</version>
		</dependency>
		<!--    xstream    -->
		<dependency>
			<groupId>com.thoughtworks.xstream</groupId>
			<artifactId>xstream</artifactId>
			<version>${xstream.version}</version>
		</dependency>
		<dependency>
			<groupId>io.github.yedaxia</groupId>
			<artifactId>japidocs</artifactId>
			<version>1.3</version>
		</dependency>
		<!--    es连接    -->
		<dependency>
			<groupId>org.elasticsearch.client</groupId>
			<artifactId>elasticsearch-rest-high-level-client</artifactId>
			<version>${es.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.elasticsearch</groupId>
					<artifactId>elasticsearch</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.elasticsearch.client</groupId>
					<artifactId>elasticsearch-rest-client</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch.client</groupId>
			<artifactId>elasticsearch-rest-client</artifactId>
			<version>${es.version}</version>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch</groupId>
			<artifactId>elasticsearch</artifactId>
			<version>${es.version}</version>
		</dependency>
		<!--    阿里云odbs    -->
		<dependency>
			<groupId>com.aliyun.odps</groupId>
			<artifactId>odps-sdk-core</artifactId>
			<version>0.35.5-public</version>
		</dependency>
		<!-- Google guava工具包-->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>${gauva.version}</version>
		</dependency>
		<!-- hutool 工具包-->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>4.1.5</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-crypto</artifactId>
			<version>5.8.13</version>
		</dependency>
		<dependency>
			<groupId>org.redisson</groupId>
			<artifactId>redisson</artifactId>
			<version>3.11.1</version>
		</dependency>
		<!-- redisson -->
		<dependency>
			<groupId>org.redisson</groupId>
			<artifactId>redisson</artifactId>
			<version>3.16.4</version>
		</dependency>
		<!--分页插件-->
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
			<version>1.2.7</version>
		</dependency>
		<!--xml解析-->
		<dependency>
			<groupId>org.jdom</groupId>
			<artifactId>jdom</artifactId>
			<version>1.1.3</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>nacos-config-spring-boot-starter</artifactId>
			<version>${nacos-config.version}</version>
		</dependency>

		<dependency>
			<groupId>net.summerfarm</groupId>
			<artifactId>xianmu-rec-strategy</artifactId>
			<version>1.5-RELEASE</version>
		</dependency>

		<dependency>
			<groupId>net.xianmu.starter</groupId>
			<artifactId>xianmu-dubbo-support</artifactId>
			<version>1.0.9</version>
		</dependency>

		<!--apache httpclient http协议相关工具类-->
		<dependency>
				<groupId>commons-httpclient</groupId>
				<artifactId>commons-httpclient</artifactId>
				<version>3.1</version>
		</dependency>

		<!--aliyun 短信sdk-->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
			<version>4.0.6</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-dysmsapi</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>1.1.17</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.4</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.session</groupId>
			<artifactId>spring-session-data-redis</artifactId>
			<version>2.2.2.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15to18</artifactId>
			<version>1.69</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.poi/poi -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>3.15</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>3.15</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun.openservices</groupId>
			<artifactId>ons-client</artifactId>
			<version>1.8.8.4.Final</version>
		</dependency>
		<!--arms-->
		<dependency>
			<groupId>com.alibaba.arms.apm</groupId>
			<artifactId>arms-sdk</artifactId>
			<version>1.7.3</version>
		</dependency>
		<dependency>
			<groupId>net.xianmu.starter</groupId>
			<artifactId>xianmu-oss-support</artifactId>
			<version>1.0.2</version>
		</dependency>

		<dependency>
			<groupId>net.xianmu</groupId>
			<artifactId>authentication-sdk</artifactId>
			<version>1.1.9</version>
		</dependency>
		<dependency>
			<groupId>net.xianmu</groupId>
			<artifactId>authentication-client</artifactId>
			<version>1.1.25</version>
		</dependency>
		<dependency>
			<groupId>com.cosfo</groupId>
			<artifactId>message-client</artifactId>
			<version>1.3.1-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>4.11.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<version>4.11.0</version>
			<scope>test</scope>
		</dependency>
		<!-- 添加 ByteBuddy 依赖 -->
		<dependency>
			<groupId>net.bytebuddy</groupId>
			<artifactId>byte-buddy</artifactId>
			<version>1.12.22</version>
<!--			Hibernate 使用 ByteBuddy 来进行字节码增强-->
<!--			<scope>test</scope>-->
		</dependency>
		<dependency>
			<groupId>net.bytebuddy</groupId>
			<artifactId>byte-buddy-agent</artifactId>
			<version>1.12.22</version>
<!--			Hibernate 使用 ByteBuddy 来进行字节码增强-->
<!--			<scope>test</scope>-->
		</dependency>
		<dependency>
			<groupId>com.lianok.docking</groupId>
			<artifactId>lianok-sdk-java</artifactId>
			<version>1.2.0-RELEASE</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<!--自动生成mybatis相关代码 插件-->
			<!--<plugin>-->
			<!--<groupId>org.mybatis.generator</groupId>-->
			<!--<artifactId>mybatis-generator-maven-plugin</artifactId>-->
			<!--<version>1.4.0</version>-->
			<!--<configuration>-->
			<!--<configurationFile>src/main/resources/generatorConfig.xml</configurationFile>-->
			<!--<verbose>true</verbose>-->
			<!--<overwrite>true</overwrite>-->
			<!--</configuration>-->
			<!--<dependencies>-->
			<!--<dependency>-->
			<!--<groupId>org.mybatis.generator</groupId>-->
			<!--<artifactId>mybatis-generator-core</artifactId>-->
			<!--<version>1.4.0</version>-->
			<!--</dependency>-->
			<!--</dependencies>-->
			<!--</plugin>-->

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<showWarnings>true</showWarnings>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>

			<plugin>
				<groupId>org.sonarsource.scanner.maven</groupId>
				<artifactId>sonar-maven-plugin</artifactId>
				<version>3.3.0.603</version>
			</plugin>
		</plugins>
	</build>


</project>
